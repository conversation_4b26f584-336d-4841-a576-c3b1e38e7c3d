import UIKit
import Flutter
// import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // if let googleMapKey = Bundle.main.object(forInfoDictionaryKey: "GOOGLE_MAP_KEY") as? String {
    //   GMSServices.provideAPIKey(googleMapKey)
    // } 

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
