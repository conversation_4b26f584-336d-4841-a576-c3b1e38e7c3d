plugins {
        id "com.android.application"
        id "kotlin-android"
        id "dev.flutter.flutter-gradle-plugin"
        id "com.google.gms.google-services"
       //id "com.google.firebase.crashlytics"
    }


def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

//DEPRECATED
//def flutterRoot = localProperties.getProperty('flutter.sdk')
//if (flutterRoot == null) {
//    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
//}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def dartEnvironmentVariables = []
if (project.hasProperty('dart-defines')) {
  dartEnvironmentVariables = project.property('dart-defines')
      .split(',')
      .collectEntries { entry ->
        def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
        [(pair.first()): pair.last()]
      }
}

//DEPRECATED
/*apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
//apply plugin: 'com.smileidentity.smile-id-android'

apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"*/


// Project background_geolocation = project(':flutter_background_geolocation')
// apply from: "${background_geolocation.projectDir}/background_geolocation.gradle"


android {
    namespace 'co.tradedepot.shop'
    compileSdkVersion 35
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    packagingOptions {
        exclude 'META-INF/androidx.*'
        exclude 'androidx.*'
        exclude 'META-INF/DEPENDENCIES'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "co.tradedepot.shop"
        minSdkVersion 28
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        buildConfigField 'String', 'INTERCOM_APP_ID', "\"${dartEnvironmentVariables.INTERCOM_APP_ID}\""
        buildConfigField 'String', 'INTERCOM_ANDROID_API_KEY', "\"${dartEnvironmentVariables.INTERCOM_ANDROID_API_KEY}\""
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            shrinkResources true
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    flavorDimensions "flavor-type"

    productFlavors {
        dev {
            // Assigns this product flavor to the "version" flavor dimension.
            // This property is optional if you are using only one dimension.
            dimension "flavor-type"
            applicationIdSuffix ".dev"
        }
        prod {
            dimension "flavor-type"
        }
    }
  
    lint {
        checkReleaseBuilds false
        disable 'InvalidPackage'
    }

    buildFeatures {
        buildConfig true
    }
}

flutter {
    source '../..'
}

dependencies {
    def work_version = "2.7.1"
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.android.gms:play-services-maps'
    implementation "androidx.work:work-runtime:$work_version"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
}

//DEPRECATED
//apply plugin: 'com.google.gms.google-services'
