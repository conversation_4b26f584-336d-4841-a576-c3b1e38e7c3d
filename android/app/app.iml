<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":app" external.linked.project.path="$MODULE_DIR$" external.root.project.path="$MODULE_DIR$/.." external.system.id="GRADLE" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":app" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" value="3.5.0" />
        <option name="LAST_KNOWN_AGP_VERSION" value="3.5.0" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="devDebug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDevDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDevDebugSources" />
        <afterSyncTasks>
          <task>generateDevDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/src/main/res;file://$MODULE_DIR$/../../build/app/generated/res/google-services/dev/debug;file://$MODULE_DIR$/../../build/app/generated/res/resValues/dev/debug" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
      </configuration>
    </facet>
    <facet type="kotlin-language" name="Kotlin">
      <configuration version="3" platform="JVM 1.6" allPlatforms="JVM [1.6]" useProjectSettings="false">
        <compilerSettings>
          <option name="additionalArguments" value="-Xallow-no-source-files" />
        </compilerSettings>
        <compilerArguments>
          <option name="destination" value="$MODULE_DIR$/../../build/app/tmp/kotlin-classes/devDebug" />
          <option name="classpath" value="$MODULE_DIR$/../../build/app/intermediates/flutter/devDebug/libs.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/location/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_plugin_android_lifecycle/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/cloud_firestore/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/firebase_auth/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/image_picker/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/geocoder/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/package_info/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/app_settings/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/shared_preferences/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_keyboard_visibility/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/libphonenumber/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/firebase_messaging/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_segment/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/sentry_flutter/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/firebase_core/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/share/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/intercom_flutter/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_paystack/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/url_launcher/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/connectivity/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_secure_storage/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_introduction_tooltip/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/Desktop/project/mobile/td-shoptopup-mobile/build/flutter_libphonenumber/intermediates/compile_library_classes/debug/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/armeabi_v7a_debug/1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb/553a9945dfe723ed36a064dda397c994dcdc9f80/armeabi_v7a_debug-1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/arm64_v8a_debug/1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb/d9b554be145a66a14832156e5e178e7a2601109e/arm64_v8a_debug-1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/x86_64_debug/1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb/80d270807fe2c1571fc9b40c610932586db5a1fb/x86_64_debug-1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/x86_debug/1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb/a97511b678a49dbb40bf6dc57f01c12d9118f136/x86_debug-1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/36cfb6d7833b4402a9fc5193ade6df28/jetified-play-services-location-16.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a25ed814bd13005063462778a2759d5d/intercom-sdk-fcm-8.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/2565311988e55b3d5312fb0a7852711a/jetified-firebase-messaging-21.0.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c3bdce67af9e5628936af356b44de019/firebase-iid-21.0.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c26768f40c4f689e9b19c82b9e39647/firebase-iid-interop-17.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8ac2c71cc9965485e4fc4eaca5d4253a/play-services-base-17.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/d5f4a8710f2e469406705a0bd4ce35a2/play-services-places-placereport-16.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/56b20d2680d3becb2b1e2898e0d7e68f/play-services-cloud-messaging-16.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7980c9ecb5be9dfc057729fef3bc5992/firebase-datatransport-17.0.10/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6cd291baf63ce1d92bd9e2b8f277d80f/firebase-installations-16.3.5/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/158f115724b1dd3bd00d627c610ae1c1/jetified-firebase-common-19.5.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c87dd258a46a0b79a93c7553dbfc38bd/firebase-installations-interop-16.0.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/0bef4825910dfde9d1394554b46b96ad/play-services-tasks-17.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/d1944091f7ff0945d4c66df681b95990/play-services-stats-17.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/17ae1d43c5d34951218770dd439cf555/firebase-measurement-connector-18.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/3122c8e7af29eb33c044408186c1ea6f/jetified-play-services-basement-17.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/flutter_embedding_debug/1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb/56afa13a367705386ec224955dcee1fbc9ac3aa9/flutter_embedding_debug-1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7574dbeb5f886b2d87e0aa1c3d3d61b3/jetified-intercom-sdk-base-8.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7574dbeb5f886b2d87e0aa1c3d3d61b3/jetified-intercom-sdk-base-8.2.0/jars/libs/repackaged_dependencies.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/1e93df84b8529c23bfb483e2ea6fcf37/material-1.3.0-alpha02/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/66bfb951e97588be5fb290119f091223/constraintlayout-2.0.0-beta7/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7a5a19094ce0a4c82d7726c62fee6e4e/appcompat-1.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5dbfce71f433e0e1a0de9678c0c2469b/viewpager2-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5666a698d92addcaa9afc0d2dc928f3c/fragment-1.2.5/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/18adaefdaea5da1a208bfe0f437541e5/appcompat-resources-1.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6334e1d703856d8e42c020026627a2b2/vectordrawable-animated-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a274ceffe9ce868572e4024cdfae6ce2/vectordrawable-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/d9499f4bdd266ea4b9460ab12a5bd541/recyclerview-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/df3ce475aa4ed38efc3f47b955505925/legacy-support-core-ui-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/fae937760ecb4bf1935899bb038386e3/coordinatorlayout-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/44719256511e1b5d9c4e884545819001/dynamicanimation-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/d46e1b8c3d69f09e5b94f068d761e87f/transition-1.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/4b650d79e6802d147079650329ad58c2/legacy-support-core-utils-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6dcfe9c402810aa38a8828de540d52b6/viewpager-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/ee6b401f71594174ea6d12f24ac8e371/loader-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a5bf625b45ef1be08b11c229ae7e5340/activity-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c384dd48e2b359b7a566e118894f1c7f/drawerlayout-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/198b917316e651b2f0fd4ad3817e978d/slidingpanelayout-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7daa265c4096fa6e90d7a6899e13f8b4/customview-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/94e748b0b7ff21e771ed2583df5487f5/swiperefreshlayout-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/aa2035b1035b9cb7aa4154f87d58e48a/asynclayoutinflater-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/aff7487e901e044f48e46661fdb6d5e4/core-1.3.2/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/28c4c5bdafea2ddf3e85d959ac73b331/versionedparcelable-1.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.2.0/cd3478503da69b1a7e0319bd2d1389943db9b364/lifecycle-common-java8-2.2.0.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7ef2e217d521f149418b0f2048edfbf2/lifecycle-runtime-2.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/371c78651af87ddced0337e11fb12f68/lifecycle-viewmodel-savedstate-2.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/cf85dc30ab6cacc9944b3909bc2d8d12/lifecycle-livedata-2.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/9eaeca43eafca3571397ba287803aba7/lifecycle-livedata-core-2.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/762ad15499b76dd30f976b7cf1343124/savedstate-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.2.0/4ef09a745007778eef83b92f8f23987a8ea59496/lifecycle-common-2.2.0.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/b794a51dbfed83de009597be2e87ed4c/jetified-transport-backend-cct-2.3.3/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/0d25d9ab8ed6fcc8fb1a19af478f6b32/jetified-transport-runtime-2.2.5/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6ba5120b11eb494c474b69e284c57d48/jetified-transport-api-2.2.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/3c3ee21d17b3b8125826328aac960b9e/jetified-firebase-components-16.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c935413843f07dc7ac82bb7eef7121de/firebase-encoders-json-17.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders/16.1.0/267565db8531da1483692e27eb58f93ec894c78/firebase-encoders-16.1.0.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/69d193ca2f1ea6b9a11050801885239e/core-runtime-2.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/185feb494a2d87b9c1f6fffc538a0d76/lifecycle-viewmodel-2.2.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/2e2a659e696f8e7d3340c29f61c83c6a/cardview-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/aefbed8c442a61c20bbad6e44b256226/cursoradapter-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/e39ea6fc52811cacf50dc270a17f570b/interpolator-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c0bcbfb021564b3b41a4611a164498f0/documentfile-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/3c765db1ed5a358b855c88f3ebc4e5cb/localbroadcastmanager-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/234d8388d3548014233e9d14d722b8ec/print-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.1.0/e3a6fb2f40e3a3842e6b7472628ba4ce416ea4c8/annotation-1.1.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.4.20/9de2c79e95d4b4699a455e88ba285a95352e0bea/kotlin-stdlib-jdk7-1.4.20.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.4.20/9be77b243a362b745e365f286627b8724337009c/kotlin-stdlib-1.4.20.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.4.20/c6761d7805b5312302f2bbd78cda68c976ce0c70/kotlin-stdlib-common-1.4.20.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-annotations/16.0.0/dbeae20d6c97b747b59ef47b6dcf770ba1a60fa6/firebase-annotations-16.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.googlecode.libphonenumber/carrier/1.136/471adaf9398172cd0ca5c52f7dcc41357e7742ce/carrier-1.136.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.googlecode.libphonenumber/prefixmapper/2.146/a40d677348ebc45d59215ed4fe8f5ba141f38289/prefixmapper-2.146.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.googlecode.libphonenumber/libphonenumber/8.12.17/4b501ec9f65cd6336148d6061e6f21dd6f45a4a2/libphonenumber-8.12.17.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.0-beta7/3bcef826a5dd45ffcb47d32042ece40dd90a598f/constraintlayout-solver-2.0.0-beta7.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/760c7c41a35101d7845bdbb0b1316e52/annotation-experimental-1.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a310f3faf960dc3f9e54b49d6bb96532/android-composer-3.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/bcf53e14af9cb69d0dec1e0860548c34/android-composer-gallery-3.1.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/7820559cc967e0e0e906f760a623263a/flexbox-2.0.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8170e1b4780369d3eb3c75a69454753d/sentry-android-3.2.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/28537cd506730af82b8bf9c7f217cff9/sentry-android-ndk-3.2.1/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/13f412b9e1906c36f757709f7de89de3/jetified-sentry-android-core-3.2.1/jars/classes.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.sentry/sentry/3.2.1/cb9bfd891aff0d78bc4dddb108ebf84aec4de271/sentry-3.2.1.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c7d953acc55dc4b3e953472bb182ad56/multidex-2.0.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f6ad3e5e4ec5ebc57f63e886c01b712/jetified-tracker-3.9.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/9b987b3e9b50ddb989cebb86615146ee/tracker-network-3.9.0/jars/classes.jar:/Users/<USER>/.gradle/caches/transforms-2/files-2.1/0d9b50a9c8d5c18b5ff2b476a436a637/installreferrer-2.1/jars/classes.jar:/Users/<USER>/Library/Android/sdk/platforms/android-30/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/28.0.3/core-lambda-stubs.jar" />
          <option name="noStdlib" value="true" />
          <option name="noReflect" value="true" />
          <option name="moduleName" value="app_devDebug" />
          <option name="languageVersion" value="1.3" />
          <option name="apiVersion" value="1.3" />
          <option name="pluginOptions">
            <array />
          </option>
          <option name="pluginClasspaths">
            <array />
          </option>
          <option name="errors">
            <ArgumentParseErrors />
          </option>
        </compilerArguments>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/../../build/app/intermediates/javac/devDebug/classes" />
    <output-test url="file://$MODULE_DIR$/../../build/app/intermediates/javac/devDebugUnitTest/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/devDebug/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDevDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDevDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/dev/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDev/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDev/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/kotlin" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/shaders" isTestSource="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/aidl_source_output_dir/devDebug/compileDevDebugAidl/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/aidl_source_output_dir/devDebug/compileDevDebugAidl/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/aidl_source_output_dir/devDebugAndroidTest/compileDevDebugAndroidTestAidl/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/aidl_source_output_dir/devDebugAndroidTest/compileDevDebugAndroidTestAidl/out" isTestSource="true" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebug/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebug/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebugAndroidTest/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebugAndroidTest/out" isTestSource="true" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebugUnitTest/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/ap_generated_sources/devDebugUnitTest/out" isTestSource="true" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/renderscript_source_output_dir/devDebug/compileDevDebugRenderscript/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/renderscript_source_output_dir/devDebug/compileDevDebugRenderscript/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/renderscript_source_output_dir/devDebugAndroidTest/compileDevDebugAndroidTestRenderscript/out">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/renderscript_source_output_dir/devDebugAndroidTest/compileDevDebugAndroidTestRenderscript/out" isTestSource="true" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/res/google-services/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/res/google-services/dev/debug" type="java-resource" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/res/resValues/androidTest/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/res/resValues/androidTest/dev/debug" type="java-test-resource" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/res/resValues/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/res/resValues/dev/debug" type="java-resource" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/res/rs/androidTest/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/res/rs/androidTest/dev/debug" type="java-test-resource" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/res/rs/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/res/rs/dev/debug" type="java-resource" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/source/buildConfig/androidTest/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/source/buildConfig/androidTest/dev/debug" isTestSource="true" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../build/app/generated/source/buildConfig/dev/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../build/app/generated/source/buildConfig/dev/debug" isTestSource="false" generated="true" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 30 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Gradle: __local_aars__:./../build/app/intermediates/flutter/devDebug/libs.jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.flutter:armeabi_v7a_debug:1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.flutter:arm64_v8a_debug:1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.flutter:x86_64_debug:1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.flutter:x86_debug:1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.flutter:flutter_embedding_debug:1.0.0-21fa8bb99e9a20563d9a7d39afeba8e5c811a7eb@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.collection:collection:1.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common-java8:2.2.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common:2.2.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-encoders:16.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-common:2.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation:1.1.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.20@jar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib:1.4.20@jar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-common:1.4.20@jar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains:annotations:13.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-annotations:16.0.0@jar" level="project" />
    <orderEntry type="library" name="Gradle: javax.inject:javax.inject:1@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.googlecode.libphonenumber:carrier:1.136@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.googlecode.libphonenumber:prefixmapper:2.146@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.googlecode.libphonenumber:libphonenumber:8.12.17@jar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.constraintlayout:constraintlayout-solver:2.0.0-beta7@jar" level="project" />
    <orderEntry type="library" name="Gradle: io.sentry:sentry:3.2.1@jar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-location:16.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: io.intercom.android:intercom-sdk-fcm:8.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-messaging:21.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-iid:21.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-iid-interop:17.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-base:17.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-places-placereport:16.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-cloud-messaging:16.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-datatransport:17.0.10@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-installations:16.3.5@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-common:19.5.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-installations-interop:16.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-tasks:17.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-stats:17.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-measurement-connector:18.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.gms:play-services-basement:17.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: io.intercom.android:intercom-sdk-base:8.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.material:material:1.3.0-alpha02@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.constraintlayout:constraintlayout:2.0.0-beta7@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat:1.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager2:viewpager2:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.fragment:fragment:1.2.5@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat-resources:1.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.recyclerview:recyclerview:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.legacy:legacy-support-core-ui:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.dynamicanimation:dynamicanimation:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.transition:transition:1.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.legacy:legacy-support-core-utils:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.loader:loader:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.activity:activity:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.customview:customview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core:1.3.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.versionedparcelable:versionedparcelable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-runtime:2.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.savedstate:savedstate:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.datatransport:transport-backend-cct:2.3.3@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.datatransport:transport-runtime:2.2.5@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.datatransport:transport-api:2.2.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-components:16.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.firebase:firebase-encoders-json:17.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-runtime:2.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cardview:cardview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.interpolator:interpolator:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.documentfile:documentfile:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.print:print:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation-experimental:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.intercom:android-composer:3.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.intercom:android-composer-gallery:3.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android:flexbox:2.0.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: io.sentry:sentry-android:3.2.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: io.sentry:sentry-android-ndk:3.2.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: io.sentry:sentry-android-core:3.2.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.multidex:multidex:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.kochava.base:tracker:3.9.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.kochava.base:tracker-network:3.9.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.android.installreferrer:installreferrer:2.1@aar" level="project" />
    <orderEntry type="module" module-name="location" />
    <orderEntry type="module" module-name="flutter_plugin_android_lifecycle" />
    <orderEntry type="module" module-name="cloud_firestore" />
    <orderEntry type="module" module-name="package_info_plus" />
    <orderEntry type="module" module-name="firebase_auth" />
    <orderEntry type="module" module-name="image_picker" />
    <orderEntry type="module" module-name="geocoder" />
    <orderEntry type="module" module-name="package_info" />
    <orderEntry type="module" module-name="app_settings" />
    <orderEntry type="module" module-name="shared_preferences" />
    <orderEntry type="module" module-name="flutter_keyboard_visibility" />
    <orderEntry type="module" module-name="libphonenumber" />
    <orderEntry type="module" module-name="firebase_messaging" />
    <orderEntry type="module" module-name="flutter_segment" />
    <orderEntry type="module" module-name="sentry_flutter" />
    <orderEntry type="module" module-name="firebase_core" />
    <orderEntry type="module" module-name="share" />
    <orderEntry type="module" module-name="intercom_flutter" />
    <orderEntry type="module" module-name="flutter_paystack" />
    <orderEntry type="module" module-name="url_launcher" />
    <orderEntry type="module" module-name="connectivity" />
    <orderEntry type="module" module-name="flutter_secure_storage" />
    <orderEntry type="module" module-name="flutter_introduction_tooltip" />
    <orderEntry type="module" module-name="flutter_libphonenumber" />
  </component>
</module>