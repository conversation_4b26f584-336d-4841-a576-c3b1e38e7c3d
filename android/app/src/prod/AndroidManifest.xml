<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="co.tradedepot.shop">
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <application
            android:name="co.tradedepot.shop.MainApplication"
            android:allowBackup="false"
            android:label="@string/app_name"
            android:icon="@mipmap/ic_launcher">
        <activity
                android:name=".MainActivity"
                android:launchMode="singleTop"
                android:exported="true"
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="7oywd.app.link" android:scheme="https"/>
                <data android:host="7oywd-alternate.app.link" android:scheme="https"/>
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="tradedepot.co" android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="tradedepot"/>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <meta-data android:name="io.branch.sdk.TestMode" tools:replace="android:value" android:value="false" />
        <meta-data android:name="io.branch.sdk.BranchKey" tools:replace="android:value"
                   android:value="key_live_ha3lTmPJmNhx1q4DUMJJGpdkEvnQNY7N"/>
    </application>
</manifest>