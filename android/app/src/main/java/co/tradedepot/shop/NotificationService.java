// package co.tradedepot.shop;

// import com.google.firebase.messaging.RemoteMessage;

// import io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService;

// import androidx.annotation.NonNull;

// import org.jetbrains.annotations.NotNull;

// public class NotificationService extends FlutterFirebaseMessagingService {

//     private static final String TAG = "NotificationService";

//     @Override
//     public void onMessageReceived(@NotNull final RemoteMessage remoteMessage) {
//         super.onMessageReceived(remoteMessage);
//         // AirshipFirebaseIntegration.processMessageSync(getApplicationContext(), remoteMessage);
//     }

//     @Override
//     public void onNewToken(@NonNull String token) {
//         super.onNewToken(token);
//         // AirshipFirebaseIntegration.processNewToken(getApplicationContext());
//     }
// }
