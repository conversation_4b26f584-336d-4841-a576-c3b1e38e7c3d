// package co.tradedepot.shop;

// import android.content.Context;
// import android.location.Location;
// import android.util.Log;

// import com.segment.analytics.Analytics;

// import org.jetbrains.annotations.NotNull;
// import org.json.JSONException;
// import org.json.JSONObject;

// import java.util.Arrays;

// import io.radar.sdk.Radar;
// import io.radar.sdk.RadarReceiver;
// import io.radar.sdk.model.RadarEvent;
// import io.radar.sdk.model.RadarEvent.RadarEventType;
// import io.radar.sdk.model.RadarGeofence;
// import io.radar.sdk.model.RadarRegion;
// import io.radar.sdk.model.RadarSegment;
// import io.radar.sdk.model.RadarUser;


// public class ShopRadarReceiver extends RadarReceiver {
//     private static final String TAG = "ShopRadar";


//     @Override
//     public void onClientLocationUpdated(@NotNull Context context, @NotNull Location location, boolean b, @NotNull Radar.RadarLocationSource radarLocationSource) {

//     }

//     @Override
//     public void onError(@NotNull Context context, @NotNull Radar.RadarStatus radarStatus) {

//     }

//     @Override
//     public void onEventsReceived(@NotNull Context context, @NotNull RadarEvent[] radarEvents, @NotNull RadarUser radarUser) {
//         if (radarEvents.length < 1) {
//             Log.e(TAG + "-onEvent", "Empty events");

//             return;
//         }

//         final RadarEvent event = radarEvents[0];

//         final RadarEventType eventType = event.getType();

//         Log.i(TAG + "-onEvent:Type", String.valueOf(eventType));

//         final RadarGeofence geofence = event.getGeofence();
//         if (geofence == null) {
//             Log.i(TAG + "-onEvent", "Geofence is null");
//             return;
//         }

//         try {
//             final JSONObject meta = radarUser.getMetadata();
//             if (meta == null) return;
//             String outletId = meta.getString("outletId");
//             if (!outletId.equals(geofence.getExternalId())) {
//                 Log.i(TAG + "-onEvent", "Outlet id is different from geofence ID");
//                 return;
//             }
//         } catch (JSONException e) {
//             e.printStackTrace();
//             return;
//         }

//         String eventName = "";

//         if (eventType == RadarEventType.USER_ENTERED_GEOFENCE) {
//             eventName = "In Store";
//         } else if (eventType == RadarEventType.USER_EXITED_GEOFENCE) {
//             eventName = "Out of Store";
//         }

//         if (eventName.isEmpty()) return;

//         try {
//             Analytics.with(context).track(eventName);
//         } catch (Exception e) {
//             Log.e(TAG + "-onEvent", "Failed to track");
//             e.printStackTrace();
//         }

//         Log.i(TAG + "-Tracked", eventName);
//     }

//     @Override
//     public void onLocationUpdated(@NotNull Context context, @NotNull Location location, @NotNull RadarUser radarUser) {
//     }

//     @Override
//     public void onLog(@NotNull Context context, @NotNull String s) {

//     }
// }