package co.tradedepot.shop;

import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.gms.maps.OnMapsSdkInitializedCallback;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugins.GeneratedPluginRegistrant;
import com.google.android.gms.maps.MapsInitializer;
import com.google.android.gms.maps.MapsInitializer.Renderer;

public class MainActivity extends FlutterFragmentActivity  implements OnMapsSdkInitializedCallback {

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    MapsInitializer.initialize(this, Renderer.LATEST, this);
  }

  @Override
  public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
    GeneratedPluginRegistrant.registerWith(flutterEngine);
  }


  @Override
  public void onMapsSdkInitialized(@NonNull Renderer renderer) {
    switch (renderer) {
      case LATEST:
        Log.d("SHOP APP NewRendererLog", "The latest version of the renderer is used.");
        break;
      case LEGACY:
        Log.d("SHOP APP NewRendererLog", "The legacy version of the renderer is used.");
        break;
    }
  }
}
