<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="co.tradedepot.shop">
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <application
            android:name="co.tradedepot.shop.MainApplication"
            android:allowBackup="false"
            android:label="@string/app_name_dev"
            tools:replace="android:label"
            android:icon="@mipmap/ic_launcher">
        <activity
                android:name=".MainActivity"
                android:launchMode="singleTop"
                android:exported="true"
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="7oywd.test-app.link" android:scheme="https"/>
                <data android:host="7oywd-alternate.test-app.link" android:scheme="https"/>
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="dev.tradedepot.co" android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="tradedepot"/>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <!-- Set to true to use Branch_Test_Key (useful when simulating installs and/or switching between debug and production flavors) -->
        <meta-data android:name="io.branch.sdk.BranchKey.test" tools:replace="android:value"
                   android:value="key_test_bi7bNfNHpJdq0sXtOOM77ofkusbOG03Y"/>
        <meta-data android:name="io.branch.sdk.TestMode" tools:replace="android:value" android:value="true"/>
    </application>
</manifest>