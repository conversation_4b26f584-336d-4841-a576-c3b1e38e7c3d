//DEPRECATED
//buildscript {
//    ext.kotlin_version = '1.9.21'
//    ext {
//        compileSdkVersion   = 34                // or higher
//        targetSdkVersion    = 33                // or higher
//        appCompatVersion    = "1.0.2"           // or higher
//        playServicesLocationVersion = "17.0.0"  // or higher
//    }
//    repositories {
//        google()
//        mavenCentral()
//          maven {
//            url 'https://oss.sonatype.org/content/repositories/snapshots/'
//          }
//    }
//
//    dependencies {
//        classpath 'com.android.tools.build:gradle:8.1.3'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath 'com.google.gms:google-services:4.4.0'
//       // classpath group: 'com.smileidentity', name: 'smile-id-android', version: '1.0.1'
//
//    }
//}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"

    // Fix namespace error on gradle 8.0 for plugins that are yet to migrate
    afterEvaluate {
        // check if android block is available
        if (it.hasProperty('android')) {

            if (it.android.namespace == null) {
                def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                def packageName = <EMAIL>()
                println("Setting ${packageName} as android namespace")
                android.namespace = packageName
            }
        }

    }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
