# ShopTopup Architecture Guidelines

## Overview

ShopTopup follows Clean Architecture principles to ensure separation of concerns, testability, and maintainability. This document outlines the standard architecture pattern that all modules should follow.

## Module Structure

Each feature module should be organized as follows:

```
/feature_name/
  /data/
    /data_sources/
      feature_data_source.dart (interface)
      impl/
        feature_data_source_impl.dart (implementation)
    /models/
      model_1.dart
      model_2.dart
    /repositories_impl/
      feature_repo_impl.dart
  /domain/
    /entities/
      entity_1.dart
      entity_2.dart
    /repositories/
      feature_repo.dart (interface)
    /use_cases/
      use_case_1.dart
      use_case_2.dart
    /params/
      param_1.dart
      param_2.dart
  /presentation/
    /logic/
      /bloc/ or /cubit/
        feature_bloc.dart or feature_cubit.dart
        feature_event.dart or feature_state.dart
    /ui/
      /screens/
        feature_screen.dart
        /mobile/
          mobile_feature_screen.dart
        /desktop/
          desktop_feature_screen.dart
      /widgets/
        feature_widget_1.dart
        feature_widget_2.dart
  feature_dependencies.dart (dependency registration)
```

## Layer Responsibilities

### Data Layer

- **Data Sources**: Responsible for fetching data from external sources (API, database, etc.)
- **Models**: Data transfer objects (DTOs) that represent the structure of data from external sources
- **Repository Implementations**: Implement the repository interfaces defined in the domain layer

### Domain Layer

- **Entities**: Core business objects
- **Repositories**: Interfaces that define the contract for data operations
- **Use Cases**: Encapsulate business logic for specific operations
- **Params**: Parameter objects for use cases

### Presentation Layer

- **Logic**: State management (Bloc/Cubit) for UI components
- **UI**: Screens and widgets that display data to the user

## Dependency Registration

All dependencies should be registered in a single file per feature module:

```dart
void registerFeatureDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<FeatureDataSource>(
    () => FeatureDataSourceImpl(locator(), config.baseUrl),
  );

  // Repositories
  locator.registerLazySingleton<FeatureRepo>(
    () => FeatureRepoImpl(locator()),
  );

  // Use cases
  locator.registerLazySingleton(() => UseCase1(locator()));
  locator.registerLazySingleton(() => UseCase2(locator()));
}
```

## Error Handling

All errors should be handled through the centralized `ErrorHandler` class:

```dart
try {
  // Operation that might fail
} catch (e, s) {
  ErrorHandler.report(e, s, hint: {'context': 'feature operation'});
  return ApiResult.apiFailure(
    error: ApiExceptions.defaultError('User-friendly error message'),
  );
}
```

## State Management

Use Bloc/Cubit for state management with sealed classes for states:

```dart
sealed class FeatureState extends Equatable {
  const FeatureState();
  
  @override
  List<Object> get props => [];
}

final class FeatureInitial extends FeatureState {}
final class FeatureLoading extends FeatureState {}
final class FeatureLoaded extends FeatureState {
  final Data data;
  const FeatureLoaded(this.data);
  
  @override
  List<Object> get props => [data];
}
final class FeatureError extends FeatureState {
  final String message;
  const FeatureError(this.message);
  
  @override
  List<Object> get props => [message];
}
```

## Responsive Design

Use the `ResponsiveDesign` widget for all screens to ensure proper rendering on different device sizes:

```dart
@override
Widget build(BuildContext context) {
  return ResponsiveDesign(
    largeScreen: DesktopFeatureScreen(),
    mediumScreen: MobileFeatureScreen(),
    smallScreen: MobileFeatureScreen(),
  );
}
```

## Testing

Each layer should have appropriate tests:

- **Data Layer**: Unit tests for data sources and repository implementations
- **Domain Layer**: Unit tests for use cases
- **Presentation Layer**: Widget tests for UI components and bloc tests for state management

## Performance Considerations

- Use `const` constructors where appropriate
- Break down large widgets into smaller, reusable components
- Implement pagination for large data sets
- Use `RepaintBoundary` for complex list items
- Optimize image loading with `CachedImage` widget
