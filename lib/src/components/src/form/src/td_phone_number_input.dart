import 'dart:async';

import 'package:bottom_sheet/bottom_sheet.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/contacts_picker/contacts_picker.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/src/components/src/form/src/bloc/td_text_controller_cubit.dart';
import 'package:shop/src/components/src/form/src/td_text_field_error.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

const invalidNumber = 'Please enter a valid phone number';

class TdPhoneNumberInput extends StatefulWidget {
  final String? title;
  final bool readonly;
  final bool disabled;
  final bool autofocus;
  final VoidCallback? autoSubmit;
  final TdTextController? textController;
  final TextInputAction? textInputAction;
  final VoidCallback? onSubmitted;
  final VoidCallback? onTap;
  final InputDecoration? inputDecoration;
  final Function(String)? onTextChanged;
  final Function(String)? watchTextChanges;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? bottom;
  final Widget? suffixIcon;
  final BoxConstraints? suffixConstraints;
  final bool inRow;
  final bool? autoValidate;
  final bool errorMessageRight;
  final bool isSuffixConstant;
  final bool fixed;
  final double? height;

  const TdPhoneNumberInput({
    super.key,
    this.title,
    this.readonly = false,
    this.disabled = false,
    this.autofocus = false,
    this.autoSubmit,
    this.textController,
    this.textInputAction,
    this.onSubmitted,
    this.onTap,
    this.inputDecoration,
    this.onTextChanged,
    this.watchTextChanges,
    this.keyboardType,
    this.inputFormatters,
    this.bottom,
    this.suffixIcon,
    this.suffixConstraints,
    this.inRow = false,
    this.autoValidate,
    this.errorMessageRight = false,
    this.isSuffixConstant = false,
    this.fixed = false,
    this.height,
  });

  /// Static method to perform async validation on a phone number
  static Future<String?> validatePhoneAsync(
      String? value, String region) async {
    if (value == null || value.isEmpty) {
      return null; // Let other validators handle empty values
    }

    try {
      await parse(value);
      return null; // Valid phone number
    } catch (_) {
      // Try with plus addition
      try {
        await parse('+$value');
        return null; // Valid with plus
      } catch (_) {
        // Try removing leading zeros
        if (value.startsWith('00')) {
          try {
            await parse('+${value.substring(2)}');
            return null; // Valid after removing zeros
          } catch (_) {
            // Try with user region
            try {
              await parse(value, region: region);
              return null; // Valid with region
            } catch (_) {
              return 'Please enter a valid phone number';
            }
          }
        } else {
          // Try with user region
          try {
            await parse(value, region: region);
            return null; // Valid with region
          } catch (_) {
            return 'Please enter a valid phone number';
          }
        }
      }
    }
  }

  @override
  State<TdPhoneNumberInput> createState() => _TdPhoneNumberInputState();
}

class _TdPhoneNumberInputState extends State<TdPhoneNumberInput> {
  late final _controller =
      TextEditingController(text: widget.textController?.text ?? '');
  final _formFieldKey = GlobalKey<FormFieldState>();
  String? _errorMessage;
  bool _hasSubmitted = false;
  Contact? _selectedContact;
  late String _region;
  CountryWithPhoneCode? _country;
  StreamSubscription? _sub;

  @override
  void initState() {
    super.initState();
    _region = context.read<UserCubit>().region;
    _initializeWidget();
  }

  Future<void> _initializeWidget() async {
    _listenToTextController();
    await _initLibPhoneNumber();
  }

  @override
  void dispose() {
    _controller.dispose();
    _sub?.cancel();
    super.dispose();
  }

  void _listenToTextController() {
    _sub?.cancel();
    _sub = widget.textController?.stream.listen(_handleTextControllerState);
  }

  void _handleTextControllerState(TdTextState state) {
    if (!mounted) return;

    if (state is TdTextValidating) {
      setState(() => _hasSubmitted = true);
    } else if (state is TdTextCleared) {
      _clearText();
    }
  }

  void _clearText() {
    widget.textController?.controller?.clear();
    _controller.clear();
  }

  Future<void> _initLibPhoneNumber() async {
    await init();
    await _setHintText();
  }

  Future<void> _setHintText() async {
    setState(() {
      _country = CountryManager().countries.firstWhereOrNull(
            (country) =>
                country.countryCode.toLowerCase() == _region.toLowerCase(),
          );
    });
  }

  Future<void> _openContacts() async {
    final permission = await ContactsPicker.invoke(context);
    if (!permission) return;

    final contact = await _showContactsBottomSheet();
    if (contact == null) return;

    await _handleSelectedContact(contact);
  }

  Future<void> _handleSelectedContact(Contact contact) async {
    final isValid = await _handlePhoneInput(contact.phones.first.number);

    if (!mounted) return;

    if (isValid) {
      FocusScope.of(context).unfocus();
    } else {
      _clearText();
      setState(() => _errorMessage = invalidNumber);
    }
  }

  Future<Contact?> _showContactsBottomSheet() async {
    final contact = await showFlexibleBottomSheet<Contact>(
      minHeight: 0,
      initHeight: 0.7,
      maxHeight: 0.95,
      context: context,
      isSafeArea: true,
      bottomSheetBorderRadius:
          const BorderRadius.vertical(top: Radius.circular(16)),
      builder: _buildContactPicker,
    );

    if (contact != null) {
      setState(() => _selectedContact = contact);
    }

    return contact;
  }

  Widget _buildContactPicker(
      BuildContext context, ScrollController scrollController, double offset) {
    return SelectContactsWidget(
      scrollController: scrollController,
      selectedContact: _selectedContact,
      onSelected: (contact) => Navigator.of(context).pop(contact),
    );
  }

  void updateValues(Map<String, dynamic> parsed, String value) {
    final formatted = parsed['e164'];
    final international = parsed['international'];

    _controller.text = international;
    widget.textController?.controller?.text = formatted;

    widget.onTextChanged?.call(formatted);
    widget.watchTextChanges?.call(formatted);

    setState(() => _errorMessage = null);

    // Trigger form field validation to clear any error
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formFieldKey.currentState?.validate();
    });
  }

  Future<bool> _handlePhoneInput(String? value) async {
    setState(() => _hasSubmitted = false);

    if (value == null || value.isEmpty) {
      setState(() => _errorMessage = invalidNumber);
      return false;
    }

    setState(() => _errorMessage = null);

    try {
      final parsed = await parse(value);
      updateValues(parsed, value);
      return true;
    } catch (_) {
      return await _parseWithPlusAddition(value);
    }
  }

  Future<bool> _parseWithPlusAddition(String phone) async {
    try {
      final parsed = await parse('+$phone');
      updateValues(parsed, phone);
      return true;
    } catch (_) {
      return await _parseWithRemovedZeros(phone);
    }
  }

  Future<bool> _parseWithRemovedZeros(String phone) async {
    if (phone.startsWith('00')) {
      try {
        final parsed = await parse('+${phone.substring(2)}');
        updateValues(parsed, phone);
        return true;
      } catch (_) {
        return await _parseWithUserRegion(phone);
      }
    }
    return await _parseWithUserRegion(phone);
  }

  Future<bool> _parseWithUserRegion(String phone) async {
    try {
      final parsed = await parse(phone, region: _region);
      updateValues(parsed, phone);
      return true;
    } catch (_) {
      setState(() => _errorMessage = invalidNumber);
      // Trigger form field validation to show the error
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _formFieldKey.currentState?.validate();
      });
      return false;
    }
  }

  bool get _renderError => widget.autoValidate == true
      ? true
      : (_hasSubmitted && _errorMessage != null);

  InputDecoration _buildInputDecoration(ThemeData theme) {
    final inputBorder = OutlineInputBorder(
        borderSide: const BorderSide(color: borderColor),
        borderRadius: BorderRadius.circular(8));

    return widget.inputDecoration ??
        InputDecoration(
          hintText: _country?.exampleNumberMobileInternational ??
              'Enter phone number',
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          suffixIcon: _buildSuffixIcon(),
          suffixIconConstraints: widget.suffixConstraints,
          enabledBorder: inputBorder,
          focusedBorder: inputBorder,
          border: inputBorder,
          counterText: "",
          hintStyle: theme.textTheme.bodyLarge?.copyWith(
            color: const Color(0xFFADAEBC),
            fontWeight: FontWeight.w400,
          ),
          filled: true,
          fillColor: Colors.white,
        );
  }

  Widget _buildSuffixIcon() {
    final icon = widget.suffixIcon ?? SvgPicture.asset(kSvgPhoneBookIcon);
    final button = IconButton(
      icon: icon,
      constraints: widget.suffixConstraints,
      onPressed: _openContacts,
    );

    return widget.isSuffixConstant ? AbsorbPointer(child: button) : button;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null) ...[
          Text(widget.title!, style: textTheme.bodyMedium),
          const SizedBox(height: 4),
        ],
        Padding(
          padding: _getPadding(),
          child: _buildTextField(theme),
        ),
        _buildErrorAndBottom(textTheme),
      ],
    );
  }

  EdgeInsets _getPadding() {
    if (!widget.inRow) return EdgeInsets.zero;
    return _errorMessage == null
        ? const EdgeInsets.only(bottom: 16)
        : EdgeInsets.zero;
  }

  Widget _buildTextField(ThemeData theme) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: widget.height ?? 48),
      child: TextFormField(
        key: _formFieldKey,
        controller: _controller,
        readOnly: widget.readonly,
        autofocus: widget.autofocus,
        keyboardType: TextInputType.phone,
        onTap: widget.onTap,
        onChanged: _handleTextChange,
        onEditingComplete: _handleEditingComplete,
        validator: _handleValidation,
        onFieldSubmitted: _handleFieldSubmitted,
        inputFormatters: widget.inputFormatters,
        decoration: _buildInputDecoration(theme),
      ),
    );
  }

  void _handleTextChange(String value) {
    if (_errorMessage != null) {
      setState(() => _errorMessage = null);
    }
  }

  void _handleEditingComplete() {
    widget.onSubmitted?.call();
    setState(() => _hasSubmitted = true);
  }

  /// Async validation method that waits for phone validation to complete
  Future<String?> validateAsync() async {
    final value = _controller.text;

    // First, run the standard validators from the textController
    if (widget.textController?.validators != null) {
      for (final validator in widget.textController!.validators) {
        final error = validator(value);
        if (error != null) {
          setState(() => _errorMessage = error);
          return error;
        }
      }
    }

    // For non-empty values, perform async phone validation and wait for result
    if (value.isNotEmpty) {
      final isValid = await _handlePhoneInput(value);
      if (!isValid && _errorMessage != null) {
        return _errorMessage;
      }
    }

    return null;
  }

  String? _handleValidation(String? value) {
    // First, run the standard validators from the textController
    if (widget.textController?.validators != null) {
      for (final validator in widget.textController!.validators) {
        final error = validator(value);
        if (error != null) {
          setState(() => _errorMessage = error);
          return error;
        }
      }
    }

    // If we have an existing phone validation error, return it
    if (_errorMessage != null) {
      return _errorMessage;
    }

    // For non-empty values, trigger async phone validation but don't wait for it
    // The async validation will update the UI when complete
    if (value != null && value.isNotEmpty) {
      _handlePhoneInput(value);
    }

    // Don't call onSubmitted during validation as it can cause focus traversal issues
    // onSubmitted should only be called when user actually submits the field
    return null;
  }

  void _handleFieldSubmitted(String value) {
    _handlePhoneInput(value);
    widget.onSubmitted?.call();
  }

  Widget _buildErrorAndBottom(TextTheme textTheme) {
    return SizedBox(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (_renderError && _errorMessage != null)
            _buildErrorMessage(textTheme)
          else
            const YMargin(20),
          if (_errorMessage == null)
            Expanded(
              child: Row(
                mainAxisAlignment: widget.errorMessageRight
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.start,
                children: [
                  TdTextFieldError(textController: widget.textController),
                  if (widget.bottom != null) widget.bottom!,
                ],
              ),
            )
          else
            const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        _errorMessage!,
        textAlign: TextAlign.right,
        style: textTheme.bodySmall?.copyWith(
          color: Colors.red,
          fontWeight: FontWeight.w500,
          height: 1,
        ),
        textHeightBehavior: const TextHeightBehavior(
          applyHeightToFirstAscent: false,
          applyHeightToLastDescent: false,
        ),
      ),
    );
  }
}

class FilterParams {
  final List<Contact> contacts;
  final String term;
  FilterParams(this.contacts, this.term);
}

List<Contact> filterContacts(FilterParams params) {
  final lowerTerm = params.term.toLowerCase();
  return params.contacts.where((contact) {
    final name = contact.displayName.toLowerCase();
    final phone = contact.phones.isNotEmpty
        ? contact.phones.first.number.toLowerCase()
        : '';
    return name.contains(lowerTerm) || phone.contains(lowerTerm);
  }).toList();
}

class SelectContactsWidget extends StatefulWidget {
  final ScrollController scrollController;
  final ValueSetter<Contact> onSelected;
  final Contact? selectedContact;

  const SelectContactsWidget({
    super.key,
    required this.scrollController,
    required this.onSelected,
    this.selectedContact,
  });

  @override
  State<SelectContactsWidget> createState() => SelectContactsWidgetState();
}

class SelectContactsWidgetState extends State<SelectContactsWidget> {
  List<Contact> contacts = [];
  bool isLoading = true;
  late List<Contact> filteredContacts = contacts;
  Timer? _debounceTimer;
  String _latestSearchTerm = '';
  bool _isFiltering = false;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), fetchContacts);
  }

  Future<void> fetchContacts() async {
    final data = await ContactsPicker.loadContacts();

    if (mounted) {
      setState(() {
        contacts = data;
        isLoading = false;
      });
    }
  }

  void _handleSearch(String value) {
    _latestSearchTerm = value;
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      if (value.isEmpty) {
        setState(() {
          filteredContacts = contacts;
          _isFiltering = false;
        });
      } else {
        setState(() {
          _isFiltering = true;
        });
        final params = FilterParams(contacts, value);
        final filtered = await compute(filterContacts, params);
        if (value == _latestSearchTerm) {
          setState(() {
            filteredContacts = filtered;
            _isFiltering = false;
          });
        } else {
          setState(() {
            _isFiltering = false;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Material(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16)
                .copyWith(top: 24),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Contact',
                    style: textTheme.titleLarge?.copyWith(fontSize: 18),
                  ),
                ),
                InkWell(
                  child: const Icon(Icons.close),
                  onTap: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: IgnorePointer(
              ignoring: isLoading,
              child: SearchTextField(
                loadingColor: primaryColor,
                svgPath: kSvgCustomerSearchIcon,
                filled: true,
                fillColor: Colors.white,
                hintText: 'Search contacts...',
                hintStyle: textTheme.bodyLarge?.copyWith(
                  color: const Color(0xFFADAEBC),
                  fontWeight: FontWeight.w400,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: borderColor),
                ),
                onSearch: _handleSearch,
                triggerSearchOnSubmit: false,
              ),
            ),
          ),
          const YMargin(5),
          const Divider(color: borderColor),
          const YMargin(2),
          Expanded(
            child: _buildContactsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactsList() {
    if (_isFiltering || isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (filteredContacts.isEmpty) {
      return const Center(child: Text('No contacts found'));
    }

    return ListView.builder(
      controller: widget.scrollController,
      itemCount: filteredContacts.length,
      itemBuilder: (context, index) =>
          _buildContactTile(filteredContacts[index]),
    );
  }

  Widget _buildContactTile(Contact contact) {
    final textTheme = Theme.of(context).textTheme;
    final isSelected = contact == widget.selectedContact;

    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => widget.onSelected(contact),
        child: ListTile(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          leading: SvgPicture.asset(kSvgPersonSelectIcon),
          title: Text(
            contact.displayName,
            textAlign: TextAlign.left,
            style:
                textTheme.bodyLarge?.copyWith(color: const Color(0xFF1F2937)),
          ),
          subtitle: contact.phones.isNotEmpty
              ? Text(
                  contact.phones.first.number,
                  style: textTheme.bodyMedium?.copyWith(color: labelColor),
                )
              : null,
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: isSelected ? primaryColor : Colors.grey,
          ),
        ),
      ),
    );
  }
}
