import 'package:dio/dio.dart';
import 'package:shop/app/beta_level/data/data_source/beta_levels_data_source.dart';
import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_commons_flutter/models/subscription.dart';
import 'package:td_flutter_core/connectivity/src/network_connection.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';

/// Implements the [BetaLevelsRepo] abstract class.
///
/// [BetaLevelsRepo] make calls to [BetaLevelsDataSource].
class BetaLevelsRepoImplementation implements BetaLevelsRepo {
  /// Instance of [BetaLevelsDataSource].
  final BetaLevelsDataSource dataSource;

  /// Returns network connectivity state.
  final NetworkConnection networkConnection;

  BetaLevelsRepoImplementation(this.dataSource, this.networkConnection);

  /// Call [BetaLevelsDataSource] to fetch Beta-Level Subscriptions.
  @override
  Future<ApiResult<List<SubscriptionPlan>>> fetchPlans() async {
    return dioInterceptor(
      () => dataSource.fetchPlans(),
    );
  }

  /// Call [BetaLevelsDataSource] to activate a Beta-Level Subscription Plan.
  @override
  Future<ApiResult> activatePlan(ActivatePlanParams params) async {
    return dioInterceptor(
      () => dataSource.activatePlan(params),
    );
  }

  /// Call [BetaLevelsDataSource] to activate a trial plan.
  @override
  Future<ApiResult> activateTrial(ActivateTrialParams params) async {
    return dioInterceptor(
      () => dataSource.activateTrial(params),
    );
  }

  /// Call [BetaLevelsDataSource] to get the total amount saved by a retail
  /// outlet on a running Beta-Level Subscription.
  @override
  Future<ApiResult<num>> getOutletSavings(String outletId) async {
    if (await networkConnection.isDeviceConnected) {
      try {
        num result = await dataSource.getOutletSavings(outletId);
        return ApiResult.success(data: result);
      } on DioError catch (error) {
        if (error.response?.statusCode != null &&
            error.response!.statusCode == 404) {
          return ApiResult.success(data: 0);
        }
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(error));
      } catch (exception, s) {
        ErrorHandler.report(exception, s);
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [BetaLevelsDataSource] to create a payment authorization request.
  @override
  Future<ApiResult<String?>> authorizePayment(
      AuthorizePaymentParams params) async {
    return dioInterceptor(
      () => dataSource.authorizePayment(params),
    );
  }

  /// Call [BetaLevelsDataSource] to validate payment from [authorizePayment] request.
  @override
  Future<ApiResult<String?>> verifyPayment(VerifyPaymentParams params) async {
    return dioInterceptor(
      () => dataSource.verifyPayment(params),
    );
  }
}
