import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:td_commons_flutter/models/subscription.dart';
import 'package:td_flutter_core/services/api/td_api.dart';

import 'beta_levels_data_source.dart';

/// Implements [BetaLevelsDataSource] abstract class.
///
/// Makes network call.
class BetaLevelsDataSourceImplementation implements BetaLevelsDataSource {
  static const String _getSubscriptionplansPath = 'shop/v4/getBetaLevels';
  static const String _subscribeBetaLevelPath = 'shop/v4/subscribeBetaLevel';
  static const String _trialSubscriptionPath = 'shop/v2/trial-subscription';
  static const String _outletSavingsPath = 'shop/v2/get-savings';
  static const String _authorizePaymentPath = 'shop/v1/authorize';
  static const String _verifyPaymentPath = 'shop/v1/verify-reference';

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  BetaLevelsDataSourceImplementation(this._apiClient, this._firebaseServiceUrl);

  /// Returns beta-level subscription plans.
  @override
  Future<List<SubscriptionPlan>> fetchPlans() async {
    String url = '$_firebaseServiceUrl/$_getSubscriptionplansPath';

    final response = await _apiClient.get(url);

    final List<dynamic> plans = response.data['plans'];

    List<SubscriptionPlan> planList = plans.map((plan) {
      return SubscriptionPlan.fromMap(plan);
    }).toList();

    return planList;
  }

  /// Activates a beta-level subscription plan.
  @override
  Future<dynamic> activatePlan(ActivatePlanParams params) async {
    String url = '$_firebaseServiceUrl/$_subscribeBetaLevelPath';
    final response = await _apiClient.post(url, data: params.toMap());
    return response.data['res']['message'];
  }

  /// Activates beta-level trial plan.
  @override
  Future<dynamic> activateTrial(ActivateTrialParams params) async {
    String url = '$_firebaseServiceUrl/$_trialSubscriptionPath';
    final response = await _apiClient.post(url, data: params.toMap());
    return response.data['data']['status'];
  }

  /// Returns the total amount saved by a retail outlet on a running beta-level subscription.
  @override
  Future<num> getOutletSavings(String outletId) async {
    String url = '$_firebaseServiceUrl/$_outletSavingsPath?outletId=$outletId';
    final response = await _apiClient.get(url);
    return response.data['totalSavings'];
  }

  /// Creates a payment authorization request.
  @override
  Future<String?> authorizePayment(AuthorizePaymentParams params) async {
    String url = '$_firebaseServiceUrl/$_authorizePaymentPath';
    final response = await _apiClient.post(url, data: params.toMap());
    return response.data['data']['transactionRef'];
  }

  /// Validates payment made from [authorizePayment] request.
  @override
  Future<String?> verifyPayment(VerifyPaymentParams params) async {
    String url = '$_firebaseServiceUrl/$_verifyPaymentPath';
    final response = await _apiClient.post(url, data: params.toMap());
    return response.data['res']['status'];
  }
}
