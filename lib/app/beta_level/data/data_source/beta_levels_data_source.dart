import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:td_commons_flutter/models/subscription.dart';

/// Base class for [BetaLevelsDataSourceImplementation].
abstract class BetaLevelsDataSource {
  Future<List<SubscriptionPlan>> fetchPlans();
  Future<dynamic> activatePlan(ActivatePlanParams params);
  Future<dynamic> activateTrial(ActivateTrialParams params);
  Future<num> getOutletSavings(String outletId);
  Future<String?> authorizePayment(AuthorizePaymentParams params);
  Future<String?> verifyPayment(VerifyPaymentParams params);
}
