import 'package:td_commons_flutter/utils/methods.dart';

class OutletActivity {
  String? id;
  String? retailOutletId;
  String? event;
  DateTime? createdAt;

  OutletActivity({
    this.id,
    this.retailOutletId,
    this.event,
    this.createdAt,
  });

  OutletActivity.fromMap(Map<String, dynamic> map)
      : id = map['_id'] ?? map['id'],
        retailOutletId = map['outletId'],
        event = map['event'],
        createdAt =
            map['createdAt'] != null ? parseDate(map['createdAt']) : null;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'retailOutletId': retailOutletId,
      'event': event,
      'createdAt': createdAt?.millisecondsSinceEpoch,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
