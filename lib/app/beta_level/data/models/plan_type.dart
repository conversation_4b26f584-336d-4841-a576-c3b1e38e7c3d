import 'package:td_commons_flutter/models/subscription.dart';

class PlanType {
  final String name;
  final String cost;
  final String benefit1;
  final String benefit2;
  final String benefit3;
  final String benefit4;
  final int bgColor;
  final int btnColor;
  final SubscriptionPlan plan;

  PlanType({
    required this.name,
    required this.cost,
    required this.benefit1,
    required this.benefit2,
    required this.benefit3,
    required this.benefit4,
    required this.bgColor,
    required this.btnColor,
    required this.plan,
  });

  PlanType copyWith({
    String? name,
    String? cost,
    String? benefit1,
    String? benefit2,
    String? benefit3,
    String? benefit4,
    int? bgColor,
    int? btnColor,
    SubscriptionPlan? plan,
  }) {
    return PlanType(
      name: name ?? this.name,
      cost: cost ?? this.cost,
      benefit1: benefit1 ?? this.benefit1,
      benefit2: benefit2 ?? this.benefit2,
      benefit3: benefit3 ?? this.benefit3,
      benefit4: benefit4 ?? this.benefit4,
      bgColor: bgColor ?? this.bgColor,
      btnColor: btnColor ?? this.btnColor,
      plan: plan ?? this.plan,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'cost': cost,
      'benefit1': benefit1,
      'benefit2': benefit2,
      'benefit3': benefit3,
      'benefit4': benefit4,
      'bgColor': bgColor,
      'btnColor': btnColor,
      'plan': plan.toMap(),
    };
  }

  factory PlanType.fromMap(Map<String, dynamic> map) {

    return PlanType(
      name: map['name'],
      cost: map['cost'],
      benefit1: map['benefit1'],
      benefit2: map['benefit2'],
      benefit3: map['benefit3'],
      benefit4: map['benefit4'],
      bgColor: map['bgColor'],
      btnColor: map['btnColor'],
      plan: SubscriptionPlan.fromMap(map['plan']),
    );
  }
}
