class ActivatePlanParams {
  final String planId;
  final String outletId;
  ActivatePlanParams({
    required this.outletId,
    required this.planId,
  });

  Map<String, dynamic> toMap() {
    return {
      'outletId': outletId,
      'planId': planId,
    };
  }

  @override
  String toString() => '''ActivatePlanParams(
    outletId: $outletId,
    planId: $planId,
  )''';
}

class ActivateTrialParams {
  final String userId;
  final String retailOutletId;
  ActivateTrialParams({
    required this.userId,
    required this.retailOutletId,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'retailOutletId': retailOutletId,
    };
  }

  @override
  String toString() => '''ActivateTrialParams(
    userId: $userId,
    retailOutletId: $retailOutletId,
  )''';
}

class AuthorizePaymentParams {
  final int amount;
  final String? userId;
  final String? authToken;
  final String? planId;
  final String? retailOutletId;
  final String paymentMethod;
  final String domain;

  AuthorizePaymentParams({
    required this.amount,
    required this.userId,
    required this.authToken,
    required this.retailOutletId,
    required this.planId,
    this.paymentMethod = 'SUBSCRIPTIONS',
    this.domain = 'RETAIL',
  });

  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'userId': userId,
      'authToken': authToken,
      'retailOutletId': retailOutletId,
      'planId': planId,
      'paymentMethod': paymentMethod,
      'domain': domain,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class VerifyPaymentParams {
  final String? userId;
  final String? authToken;
  final String? transactionRef;
  final String domain;

  VerifyPaymentParams({
    required this.userId,
    required this.authToken,
    required this.transactionRef,
    this.domain = 'RETAIL',
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'authToken': authToken,
      'transactionRef': transactionRef,
      'domain': domain,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
