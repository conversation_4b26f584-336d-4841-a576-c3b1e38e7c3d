import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:td_commons_flutter/models/subscription.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Base class for [BetaLevelsRepoImplementation].
abstract class BetaLevelsRepo {
  Future<ApiResult<List<SubscriptionPlan>>> fetchPlans();
  Future<ApiResult<dynamic>> activatePlan(ActivatePlanParams params);
  Future<ApiResult<dynamic>> activateTrial(ActivateTrialParams params);
  Future<ApiResult<num>> getOutletSavings(String outletId);
  Future<ApiResult<String?>> authorizePayment(AuthorizePaymentParams params);
  Future<ApiResult<String?>> verifyPayment(VerifyPaymentParams params);
}
