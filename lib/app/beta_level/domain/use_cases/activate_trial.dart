import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ActivateTrial with UseCases<ApiResult<dynamic>, ActivateTrialParams> {
  ActivateTrial(this._repo);

  /// Instance of [BetaLevelsRepo]
  final BetaLevelsRepo? _repo;

  /// Activates Beta-Level Trial Plan.
  ///
  /// Beta-Level Trial Plan gives you the opportunity to experience a small amount
  ///  of the many benefits awaiting you on a premium beta-level subscription.
  @override
  Future<ApiResult<dynamic>> call(ActivateTrialParams params) =>
      _repo!.activateTrial(params);
}
