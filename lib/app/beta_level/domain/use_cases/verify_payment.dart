import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class VerifyPayment with UseCases<ApiResult<String?>, VerifyPaymentParams> {
  VerifyPayment(this._repo);

  /// Instance of [BetaLevelsRepo].
  final BetaLevelsRepo? _repo;

  /// Validate payment made from [AuthorizePayment] use case.
  @override
  Future<ApiResult<String?>> call(VerifyPaymentParams params) =>
      _repo!.verifyPayment(params);
}
