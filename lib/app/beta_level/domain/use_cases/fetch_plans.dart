import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_commons_flutter/models/subscription.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchPlans with UseCases<ApiResult<List<SubscriptionPlan>>, String> {
  FetchPlans(this._repo);

  /// Instance of [BetaLevelsRepo].
  final BetaLevelsRepo? _repo;

  /// Returns a list of Beta-Level Subscription plans.
  @override
  Future<ApiResult<List<SubscriptionPlan>>> call([String? userId]) =>
      _repo!.fetchPlans();
}
