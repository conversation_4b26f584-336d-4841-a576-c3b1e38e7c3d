import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetOutletSavings with UseCases<ApiResult<num?>, String> {
  GetOutletSavings(this._repo);

  /// Instance of [BetaLevelsRepo].
  final BetaLevelsRepo? _repo;

  /// Returns the total amount saved by a retail outlet on a running Beta-Level Subscription.
  @override
  Future<ApiResult<num?>> call(String outletId) =>
      _repo!.getOutletSavings(outletId);
}
