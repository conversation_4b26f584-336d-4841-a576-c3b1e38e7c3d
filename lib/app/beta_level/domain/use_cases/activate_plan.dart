import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ActivatePlan with UseCases<ApiResult<dynamic>, ActivatePlanParams> {
  ActivatePlan(this._repo);

  /// Instance of [BetaLevelsRepo].
  final BetaLevelsRepo? _repo;

  /// Activates a Beta-Level subscription Plan.
  @override
  Future<ApiResult<dynamic>> call(ActivatePlanParams params) =>
      _repo!.activatePlan(params);
}
