import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
import 'package:shop/app/beta_level/domain/repositories/beta_levels_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class AuthorizePayment
    with UseCases<ApiResult<String?>, AuthorizePaymentParams> {
  AuthorizePayment(this._repo);

  /// Instance of [BetaLevelsRepo].
  final BetaLevelsRepo? _repo;

  /// Create a payment authorization request.
  @override
  Future<ApiResult<String?>> call(AuthorizePaymentParams params) =>
      _repo!.authorizePayment(params);
}
