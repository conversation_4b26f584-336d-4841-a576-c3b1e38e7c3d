// import 'dart:async';

// import 'package:collection/collection.dart' show IterableExtension;
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:shop/app/authentication/domain/params/post_params.dart';
// import 'package:shop/app/authentication/domain/use_cases/fetch_outlet.dart';
// import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
// import 'package:shop/app/authentication/presentation/listeners/on_logout.dart';
// import 'package:shop/app/beta_level/domain/use_cases/fetch_plans.dart';
// import 'package:shop/app/beta_level/domain/use_cases/get_outlet_savings.dart';
// import 'package:td_commons_flutter/constants/app_values.dart';
// import 'package:td_commons_flutter/models/index.dart';
// import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

// import 'beta_levels_state.dart';

// const kStatusActive = 'active';

// class BetaLevelsCubit extends Cubit<BetaLevelsState>
//     implements OnLogin, OnLogout {
//   final FetchPlans _fetchPlans;
//   final FetchOutlet _fetchOutlet;
//   final GetOutletSavings _getOutletSavings;

//   User? currentUser;
//   RetailOutlet? outlet;
//   List<SubscriptionPlan>? plans;

//   BetaLevelsCubit(
//     this._fetchPlans,
//     this._fetchOutlet,
//     this._getOutletSavings,
//   ) : super(BetaLevelsState.initialState());

//   @override
//   Future<void> onLogin(User user) async {
//     currentUser = user;
//     await fetchPlans();
//   }

//   @override
//   Future<void> onLogout() async {
//     currentUser = null;
//   }

//   /// Fetch Beta-Level Subscription Plans with HTTP API.
//   Future fetchPlans() async {
//     final BetaLevelsState currentState = state;
//     emit(currentState.copyWith(isLoading: true, isError: false));
//     _mapResultToState(currentState);
//   }

//   /// Refresh Beta-Level Subscription Plans with HTTP API.
//   Future syncPlans() async {
//     final BetaLevelsState currentState = state;
//     if (currentState.isLoading) return;
//     _mapResultToState(currentState);
//   }

//   /// Returns appropriate state based on HTTP/API response.
//   void _mapResultToState(BetaLevelsState currentState) async {
//     final result = await _fetchPlans();
//     result.maybeWhen(
//       success: (List<SubscriptionPlan> data) {
//         List<SubscriptionPlan> sortedPlans = data
//           ..sort((a, b) {
//             int queryTerm;
//             if (b.amount != null) {
//               queryTerm = b.amount!.compareTo(a.amount ?? 1);
//             } else {
//               queryTerm = 1;
//             }
//             return queryTerm;
//           });
//         this.plans = sortedPlans;
//         fetchOutlet();
//       },
//       apiFailure: (error, _) {
//         String errorMessage = ApiExceptions.getErrorMessage(error);
//         emit(currentState.copyWith(
//             isLoading: false,
//             isError: true,
//             errorMessage: errorMessage,
//             errorCode: errorMessage));
//       },
//       orElse: () {
//         emit(
//           currentState.copyWith(
//               isLoading: false,
//               isError: true,
//               errorMessage: DEFAULT_ERROR,
//               errorCode: DEFAULT_ERROR),
//         );
//       },
//     );
//   }

//   /// Fetch a retail outlet latest value with HTTP API.
//   Future fetchOutlet() async {
//     final BetaLevelsState currentState = state;

//     if (currentUser?.currentRetailOutlet?.id == null) return;

//     FetchOutletParams params = FetchOutletParams(
//       outletId: currentUser!.currentRetailOutlet!.id,
//     );

//     final res = await _fetchOutlet(params);

//     res.maybeWhen(
//       success: (RetailOutlet? outlet) async {
//         this.outlet = outlet;

//         // return early if outlet is null
//         if (outlet == null) {
//           emit(currentState.copyWith(
//             isLoading: false,
//             isError: false,
//             isSubscribed: false,
//             hasTrial: false,
//             activePlan: null,
//             subscriptionPlans: this.plans,
//             retailOutlet: outlet,
//             totalSavings: null,
//           ));
//           return;
//         }

//         if ((outlet.subscriptionStatus != null &&
//                 outlet.subscriptionStatus == kStatusActive) &&
//             outlet.planId != null) {
//           num? totalSavings = await getOutletSavings();

//           SubscriptionPlan? activePlan =
//               this.plans?.firstWhereOrNull((plan) => plan.id == outlet.planId);

//           List<SubscriptionPlan> otherPlans =
//               this.plans?.where((plan) => plan.id != outlet.planId).toList() ??
//                   [];

//           emit(
//             currentState.copyWith(
//               isLoading: false,
//               isError: false,
//               isSubscribed: activePlan != null,
//               hasTrial: false,
//               activePlan: activePlan,
//               subscriptionPlans: otherPlans,
//               retailOutlet: outlet,
//               totalSavings: totalSavings,
//             ),
//           );
//         } else if (outlet.subscriptionStatus == kStatusActive &&
//             outlet.hasTrial == true) {
//           num? totalSavings = await getOutletSavings();

//           emit(currentState.copyWith(
//             isLoading: false,
//             isError: false,
//             isSubscribed: false,
//             hasTrial: true,
//             activePlan: null,
//             subscriptionPlans: this.plans,
//             retailOutlet: outlet,
//             totalSavings: totalSavings,
//           ));
//         } else {
//           emit(currentState.copyWith(
//             isLoading: false,
//             isError: false,
//             isSubscribed: false,
//             hasTrial: false,
//             activePlan: null,
//             subscriptionPlans: this.plans,
//             retailOutlet: outlet,
//             totalSavings: null,
//           ));
//         }
//       },
//       apiFailure: (error, _) {
//         String errorMessage = ApiExceptions.getErrorMessage(error);
//         emit(
//           currentState.copyWith(
//               isLoading: false,
//               isError: true,
//               errorMessage: errorMessage,
//               errorCode: errorMessage),
//         );
//       },
//       orElse: () {
//         emit(
//           currentState.copyWith(
//             isLoading: false,
//             isError: true,
//             errorMessage: DEFAULT_ERROR,
//             errorCode: DEFAULT_ERROR,
//           ),
//         );
//       },
//     );
//   }

//   /// Fetch the total amount  saved by a retail outlet on a running Beta-Level Subscription.
//   Future<num?> getOutletSavings() async {
//     // explicitly check for null first
//     if (outlet?.id == null) return null;

//     num? totalSavings;
//     final BetaLevelsState currentState = state;
//     final res = await _getOutletSavings(outlet!.id!);

//     res.maybeWhen(
//       success: (num? data) {
//         totalSavings = data;
//       },
//       apiFailure: (error, _) {
//         String errorMessage = ApiExceptions.getErrorMessage(error);
//         emit(
//           currentState.copyWith(
//             isLoading: false,
//             isError: true,
//             errorMessage: errorMessage,
//             errorCode: errorMessage,
//           ),
//         );
//       },
//       orElse: () {
//         emit(
//           currentState.copyWith(
//             isLoading: false,
//             isError: true,
//             errorMessage: DEFAULT_ERROR,
//             errorCode: DEFAULT_ERROR,
//           ),
//         );
//       },
//     );

//     return totalSavings;
//   }
// }
