// import 'package:flutter/foundation.dart';
// import 'package:shop/app/beta_level/data/models/outlet_activity.dart';
// import 'package:td_commons_flutter/models/retailer.dart';
// import 'package:td_commons_flutter/models/subscription.dart';

// @immutable
// class BetaLevelsState {
//   final bool isLoading;
//   final bool? isError;
//   final String? errorMessage;
//   final String? errorCode;
//   final List<SubscriptionPlan>? subscriptionPlans;
//   final SubscriptionPlan? activePlan;
//   final RetailOutlet? retailOutlet;
//   final bool? isSubscribed;
//   final bool? hasTrial;
//   final bool? verifyingStatus;
//   final num? totalSavings;
//   final List<OutletActivity>? activities;

//   BetaLevelsState({
//     required this.isLoading,
//     this.isError,
//     this.errorMessage,
//     this.errorCode,
//     this.subscriptionPlans,
//     this.activePlan,
//     this.retailOutlet,
//     this.isSubscribed,
//     this.hasTrial,
//     this.verifyingStatus,
//     this.totalSavings,
//     this.activities,
//   });

//   factory BetaLevelsState.initialState() {
//     return BetaLevelsState(
//       isLoading: false,
//       isError: false,
//       errorMessage: null,
//       subscriptionPlans: null,
//       activePlan: null,
//       retailOutlet: null,
//       isSubscribed: false,
//       hasTrial: false,
//       verifyingStatus: false,
//       totalSavings: null,
//     );
//   }

//   factory BetaLevelsState.subscriptionLoadError(
//     errorCode,
//     errorMessage,
//   ) {
//     return BetaLevelsState(
//       isLoading: false,
//       errorMessage: errorMessage,
//       errorCode: errorCode,
//       isError: true,
//     );
//   }

//   BetaLevelsState copyWith({
//     bool? isLoading,
//     bool? isError,
//     String? errorMessage,
//     String? errorCode,
//     List<SubscriptionPlan>? subscriptionPlans,
//     SubscriptionPlan? activePlan,
//     RetailOutlet? retailOutlet,
//     bool? isSubscribed,
//     bool? hasTrial,
//     bool? verifyingStatus,
//     num? totalSavings,
//     List<OutletActivity>? activities,
//   }) {
//     return BetaLevelsState(
//       isLoading: isLoading ?? this.isLoading,
//       isError: isError ?? this.isError,
//       errorMessage: errorMessage ?? this.errorMessage,
//       errorCode: errorCode ?? this.errorCode,
//       subscriptionPlans: subscriptionPlans ?? this.subscriptionPlans,
//       activePlan: activePlan ?? this.activePlan,
//       retailOutlet: retailOutlet ?? this.retailOutlet,
//       isSubscribed: isSubscribed ?? this.isSubscribed,
//       hasTrial: hasTrial ?? this.hasTrial,
//       verifyingStatus: verifyingStatus ?? this.verifyingStatus,
//       totalSavings: totalSavings ?? this.totalSavings,
//       activities: activities ?? this.activities,
//     );
//   }

//   @override
//   String toString() {
//     return '''BetaLevelsState state {
//       isLoading: $isLoading,
//       isError: $isError,
//       errorMessage: $errorMessage,
//       errorCode: $errorCode,
//       subscriptionPlans: $subscriptionPlans,
//       activePlan: $activePlan,
//       retailOutlet: $retailOutlet,
//       isSubscribed: $isSubscribed,
//       hasTrial: $hasTrial,
//       verifyingStatus: $verifyingStatus,
//       totalSavings: $totalSavings,
//       activities: $activities,
//     }''';
//   }
// }
