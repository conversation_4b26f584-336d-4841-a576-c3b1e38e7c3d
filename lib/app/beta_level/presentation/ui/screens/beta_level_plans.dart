// import 'package:dots_indicator/dots_indicator.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_paystack_client/flutter_paystack_client.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/beta_level/data/models/plan_type.dart';
// import 'package:shop/app/beta_level/domain/params/beta_levels_params.dart';
// import 'package:shop/app/beta_level/domain/use_cases/authorize_payment.dart';
// import 'package:shop/app/beta_level/domain/use_cases/verify_payment.dart';
// import 'package:shop/app/beta_level/presentation/logic/blocs/beta_levels_cubit.dart';
// import 'package:shop/app/beta_level/presentation/logic/blocs/beta_levels_state.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:shop/src/components/src/error/error_widget.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:shop/src/components/src/loader/loader.dart';
// import 'package:shop/src/components/src/widgets/app_screen.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/values/analytics/segment_events.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_commons_flutter/models/retailer.dart';
// import 'package:td_commons_flutter/models/subscription.dart';
// import 'package:td_commons_flutter/models/user.dart';
// import 'package:td_flutter_core/config/DI/di.dart';
// import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
// import 'package:td_flutter_src/scaler/src/margin.dart';

// const kSilverGroup = 'spo';
// const kGoldGroup = 'gpo';
// const kPlatinumGroup = 'ppo';
// const kActiveMsg = 'you currently have an active subscription';

// class BetaLevelPlans extends StatefulWidget {
//   @override
//   _BetaLevelPlansState createState() => _BetaLevelPlansState();
// }

// class _BetaLevelPlansState extends State<BetaLevelPlans> {
//   PageController _controller =
//       PageController(viewportFraction: 0.8, keepPage: true);
//   ValueNotifier<bool> _loading = ValueNotifier<bool>(false);
//   double _currentPosition = 0;
//   late BetaLevelsCubit _cubit;

//   @override
//   void initState() {
//     _cubit = context.read();
//     _cubit.syncPlans();
//     super.initState();
//   }

//   void _handleErrorButton(context) {
//     BlocProvider.of<BetaLevelsCubit>(context, listen: false).fetchPlans();
//   }

//   PlanType _getPlanType(SubscriptionPlan plan) {
//     Map<String, dynamic> types = {
//       kSilverGroup: {
//         'name': 'Silver Plan',
//         'cost': 'N 1,200 / Month',
//         'benefit1': 'Free delivery in 24hrs',
//         'benefit2': '1% Cashback Promo',
//         'benefit3': 'N 10,000 sick day cash benefit',
//         'benefit4': 'N 100,000 Beta Care',
//         'bgColor': Color(0xffBEC2CB).withValues(alpha: 0.3).value,
//         'btnColor': Color(0xff6F7A8D).value,
//         'plan': plan.toMap(),
//       },
//       kGoldGroup: {
//         'name': 'Gold Plan',
//         'cost': 'N 3,000 / Month',
//         'benefit1': 'Free delivery in 24hrs',
//         'benefit2': '1.15% Cashback Promo',
//         'benefit3': 'N 20,000 sick day cash benefit',
//         'benefit4': 'N 200,000 Beta Care',
//         'bgColor': Color(0xffD4AF37).withValues(alpha: 0.6).value,
//         'btnColor': Color(0xffD4AF37).value,
//         'plan': plan.toMap(),
//       },
//       kPlatinumGroup: {
//         'name': 'Plantinum Plan',
//         'cost': 'N 5,000 / Month',
//         'benefit1': 'Free delivery in 24hrs',
//         'benefit2': '1.25% Cashback Promo',
//         'benefit3': 'N 30,000 sick day cash benefit',
//         'benefit4': 'N 300,000 Beta Care',
//         'bgColor': Color(0xff9FD0E1).withValues(alpha: 0.6).value,
//         'btnColor': Color(0xff9FD0E1).value,
//         'plan': plan.toMap(),
//       },
//     };

//     switch (plan.customerGroup!.toLowerCase()) {
//       case kSilverGroup:
//         return PlanType.fromMap(types[kSilverGroup]);
//       case kGoldGroup:
//         return PlanType.fromMap(types[kGoldGroup]);
//       case kPlatinumGroup:
//         return PlanType.fromMap(types[kPlatinumGroup]);
//       default:
//         return PlanType.fromMap(types[kGoldGroup]);
//     }
//   }

//   Widget _buildBetaLevels(BuildContext context, BetaLevelsState state) {
//     List<Widget> widgets = [];

//     if (state.isSubscribed != null && state.isSubscribed!) {
//       widgets.add(
//         buildPlanCard(
//           context,
//           _getPlanType(state.activePlan!),
//           true,
//         ),
//       );
//     }

//     widgets.addAll(
//       state.subscriptionPlans!.map(
//         (plan) => buildPlanCard(
//           context,
//           _getPlanType(plan),
//           false,
//         ),
//       ),
//     );

//     String introText = 'your preferred subscription plan';
//     bool hasActivePlan = state.activePlan != null;

//     return Column(
//       children: [
//         YMarginScale(0.02),
//         Align(
//           alignment: Alignment.centerLeft,
//           child: Padding(
//             padding: const EdgeInsets.only(left: 20),
//             child: Text(
//               hasActivePlan ? 'Extend $introText' : 'Select $introText',
//               style: KTextStyle.subtitleTitleText,
//               textAlign: TextAlign.justify,
//             ),
//           ),
//         ),
//         SizedBox(
//           height: screenHeight(context, percent: 0.05),
//         ),
//         Container(
//           height: screenHeight(context, percent: 0.54),
//           child: PageView(
//             onPageChanged: (index) {
//               setState(() {
//                 _currentPosition = index.toDouble();
//               });
//             },
//             controller: _controller,
//             scrollDirection: Axis.horizontal,
//             children: widgets,
//           ),
//         ),
//         SizedBox(
//           height: screenHeight(context, percent: 0.05),
//         ),
//         DotsIndicator(
//           dotsCount: 3,
//           position: _currentPosition,
//           decorator: DotsDecorator(
//             size: const Size.square(10.0),
//             activeSize: const Size.square(10.0),
//             activeColor: Colors.white,
//             color: kColorBlue,
//             activeShape: CircleBorder(side: BorderSide(color: kColorBlue)),
//           ),
//         ),
//       ],
//     );
//   }

//   void showToastInfo() {
//     Toast.info('You cannot go back at this time', context,
//         duration: 5, title: 'Processing');
//   }

//   Future<bool> _handlePop() async {
//     if (_loading.value) {
//       showToastInfo();
//       return false;
//     }
//     return true;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: _handlePop,
//       child: AppScreen(
//         child: Scaffold(
//           body: SafeArea(
//             child: SingleChildScrollView(
//               child: Column(
//                 children: [
//                   Align(
//                     alignment: Alignment.centerLeft,
//                     child: KBackButton(),
//                   ),
//                   SizedBox(
//                     height: screenHeight(context, percent: 0.02),
//                   ),
//                   Align(
//                     alignment: Alignment.centerLeft,
//                     child: Padding(
//                       padding: const EdgeInsets.only(left: 20),
//                       child: Text(
//                         'Beta Levels',
//                         style: KTextStyle.headerTitleText,
//                       ),
//                     ),
//                   ),
//                   BlocBuilder<BetaLevelsCubit, BetaLevelsState>(
//                     builder: (context, state) {
//                       Widget child = SizedBox.shrink();

//                       if (state.isLoading) {
//                         child = Center(
//                           child: Container(
//                               height: screenHeight(context, percent: 0.5),
//                               child: Center(
//                                 child: KLoader(),
//                               )),
//                         );
//                       } else if ((state.isError != null && state.isError!) &&
//                           (state.subscriptionPlans == null ||
//                               state.subscriptionPlans!.isEmpty)) {
//                         child = Container(
//                             height: screenHeight(context, percent: 0.7),
//                             child: Center(
//                               child: KErrorScreen(
//                                 state.errorCode,
//                                 () => _handleErrorButton(context),
//                                 displayErrorCode: true,
//                               ),
//                             ));
//                       } else if (state.subscriptionPlans == null ||
//                           state.subscriptionPlans!.isEmpty) {
//                         child = _emptyState();
//                       } else {
//                         child = _buildBetaLevels(context, state);
//                       }
//                       return AnimatedSwitcher(
//                         duration: kThemeAnimationDuration,
//                         child: child,
//                       );
//                     },
//                   )
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _emptyState() {
//     return Center(
//       child: Column(
//         children: [
//           YMarginScale(0.2),
//           Container(
//               padding: EdgeInsets.all(30),
//               decoration: BoxDecoration(
//                 color: Color(0xffA1C3FF).withValues(alpha: 0.3),
//                 shape: BoxShape.circle,
//               ),
//               child: SvgPicture.asset(kSvgloan)),
//           YMarginScale(0.07),
//           SizedBox(
//             width: screenWidth(context, percent: 0.5),
//             child: Text(
//               'No Subscription Plans',
//               style: KTextStyle.subtitleTitleText,
//               textAlign: TextAlign.center,
//             ),
//           ),
//           YMarginScale(0.16),
//         ],
//       ),
//     );
//   }

//   // void _showDialogue(BuildContext context, SubscriptionPlan plan) {
//   //   String code = '*402*89413914*${plan.amount}#';

//   //   showDialog(
//   //     context: context,
//   //     builder: (BuildContext context) {
//   //       return AlertDialog(
//   //         content: new Text(
//   //             'You have insufficient funds, please fund your wallet or dial $code to join the plan.'),
//   //         actions: <Widget>[
//   //           new FlatButton(
//   //             child: new Text('Dismiss'),
//   //             onPressed: () {
//   //               Navigator.of(context).pop();
//   //             },
//   //           ),
//   //           new FlatButton(
//   //             child: new Text('Dial'),
//   //             onPressed: () async {
//   //               // refresh();
//   //               ussd(code);
//   //               Navigator.of(context).pop();
//   //             },
//   //           ),
//   //         ],
//   //       );
//   //     },
//   //   );
//   // }

//   Widget buildPlanCard(
//     BuildContext context,
//     PlanType planType,
//     bool isActive,
//   ) {
//     return Padding(
//       padding: const EdgeInsets.only(left: 20, right: 20),
//       child: Container(
//         height: screenHeight(context, percent: 0.03),
//         width: screenWidth(context, percent: 0.4),
//         decoration: BoxDecoration(
//           color: Color(planType.bgColor),
//           boxShadow: [
//             BoxShadow(
//                 color: Color.fromARGB(25, 0, 95, 255),
//                 blurRadius: 5,
//                 spreadRadius: 3,
//                 offset: Offset(1, 3)),
//           ],
//           borderRadius: BorderRadius.circular(10),
//         ),
//         child: SingleChildScrollView(
//           child: Column(
//             children: [
//               SizedBox(height: screenHeight(context, percent: 0.03)),
//               Text(
//                 planType.name,
//                 style: KTextStyle.headerTitleText,
//               ),
//               SizedBox(height: screenHeight(context, percent: 0.03)),
//               Text(
//                 planType.cost,
//                 style: KTextStyle.headerTitleText.copyWith(
//                   fontSize: 18,
//                   fontWeight: FontWeight.normal,
//                 ),
//               ),
//               SizedBox(height: screenHeight(context, percent: 0.04)),
//               Text(planType.benefit1),
//               Divider(color: kColorWhite),
//               Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Text(planType.benefit2),
//               ),
//               Divider(color: kColorWhite),
//               Padding(
//                 padding: const EdgeInsets.all(6.0),
//                 child: Text(planType.benefit3),
//               ),
//               Divider(color: kColorWhite),
//               Text(planType.benefit4),
//               SizedBox(height: screenHeight(context, percent: 0.03)),
//               ElevatedButton(
//                 // padding: EdgeInsets.all(15),
//                 onPressed: () async {
//                   _payWithCard(planType);
//                   // _payWithWallet(planType);
//                 },
//                 child: Text(
//                   isActive ? 'Extend Plan' : 'Choose Plan',
//                   style: TextStyle(color: kColorWhite),
//                 ),
//                 style: ElevatedButton.styleFrom(
//                   primary: Color(planType.btnColor),
//                   onPrimary: Color(planType.btnColor),
//                   shape: const BeveledRectangleBorder(
//                       borderRadius: BorderRadius.all(Radius.circular(7))),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   void _payWithCard(PlanType planType) async {
//     TdLoader.show(context);
//     UserCubit _userCubit = context.read();
//     User user = _userCubit.currentUser!;
//     RetailOutlet? outlet = _userCubit.currentOutlet;

//     int amount = planType.plan.amount! * 100 as int; // Convert amount to kobo

//     AuthorizePaymentParams params = AuthorizePaymentParams(
//       amount: amount,
//       authToken: user.authToken,
//       retailOutletId: outlet?.id,
//       userId: user.userId,
//       planId: planType.plan.id,
//     );

//     final res = await locator.get<AuthorizePayment>().call(params);

//     res.maybeWhen(
//       success: (transactionRef) {
//         return _checkoutPaystack(transactionRef!, user, planType, amount);
//       },
//       apiFailure: (error, _) {
//         Toast.error(ApiExceptions.getErrorMessage(error), context, duration: 5);
//       },
//       orElse: () {
//         Toast.error('An unexpected error occurred, please try again', context,
//             duration: 5);
//       },
//     );
//   }

//   void _checkoutPaystack(
//       String transactionRef, User user, PlanType planType, int amount) async {
//     final charge = Charge();
//     charge.amount = amount;
//     charge.reference = transactionRef;
//     charge.email = user.email;

//     TdLoader.hide();
//     final response = await PaystackClient.checkout(
//       context,
//       hideEmail: true,
//       charge: charge,
//     );

//     if (response.status) {
//       _loading.value = true;
//       TdLoader.show(context);
//       VerifyPaymentParams params = VerifyPaymentParams(
//         authToken: user.authToken,
//         transactionRef: transactionRef,
//         userId: user.userId,
//       );

//       final res = await locator.get<VerifyPayment>().call(params);

//       res.maybeWhen(
//         success: (data) async {
//           Segment.track(
//             eventName: SegmentEvents.betaLevelSubscribed,
//             properties: {
//               'beta_level': planType.plan.toMap(),
//               'status': 'active',
//             },
//           );
//           await _cubit.syncPlans();
//           TdLoader.hide();
//           _loading.value = false;
//           Toast.success(data, context, duration: 5);
//         },
//         apiFailure: (error, _) {
//           TdLoader.hide();
//           _loading.value = false;
//           Toast.error(ApiExceptions.getErrorMessage(error), context);
//         },
//         orElse: () {
//           TdLoader.hide();
//           _loading.value = false;
//           Toast.error('An error occurred, please try again', context);
//         },
//       );
//     }

//     /// Kept here for future usage
//     // void _payWithWallet(PlanType planType) async {
//     //   UserCubit _userCubit = context.read();
//     //   RetailOutlet outlet = _userCubit.currentOutlet;

//     //   if (outlet.walletAccount.currentBalance < planType.plan.amount) {
//     //     Toast.error('Please fund your wallet, or subscribe via USSD', context,
//     //         title: 'Insufficient Balance', duration: 3);

//     //     return _showDialogue(context, planType.plan);
//     //   }

//     //   if (planType.plan?.requireApproval != null &&
//     //       planType.plan.requireApproval) {
//     //     String message =
//     //         '${planType.plan.name} requires approval, please help.';
//     //     await Intercom.instance.displayMessageComposer(message);
//     //     return;
//     //   }

//     //   TdLoader.show(context);
//     //   _loading.value = true;

//     //   ActivatePlanParams params =
//     //       ActivatePlanParams(outletId: outlet.id, planId: planType.plan.id);
//     //   final res = await locator.get<ActivatePlan>().call(params);
//     //   res.maybeWhen(
//     //     success: (data) async {
//     //       await _cubit.syncPlans();
//     //       TdLoader.hide();
//     //       _loading.value = false;
//     //       return data.toString().toLowerCase().startsWith(kActiveMsg)
//     //           ? Toast.info(data, context, title: 'Attention!', duration: 15)
//     //           : Toast.success(data, context, duration: 15);
//     //     },
//     //     apiFailure: (error) {
//     //       TdLoader.hide();
//     //       _loading.value = false;
//     //       String message = ApiExceptions.getErrorMessage(error);
//     //       Toast.error(message, context, duration: 5);
//     //     },
//     //     orElse: () {
//     //       TdLoader.hide();
//     //       _loading.value = false;
//     //       String message = 'An unexpected error occurred, please try again';
//     //       Toast.error(message, context, duration: 5);
//     //     },
//     //   );
//     // }
//   }
// }
