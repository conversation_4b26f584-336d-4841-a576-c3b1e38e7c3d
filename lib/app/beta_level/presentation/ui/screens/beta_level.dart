// import 'package:flutter/material.dart';
// import 'package:shop/app/beta_level/presentation/ui/screens/beta_level_plans.dart';
// import 'package:shop/src/components/src/buttons/buttons.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:shop/src/res/assets/svgs/svgs.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_src/navigation/navigation.dart';
// import 'package:td_flutter_src/scaler/scaler.dart';

// class BetaLevel extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         elevation: 0,
//         backgroundColor: Colors.white,
//         leading: KBackButton(),
//       ),
//       body: CustomScrollView(
//         slivers: [
//           SliverPadding(
//             padding: const EdgeInsets.symmetric(horizontal: 30),
//             sliver: SliverToBoxAdapter(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   YMarginScale(0.02),
//                   Text(
//                     'Beta Levels',
//                     style: KTextStyle.headerTitleText,
//                   ),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(vertical: 30),
//                     child: Text(
//                       'Get more from shopping on the ShopTopUp App with our monthly subscription plans.',
//                       style: KTextStyle.subtitleTitleText,
//                     ),
//                   ),
//                   buildInfoItem(
//                       'Exclusive and premium service with prompt delivery of your items.'),
//                   buildInfoItem(
//                       'Cashbacks promo for items ordered on the ShopTopUp App.'),
//                   buildInfoItem(
//                       'Special packages including access to Healthcare & property insurance for you and your family.'),
//                   YMarginScale(0.1),
//                   KButton(
//                       onPressed: () => navigate(context, BetaLevelPlans()),
//                       text: 'Continue')
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget buildInfoItem(
//     String text,
//   ) {
//     return Padding(
//       padding: const EdgeInsets.only(top: 25),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Padding(
//             padding: const EdgeInsets.only(top: 10),
//             child: SvgPicture.asset(
//               kSvgbetaIcon,
//               color: kColorBlue,
//               width: 20,
//               height: 25,
//             ),
//           ),
//           SizedBox(
//             width: 20,
//           ),
//           Flexible(
//             child: Text(
//               text,
//               style: KTextStyle.subtitleTitleText,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
