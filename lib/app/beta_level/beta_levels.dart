import 'package:shop/app/beta_level/domain/use_cases/activate_plan.dart';
import 'package:shop/app/beta_level/domain/use_cases/activate_trial.dart';
import 'package:shop/app/beta_level/domain/use_cases/authorize_payment.dart';
import 'package:shop/app/beta_level/domain/use_cases/fetch_plans.dart';
import 'package:shop/app/beta_level/domain/use_cases/get_outlet_savings.dart';
import 'package:shop/app/beta_level/domain/use_cases/verify_payment.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'data/data_source/beta_levels_data_source.dart';
import 'data/data_source/beta_levels_data_source_impl.dart';
import 'data/repo_impl/beta_levels_repo_impl.dart';
import 'domain/repositories/beta_levels_repo.dart';

void registerBetaLevelsDependencies(AppConfig config) {
  // Use cases
  locator.registerLazySingleton(() => FetchPlans(locator()));
  locator.registerLazySingleton(() => ActivatePlan(locator()));
  locator.registerLazySingleton(() => ActivateTrial(locator()));
  locator.registerLazySingleton(() => GetOutletSavings(locator()));
  locator.registerLazySingleton(() => AuthorizePayment(locator()));
  locator.registerLazySingleton(() => VerifyPayment(locator()));

  // Repositories
  locator.registerLazySingleton<BetaLevelsRepo>(
    () => BetaLevelsRepoImplementation(locator(), locator()),
  );

  // Data sources
  locator.registerLazySingleton<BetaLevelsDataSource>(
    () => BetaLevelsDataSourceImplementation(
        locator(), config.firebaseServiceUrl!),
  );
}
