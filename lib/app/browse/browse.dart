import 'package:shop/app/browse/domain/use-cases/fetch_variant.dart';
import 'package:shop/app/product_search/domain/usecase/fetch_related_items.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';

import 'data/data_source/browse_collection_data_source.dart';
import 'data/data_source/browse_collection_data_source_impl.dart';
import 'data/repo_impl/browse_collection_repo_impl.dart';
import 'domain/repositories/browse_collection_repo.dart';
import 'domain/use-cases/fetch_browse_collection.dart';

void registerBrowseCollectionDependencies(AppConfig config) {
  // Use cases
  locator.registerLazySingleton(() => FetchBrowseCollection(locator()));
  locator.registerLazySingleton(() => FetchRelatedItems(locator()));
  locator.registerLazySingleton(() => FetchVariant(locator()));

  // Repositories
  locator.registerLazySingleton<BrowseCollectionRepo>(
    () => BrowseCollectionRepoImplementation(locator()),
  );

  // Data sources
  locator.registerLazySingleton<BrowseCollectionDataSource>(
    () => BrowseCollectionDataSourceImplementation(
      locator(),
      config.firebaseServiceUrl!,
    ),
  );
}
