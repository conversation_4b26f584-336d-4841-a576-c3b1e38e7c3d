import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/browse/domain/use-cases/fetch_variant.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_variant_inventory.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';

part 'product_state.dart';

class ProductCubit extends Cubit<ProductState> {
  ProductCubit() : super(ProductInitial());

  void loadProduct(String variantId) async {
    final plusCode = UserCubit
        .instance?.currentUser?.currentRetailOutlet?.coordinates?.plusCode6Hex;

    if (plusCode == null) {
      emit(ProductError('Invalid plus code. Please try again'));
      return;
    }

    emit(ProductLoading());

    final params = VariantParams(variantId, plusCode);

    final res = await locator<FetchVariant>().call(params);

    res.when(
      success: (data) {
        _fetchVariantInventory(data);
      },
      apiFailure: (e, _) {
        emit(ProductError(ApiExceptions.getErrorMessage(e)));
      },
    );
  }

  Future<void> _fetchVariantInventory(Variant variant) async {
    final outlet = UserCubit.instance?.currentOutlet;
    String? hexCode = outlet?.coordinates?.toPlus6Hex();
    String? variantId = variant.variantId;
    String? outletId = outlet?.id;

    if (outletId == null || hexCode == null) {
      emit(ProductLoaded(variant: variant));
      return;
    }

    final params = VariantInventoryParams(
        hexCode: hexCode, variantId: variantId, outletId: outletId);

    final res = await locator.get<FetchVariantInventory>().call(params);

    res.when(
      success: (VariantInventory data) {
        emit(ProductLoaded(variant: variant, variantInventory: data));
      },
      apiFailure: (error, _) {
        emit(ProductLoaded(variant: variant));
      },
    );
  }
}
