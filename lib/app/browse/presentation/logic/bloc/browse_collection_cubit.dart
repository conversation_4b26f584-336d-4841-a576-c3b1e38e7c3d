import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/anonymous_user.dart';
import 'package:shop/app/authentication/presentation/listeners/on_anonymous_login.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/domain/use-cases/fetch_browse_collection.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'browse_collection_state.dart';

class BrowseCollectionCubit extends Cubit<BrowseCollectionState>
    implements OnLogin, OnAnonymousLogin, OnBeforeLogout {
  final FetchBrowseCollection _fetchBrowseCollection;
  RetailOutlet? currentOutlet;

  BrowseCollectionCubit(this._fetchBrowseCollection)
      : super(BrowseCollectionState.initialState());

  @override
  Future<void> onLogin(User? user) async {
    currentOutlet = user!.currentRetailOutlet;
    fetchBrowseCollection(
        currentOutlet, currentOutlet?.coordinates?.plusCode6Hex);
  }

  @override
  Future<void> onAnonymousLogin(AnonymousUser user) async {
    fetchBrowseCollection(currentOutlet, user.hexCode);
  }

  @override
  Future<void> onBeforeLogout() async {
    currentOutlet = null;
    emit(BrowseCollectionState.initialState());
  }

  /// Fetch browse collection for a retail outlets hexCode with HTTP API.
  Future<bool> fetchBrowseCollection(
      RetailOutlet? outlet, String? hexCode) async {
    final BrowseCollectionState currentState = state;
    emit(currentState.copyWith(isLoading: true, isError: false, result: null));
    return await _mapResultToState(currentState, outlet, hexCode);
  }

  /// Refresh browse Collection with HTTP API.
  Future<bool> syncBrowseCollection() async {
    if (currentOutlet == null) return false;
    final BrowseCollectionState currentState = state;
    return _mapResultToState(
        currentState, currentOutlet, currentOutlet?.coordinates?.plusCode6Hex);
  }

  /// Returns appropriate state based on HTTP/API response.
  Future<bool> _mapResultToState(BrowseCollectionState currentState,
      RetailOutlet? outlet, String? hexCode) async {
    if (outlet == null && UserCubit.instance?.isAnonymous != true) return false;
    late bool success;
    final result = await _fetchBrowseCollection(outlet);
    result.when(
      success: (BrowseCollectionResult data) {
        // _handleInventorySync(data, hexCode);
        emit(currentState.copyWith(
            isLoading: false,
            isError: false,
            result: data,
            hasLoaded: true,
            availableInYourArea: data.collections.isNotEmpty));
        success = true;
      },
      apiFailure: (error, _) {
        // _handleInventorySync(currentState.result, hexCode);
        String errorMessage = ApiExceptions.getErrorMessage(error);
        emit(
          BrowseCollectionState.loadError(
            errorMessage,
            errorMessage,
            currentState.result,
            currentState.hasLoaded,
            currentState.availableInYourArea,
          ),
        );
        success = false;
      },
    );

    return success;
  }

  // Future<void> _handleInventorySync(
  //   BrowseCollectionResult? data,
  //   String? hexCode,
  // ) async {
  //   final collections = data?.collections ?? [];
  //   if (collections.isEmpty || hexCode == null) return;

  //   final List<Collection> updatedCollections = [];

  //   for (final collection in collections) {
  //     log('collection.validVariants: ${collection.validVariants.length}');
  //     try {
  //       final res = await locator.get<FetchVariantInventoryList>().call(
  //             VariantInventoryListParams(
  //               hexCode: hexCode,
  //               variantIds: collection.validVariantIds,
  //             ),
  //           );

  //       res.when(
  //         success: (inventories) {
  //           final validVariants = collection.validVariants;
  //           final _updatedVariants =
  //               updateVariantsWithInventory(validVariants, inventories);
  //           updatedCollections
  //               .add(collection.copyWith(variants: _updatedVariants));
  //         },
  //         apiFailure: (error, _) {
  //           updatedCollections.add(collection);
  //           ErrorHandler.report(
  //             error,
  //             null,
  //             hint: {
  //               'info':
  //                   'Api Failure during inventory fetch for browse collection: ${collection.id}'
  //             },
  //           );
  //         },
  //       );
  //     } catch (error) {
  //       updatedCollections.add(collection);
  //       ErrorHandler.report(
  //         error,
  //         StackTrace.current,
  //         hint: {
  //           'info':
  //               'Exception during inventory fetch for browse collection: ${collection.id}'
  //         },
  //       );
  //     }
  //   }

  //   final filteredCollection = updatedCollections
  //       .where((e) => e.variants != null && e.variants!.isNotEmpty)
  //       .toList();
  //   final updatedResult = data?.copyWith(collections: filteredCollection);
  //   final currentState = state.copyWith(
  //       isLoading: false,
  //       isError: false,
  //       result: updatedResult,
  //       hasLoaded: true,
  //       availableInYourArea: state.availableInYourArea);
  //   emit(currentState);
  // }

  @override
  Future<void> close() {
    currentOutlet = null;
    emit(BrowseCollectionState.initialState());
    return super.close();
  }
}
