import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:shop/app/browse/data/models/browse_collection_result.dart';

@immutable
class BrowseCollectionState extends Equatable {
  final bool isLoading;
  final bool isError;
  final String? errorMessage;
  final String? errorCode;
  final bool availableInYourArea;
  final BrowseCollectionResult? result;
  final bool hasLoaded;

  const BrowseCollectionState({
    required this.isLoading,
    required this.isError,
    this.errorMessage,
    this.errorCode,
    required this.availableInYourArea,
    this.result,
    required this.hasLoaded,
  });

  factory BrowseCollectionState.initialState() {
    return BrowseCollectionState(
      isLoading: false,
      isError: false,
      errorMessage: null,
      errorCode: null,
      availableInYourArea: false,
      result: null,
      hasLoaded: false,
    );
  }

  factory BrowseCollectionState.loadError(
    errorMessage,
    errorCode,
    BrowseCollectionResult? result,
    bool hasLoaded,
    bool availableInYourArea,
  ) {
    return BrowseCollectionState(
      isLoading: false,
      isError: true,
      errorMessage: errorMessage,
      errorCode: errorCode,
      result: result,
      hasLoaded: hasLoaded,
      availableInYourArea: availableInYourArea,
    );
  }

  BrowseCollectionState copyWith({
    bool? isLoading,
    bool? isError,
    String? errorMessage,
    String? errorCode,
    bool? availableInYourArea,
    BrowseCollectionResult? result,
    bool? hasLoaded,
  }) {
    return BrowseCollectionState(
      isLoading: isLoading ?? this.isLoading,
      isError: isError ?? this.isError,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      availableInYourArea: availableInYourArea ?? this.availableInYourArea,
      // result: result,
      result: result ?? this.result,
      hasLoaded: hasLoaded ?? this.hasLoaded,
    );
  }

  @override
  String toString() {
    return '''BestSelling state {
      isLoading: $isLoading,
      isError: $isError,
      errorMessage: $errorMessage,
      errorCode: $errorCode,
      availableInYourArea: $availableInYourArea,
      result: $result,
      hasLoaded: $hasLoaded,
    }''';
  }

  @override
  List<Object?> get props => [
        isLoading,
        isError,
        errorMessage,
        errorCode,
        availableInYourArea,
        result,
        hasLoaded,
      ];
}
