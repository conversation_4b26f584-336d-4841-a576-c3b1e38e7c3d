import 'package:flutter/cupertino.dart';
import 'package:shop/app/browse/presentation/ui/screens/browse/mobile/mobile_browse_screen.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_items.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class Browse extends StatelessWidget {
  const Browse({super.key, this.categoryId});
  final String? categoryId;
  final routeName = BrowsePath;

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: CollectionItems('browse'),
      mediumScreen: MobileBrowseScreen(collectionId: categoryId),
      smallScreen: MobileBrowseScreen(collectionId: categoryId),
    );
  }
}

class BrowseArgs {
  final String? categoryId;
  BrowseArgs(this.categoryId);
}
