import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_cubit.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_state.dart';
import 'package:shop/app/homepage/presentation/logic/bloc/scroll_cubit.dart';
import 'package:shop/app/homepage/presentation/ui/widgets/edit_location_widget.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/cart_icon.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/widgets/placeholders.dart';
import 'package:shop/src/components/src/widgets/search_widget.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

import '../../../../../../collections/presentation/logic/bloc/variant_collection_cubit.dart';
import '../../../../../../collections/presentation/logic/bloc/variant_collection_state.dart';
import '../../../../../../collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import '../../../../../../homepage/presentation/ui/widgets/user_intro.dart';

final _setIndex = ValueNotifier<int>(0);

class MobileBrowseScreen extends StatefulWidget {
  const MobileBrowseScreen({super.key, this.collectionId});
  final String? collectionId;
  @override
  _BrowseState createState() => _BrowseState();
}

class _BrowseState extends State<MobileBrowseScreen> {
  late Completer<void> _refreshCompleter;
  late VariantCollectionCubit _cubit;
  late BrowseCollectionCubit _browseCubit;
  late UserCubit _userCubit;
  ScrollController? _scrollController;
  String? hexCode;
  int batch = 1;
  int? itemCount;
  Collection? currentCollection;

  @override
  void initState() {
    _cubit = BlocProvider.of<VariantCollectionCubit>(context, listen: false);
    _browseCubit =
        BlocProvider.of<BrowseCollectionCubit>(context, listen: false);
    _userCubit = context.read();

    _scrollController = ScrollController();
    // _scrollController?.addListener(_onScroll);
    hexCode = _userCubit.currentUser?.outletLocation?.toPlus6Hex();
    _setIndex.value = 0;

    /*if (widget.categoryId != null) {
      _onCollectionViewed(_browseCubit.state.result!.collections.first);
      return;
    }*/

    if (_browseCubit.state.hasLoaded &&
        _browseCubit.state.result != null &&
        _browseCubit.state.availableInYourArea) {
      if (widget.collectionId != null && widget.collectionId!.isNotEmpty) {
        final collection = _browseCubit.state.result!.collections
            .firstWhere((element) => element.id == widget.collectionId);
        _onCollectionViewed(collection);
      } else {
        _onCollectionViewed(_browseCubit.state.result!.collections.first);
      }
    }

    super.initState();
  }

  // void _onScroll() {
  //   if (_scrollController?.position.pixels ==
  //       _scrollController?.position.maxScrollExtent) {
  //     if ((_cubit.state.collection?.variants?.length ?? 0) < (itemCount ?? 0)) {
  //       batch++;
  //       _cubit.fetchMoreVariantCollection(hexCode, batch.toString());
  //     }
  //   }
  // }

  void scrollToTop() {
    _scrollController?.animateTo(
      0,
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOut,
    );
  }

  Future<void> _refreshData([bool silent = true]) async {
    final success = await _browseCubit.syncBrowseCollection();

    if (success) {
      _setIndex.value = 0;
      _cubit.init();
      _cubit.fetchVariantCollection(
          _browseCubit.state.result!.collections.first, hexCode, silent);
    }

    // Complete the Completer to indicate that the refreshing process is complete
    if (!_refreshCompleter.isCompleted) {
      _refreshCompleter.complete();
    }
  }

  void _onCollectionViewed(Collection collection, [bool silent = false]) {
    _cubit.fetchVariantCollection(
      collection,
      hexCode,
      silent,
    );
    if (mounted) {
      setState(() {
        currentCollection = collection;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final userCubit = context.read<UserCubit>();
    return RefreshIndicator(
      onRefresh: () {
        _refreshCompleter = Completer<void>();

        _refreshData();

        // Return the Completer's future to the RefreshIndicator
        return _refreshCompleter.future;
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          notificationPredicate: (notification) => false,
          title: userCubit.isAnonymous
              ? null
              : Text('Browse',
                  style: Theme.of(context).textTheme.headlineSmall),
          centerTitle: true,
          leadingWidth: userCubit.isAnonymous
              ? MediaQuery.of(context).size.width * 0.7
              : null,
          leading:
              userCubit.isAnonymous ? const EditLocationWidget() : AppBarMenu(),
          actions: [
            CartIcon(iconColor: Theme.of(context).disabledColor),
            const XMargin(16),
          ],
        ),
        body: BlocListener<ScrollCubit, ScrollState>(
          listener: (context, state) {
            if (state.index == 1) {
              scrollToTop();
            }
          },
          child: Material(
            color: Theme.of(context).colorScheme.surface,
            child: BlocConsumer<BrowseCollectionCubit, BrowseCollectionState>(
              listener: (context, state) {
                if (state.hasLoaded &&
                    state.result != null &&
                    state.availableInYourArea) {
                  _cubit.fetchVariantCollection(
                      state.result!.collections.first, hexCode, false);
                }
              },
              builder: (context, state) {
                return CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    if (state.isLoading)
                      SliverToBoxAdapter(
                        // hasScrollBody: false,
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: defaultHorizontalContentPadding,
                                  right: defaultHorizontalContentPadding,
                                  bottom: 12),
                              child: ConstrainedBox(
                                  constraints:
                                      const BoxConstraints.tightFor(height: 40),
                                  child: SearchWidget('Browse')),
                            ),
                            Shimmer.fromColors(
                              baseColor: Colors.grey.withValues(alpha: 0.3),
                              highlightColor: Theme.of(context)
                                  .colorScheme
                                  .primaryContainer,
                              child: const BrowseListShimmer(),
                            ),
                          ],
                        ),
                      )
                    else if (state.isError &&
                        !state.hasLoaded &&
                        state.result == null)
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            KErrorScreen(state.errorCode, () {
                              _browseCubit.fetchBrowseCollection(
                                  _userCubit.currentOutlet,
                                  _userCubit.currentOutlet?.coordinates
                                      ?.plusCode6Hex);
                            }, displayErrorCode: true)
                          ],
                        ),
                      )
                    else if (state.hasLoaded && state.result != null)
                      if (state.availableInYourArea) ...[
                        SliverPadding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: defaultHorizontalContentPadding,
                              vertical: 14),
                          sliver: SliverToBoxAdapter(
                            child: ConstrainedBox(
                                constraints:
                                    const BoxConstraints.tightFor(height: 40),
                                child: SearchWidget('Browse')),
                          ),
                        ),
                        SliverPersistentHeader(
                          pinned: true,
                          delegate: BrowseDelegate(
                            (collection) {
                              _onCollectionViewed(collection);
                            },
                            widget.collectionId,
                          ),
                        ),
                        BlocConsumer<VariantCollectionCubit,
                            VariantCollectionState>(
                          listener: (context, state) {
                            if (state.isError == true &&
                                state.collection != null) {
                              Toast.error(state.errorMessage, context,
                                  duration: 2);
                            }
                          },
                          builder: (context, state) {
                            Widget sliver;
                            if (state.isLoading) {
                              sliver = SliverFillRemaining(
                                hasScrollBody: false,
                                child: Shimmer.fromColors(
                                  baseColor: Colors.grey.withValues(alpha: 0.3),
                                  highlightColor: Theme.of(context)
                                      .colorScheme
                                      .primaryContainer,
                                  child: const VariantListShimmer(),
                                ),
                              );
                            } else if (state.isError == true &&
                                (state.collection == null ||
                                    state.collection?.variants?.isEmpty ==
                                        true)) {
                              sliver = SliverFillRemaining(
                                hasScrollBody: false,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    KErrorScreen(state.errorCode, () {
                                      _cubit.fetchVariantCollection(
                                          currentCollection!, hexCode);
                                    }, displayErrorCode: true)
                                  ],
                                ),
                              );
                            } else {
                              sliver = SliverPadding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical:
                                        defaultVerticalContentPaddingLarge),
                                sliver: _buildBrowseCard(context, state),
                              );
                            }

                            return sliver;
                          },
                        ),
                      ] else
                        SliverFillRemaining(
                          hasScrollBody: false,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(kSvgEmptyCollectionIcon),
                                const YMargin(20),
                                Text(
                                  "No items for your location",
                                  style: textTheme.headlineSmall,
                                  textAlign: TextAlign.center,
                                ),
                                const YMargin(10),
                                Text(
                                  "We’re expanding quickly so please \ncheck back soon",
                                  style: textTheme.bodyLarge,
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        )
                    else
                      const SliverToBoxAdapter(
                        child: SizedBox.shrink(),
                      )
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBrowseCard(BuildContext context, VariantCollectionState state) {
    List<Variant> variants = state.collection?.variants ?? [];
    itemCount = state.collection?.total ?? 0;
    int length = variants.length;
    final textTheme = Theme.of(context).textTheme;

    if (state.hasLoaded && variants.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(kSvgEmptyCollectionIcon),
              const YMargin(20),
              Text(
                "No items for this collection",
                style: textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const YMargin(10),
              Text(
                "Please check back soon",
                style: textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final size = MediaQuery.sizeOf(context);
    final isSmallScreen = size.height < kGridScreenHeight;

    final listDelegate = SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      childAspectRatio:
          (size.width / size.height) * (isSmallScreen ? 0.9 : 1.4),
      crossAxisSpacing: 10.0,
      mainAxisSpacing: 10.0,
    );

    return SliverGrid(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          if (index >= length) {
            return _cubit.state.collection!.variants!.length < itemCount!
                ? KLoader()
                : const SizedBox.shrink();
          }

          Variant variant = variants[index];
          return VariantGridItem(
            key: ValueKey(state.collection?.name),
            variant: variant,
            variantList: variants,
            collectionName: currentCollection?.name,
            isGridView: true,
            position: 'browse',
          );
        },
        childCount: (state.hasReachedMax || length < variantCollectionsLimit)
            ? length
            : length + 1,
      ),
      gridDelegate: listDelegate,
    );
  }
}

class BrowseDelegate extends SliverPersistentHeaderDelegate {
  final Function(Collection collection) onCollectionViewed;
  final String? categoryId;
  BrowseDelegate(this.onCollectionViewed, this.categoryId);

  @override
  double get minExtent => 42;

  @override
  double get maxExtent => 60;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      child: BlocConsumer<BrowseCollectionCubit, BrowseCollectionState>(
        listener: (context, state) {
          if (state.isError == true && state.result != null) {
            Toast.error(state.errorMessage, context, duration: 2);
          }
        },
        builder: (context, state) {
          Widget child = const Center(
            child: CupertinoActivityIndicator(),
          );

          if (state.result != null) {
            final collections = state.result?.collections ?? [];
            // collections.insert(0, Collection(id: 'name', name: 'All'));

            final List<Widget> items =
                mapIndexed(collections, (int mapIndex, Collection item) {
              return Padding(
                key: PageStorageKey(item.id ?? mapIndex),
                padding: EdgeInsets.only(
                    right: 14,
                    left: mapIndex == 0 ? defaultHorizontalContentPadding : 0),
                child: Center(
                  child: ValueListenableBuilder<int>(
                    valueListenable: _setIndex,
                    builder: (context, index, _) {
                      return InkWell(
                        onTap: () {
                          _setIndex.value = mapIndex;
                          onCollectionViewed(item);
                        },
                        child: SizedBox(
                          width: 120,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              toTitleCase('${item.name}'),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                      color: _setIndex.value == 0
                                          ? categoryId != null
                                              ? categoryId == item.id
                                                  ? shopActiveColor(context)
                                                  : shopInActiveColor(context)
                                              : mapIndex == 0
                                                  ? shopActiveColor(context)
                                                  : shopInActiveColor(context)
                                          : index == mapIndex
                                              ? shopActiveColor(context)
                                              : shopInActiveColor(context)),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              );
            }).toList();

            child = ListView.builder(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              // children: items,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return items[index];
              },
            );
          }
          return AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            switchInCurve: Curves.easeOut,
            switchOutCurve: Curves.fastOutSlowIn,
            transitionBuilder: (Widget widget, Animation<double> animation) {
              return SizeTransition(
                axis: Axis.vertical,
                axisAlignment: -1.0,
                sizeFactor: animation,
                child: widget,
              );
            },
            child: child,
          );
        },
      ),
    );
  }

  @override
  bool shouldRebuild(covariant BrowseDelegate oldDelegate) {
    return true;
  }
}
