import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_cubit.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_state.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_detail/collection_detail.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/cart_icon.dart';
import 'package:shop/app/notification/presentation/ui/widgets/notification_bell.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/cached_image/index.dart';
import 'package:shop/src/components/src/widgets/search_widget.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

const browse = 'browse';

class WebBrowseScreen extends StatefulWidget {
  const WebBrowseScreen({super.key});

  @override
  _BrowseState createState() => _BrowseState();
}

class _BrowseState extends State<WebBrowseScreen> {
  @override
  void initState() {
    BrowseCollectionCubit cubit = context.read();
    cubit.syncBrowseCollection();
    super.initState();
  }

  void _handleErrorButton(context) {
    UserCubit userCubit = Provider.of<UserCubit>(context, listen: false);
    RetailOutlet? outlet = userCubit.currentOutlet;
    BlocProvider.of<BrowseCollectionCubit>(context, listen: false)
        .fetchBrowseCollection(outlet, outlet?.coordinates?.plusCode6Hex);
  }

  List<Widget> _buildHeaderSlivers(
      BuildContext context, bool innerBoxIsScrolled) {
    return <Widget>[
      SliverToBoxAdapter(
        child: Container(
          padding: EdgeInsets.only(left: 20, right: 20),
          child: buildIntro(context),
          // color: Colors.white,
        ),
      ),
      SliverOverlapAbsorber(
        // This widget takes the overlapping behavior of the SliverAppBar,
        // and redirects it to the SliverOverlapInjector below. If it is
        // missing, then it is possible for the nested "inner" scroll view
        // below to end up under the SliverAppBar even when the inner
        // scroll view thinks it has not been scrolled.
        // This is not necessary if the "headerSliverBuilder" only builds
        // widgets that do not overlap the next sliver.
        handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
        sliver: SliverPersistentHeader(
          pinned: true,
          delegate: KSearchDelegate(addPadding: true, position: 'Browse'),
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        actions: [
          CartIcon(
            iconColor: Theme.of(context).disabledColor,
          ),
          NotificationBell(),
        ],
      ),
      body: NestedScrollView(
        key: PageStorageKey('browse'),
        headerSliverBuilder: _buildHeaderSlivers,
        body: BlocBuilder<BrowseCollectionCubit, BrowseCollectionState>(
          builder: (context, state) {
            Widget sliver;
            if (state.isLoading) {
              sliver =
                  SliverFillRemaining(hasScrollBody: false, child: KLoader());
            } else if (state.isError && state.result == null) {
              sliver = SliverFillRemaining(
                hasScrollBody: false,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    YMarginScale(0.09),
                    KErrorScreen(state.errorCode, () {
                      _handleErrorButton(context);
                    }, displayErrorCode: true)
                  ],
                ),
              );
            } else {
              sliver = SliverPadding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                sliver: _buildBrowseCard(context, state.result?.collections),
              );
            }

            return CustomScrollView(
              slivers: [
                SliverOverlapInjector(
                  // This is the flip side of the SliverOverlapAbsorber above.
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                sliver
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildBrowseCard(BuildContext context, List<Collection>? collections) {
    if (collections == null || collections.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(kSvgloanFailed),
              YMarginScale(0.07),
              SizedBox(
                width: screenWidth(context, percent: 0.5),
                child: Text(
                  "There are no available product categories at the moment.",
                  style: KTextStyle.subtitleTitleText,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      );
    }

    getHeight() {
      double height = MediaQuery.of(context).size.height;

      return height <= 545
          ? 42
          : (height > 545 && height <= 600)
              ? 35
              : 42;
    }

    final listDelegate = SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8.0,
        crossAxisSpacing: 8.0,
        childAspectRatio: 34 / getHeight());

    return SliverGrid(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          String? imageUrl = collections[index].imageUrl ?? '';
          return Material(
            elevation: 0.3,
            child: InkWell(
              onTap: () {
                _showCategoryItems(context, collections[index]);
                Segment.track(
                  eventName: SegmentEvents.productListViewed,
                  properties: {
                    'list_id': collections[index].id,
                    'name': collections[index].name,
                    'category': collections[index].type,
                  },
                );
              },
              child: Container(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 100,
                      child: Center(
                        child: Hero(
                          tag: collections[index],
                          child: AspectRatio(
                            aspectRatio: 0.947,
                            child: RenderImageUrl(imageUrl),
                          ),
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        XMarginScale(0.08),
                        Text(toTitleCase(collections[index].name),
                            style: KTextStyle.bodyText2,
                            overflow: TextOverflow.ellipsis),
                      ],
                    ),
                    Row(
                      children: [
                        XMarginScale(0.08),
                        Text('(${collections[index].count})',
                            style: KTextStyle.selectableText
                                .copyWith(fontSize: 14),
                            overflow: TextOverflow.ellipsis),
                      ],
                    ),
                    YMarginScale(0.02),
                  ],
                ),
              ),
            ),
          );
        },
        childCount: collections.length,
      ),
      gridDelegate: listDelegate,
    );
  }

  Widget buildIntro(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Browse',
          style: KTextStyle.headerTitleText,
        ),
        YMarginScale(0.03),
        Text(
          'Select your preferred product category',
          style: KTextStyle.subtitleTitleText,
        ),
        YMarginScale(0.02),
      ],
    );
  }
}

void _showCategoryItems(BuildContext context, Collection collection) {
  context.pushNamed(
    CollectionDetailsPath,
    extra: CollectionDetailArgs(collection, true, browse),
  );
}
