import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/presentation/logic/bloc/product_cubit.dart';
import 'package:shop/app/product_search/presentation/screens/related_items/related_items.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_commons_flutter/models/user.dart';

class ProductScreen extends StatefulWidget {
  /// The variant id.
  final String variantId;

  const ProductScreen({super.key, required this.variantId});

  @override
  _ProductScreenState createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  late final ProductCubit _cubit;
  StreamSubscription<User?>? _authSubscription;

  @override
  void initState() {
    super.initState();
    _authSubscription =
        context.read<UserCubit>().currentUserStream.listen(handleAuthState);
    _cubit = ProductCubit();
  }

  void handleAuthState(User? user) async {
    if (user != null) _cubit.loadProduct(widget.variantId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder(
      bloc: _cubit,
      builder: (_, state) {
        if (state is ProductLoading) {
          return Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is ProductError) {
          return Scaffold(
            appBar: AppBar(
              elevation: 0,
              leading: KBackButton(),
            ),
            body: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ErrorView(
                  text: state.error,
                  imageSrc: ERROR_IMAGE,
                  onTap: () {
                    _cubit.loadProduct(widget.variantId);
                  },
                ),
                const SizedBox(height: 16),
                OutlinedButton(
                  onPressed: () => context.goNamed(HomePath),
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text('Go Home'),
                ),
              ],
            ),
          );
        }

        if (state is ProductLoaded) {
          return RelatedItems(RelatedItemsArgs(state.variant,
              variantInventory: null, hasInventoryError: false));
        }

        return Center(
          child: KLoader(),
        );
      },
    );
  }

  @override
  void dispose() {
    _cubit.close();
    _authSubscription?.cancel();
    super.dispose();
  }
}
