import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class CategoryItem extends StatelessWidget {
  final String name;
  final String imageUrl;
  final VoidCallback onTap;
  const CategoryItem(this.name, this.imageUrl, this.onTap, {super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 260,
        height: 100,
        decoration:
            BoxDecoration(borderRadius: BorderRadius.circular(5), boxShadow: [
          BoxShadow(blurRadius: 10, offset: Offset(0, 5)),
        ]),
        margin: EdgeInsets.only(right: 10),
        child: Padding(
          padding: screenPadding,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  name,
                  maxLines: 1,
                  softWrap: true,
                  style: KTextStyle.medium14
                      .copyWith(fontSize: 16, overflow: TextOverflow.ellipsis),
                ),
              ),
              CachedNetworkImage(
                imageUrl: imageUrl,
                height: 50,
                width: 50,
              )
            ],
          ),
        ),
      ),
    );
  }
}
