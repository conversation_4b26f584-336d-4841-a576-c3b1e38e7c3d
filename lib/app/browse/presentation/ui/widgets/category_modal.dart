import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/browse/data/models/browse_item.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_cubit.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_state.dart';
import 'package:shop/app/collections/presentation/logic/bloc/collection_cubit.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_config.dart';

import 'category_Item.dart';

class CategoryModal extends StatelessWidget {
  final _delegate = const SliverGridDelegateWithMaxCrossAxisExtent(
      maxCrossAxisExtent: 300,
      childAspectRatio: 1.0,
      mainAxisExtent: 100,
      crossAxisSpacing: 15,
      mainAxisSpacing: 20);

  const CategoryModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: AlignmentDirectional.topCenter,
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            behavior: HitTestBehavior.opaque,
            child: Container(
              constraints: BoxConstraints.expand(),
              padding: EdgeInsets.only(top: 100),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              YSpacing(50),
              CircleAvatar(
                radius: 20,
                child: IconButton(
                  icon: Icon(Icons.clear, size: 20),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              YSpacing(15),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(blurRadius: 10, offset: Offset(0, 5)),
                  ],
                ),
                height: 600,
                width: 1000,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 40, top: 30, bottom: 15),
                      child: Text(
                        "Select your preferred product category",
                        style: KTextStyle.regular14.copyWith(fontSize: 20),
                      ),
                    ),
                    Divider(thickness: 1, height: 1),
                    Expanded(
                      child: BlocBuilder<BrowseCollectionCubit,
                          BrowseCollectionState>(builder: (context, state) {
                        if (state.isLoading) {
                          return KLoader();
                        }
                        final collections = state.result?.collections;
                        var drawerItems = (collections == null ||
                                collections.isEmpty)
                            ? browseItems
                            : collections
                                .map((e) => BrowseItem('${e.name}', '${e.id}'))
                                .toList();
                        final env = context.read<AppConfig>().environment ==
                                Environment.dev
                            ? "dev"
                            : "prod";
                        return GridView(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          padding: EdgeInsets.symmetric(
                              horizontal: 70, vertical: 25),
                          gridDelegate: _delegate,
                          children: drawerItems
                              .map(
                                (e) => CategoryItem(e.name,
                                    "https://s3.amazonaws.com/td-$env-img/xl/${e.id}.png",
                                    () {
                                  context
                                      .read<CollectionCubit>()
                                      .drawerItem
                                      .sink
                                      .add(e);
                                  Navigator.pop(context);
                                  context.pushNamed(BrowsePath);
                                }),
                              )
                              .toList(),
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
