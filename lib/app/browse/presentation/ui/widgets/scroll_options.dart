import 'package:flutter/material.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class ScrollOptions extends StatelessWidget {
  final VoidCallback next;
  final VoidCallback prev;
  final VoidCallback all;

  const ScrollOptions(this.all, this.next, this.prev, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: all,
          child: Text(
            "See All",
            style: KTextStyle.medium14.copyWith(
                fontSize: 16, color: Theme.of(context).colorScheme.primary),
          ),
        ),
        XSpacing(20),
        InkWell(
          onTap: prev,
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            width: 48,
            margin: EdgeInsets.only(right: 10),
            height: 48,
            child: Icon(Icons.arrow_back),
          ),
        ),
        InkWell(
          onTap: next,
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            width: 48,
            height: 48,
            child: Icon(Icons.arrow_forward),
          ),
        )
      ],
    );
  }
}
