import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/browse/domain/repositories/browse_collection_repo.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariant with UseCases<ApiResult<Variant>, VariantParams> {
  FetchVariant(this._repo);

  /// Instance of [BrowseCollectionRepo].
  final BrowseCollectionRepo _repo;

  /// Returns a product for the given [params].
  @override
  Future<ApiResult<Variant>> call(VariantParams params) =>
      _repo.fetchVariant(params);
}
