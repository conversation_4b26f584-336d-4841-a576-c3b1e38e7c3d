import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/domain/repositories/browse_collection_repo.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchBrowseCollection
    with UseCases<ApiResult<BrowseCollectionResult>, RetailOutlet?> {
  FetchBrowseCollection(this._repo);

  /// Instance of [BrowseCollectionRepo].
  final BrowseCollectionRepo? _repo;

  /// Returns `browse` product collection for the given [outlet.coordinates.plusCode6Hex].
  @override
  Future<ApiResult<BrowseCollectionResult>> call(RetailOutlet? outlet) =>
      _repo!.fetchBrowseCollection(outlet);
}
