import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/data/repo_impl/browse_collection_repo_impl.dart';
import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/product_search/domain/params/search_params.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Base class for [BrowseCollectionRepoImplementation].
abstract class BrowseCollectionRepo {
  Future<ApiResult<BrowseCollectionResult>> fetchBrowseCollection(
      RetailOutlet? outlet); 
  Future<ApiResult<List<Variant>>> fetchRelatedItems(RelatedItemsParams params);

  Future<ApiResult<Variant>> fetchVariant(VariantParams params);
}
