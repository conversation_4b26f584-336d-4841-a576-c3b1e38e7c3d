class BrowseItem {
  final String id;
  final String name;

  const BrowseItem(this.name, this.id);

  factory BrowseItem.fromMap(Map<String, Object?> map) {
    return BrowseItem(
      map['name'] as String,
      map['id'] as String,
    );
  }
}

const browseItems = [
  BrowseItem("Foodstuff", "DQyxYne8eHdhzyvK4"),
  BrowseItem("Baby Products", "FCnXi9wj7uxhMifrt"),
  BrowseItem("Tea & Beverage", "aspNWbNLh98q47h2J"),
  BrowseItem("Nestle", "y2uEFjTzp9cdKxCpp"),
  BrowseItem("Wash & Clean", "v7QrKTQhXAktdiEfk"),
  BrowseItem("Breakfast", "qc5nmq3RYjfafQfG8"),
  BrowseItem("Toiletries", "aKnTmywPCHibcHGJz"),
];
