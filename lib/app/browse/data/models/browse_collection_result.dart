import 'package:equatable/equatable.dart';

import 'collection.dart';

class BrowseCollectionResult extends Equatable {
  final List<Collection> collections;
  const BrowseCollectionResult({required this.collections});
  @override
  List get props => [collections];

  BrowseCollectionResult copyWith({
    List<Collection>? collections,
  }) {
    return BrowseCollectionResult(
      collections: collections ?? this.collections,
    );
  }

  bool get isNotEmpty => collections.isNotEmpty;

  @override
  String toString() {
    return '''BrowseCollectionResult {
      collections: $collections,
    }''';
  }
}
