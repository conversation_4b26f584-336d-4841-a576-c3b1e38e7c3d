class SelectionRule {
  final String? id;
  final String? fieldName;
  final String? fieldType;
  final String? fieldValue;
  final String? operatorValue;
  SelectionRule({
    this.id,
    this.fieldName,
    this.fieldType,
    this.fieldValue,
    this.operatorValue,
  });

  factory SelectionRule.fromMap(Map<String, dynamic>? map) {
    if (map == null) return SelectionRule();

    return SelectionRule(
      id: map['_id'],
      fieldName: map['fieldName'],
      fieldType: map['fieldType'],
      fieldValue: map['fieldValue'],
      operatorValue: map['operator'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fieldName': fieldName,
      'fieldType': fieldType,
      'fieldValue': fieldValue,
      'operator': operatorValue,
    };
  }

  @override
  String toString() {
    return '''SelectionRule(
      id: $id, 
      fieldName: $fieldName, 
      fieldType: $fieldType, 
      fieldValue: $fieldValue, 
      operator: $operatorValue
    )''';
  }
}