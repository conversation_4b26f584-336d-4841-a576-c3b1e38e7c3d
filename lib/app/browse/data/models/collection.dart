import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';

import 'selection_rule.dart';

class Collection extends Equatable {
  @override
  List<Object?> get props => [
        id,
        name,
        productTags,
        variantIds,
        serviceAreaCodes,
        isActive,
        isManual,
        isLocationSensitive,
        type,
        position,
        createdAt,
        imageUrl,
        logoUrl,
        domain,
        variants,
        count,
        total,
        headerText,
        headerBgColor,
      ];

  final String? id;
  final String? name;
  final List<String>? productTags;
  final List<String>? variantIds;
  final List<String>? serviceAreaCodes;
  final bool? isActive;
  final bool? isManual;
  final bool? isLocationSensitive;
  // final List<SelectionRule> rules;
  final String? type;
  final num? position;
  final DateTime? createdAt;
  final String? createdBy;
  final String? imageUrl;
  final String? logoUrl;
  final String? domain;
  final List<Variant>? variants;
  final int? count;
  final int? total;
  final String? headerText;
  final String? headerBgColor;
  const Collection({
    this.id,
    this.name,
    this.productTags,
    this.variantIds,
    this.serviceAreaCodes,
    this.isActive,
    this.isManual,
    this.isLocationSensitive,
    this.type,
    this.position,
    this.createdAt,
    this.createdBy,
    this.imageUrl,
    this.logoUrl,
    this.domain,
    this.variants,
    this.count,
    this.total,
    this.headerText,
    this.headerBgColor,
  });

  List<String> get validVariantIds =>
      validVariants.map((e) => e.variantId).whereType<String>().toList();

  List<Variant> get validVariants =>
      variants?.whereType<Variant>().toList() ?? [];

  factory Collection.fromMap(Map<String, dynamic>? map) {
    if (map == null) return const Collection();

    return Collection(
      id: map['id'] ?? map['_id'],
      name: map['name'],
      productTags: List<String>.from(map['productTags']),
      variantIds: List<String>.from(map['variantIds']),
      serviceAreaCodes: List<String>.from(map['serviceAreaCodes']),
      isActive: map['isActive'],
      isManual: map['isManual'],
      isLocationSensitive: map['isLocationSensitive'],
      type: map['type'],
      total: map['total'],
      position: map['position'],
      createdAt: map['createdAt'] != null ? parseDate(map['createdAt']) : null,
      createdBy: map['createdBy'],
      imageUrl: map['imageUrl'],
      logoUrl: map['logoUrl'],
      domain: map['domain'],
      variants: map['productVariants'] != null
          ? List<Variant>.from(
              map['productVariants'].map((x) => Variant.fromMap(x)))
          : [],
      count: map['count'],
      headerText: map['headerText'],
      headerBgColor: map['headerBgColor'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'productTags': productTags,
      'variantIds': variantIds,
      'serviceAreaCodes': serviceAreaCodes,
      'isActive': isActive,
      'isManual': isManual,
      'isLocationSensitive': isLocationSensitive,
      'type': type,
      'total': total,
      'position': position,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'createdBy': createdBy,
      'imageUrl': imageUrl,
      'logoUrl': logoUrl,
      'domain': domain,
      'variants': variants?.map((Variant? x) => x?.toMap()).toList(),
      'count': count,
      'headerText': headerText,
      'headerBgColor': headerBgColor,
    };
  }

  Collection copyWith({
    String? id,
    String? name,
    List<String>? productTags,
    List<String>? variantIds,
    List<String>? serviceAreaCodes,
    bool? isActive,
    bool? isManual,
    bool? isLocationSensitive,
    List<SelectionRule>? rules,
    String? type,
    num? position,
    DateTime? createdAt,
    String? createdBy,
    String? imageUrl,
    String? logoUrl,
    String? domain,
    List<Variant>? variants,
    int? total,
    int? count,
  }) {
    return Collection(
        id: id ?? this.id,
        name: name ?? this.name,
        productTags: productTags ?? this.productTags,
        variantIds: variantIds ?? this.variantIds,
        serviceAreaCodes: serviceAreaCodes ?? this.serviceAreaCodes,
        isActive: isActive ?? this.isActive,
        isManual: isManual ?? this.isManual,
        isLocationSensitive: isLocationSensitive ?? this.isLocationSensitive,
        type: type ?? this.type,
        position: position ?? this.position,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        imageUrl: imageUrl ?? this.imageUrl,
        logoUrl: logoUrl ?? this.logoUrl,
        domain: domain ?? this.domain,
        variants: variants ?? this.variants,
        total: total ?? this.total);
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
