import 'package:shop/app/browse/data/data_source/browse_collection_data_source.dart';
import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/browse/domain/repositories/browse_collection_repo.dart';
import 'package:shop/app/product_search/domain/params/search_params.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';

/// Implements the [BrowseCollectionRepo] abstract class.
///
/// [BrowseCollectionRepo] make calls to [BrowseCollectionDataSource].
class BrowseCollectionRepoImplementation extends BrowseCollectionRepo {
  /// Instance of [BrowseCollectionDataSource].
  final BrowseCollectionDataSource _browseCollectionDataSource;

  BrowseCollectionRepoImplementation(this._browseCollectionDataSource);

  /// Call [BrowseCollectionDataSource] to fetch `browse` product collection.
  @override
  Future<ApiResult<BrowseCollectionResult>> fetchBrowseCollection(
      RetailOutlet? outlet) async {
    return dioInterceptor(
      () => _browseCollectionDataSource.fetchBrowseCollection(outlet),
    );
  }

  /// Call [BrowseCollectionDataSource] to fetch related products
  /// for a given product variant.
  @override
  Future<ApiResult<List<Variant>>> fetchRelatedItems(
      RelatedItemsParams params) async {
    return dioInterceptor(
        () => _browseCollectionDataSource.fetchRelatedItems(params),
        doNotReport: [
          400
        ] // We query mongo on CF directly, 400 is returned if the query returns empty or null
        );
  }

  /// Call [BrowseCollectionDataSource] to fetch a product variant.
  @override
  Future<ApiResult<Variant>> fetchVariant(VariantParams params) async {
    try {
      final res = await dioInterceptor(
        () => _browseCollectionDataSource.fetchVariant(params),
      );
      return res;
    } catch (e) {
      return ApiResult.apiFailure(
        error: ApiExceptions.defaultError('Product not found'),
      );
    }
  }
}
