import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/product_search/domain/params/search_params.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'browse_collection_data_source.dart';

/// Implements [BrowseCollectionDataSource] abstract class.
///
/// Makes network call.
class BrowseCollectionDataSourceImplementation
    implements BrowseCollectionDataSource {
  static const String _browsePath = 'shop/v3/browse';
  static const String _relatedItemsPath = 'v3/variants/related-items';
  static const String _searchVariantPath = 'variant';

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  BrowseCollectionDataSourceImplementation(
      this._apiClient, this._firebaseServiceUrl);

  /// Returns `browse` product collection for the given [outlet.coordinates.plusCode6Hex].
  @override
  Future<BrowseCollectionResult> fetchBrowseCollection(
      RetailOutlet? outlet) async {
    String hexCode = outlet?.coordinates?.plusCode6Hex ?? '';
    late String url;
    if (UserCubit.instance?.isAnonymous == true) {
      final anonUser = UserCubit.instance!.currentAnonymousUser!;
      url =
          '$_firebaseServiceUrl/$_browsePath?lat=${anonUser.latitude}&lng=${anonUser.longitude}';
    } else {
      url =
          '$_firebaseServiceUrl/$_browsePath?plusCode6Hex=${Uri.encodeComponent(hexCode)}';
    }

    final response = await _apiClient.get(url);

    List<dynamic> res = response.data['browse'];

    List<Collection> collections =
        res.map((collection) => Collection.fromMap(collection)).toList();

    BrowseCollectionResult result =
        BrowseCollectionResult(collections: collections);

    return result;
  }

  /// Returns related products for a list of variantIds.
  @override
  Future<List<Variant>> fetchRelatedItems(RelatedItemsParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'type': 'app',
        'method': 'post',
        'path': _relatedItemsPath,
        'data': params.toMap(),
      },
    );

    List<dynamic> response = res.data['data'];
    List<Variant> relatedVariants =
        response.map((variant) => Variant.fromMap(variant)).toList();
    return relatedVariants;
  }

  /// Returns a given product variant.
  @override
  Future<Variant> fetchVariant(VariantParams params) async {
    final url = '$_firebaseServiceUrl/shop/v3/proxy';

    final res = await _apiClient.post(url, data: {
      'type': 'searchUrl',
      'method': 'get',
      'path': _searchVariantPath,
      'params': params.toMap()
    });
    if (res.data?['body'] == null) {
      throw DioError(
        requestOptions: res.requestOptions,
        type: DioErrorType.response,
        response: Response(
            requestOptions: res.requestOptions,
            statusCode: res.statusCode,
            data: {'message': 'Product is unavailable'}),
      );
    }
    return Variant.fromMap(res.data!['body']);
  }
}
