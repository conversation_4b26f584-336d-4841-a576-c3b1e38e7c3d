import 'package:shop/app/browse/data/models/browse_collection_result.dart';
import 'package:shop/app/browse/domain/params/variant_params.dart';
import 'package:shop/app/product_search/domain/params/search_params.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';

import 'browse_collection_data_source_impl.dart';

/// Base class for [BrowseCollectionDataSourceImplementation].
abstract class BrowseCollectionDataSource {
  Future<BrowseCollectionResult> fetchBrowseCollection(RetailOutlet? outlet);
  Future<List<Variant>> fetchRelatedItems(RelatedItemsParams params);
  Future<Variant> fetchVariant(VariantParams params);  
}
