import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/use_case/create_invoice.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/item_cubit/item_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/screens/share_invoice.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/custom_button.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/k_tooltip.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/toast/snackbar.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/extensions/dates.dart';
import 'package:shop/src/res/extensions/strings.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/connectivity/src/network_connection.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

enum InvoiceType { draft, active }

class InvoiceReviewScreen extends StatefulWidget {
  const InvoiceReviewScreen(this.params, {super.key});

  final CreateInvoiceParams params;

  @override
  State<InvoiceReviewScreen> createState() => _InvoiceReviewScreenState();
}

class _InvoiceReviewScreenState extends State<InvoiceReviewScreen> {
  InvoiceType? _invoiceType;

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: 'Invoice Review',
        titleStyle: textTheme.titleLarge
            ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YMargin(11),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InvoiceHeader(),
                  YMargin(16),
                  Divider(
                    height: 1,
                    color: Color(0xFFE5E7EB),
                  ),
                  YMargin(16),
                  CustomerSection(),
                ],
              ),
            ),
            YMargin(24),
            if (widget.params.shipping?.note != null &&
                widget.params.shipping!.note!.isNotEmpty) ...[
              Container(
                width: double.maxFinite,
                decoration: BoxDecoration(
                  color: Colors.white,
                ),
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Shipping Note',
                      style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2937)),
                    ),
                    Text(
                      '${widget.params.shipping?.note}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              YMargin(24),
            ],
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              padding: EdgeInsets.all(16),
              child: ItemsSection(widget.params),
            ),
            YMargin(24),
          ],
        ),
      ),
      bottomNavigationBar: FooterButtonWidget(
        child: StatefulBuilder(
          builder: (context, updateState) {
            return Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Save as Draft',
                    isLoading: isLoading && _invoiceType == InvoiceType.draft,
                    onPressed: () =>
                        handleInvoiceCreation(InvoiceType.draft, updateState),
                    isOutline: true,
                  ),
                ),
                XMargin(16),
                Expanded(
                  child: CustomButton(
                    text: 'Confirm',
                    isLoading: isLoading && _invoiceType == InvoiceType.active,
                    onPressed: () =>
                        handleInvoiceCreation(InvoiceType.active, updateState),
                    isOutline: false,
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }

  Future<void> handleInvoiceCreation(
      InvoiceType invoiceType, Function updateState) async {
    if (!await locator<NetworkConnection>().isDeviceConnected) {
      SnackBarHelper.error(
        'Please check internet connection',
        context,
      );
      return;
    }

    updateState(() {
      isLoading = true;
      _invoiceType = invoiceType;
    });

    final res = await locator<CreateInvoiceUseCase>()
        .call(widget.params.copyWith(status: invoiceType.name));

    res.when(
      success: (CustomerInvoice data) {
        updateState(() {
          isLoading = false;
          _invoiceType = null;
        });
        if (invoiceType == InvoiceType.draft) {
          context.pushReplacementNamed(PastInvoicesPath);
        } else {
          context.pushReplacementNamed(ShareInvoicePath,
              extra: ShareInvoiceScreenArgs(invoice: data));
        }
      },
      apiFailure: (e, s) {
        SnackBarHelper.apiError(e, context);
        updateState(() {
          isLoading = false;
          _invoiceType = null;
        });
      },
    );
  }
}

class InvoiceHeader extends StatelessWidget {
  const InvoiceHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice #',
              style: textTheme.bodyMedium
                  ?.copyWith(color: labelColor, fontWeight: FontWeight.w400),
            ),
            Text(
              'INV-${DateTime.now().year}-${generateNumbers(3)}',
              style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600, color: Color(0xFF1F2937)),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Date',
              style: textTheme.bodyMedium
                  ?.copyWith(color: labelColor, fontWeight: FontWeight.w400),
            ),
            Text(
              DateTime.now().toMonthYear(),
              style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600, color: Color(0xFF1F2937)),
            ),
          ],
        ),
      ],
    );
  }
}

class CustomerSection extends StatelessWidget {
  const CustomerSection({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final customerCubit = context.read<CustomerCubit>();
    return BlocBuilder<CustomerCubit, CustomerState>(
      bloc: customerCubit,
      builder: (context, state) {
        final customer = customerCubit.selectedCustomer;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer',
              style: textTheme.bodyMedium
                  ?.copyWith(color: labelColor, fontWeight: FontWeight.w400),
            ),
            YMargin(8),
            Text(
              capitalize(customer?.name),
              style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600, color: Color(0xFF1F2937)),
            ),
            Text(
              customer?.phoneNumber.formatInternationally() ?? '',
              style: textTheme.bodyMedium
                  ?.copyWith(color: btnTextColor, fontWeight: FontWeight.w400),
            ),
          ],
        );
      },
    );
  }
}

class ItemsSection extends StatelessWidget {
  const ItemsSection(this.params, {super.key});

  final CreateInvoiceParams params;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final shippingCost = params.shipping?.amount ?? 0;

    final amountStyle = textTheme.bodyLarge?.copyWith(
      fontWeight: FontWeight.w600,
      color: Color(0xFF1F2937),
    );

    final textStyle = textTheme.bodyLarge
        ?.copyWith(color: Color(0xFF4B5563), fontWeight: FontWeight.w400);

    return BlocBuilder<ItemCubit, ItemState>(
      builder: (context, state) {
        if ((state is ManageItems) && state.items.isNotEmpty) {
          final items = state.items;
          final subtotal = items.fold(
              0.0, (sum, item) => sum + (item.price * item.quantity));

          final total = subtotal + shippingCost;

          // final vat = (subtotal / 100) * 7.5;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Items (${items.length})',
                style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600, color: Color(0xFF1F2937)),
              ),
              YMargin(16),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  final isLastIndex = index == items.length - 1;
                  return Padding(
                    padding: EdgeInsets.only(bottom: isLastIndex ? 0 : 16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.name,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: textTheme.bodyLarge?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                              YMargin(6),
                              Text(
                                '${item.quantity} units × ${CurrencyItem.value(context, item.price, UserCubit.instance!.currencyCode)}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF6B7280),
                                ),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: IntrinsicWidth(
                            child: KTooltip(
                              message: CurrencyItem.value(
                                  context,
                                  (item.price * item.quantity),
                                  UserCubit.instance!.currencyCode),
                              child: Text(
                                textAlign: TextAlign.end,
                                overflow: TextOverflow.ellipsis,
                                CurrencyItem.value(
                                    context,
                                    (item.price * item.quantity),
                                    UserCubit.instance!.currencyCode),
                                style: amountStyle,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              YMargin(16),
              Divider(
                height: 1,
                color: Color(0xFFE5E7EB),
              ),
              YMargin(16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      'Subtotal',
                      maxLines: 1,
                      style: textStyle,
                    ),
                  ),
                  Expanded(
                    child: IntrinsicWidth(
                      child: KTooltip(
                        message: CurrencyItem.value(context, subtotal,
                            UserCubit.instance!.currencyCode),
                        child: Text(
                          textAlign: TextAlign.end,
                          overflow: TextOverflow.ellipsis,
                          CurrencyItem.value(context, subtotal,
                              UserCubit.instance!.currencyCode),
                          style: amountStyle,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              YMargin(6),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      'Shipping Cost',
                      maxLines: 1,
                      style: textStyle,
                    ),
                  ),
                  Expanded(
                    child: IntrinsicWidth(
                      child: KTooltip(
                        message: CurrencyItem.value(context, shippingCost,
                            UserCubit.instance!.currencyCode),
                        child: Text(
                          textAlign: TextAlign.end,
                          overflow: TextOverflow.ellipsis,
                          CurrencyItem.value(context, shippingCost,
                              UserCubit.instance!.currencyCode),
                          style: amountStyle,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // YMargin(12),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     Text(
              //       'VAT (7.5%)',
              //       style: textStyle,
              //     ),
              //     Text(
              //       CurrencyItem.value(
              //           context, vat, UserCubit.instance!.currencyCode),
              //       style: amountStyle,
              //     ),
              //   ],
              // ),
              YMargin(12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      'Total',
                      maxLines: 1,
                      style: amountStyle?.copyWith(fontSize: 18),
                    ),
                  ),
                  Expanded(
                    child: IntrinsicWidth(
                      child: KTooltip(
                        message: CurrencyItem.value(
                            context, total, UserCubit.instance!.currencyCode),
                        child: Text(
                          textAlign: TextAlign.end,
                          overflow: TextOverflow.ellipsis,
                          CurrencyItem.value(
                              context, total, UserCubit.instance!.currencyCode),
                          style: amountStyle?.copyWith(
                            fontSize: 18,
                            color: Color(0xFFF97316),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        }
        return Offstage();
      },
    );
  }
}
