import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/app/create_invoice/presentation/logic/account_cubit/account_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:shop/app/credit/domain/repo/credit_repo.dart';
import 'package:shop/app/credit/presentation/logic/methods/events_tracking.dart';
import 'package:shop/app/credit/presentation/logic/states/credit_docs_state.dart';
import 'package:shop/app/credit/presentation/ui/modals/settlement_bank_list.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/toast/snackbar.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class AddBankAccountScreen extends StatefulWidget {
  const AddBankAccountScreen({super.key});

  @override
  State<AddBankAccountScreen> createState() => _AddBankAccountScreenState();
}

class _AddBankAccountScreenState extends State<AddBankAccountScreen>
    with EventsTracking {
  final _controllers = <String, TdTextController>{};
  final ValueNotifier<CubitState<List<SettlementBank>>> _bankOptions =
      ValueNotifier(const CubitState.init());
  SettlementBank? selectedBank;
  LinkBankParams? linkBankParams;
  bool _confirmAccount = false;
  bool _accountNameValid = false;
  final _validating = ValueNotifier<bool>(false);
  bool _loading = false;

  // This token will be incremented on each new validation.
  int _currentValidationRequestId = 0;

  @override
  void initState() {
    _registerControllers();
    _loadBankOptions();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: 'Add Bank Account',
        titleStyle: textTheme.titleLarge
            ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.05),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    ValueListenableBuilder<CubitState<List<SettlementBank>>>(
                      valueListenable: _bankOptions,
                      builder: (context, options, _) {
                        return TdTextField(
                          readonly: true,
                          textController: _controllers['bank_name'],
                          inputFormatters: [validInput()],
                          onSubmitted: () {
                            FocusScope.of(context).nextFocus();
                          },
                          inputDecoration: InputDecoration(
                            isCollapsed: true,
                            border: inputBorder,
                            enabledBorder: inputBorder,
                            focusedBorder: inputBorder,
                            hintText: "Choose your bank",
                            hintStyle: textTheme.bodyLarge,
                            suffixIconConstraints:
                                const BoxConstraints.tightFor(
                              height: 30,
                              width: 40,
                            ),
                            suffixIcon: options.maybeWhen(
                              orElse: () => const Icon(
                                  Icons.keyboard_arrow_down_outlined),
                              loading: (load) => const Padding(
                                padding: EdgeInsets.only(right: 10),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    primaryColor,
                                  ),
                                ),
                              ),
                              error: (_) => const Icon(
                                Icons.error_outline_outlined,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 12,
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            constraints:
                                BoxConstraints.tight(const Size(388, 48)),
                          ),
                          onTap: () {
                            _bankOptions.value.maybeWhen(
                              orElse: () {},
                              completed: (options) async {
                                final res =
                                    await SettlementBankList.displayModal(
                                        context, options, Colors.white);
                                if (res == null || selectedBank == res) return;
                                selectedBank = res;
                                _controllers['bank_name']?.controller?.text =
                                    res.name;
                                // Cancel any pending validations when bank changes.
                                _currentValidationRequestId++;
                                _validateAccount();
                              },
                              error: (err) {
                                SnackBarHelper.error(err, context,
                                    mcaError: true);
                                _loadBankOptions();
                              },
                            );
                          },
                        );
                      },
                    ),
                    TdTextField(
                      textController: _controllers['account_number'],
                      keyboardType: TextInputType.number,
                      maxLength: 10,
                      inputFormatters: [
                        validInput(),
                        FilteringTextInputFormatter.digitsOnly
                      ],
                      onSubmitted: () {
                        FocusScope.of(context).unfocus();
                        _validateAccount();
                      },
                      onTextChanged: (value) {
                        if (value.length < 10) {
                          // Cancel any pending validation.
                          _currentValidationRequestId++;
                          final accountName =
                              _controllers['account_name']?.controller?.text ??
                                  '';
                          if (accountName.isNotEmpty) {
                            _controllers['account_name']?.controller?.clear();
                          }
                          _validating.value = false;

                          setState(() {
                            _accountNameValid = false;
                          });
                        } else if (value.length == 10) {
                          _validateAccount();
                        }
                      },
                      inputDecoration: inputDecoration(
                          'Enter 10-digit account number', context),
                    ),
                    ValueListenableBuilder<bool>(
                      valueListenable: _validating,
                      builder: (context, validating, _) {
                        return TdTextField(
                          readonly: true,
                          title: 'Account Name',
                          textController: _controllers['account_name'],
                          inputFormatters: [validInput()],
                          onSubmitted: () {},
                          inputDecoration: inputDecoration(
                            'John Doe',
                            context,
                            suffixIcon: validating
                                ? const FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        primaryColor,
                                      ),
                                    ),
                                  )
                                : const Offstage(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const YMargin(20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.05),
                    ),
                  ],
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _confirmAccount,
                      onChanged: (value) {
                        setState(() {
                          _confirmAccount = value!;
                        });
                      },
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                      side: WidgetStateBorderSide.resolveWith(
                          (Set<WidgetState> states) {
                        if (!states.contains(WidgetState.selected)) {
                          return const BorderSide(color: Color(0xFF000000));
                        }
                        return const BorderSide(color: primaryColor);
                      }),
                    ),
                    const XMargin(12),
                    Expanded(
                      child: Text(
                        'I confirm that this bank account belongs to me and I authorize TradeDepot to verify this account.',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF4B5563),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: FooterButtonWidget(
        child: StatefulBuilder(builder: (context, updateState) {
          return KButtonPrimary(
            disabled: !_confirmAccount || !_accountNameValid || _loading,
            isLoading: _loading,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            elevation: 0,
            constraints: BoxConstraints.tight(const Size(388, 48)),
            onTap: () async {
              if (!_controllers.validate()) return;

              if (linkBankParams == null) {
                return SnackBarHelper.error('Enter a valid account', context);
              }

              if (!_confirmAccount) {
                return SnackBarHelper.error(
                    'Tap on the checkbox to confirm account', context);
              }

              updateState(() {
                _loading = true;
              });

              final res = await context
                  .read<CreditDocsState>()
                  .linkBank(linkBankParams!);

              res.when(
                success: (bool success) async {
                  context.read<AccountCubit>().fetchAccounts();
                  updateState(() {
                    _loading = false;
                  });
                  linkAccountCompleted();
                  SnackBarHelper.success(
                      "Account linked successfully", context);
                  context.pop();
                },
                apiFailure: (e, _) {
                  updateState(() {
                    _loading = false;
                  });
                  linkAccountFailed();
                  SnackBarHelper.error(
                      ApiExceptions.getErrorMessage(e), context,
                      mcaError: true);
                },
              );
            },
            text: 'Add Bank Account',
          );
        }),
      ),
    );
  }

  Future<void> _validateAccount() async {
    setState(() {
      _accountNameValid = false;
    });

    // Reset account name.
    _controllers['account_name']?.controller?.text = '';
    final accountNumber =
        _controllers['account_number']?.controller?.text ?? '';

    if (accountNumber.isEmpty || accountNumber.length != 10) {
      _validating.value = false;
      return;
    }

    if (selectedBank == null) return;

    // Increment the validation token.
    _currentValidationRequestId++;
    final int currentRequest = _currentValidationRequestId;
    _validating.value = true;

    final res = await locator.get<CreditRepo>().validateAccount(
      (code: selectedBank!.code, number: accountNumber),
    );

    // If a new validation request has been initiated, ignore this result.
    if (currentRequest != _currentValidationRequestId) return;

    res.when(
      success: (validated) async {
        if (_validating.value) {
          _validating.value = false;
          _controllers['account_name']?.controller?.text = validated.$1;
          linkBankParams = LinkBankParams(
            accountNumber,
            selectedBank!.code,
            validated.$1,
            selectedBank!.name,
            validated.$2,
            LinkBankType.terminal,
          );

          setState(() {
            _accountNameValid = true;
          });
        }
      },
      apiFailure: (e, _) {
        if (_validating.value) {
          _validating.value = false;
          SnackBarHelper.error(ApiExceptions.getErrorMessage(e), context,
              mcaError: true);
        }
      },
    );
  }

  void _loadBankOptions() async {
    if (_bankOptions.value == const CubitState.loading(loading: true)) return;
    _bankOptions.value = const CubitState.loading(loading: true);
    final res = await locator.get<CreditRepo>().getBanks();
    if (!mounted) return;
    res.when(
      success: (banks) =>
          _bankOptions.value = CubitState.completed(model: banks),
      apiFailure: (e, _) {
        final String errMsg = ApiExceptions.getErrorMessage(e);
        _bankOptions.value = CubitState.error(errorMessage: errMsg);
        SnackBarHelper.error(errMsg, context, mcaError: true);
      },
    );
  }

  void _registerControllers() {
    _controllers['bank_name'] = TdTextController(
      validators: [
        Validators.required(),
      ],
    );
    _controllers['account_number'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(10),
        Validators.max(10),
      ],
    );
    _controllers['account_name'] = TdTextController();
  }
}

// class AddBankAccountScreen extends StatefulWidget {
//   @override
//   State<AddBankAccountScreen> createState() => _AddBankAccountScreenState();
// }

// class _AddBankAccountScreenState extends State<AddBankAccountScreen>
//     with EventsTracking {
//   final _controllers = <String, TdTextController>{};
//   ValueNotifier<CubitState<List<SettlementBank>>> _bankOptions =
//       ValueNotifier(CubitState.init());
//   SettlementBank? selectedBank;
//   LinkBankParams? linkBankParams;
//   bool _confirmAccount = false;
//   final _validating = ValueNotifier<bool>(false);
//   bool _loading = false;

//   @override
//   void initState() {
//     _registerControllers();
//     _loadBankOptions();
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Scaffold(
//       backgroundColor: backgroundColor,
//       appBar: ShopAppBar.invoiceAppBar(
//         context,
//         title: 'Add Bank Account',
//         titleStyle: textTheme.titleLarge
//             ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
//         centerTitle: false,
//       ),
//       body: SingleChildScrollView(
//         child: Padding(
//           padding: EdgeInsets.all(16),
//           child: Column(
//             children: [
//               Container(
//                 padding: EdgeInsets.all(16),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(12),
//                   boxShadow: [
//                     BoxShadow(
//                       offset: Offset(0, 1),
//                       blurRadius: 2,
//                       color: Colors.black.withValues(alpha:0.05),
//                     ),
//                   ],
//                 ),
//                 child: Column(
//                   children: [
//                     // DropdownButtonFormField<String>(
//                     //   icon: Icon(Icons.keyboard_arrow_down_rounded),
//                     //   decoration: InputDecoration(
//                     //     labelText: 'Choose your bank',
//                     //     border: _inputBorder,
//                     //     enabledBorder: _inputBorder,
//                     //     focusedBorder: _inputBorder,
//                     //   ),
//                     //   items: [],
//                     //   onChanged: (value) {},
//                     // ),
//                     // YMargin(20),
//                     ValueListenableBuilder<CubitState<List<SettlementBank>>>(
//                       valueListenable: _bankOptions,
//                       builder: (context, options, _) {
//                         return TdTextField(
//                           // height: 50,
//                           readonly: true,
//                           textController: _controllers['bank_name'],
//                           inputFormatters: [validInput()],
//                           onSubmitted: () {
//                             FocusScope.of(context).nextFocus();
//                           },
//                           inputDecoration: InputDecoration(
//                             isCollapsed: true,
//                             border: inputBorder,
//                             enabledBorder: inputBorder,
//                             focusedBorder: inputBorder,
//                             hintText: "Choose your bank",
//                             hintStyle: textTheme.bodyLarge,
//                             suffixIconConstraints: BoxConstraints.tightFor(
//                               height: 30,
//                               width: 40,
//                             ),
//                             suffixIcon: options.maybeWhen(
//                               orElse: () =>
//                                   Icon(Icons.keyboard_arrow_down_outlined),
//                               loading: (load) => Padding(
//                                 padding: EdgeInsets.only(right: 10),
//                                 child: CircularProgressIndicator(
//                                   strokeWidth: 2,
//                                   valueColor: AlwaysStoppedAnimation<Color>(
//                                     primaryColor,
//                                   ),
//                                 ),
//                               ),
//                               error: (_) => Icon(
//                                 Icons.error_outline_outlined,
//                               ),
//                             ),
//                             // counterText: "",
//                             contentPadding: const EdgeInsets.symmetric(
//                               horizontal: 10,
//                               vertical: 12,
//                             ),
//                             filled: true,
//                             fillColor: Colors.white,
//                             constraints: BoxConstraints.tight(Size(388, 48)),
//                           ),
//                           onTap: () {
//                             _bankOptions.value.maybeWhen(
//                               orElse: () {},
//                               completed: (options) async {
//                                 final res =
//                                     await SettlementBankList.displayModal(
//                                         context, options, Colors.white);
//                                 if (res == null || selectedBank == res) return;
//                                 selectedBank = res;
//                                 _controllers['bank_name']?.controller?.text =
//                                     res.name;

//                                 _validateAccount();
//                               },
//                               error: (err) {
//                                 SnackBarHelper.error(err, context, mcaError: true);
//                                 _loadBankOptions();
//                               },
//                             );
//                           },
//                         );
//                       },
//                     ),
//                     TdTextField(
//                       // height: 50,
//                       textController: _controllers['account_number'],
//                       keyboardType: TextInputType.number,
//                       maxLength: 10,
//                       inputFormatters: [
//                         validInput(),
//                         FilteringTextInputFormatter.digitsOnly
//                       ],
//                       onSubmitted: () {
//                         FocusScope.of(context).unfocus();
//                         _validateAccount();
//                       },
//                       onTextChanged: (value) {
//                         if (value.length == 10) {
//                           _validateAccount();
//                         } else {
//                           final accountName =
//                               _controllers['account_name']?.controller?.text ??
//                                   '';
//                           if (accountName.isNotEmpty) {
//                             _controllers['account_name']?.controller?.clear();
//                             _validating.value = false;
//                           }
//                         }
//                       },
//                       inputDecoration: inputDecoration(
//                           'Enter 10-digit account number', context),
//                     ),

//                     ValueListenableBuilder<bool>(
//                         valueListenable: _validating,
//                         builder: (context, validating, _) {
//                           return TdTextField(
//                             // height: 50,
//                             readonly: true,
//                             title: 'Account Name',
//                             textController: _controllers['account_name'],
//                             inputFormatters: [validInput()],
//                             onSubmitted: () {},
//                             inputDecoration: inputDecoration(
//                                 'John Doe',
//                                 context,
//                                 validating
//                                     ? FittedBox(
//                                         fit: BoxFit.scaleDown,
//                                         child: CircularProgressIndicator(
//                                           strokeWidth: 2,
//                                           valueColor:
//                                               AlwaysStoppedAnimation<Color>(
//                                             primaryColor,
//                                           ),
//                                         ),
//                                       )
//                                     : Offstage()),
//                           );
//                         }),

//                     // _InputField(
//                     //   label: 'Account Number',
//                     //   hint: 'Enter 10-digit account number',
//                     // ),
//                     // YMargin(20),
//                     // _InputField(
//                     //   label: 'Account Name',
//                     //   hint: 'John Doe',
//                     //   readOnly: true,
//                     // ),
//                     // YMargin(20),
//                   ],
//                 ),
//               ),
//               YMargin(20),
//               Container(
//                 padding: EdgeInsets.all(16),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(12),
//                   boxShadow: [
//                     BoxShadow(
//                       offset: Offset(0, 1),
//                       blurRadius: 2,
//                       color: Colors.black.withValues(alpha:0.05),
//                     ),
//                   ],
//                 ),
//                 child: Row(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     StatefulBuilder(
//                       builder: (context, updateState) {
//                         return Checkbox(
//                           value: _confirmAccount,
//                           onChanged: (value) {
//                             updateState(() {
//                               _confirmAccount = value!;
//                             });
//                           },
//                           materialTapTargetSize:
//                               MaterialTapTargetSize.shrinkWrap,
//                           visualDensity:
//                               const VisualDensity(horizontal: -4, vertical: -4),
//                           side: MaterialStateBorderSide.resolveWith(
//                               (Set<MaterialState> states) {
//                             if (!states.contains(MaterialState.selected)) {
//                               return const BorderSide(color: Color(0xFF000000));
//                             }
//                             return BorderSide(color: primaryColor);
//                           }),
//                         );
//                       },
//                     ),
//                     XMargin(12),
//                     Expanded(
//                       child: Text(
//                         'I confirm that this bank account belongs to me and I authorize TradeDepot to verify this account.',
//                         style: textTheme.bodyMedium?.copyWith(
//                           fontWeight: FontWeight.w400,
//                           color: Color(0xFF4B5563),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//       bottomNavigationBar: FooterButtonWidget(
//         child: StatefulBuilder(builder: (context, updateState) {
//           return KButtonPrimary(
//             isLoading: _loading,
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(8.0),
//             ),
//             constraints: BoxConstraints.tight(Size(388, 48)),
//             onTap: () async {
//               if (!_controllers.validate()) return;

//               if (linkBankParams == null) {
//                 return SnackBarHelper.error('Enter a valid account', context);
//               }

//               if (_confirmAccount == false) {
//                 return SnackBarHelper.error(
//                     'Tap on the checkbox to confirm account', context);
//               }

//               updateState(() {
//                 _loading = true;
//               });

//               final res = await context
//                   .read<CreditDocsState>()
//                   .linkBank(linkBankParams!);

//               res.when(
//                 success: (bool) async {
//                   context.read<AccountCubit>().fetchAccounts();
//                   updateState(() {
//                     _loading = false;
//                   });
//                   linkAccountCompleted();
//                   SnackBarHelper.success("Account linked successfully", context);
//                   context.pop();
//                 },
//                 apiFailure: (e, _) {
//                   updateState(() {
//                     _loading = false;
//                   });
//                   linkAccountFailed();
//                   SnackBarHelper.error(ApiExceptions.getErrorMessage(e), context,
//                       mcaError: true);
//                 },
//               );
//             },
//             text: 'Add Bank Account',
//           );
//         }),
//       ),
//     );
//   }

//   Future<void> _validateAccount() async {
//     //reset account name
//     _controllers['account_name']?.controller?.text = '';

//     final accountNumber =
//         _controllers['account_number']?.controller?.text ?? '';

//     if (accountNumber.isEmpty || accountNumber.length != 10) {
//       _validating.value = false;
//       return;
//     }

//     if (selectedBank == null) return;

//     _validating.value = true;

//     final res = await locator.get<CreditRepo>().validateAccount(
//       (
//         code: selectedBank!.code,
//         number: accountNumber,
//       ),
//     );

//     res.when(
//       success: (validated) async {
//         if (_validating.value) {
//           _validating.value = false;
//           _controllers['account_name']?.controller?.text = validated.$1;
//           linkBankParams = LinkBankParams(
//               accountNumber,
//               selectedBank!.code,
//               validated.$1,
//               selectedBank!.name,
//               validated.$2,
//               LinkBankType.terminal);
//         }
//       },
//       apiFailure: (e, _) {
//         if (_validating.value) {
//           _validating.value = false;
//           SnackBarHelper.error(ApiExceptions.getErrorMessage(e), context,
//               mcaError: true);
//         }
//       },
//     );
//   }

//   void _loadBankOptions() async {
//     if (_bankOptions.value == CubitState.loading(loading: true)) return;
//     _bankOptions.value = CubitState.loading(loading: true);
//     final res = await locator.get<CreditRepo>().getBanks();
//     if (!mounted) return;
//     res.when(
//       success: (banks) =>
//           _bankOptions.value = CubitState.completed(model: banks),
//       apiFailure: (e, _) {
//         String errMsg = ApiExceptions.getErrorMessage(e);
//         _bankOptions.value = CubitState.error(errorMessage: errMsg);
//         SnackBarHelper.error(errMsg, context, mcaError: true);
//       },
//     );
//   }

//   _registerControllers() {
//     _controllers['bank_name'] = TdTextController(
//       validators: [
//         Validators.required(),
//       ],
//     );
//     _controllers['account_number'] = TdTextController(
//       validators: [
//         Validators.required(),
//         Validators.min(10),
//         Validators.max(10),
//       ],
//     );

//     _controllers['account_name'] = TdTextController();
//   }
// }
