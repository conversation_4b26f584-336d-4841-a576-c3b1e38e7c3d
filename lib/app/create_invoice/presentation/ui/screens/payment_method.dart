import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/account_cubit/account_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../widgets/colors.dart';
import '../widgets/payment_method_item.dart';

class PaymentMethodScreen extends StatefulWidget {
  const PaymentMethodScreen({super.key});

  @override
  State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends State<PaymentMethodScreen> {
  final GlobalKey _addNewBankKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final userCubit = context.read<UserCubit>();
    final walletAccount = userCubit.currentOutlet?.walletAccount;
    final showWallet = walletAccount?.colBankName != null &&
        walletAccount?.colAccountNumber != null;
    final cubit = context.read<SelectedAccountCubit>();

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: 'Payment Method',
        titleStyle: textTheme.titleLarge
            ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
        centerTitle: false,
      ),
      body: BlocBuilder<SelectedAccountCubit, WalletBank?>(
        builder: (context, state) {
          final bank0 = state;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: CustomScrollView(
              slivers: [
                // TradeDepot Wallet Section
                if (showWallet) ...[
                  const SliverToBoxAdapter(child: YMargin(18)),
                  SliverToBoxAdapter(
                    child: Text(
                      'TradeDepot Wallet',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                        color: labelColor,
                      ),
                    ),
                  ),
                  const SliverToBoxAdapter(child: YMargin(14)),
                  SliverToBoxAdapter(
                    child: PaymentMethodItem(
                      title: 'TradeDepot Wallet',
                      subtitle:
                          "Balance: ${CurrencyItem.value(context, walletAccount?.currentBalance ?? 0, userCubit.currencyCode)}",
                      icon: kSvgWalletCircled,
                      isSelected: bank0?.id != null && bank0?.id == 'wallet',
                      onTap: () {
                        final bank = WalletBank(
                          walletAccount?.colBankName ?? 'TradeDepot Wallet',
                          walletAccount?.colAccountNumber ?? '',
                          null,
                          'wallet',
                          userCubit.currentOutlet?.outletBusinessName,
                          walletAccount?.colAccountNumber ?? '',
                          null,
                          null,
                          WalletBankType.payment,
                        );
                        cubit.updateAccount(bank);
                        context.pop();
                      },
                    ),
                  ),
                ],
                // Bank Accounts Header
                const SliverToBoxAdapter(child: YMargin(24)),
                SliverToBoxAdapter(
                  child: Text(
                    'Bank Accounts',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: labelColor,
                    ),
                  ),
                ),
                const SliverToBoxAdapter(child: YMargin(14)),
                // Bank Accounts List / States
                BlocConsumer<AccountCubit, AccountState>(
                  listener: (context, state) {
                    if (state is AccountError) {
                      positionedBottom();
                      return;
                    }

                    if (state is ExistingAccount) {
                      positionedBottom();
                      return;
                    }
                  },
                  builder: (context, state) {
                    if (state is AccountLoading) {
                      // Return each skeleton as a separate sliver child.
                      return SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) => const Padding(
                            padding: EdgeInsets.only(bottom: 16),
                            child: PaymentMethodItemSkeleton(),
                          ),
                          childCount: 3,
                        ),
                      );
                    } else if (state is AccountError) {
                      return SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            KErrorScreen(
                              state.errorMessage,
                              () =>
                                  context.read<AccountCubit>().fetchAccounts(),
                            ),
                          ],
                        ),
                      );
                    } else if (state is ExistingAccount) {
                      if (state.getBankAccounts.isNotEmpty) {
                        return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final WalletBank item =
                                  state.getBankAccounts[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 18.0),
                                child: PaymentMethodItem(
                                  title: capitalize(item.bankName),
                                  subtitle:
                                      '**** ${item.accountNumber?.substring(item.accountNumber!.length - 4)}',
                                  icon: kSvgBankCircled,
                                  isSelected: bank0?.id != null &&
                                      bank0?.id == item.id &&
                                      bank0?.accountNumber ==
                                          item.accountNumber,
                                  onTap: () {
                                    cubit.updateAccount(item);
                                    context.pop();
                                  },
                                ),
                              );
                            },
                            childCount: state.getBankAccounts.length,
                          ),
                        );
                      } else {
                        return SliverFillRemaining(
                          hasScrollBody: false,
                          child: Center(
                            child: Text(
                              'No Bank Accounts',
                              style: textTheme.bodyMedium,
                            ),
                          ),
                        );
                      }
                    }
                    return const SliverToBoxAdapter(
                      child: SizedBox.shrink(),
                    );
                  },
                ),
                // Add New Bank Account Button
                SliverToBoxAdapter(
                  key: _addNewBankKey,
                  child: Center(
                    child: TextButton.icon(
                      onPressed: () {
                        context.pushNamed(AddBankAccountPath);
                      },
                      icon: const Icon(
                        Icons.add,
                        color: primaryColor,
                        size: 16,
                      ),
                      label: const Text(
                        'Add New Bank Account',
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void positionedBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bottomContext = _addNewBankKey.currentContext;
      if (bottomContext != null) {
        Scrollable.ensureVisible(
          bottomContext,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}

// class PaymentMethodScreen extends StatefulWidget {
//   @override
//   State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
// }

// class _PaymentMethodScreenState extends State<PaymentMethodScreen> {
//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final userCubit = context.read<UserCubit>();
//     final walletAccount = userCubit.currentOutlet?.walletAccount;
//     final showWallet = walletAccount?.colBankName != null &&
//         walletAccount?.colAccountNumber != null;

//     final cubit = context.read<SelectedAccountCubit>();

//     return Scaffold(
//       backgroundColor: backgroundColor,
//       appBar: ShopAppBar.invoiceAppBar(
//         context,
//         title: 'Payment Method',
//         titleStyle: textTheme.titleLarge
//             ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
//         centerTitle: false,
//       ),
//       body: BlocBuilder<SelectedAccountCubit, WalletBank?>(
//         builder: (context, state) {
//           final _bank = state;

//           return Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16.0),
//             child: ListView(
//               children: [
//                 if (showWallet) ...[
//                   YMargin(18),
//                   Text(
//                     'TradeDepot Wallet',
//                     style: textTheme.bodyMedium?.copyWith(
//                       fontWeight: FontWeight.w400,
//                       color: labelColor,
//                     ),
//                   ),
//                   YMargin(14),
//                   PaymentMethodItem(
//                     title: 'TradeDepot Wallet',
//                     subtitle:
//                         "Balance: ${CurrencyItem.value(context, walletAccount?.currentBalance ?? 0, userCubit.currencyCode)}",
//                     icon: kSvgWalletCircled,
//                     isSelected: _bank?.id != null && _bank?.id == 'wallet',
//                     // iconBgColor: primaryColor,
//                     onTap: () {
//                       final bank = WalletBank(
//                           walletAccount?.colBankName ?? 'TradeDepot Wallet',
//                           walletAccount?.colAccountNumber ?? '',
//                           null,
//                           'wallet',
//                           userCubit.currentOutlet?.outletBusinessName,
//                           walletAccount?.colAccountNumber ?? '',
//                           null,
//                           null,
//                           WalletBankType.payment);

//                       cubit.updateAccount(bank);
//                       context.pop();
//                     },
//                   ),
//                 ],
//                 YMargin(24),
//                 Text(
//                   'Bank Accounts',
//                   style: textTheme.bodyMedium?.copyWith(
//                     fontWeight: FontWeight.w400,
//                     color: labelColor,
//                   ),
//                 ),
//                 YMargin(14),
//                 BlocBuilder<AccountCubit, AccountState>(
//                   builder: (context, state) {
//                     Widget child = SizedBox.shrink();
//                     if (state is AccountLoading)
//                       child = Column(
//                         children: List.filled(
//                             3,
//                             Padding(
//                               padding: const EdgeInsets.only(bottom: 16),
//                               child: PaymentMethodItemSkeleton(),
//                             )),
//                       );

//                     if (state is AccountError)
//                       child = SizedBox(
//                         height: MediaQuery.sizeOf(context).height * 0.4,
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             KErrorScreen(
//                               state.errorMessage,
//                               () =>
//                                   context.read<AccountCubit>().fetchAccounts(),
//                             ),
//                           ],
//                         ),
//                       );

//                     if (state is ExistingAccount) {
//                       if (state.getBankAccounts.isNotEmpty) {
//                         child = Column(
//                           children: mapIndexed(
//                             state.getBankAccounts,
//                             (int index, WalletBank item) => Padding(
//                               padding: const EdgeInsets.only(bottom: 18.0),
//                               child: PaymentMethodItem(
//                                 title: capitalize(item.bankName),
//                                 subtitle:
//                                     '**** ${item.accountNumber?.substring(item.accountNumber!.length - 4)}',
//                                 icon: kSvgBankCircled,
//                                 isSelected: _bank?.id != null &&
//                                     _bank?.id == item.id &&
//                                     _bank?.accountNumber == item.accountNumber,
//                                 onTap: () {
//                                   cubit.updateAccount(item);
//                                   context.pop();
//                                 },
//                               ),
//                             ),
//                           ).toList(),
//                         );
//                       } else {
//                         child = Padding(
//                           padding: const EdgeInsets.only(top: 100),
//                           child: Text(
//                             'No Bank Accounts',
//                             style: textTheme.bodyMedium,
//                           ),
//                         );
//                       }
//                     }
//                     return AnimatedSwitcher(
//                       duration: kThemeAnimationDuration,
//                       child: child,
//                     );
//                   },
//                 ),
//                 Center(
//                   child: TextButton.icon(
//                     onPressed: () {
//                       context.pushNamed(AddBankAccountPath);
//                     },
//                     icon: Icon(
//                       Icons.add,
//                       color: primaryColor,
//                       size: 16,
//                     ),
//                     label: Text(
//                       'Add New Bank Account',
//                       style: TextStyle(
//                         color: primaryColor,
//                         fontSize: 16,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
