import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../widgets/colors.dart';

enum CustomerListType { recentCustomers, searchResult }

class SelectCustomer extends StatefulWidget {
  const SelectCustomer({super.key});

  @override
  State<SelectCustomer> createState() => _SelectCustomerState();
}

class _SelectCustomerState extends State<SelectCustomer> {
  final _textEditingController = TextEditingController();
  final _loading = ValueNotifier<bool>(false);
  final _customers = ValueNotifier<List<Customer>>([]);
  Timer? _debounce;
  CustomerListType customerType = CustomerListType.recentCustomers;
  bool get isRecentCustomers =>
      customerType == CustomerListType.recentCustomers;
  List<Customer> recentCustomers = [];
  bool _isRequestCanceled = false;
  String? get searchText => _textEditingController.text.isNotEmpty
      ? _textEditingController.text
      : null;

  @override
  void initState() {
    _customers.value = recentCustomers;
    _getRecentCustomers();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant SelectCustomer oldWidget) {
    if (ModalRoute.of(context)?.isCurrent ?? false) {
      _getRecentCustomers();
    }
    super.didUpdateWidget(oldWidget);
  }

  void _getRecentCustomers() async {
    final customers = await context.read<CustomerCubit>().getRecentCustomers();

    recentCustomers = customers;
    _customers.value = customers;

    if (!isRecentCustomers) {
      _textEditingController.clear();

      setState(() {
        customerType = CustomerListType.recentCustomers;
      });
    }
  }

  void _onSearch(String searchWord) {
    if (searchWord.isEmpty || searchWord.length < 3) {
      if (!isRecentCustomers) {
        _isRequestCanceled = true;
        _loading.value = false;
        _customers.value = recentCustomers;
        setState(() {
          customerType = CustomerListType.recentCustomers;
        });
      }
      return;
    }
    _loading.value = true;
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _isRequestCanceled = false;
      _fetchSearchResults(searchWord);
    });
  }

  void _fetchSearchResults(String query) async {
    final res = await context.read<CustomerCubit>().fetchMyCustomers(query);
    if (_isRequestCanceled) return;
    res.when(
      success: (customers) {
        _customers.value = customers;
        _loading.value = false;
        setState(() {
          customerType = CustomerListType.searchResult;
        });
      },
      apiFailure: (error, _) {
        _customers.value = [];
        _loading.value = false;
        setState(() {
          customerType = CustomerListType.searchResult;
        });

        ScaffoldMessenger.of(context).removeCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(ApiExceptions.getErrorMessage(error))));
      },
    );
    _isRequestCanceled = false;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final customerCubit = context.read<CustomerCubit>();

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: 'Select Customer',
        titleStyle: textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
        centerTitle: false,
      ),
      body: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.symmetric(
              horizontal: defaultHorizontalContentPadding,
              vertical: 16,
            ),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // Search Bar
                ValueListenableBuilder<bool>(
                  valueListenable: _loading,
                  builder: (context, loading, _) {
                    return SearchTextField(
                      loading: loading,
                      loadingColor: primaryColor,
                      textEditingController: _textEditingController,
                      svgPath: kSvgCustomerSearchIcon,
                      filled: true,
                      fillColor: Colors.white,
                      hintText: 'Search customers...',
                      hintStyle: textTheme.bodyLarge?.copyWith(
                        color: const Color(0xFFADAEBC),
                        fontWeight: FontWeight.w400,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: borderColor),
                      ),
                      onSearch: _onSearch,
                      triggerSearchOnSubmit: false,
                    );
                  },
                ),
                const YMargin(24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isRecentCustomers ? "RECENT CUSTOMERS" : "SEARCH RESULTS",
                      style: textTheme.bodyMedium?.copyWith(
                        color: const Color(0xFF4B5563),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Offstage(),
                  ],
                ),
                const YMargin(8),
              ]),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(
              horizontal: defaultHorizontalContentPadding,
            ),
            sliver: BlocBuilder<CustomerCubit, CustomerState>(
              builder: (context, state) {
                final selectedCustomer = customerCubit.selectedCustomer;
                return ValueListenableBuilder<List<Customer>>(
                  valueListenable: _customers,
                  builder: (context, customers, _) {
                    if (customers.isEmpty) {
                      return SliverFillRemaining(
                        hasScrollBody: false,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SvgPicture.asset(kSvgPersonSelectIcon),
                              const YMargin(16),
                              Text(
                                isRecentCustomers
                                    ? 'No Recent Customers'
                                    : 'No Matching results',
                                style: textTheme.bodyMedium,
                                textAlign: TextAlign.center,
                              ),
                              if (isRecentCustomers) ...[
                                const YMargin(5),
                                Text(
                                  "Tap the 'Add New Customer' button\nto create a new customer",
                                  style: textTheme.bodySmall,
                                  textAlign: TextAlign.center,
                                ),
                              ]
                            ],
                          ),
                        ),
                      );
                    }
                    return SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final isLastIndex = index == customers.length - 1;
                          final customer = customers[index];
                          final isSelected = selectedCustomer != null &&
                              selectedCustomer.id == customer.id;
                          return Container(
                            margin:
                                EdgeInsets.only(bottom: isLastIndex ? 0 : 12),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withValues(alpha: 0.05),
                                ),
                              ],
                            ),
                            child: Material(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(8),
                                onTap: () {
                                  customerCubit.selectedExistingCustomer(
                                      customer: customer);
                                  context.pop();
                                },
                                child: ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  leading:
                                      SvgPicture.asset(kSvgPersonSelectIcon),
                                  title: Text(
                                    customer.name.toTitleCase(),
                                    textAlign: TextAlign.left,
                                    style: textTheme.bodyLarge?.copyWith(
                                        color: const Color(0xFF1F2937)),
                                  ),
                                  subtitle: Text(
                                    customer.phoneNumber
                                        .formatInternationally(),
                                    style: textTheme.bodyMedium
                                        ?.copyWith(color: labelColor),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          context.pushNamed(AddCustomerPath,
                                              extra: customer);
                                        },
                                        icon: const Icon(
                                          Icons.edit_outlined,
                                          size: 20,
                                          color: primaryColor,
                                        ),
                                      ),
                                      const XMargin(12),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 16,
                                        color: isSelected
                                            ? primaryColor
                                            : Colors.grey,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        childCount: customers.length,
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: FooterButtonWidget(
        child: KButtonPrimary.icon(
          icon: const Icon(Icons.add),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          elevation: 0,
          constraints: BoxConstraints.tight(const Size(388, 48)),
          onTap: () async {
            context.pushNamed(AddCustomerPath);
          },
          text: 'Add New Customer',
        ),
      ),
    );
  }
}


// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:shop/app/create_invoice/data/model/customer.dart';
// import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
// import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
// import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
// import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/values/app_values/app_values.dart';
// import 'package:td_flutter_src/scaler/src/margin.dart';

// import '../widgets/colors.dart';

// enum CustomerListType { recentCustomers, searchResult }

// class SelectCustomer extends StatefulWidget {
//   const SelectCustomer({super.key});

//   @override
//   State<SelectCustomer> createState() => _SelectCustomerState();
// }

// class _SelectCustomerState extends State<SelectCustomer> {
//   final _textEditingController = TextEditingController();
//   final _loading = ValueNotifier<bool>(false);
//   final _customers = ValueNotifier<List<Customer>>([]);

//   Timer? _debounce;
//   CustomerListType customerType = CustomerListType.recentCustomers;
//   bool get isRecentCustomers =>
//       customerType == CustomerListType.recentCustomers;

//   List<Customer> recentCustomers = [];

//   bool _isRequestCanceled = false;

//   @override
//   void initState() {
//     _customers.value = recentCustomers;
//     _getRecentCustomers();
//     super.initState();
//   }

//   @override
//   void didUpdateWidget(covariant SelectCustomer oldWidget) {
//     if (ModalRoute.of(context)?.isCurrent ?? false) {
//       _getRecentCustomers(true);
//     }
//     super.didUpdateWidget(oldWidget);
//   }

//   void _getRecentCustomers([bool checkListType = false]) async {
//     final customers = await context.read<CustomerCubit>().getRecentCustomers();
//     if (checkListType) {
//       if (customerType == CustomerListType.recentCustomers) {
//         recentCustomers = customers;
//         _customers.value = customers;
//       }
//     } else {
//       recentCustomers = customers;
//       _customers.value = customers;
//     }
//   }

//   void _onSearch(String searchWord) {
//     // If search word is too short, revert to recent customers.
//     if (searchWord.isEmpty || searchWord.length < 3) {
//       if (!isRecentCustomers) {
//         _isRequestCanceled = true;
//         _loading.value = false;
//         _customers.value = recentCustomers;
//         setState(() {
//           customerType = CustomerListType.recentCustomers;
//         });
//       }
//       return;
//     }

//     _loading.value = true;
//     _debounce?.cancel();
//     _debounce = Timer(const Duration(milliseconds: 500), () {
//       _fetchSearchResults(searchWord);
//     });
//   }

//   void _fetchSearchResults(String query) async {
//     final res = await context.read<CustomerCubit>().fetchMyCustomers(query);
//     if (_isRequestCanceled) return;

//     res.when(
//       success: (customers) {
//         _customers.value = customers;
//         _loading.value = false;
//         setState(() {
//           customerType = CustomerListType.searchResult;
//         });
//       },
//       apiFailure: (error, _) {
//         _customers.value = [];
//         _loading.value = false;
//         setState(() {
//           customerType = CustomerListType.searchResult;
//         });
//       },
//     );

//     // Reset cancellation flag after request completion.
//     _isRequestCanceled = false;
//   }

//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//     final textTheme = theme.textTheme;
//     final customerCubit = context.read<CustomerCubit>();

//     return Scaffold(
//       backgroundColor: backgroundColor,
//       appBar: ShopAppBar.invoiceAppBar(
//         context,
//         title: 'Select Customer',
//         titleStyle: textTheme.titleLarge?.copyWith(
//           fontWeight: FontWeight.w600,
//           fontSize: 18,
//         ),
//         centerTitle: false,
//         // actions: [
//         //   TextButton(
//         //     onPressed: () {},
//         //     child: Text(
//         //       "Cancel",
//         //       style: theme.textTheme.bodyMedium?.copyWith(color: primaryColor),
//         //     ),
//         //   ),
//         // ],
//       ),
//       body: SingleChildScrollView(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(
//             horizontal: defaultHorizontalContentPadding,
//             vertical: 16,
//           ),
//           child: Column(
//             children: [
//               // Search Bar
//               ValueListenableBuilder<bool>(
//                 valueListenable: _loading,
//                 builder: (context, loading, _) {
//                   return SearchTextField(
//                     loading: loading,
//                     loadingColor: primaryColor,
//                     textEditingController: _textEditingController,
//                     svgPath: kSvgCustomerSearchIcon,
//                     filled: true,
//                     fillColor: Colors.white,
//                     hintText: 'Search customers...',
//                     hintStyle: textTheme.bodyLarge?.copyWith(
//                       color: const Color(0xFFADAEBC),
//                       fontWeight: FontWeight.w400,
//                     ),
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(8),
//                       borderSide: const BorderSide(color: borderColor),
//                     ),
//                     onSearch: _onSearch,
//                   );
//                 },
//               ),
//               const YMargin(24),
//               // Header for recent or search results
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     isRecentCustomers ? "RECENT CUSTOMERS" : "SEARCH RESULT",
//                     style: textTheme.bodyMedium?.copyWith(
//                       color: const Color(0xFF4B5563),
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   // Uncomment and implement if needed:
//                   // TextButton(
//                   //   onPressed: () {},
//                   //   child: Text(
//                   //     "See All",
//                   //     style: textTheme.bodyMedium?.copyWith(color: primaryColor),
//                   //   ),
//                   // ),
//                   const Offstage(),
//                 ],
//               ),
//               const YMargin(8),
//               // Customer list
//               BlocBuilder<CustomerCubit, CustomerState>(
//                 builder: (context, state) {
//                   final selectedCustomer = customerCubit.selectedCustomer;
//                   return AnimatedSwitcher(
//                     duration: kThemeAnimationDuration,
//                     child: ValueListenableBuilder<List<Customer>>(
//                       valueListenable: _customers,
//                       builder: (context, customers, _) {
//                         if (customers.isEmpty) {
//                           return Center(
//                             child: Text(
//                               isRecentCustomers
//                                   ? 'No recent customer'
//                                   : 'No matching result',
//                               style: textTheme.bodyMedium,
//                             ),
//                           );
//                         }

//                         return ListView.builder(
//                           itemCount: customers.length,
//                           shrinkWrap: true,
//                           physics: NeverScrollableScrollPhysics(),
//                           itemBuilder: (context, index) {
//                             final isLastIndex = index == customers.length - 1;
//                             final customer = customers[index];
//                             final isSelected = selectedCustomer != null &&
//                                 selectedCustomer.id == customer.id;

//                             return Container(
//                               margin:
//                                   EdgeInsets.only(bottom: isLastIndex ? 0 : 12),
//                               // Wrapping in Material to allow InkWell to show ripple
//                               decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(16),
//                                   boxShadow: [
//                                     BoxShadow(
//                                         offset: Offset(0, 1),
//                                         blurRadius: 2,
//                                         color: Colors.black.withValues(alpha: 0.05)),
//                                   ]),
//                               child: Material(
//                                 color: Colors.white,
//                                 borderRadius: BorderRadius.circular(8),
//                                 child: InkWell(
//                                   borderRadius: BorderRadius.circular(8),
//                                   onTap: () {
//                                     customerCubit.selectedExistingCustomer(
//                                         customer: customer);
//                                   },
//                                   child: ListTile(
//                                     contentPadding: const EdgeInsets.symmetric(
//                                         horizontal: 16, vertical: 8),
//                                     leading:
//                                         SvgPicture.asset(kSvgPersonSelectIcon),
//                                     title: Text(
//                                       customer.name,
//                                       style: textTheme.bodyLarge?.copyWith(
//                                           color: const Color(0xFF1F2937)),
//                                     ),
//                                     subtitle: Text(
//                                       customer.phoneNumber,
//                                       style: textTheme.bodyMedium
//                                           ?.copyWith(color: labelColor),
//                                     ),
//                                     trailing: Icon(
//                                       Icons.arrow_forward_ios,
//                                       size: 16,
//                                       color: isSelected
//                                           ? primaryColor
//                                           : Colors.grey,
//                                     ),
//                                     // Removed onTap from ListTile to let InkWell handle taps.
//                                   ),
//                                 ),
//                               ),
//                             );
//                           },
//                         );
//                       },
//                     ),
//                   );
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//       bottomNavigationBar: FooterButtonWidget(
//         child: KButtonPrimary.icon(
//           icon: const Icon(Icons.add),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(8.0),
//           ),
//           constraints: BoxConstraints.tight(Size(388, 48)),
//           onTap: () async {
//             context.pushNamed(AddCustomerPath);
//           },
//           text: 'Add New Customer',
//         ),
//       ),
//     );
//   }
// }