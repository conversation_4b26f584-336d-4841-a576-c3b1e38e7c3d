import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdfx/pdfx.dart';
import 'package:shop/app/create_invoice/presentation/logic/invoice/invoice_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/item_cubit/item_cubit_cubit.dart';
import 'package:shop/app/my_cart/presentation/logic/utils/quick_buy_notifier.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/components/src/widgets/states/error_state.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/src/services/url_service.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class CreateInvoiceViewerScreen extends StatefulWidget {
  final String id;
  const CreateInvoiceViewerScreen({
    required this.id,
    super.key,
  });

  @override
  State<CreateInvoiceViewerScreen> createState() =>
      _CreateInvoiceViewerScreenState();
}

class _CreateInvoiceViewerScreenState extends State<CreateInvoiceViewerScreen> {
  late InvoiceViewerCubit cubit;

  @override
  void initState() {
    cubit = context.read();
    cubit.getInvoice(widget.id);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ShopAppBar.shopAppBar(
        context,
        title: 'Invoice',
        actions: [
          BlocBuilder<InvoiceViewerCubit, InvoiceState>(
            builder: (context, state) {
              if (state is InvoiceLoaded) {
                return IconButton(
                  onPressed: () =>
                      UrlService.it.urlLink(state.invoiceFile.urlPath ?? ''),
                  icon: Icon(Icons.download),
                  iconSize: 28,
                  color: Theme.of(context).colorScheme.primary,
                );
              }
              return SizedBox.shrink();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: BlocBuilder<InvoiceViewerCubit, InvoiceState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (state is InvoiceLoading)
                  Expanded(
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                if (state is InvoiceError)
                  Expanded(
                    child: Center(
                      child: ErrorStateWidget(
                        heading: state.error,
                        subHeading: "",
                        svgAsset: kSvgTransactionsError,
                        retryAction: () => cubit.getInvoice(widget.id),
                      ),
                    ),
                  ),
                if (state is InvoiceLoaded)
                  Expanded(
                    child: SizedBox(
                      height: double.infinity,
                      child: Padding(
                        padding: context.insetsSymetric(horizontal: 20),
                        child: PdfViewPinch(
                          key: UniqueKey(),
                          controller: PdfControllerPinch(
                            document: PdfDocument.openFile(
                              state.invoiceFile.file?.path ?? '',
                            ),
                          ),
                          padding: 0.1,
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
      bottomSheet: BlocBuilder<InvoiceViewerCubit, InvoiceState>(
        builder: (context, state) {
          if (state is InvoiceLoaded) {
            return Padding(
              padding: const EdgeInsets.all(15.0).copyWith(bottom: 30),
              child: Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: screenHeight(context, percent: 0.05),
                      child: KButtonPrimary(
                        text: 'Share',
                        textStyle: textStyleMedium(context, fontSize: 14),
                        elevation: 0,
                        onTap: () {
                          final RenderBox box =
                              context.findRenderObject() as RenderBox;
                          Share.shareXFiles([
                            XFile(state.invoiceFile.file?.path ?? '',
                                mimeType: 'application/pdf')
                          ],
                              subject: 'Invoice',
                              // text: data.substring(data.lastIndexOf("/") + 1),
                              sharePositionOrigin:
                                  box.localToGlobal(Offset.zero) & box.size);
                        },
                      ),
                    ),
                  ),
                  XMargin(8),
                  Expanded(
                    child: SizedBox(
                      height: screenHeight(context, percent: 0.05),
                      child: KButtonPrimary(
                        text: 'Close',
                        textStyle: textStyleMedium(context, fontSize: 14),
                        color: kColorWhite,
                        textColor: Theme.of(context).colorScheme.primary,
                        shape: RoundedRectangleBorder(
                            side: BorderSide(color: kColorOrange)),
                        elevation: 0,
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pop(context);
                          Navigator.pop(context);
                          context.read<QuickBuyNotifier>().disableQuickBuy();
                          context.read<ItemCubit>().clear();
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return Offstage();
        },
      ),
    );
  }
}
