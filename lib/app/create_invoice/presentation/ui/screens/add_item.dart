import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_quantity_picker.dart';
import 'package:shop/app/create_invoice/data/model/item.dart';
import 'package:shop/app/create_invoice/presentation/logic/item_cubit/item_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/input_widget.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/item_search_auto_complete.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/toast/snackbar.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class AddItemScreen extends StatefulWidget {
  const AddItemScreen(this.item, {super.key});

  final InvoiceItem? item;

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _controllers = <String, TdTextController>{};

  bool get isEditing => widget.item != null;
  late final item = widget.item;
  bool deleting = false;

  @override
  void initState() {
    _registerControllers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final itemsCubit = context.read<ItemCubit>();

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: '${isEditing ? 'Edit' : 'Add'} Item',
        titleStyle: textTheme.titleLarge
            ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
        centerTitle: false,
        actions: [
          if (isEditing)
            IconButton(
              icon: SvgPicture.asset(kSvgDeleteInvoiceItem),
              onPressed: () {
                deleting = true;
                itemsCubit.removeInvoiceItem(
                  // Create a new InvoiceItem instance without including index so it correctly matches the corresponding item stored in the state.
                  InvoiceItem(
                    name: item!.name,
                    price: item!.price,
                    quantity: item!.quantity,
                  ),
                );
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  const YMargin(36),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black.withValues(alpha: 0.05),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ItemSearchAutocompleteInput(
                          title: 'Item Description',
                          hint: 'Enter item description',
                          controller: _controllers['item_name']!,
                          onSubmitted: () => FocusScope.of(context).nextFocus(),
                          onSelected: (item) {
                            updateFormattedText(
                                _controllers['item_name']!.controller!,
                                item?.name ?? '', []);
                            updateFormattedText(
                                _controllers['item_price']!.controller!,
                                item?.price.toString() ?? '', [
                              LengthLimitingTextInputFormatter(15),
                              CurrencyInputFormatter(decimalPlaces: 2)
                            ]);

                            FocusScope.of(context).nextFocus();
                          },
                        ),
                        // KInputWidget(
                        //   title: 'Item Description',
                        //   hintText: 'Enter item description',
                        //   controller: _controllers['item_name']!,
                        //   onSubmitted: () => FocusScope.of(context).nextFocus(),
                        // ),
                        const YMargin(8),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: KInputWidget(
                                title: 'Quantity',
                                hintText: '0',
                                controller: _controllers['item_qty']!,
                                textInputType: TextInputType.number,
                                onSubmitted: () =>
                                    FocusScope.of(context).nextFocus(),
                                inputFormatters: [
                                  NoLeadingZeroFormatter(),
                                  LengthLimitingTextInputFormatter(15),
                                  // FilteringTextInputFormatter.digitsOnly,
                                  DecimalTextInputFormatter(
                                    decimalRange: 2,
                                    allowedDecimals: allowedDecimals,
                                  ),
                                ],
                              ),
                            ),
                            const XMargin(12),
                            Expanded(
                              flex: 3,
                              child: KInputWidget(
                                title: 'Price',
                                hintText: '0',
                                isPrice: true,
                                controller: _controllers['item_price']!,
                                textInputType:
                                    const TextInputType.numberWithOptions(),
                                onSubmitted: () =>
                                    FocusScope.of(context).unfocus(),
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(15),
                                  CurrencyInputFormatter(decimalPlaces: 2),
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: BlocListener<ItemCubit, ItemState>(
        bloc: itemsCubit,
        listener: (context, state) {
          if (state is ManageItems) {
            SnackBarHelper.success(
                'Item ${deleting ? 'deleted' : isEditing ? 'updated' : 'added'} successfully',
                context);
            context.pop();
          }
        },
        child: FooterButtonWidget(
          child: StatefulBuilder(builder: (context, updateState) {
            return KButtonPrimary(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              elevation: 0,
              constraints: BoxConstraints.tight(const Size(388, 48)),
              onTap: () async {
                if (!_controllers.validate()) return;
                itemsCubit.addInvoiceItem(_controllers, widget.item?.index);
              },
              text: '${isEditing ? 'Edit' : 'Add'} Item',
            );
          }),
        ),
      ),
    );
  }

  void _registerControllers() {
    _controllers['item_name'] = TdTextController(
      initialValue: item?.name ?? '',
      validators: [
        Validators.required(),
      ],
    );
    _controllers['item_qty'] = TdTextController(
      initialValue: item?.quantity.toString() ?? '',
      validators: [
        Validators.required(),
      ],
    );
    _controllers['item_price'] = TdTextController(
      initialValue: item?.price.toString() ?? '',
      validators: [
        Validators.required(),
      ],
    );
  }

  void updateFormattedText(
    TextEditingController controller,
    String newText,
    List<TextInputFormatter> formatters,
  ) {
    TextEditingValue value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );

    for (final formatter in formatters) {
      value = formatter.formatEditUpdate(controller.value, value);
    }

    controller.value = value;
  }
}
