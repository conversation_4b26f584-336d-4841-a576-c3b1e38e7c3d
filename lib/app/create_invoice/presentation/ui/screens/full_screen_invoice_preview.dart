import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';

class FullScreenInvoicePreview extends StatefulWidget {
  const FullScreenInvoicePreview({super.key, required this.filePath});

  final String filePath;

  @override
  State<FullScreenInvoicePreview> createState() => _FullScreenInvoicePreviewState();
}

class _FullScreenInvoicePreviewState extends State<FullScreenInvoicePreview> {
  late final PdfController _pdfController;

  @override
  void initState() {
    super.initState();
    _pdfController = PdfController(
      document: PdfDocument.openFile(widget.filePath),
    );
  }

  @override
  void dispose() {
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: 'Invoice Preview',
      ),
      body: PdfView(
        controller: _pdfController,
      ),
    );
  }
}