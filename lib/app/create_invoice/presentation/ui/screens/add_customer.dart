import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/address_field.dart';
import 'package:shop/src/components/src/form/src/td_phone_number_input.dart';
import 'package:shop/src/components/src/toast/snackbar.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../widgets/colors.dart';

class AddCustomer extends StatefulWidget {
  const AddCustomer({super.key, this.customer});
  final Customer? customer;

  @override
  State<AddCustomer> createState() => _AddCustomerState();
}

class _AddCustomerState extends State<AddCustomer> {
  final _formKey = GlobalKey<FormState>();
  final _controllers = <String, TdTextController>{};
  bool isLoading = false;
  TdAddress? address;
  final _customerType = ValueNotifier<CustomerType>(CustomerType.individual);

  bool get isUpdate => widget.customer != null;

  @override
  void initState() {
    _registerControllers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    final customerCubit = context.read<CustomerCubit>();

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: ShopAppBar.invoiceAppBar(
        context,
        title: isUpdate ? 'Update Customer' : 'Add New Customer',
        titleStyle: textTheme.titleLarge
            ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
        centerTitle: false,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultHorizontalContentPadding,
          vertical: 16,
        ),
        child: SingleChildScrollView(
          child: BlocListener<CustomerCubit, CustomerState>(
            bloc: customerCubit,
            listener: (context, state) {
              if (state is AddNewCustomerSuccess) {
                // clear inputs
                _controllers['cus_name']?.controller?.clear();
                _controllers['cus_email']?.controller?.clear();
                _controllers['cus_address']?.controller?.clear();
                _controllers['cus_phone']?.clear();

                final msg = state.customerResult.type == ResultType.success
                    ? 'Customer added successfully'
                    : "We've placed it at the top for easy selection";

                final title = state.customerResult.type == ResultType.success
                    ? 'Success'
                    : "Existing customer found";

                SnackBarHelper.success(msg, context, title: title);
                context.pop();
              }

              if (state is AddNewCustomerError) {
                SnackBarHelper.error(state.errorMessage, context);
              }

              if (state is UpdateCustomerSuccess) {
                SnackBarHelper.success('Customer updated successfully', context,
                    title: 'Success');
                context.pop();
              }

              if (state is UpdateCustomerError) {
                SnackBarHelper.error(state.errorMessage, context);
              }
            },
            child: Form(
              key: _formKey,
              // autovalidateMode: AutovalidateMode.,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TdTextField(
                    height: 50,
                    textController: _controllers['cus_name'],
                    onSubmitted: () {
                      FocusScope.of(context).nextFocus();
                    },
                    inputDecoration: inputDecoration('Customer Name', context),
                  ),
                  TdPhoneNumberInput(
                    height: 50,
                    fixed: true,
                    autoValidate: true,
                    textController: _controllers['cus_phone'],
                    errorMessageRight: true,
                    onSubmitted: () {
                      if (mounted) FocusScope.of(context).nextFocus();
                    },
                  ),

                  TdTextField(
                    height: 50,
                    textController: _controllers['cus_email'],
                    keyboardType: TextInputType.emailAddress,
                    // inputFormatters: [validInput()],
                    onSubmitted: () {
                      FocusScope.of(context).nextFocus();
                    },
                    inputDecoration: inputDecoration('Email Address', context),
                  ),
                  AddressField(
                    height: 98,
                    controller: _controllers['cus_address']!,
                    countryCode: UserCubit.instance!.region,
                    isLoading: isLoading,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    inputDecoration: inputDecoration('Address', context),
                    onSetLocation: (location) {
                      address = location;
                    },
                    loading: (value) {
                      setState(() {
                        isLoading = value;
                      });
                    },
                  ),
                  // YMargin(24),
                  Text(
                    'Customer Type',
                    style: textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF374151),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YMargin(8),
                  ValueListenableBuilder<CustomerType?>(
                    valueListenable: _customerType,
                    builder: (context, customerType, _) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: CustomerTypeButton(
                              label: CustomerType.individual,
                              svgPath: kSvgPersonIcon,
                              isSelected:
                                  customerType == CustomerType.individual,
                              onTap: (type) {
                                _customerType.value = type;
                              },
                            ),
                          ),
                          const XMargin(8),
                          Expanded(
                            child: CustomerTypeButton(
                              label: CustomerType.business,
                              svgPath: kSvgBusinessIcon,
                              isSelected: customerType == CustomerType.business,
                              onTap: (type) {
                                _customerType.value = type;
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const YMargin(32),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: FooterButtonWidget(
        child: BlocBuilder<CustomerCubit, CustomerState>(
          bloc: customerCubit,
          builder: (context, state) {
            final isLoading = (state is AddNewCustomerLoading ||
                state is UpdateCustomerLoading);
            return KButtonPrimary(
              isLoading: isLoading,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              elevation: 0,
              constraints: BoxConstraints.tight(const Size(388, 48)),
              onTap: isLoading
                  ? null
                  : () async {
                      // Use async validation that waits for phone validation
                      final isValid = await _validateFormAsync();
                      if (!isValid) return;

                      if (isUpdate) {
                        await customerCubit.updateCustomer(
                          customerController: _controllers,
                          customerType: _customerType.value,
                          address: address,
                          customerId: widget.customer!.id!,
                        );
                      } else {
                        await customerCubit.addNewCustomer(
                            customerController: _controllers,
                            customerType: _customerType.value,
                            address: address);
                      }
                    },
              text: isUpdate ? 'Update Customer' : 'Save Customer',
            );
          },
        ),
      ),
    );
  }

  /// Async form validation that waits for phone validation to complete
  Future<bool> _validateFormAsync() async {
    // First, validate all standard form fields
    final isFormValid = _formKey.currentState?.validate() ?? false;

    // Get the phone number value
    final phoneValue = _controllers['cus_phone']?.controller?.text;

    // Perform async phone validation
    String? phoneError;
    if (phoneValue != null && phoneValue.isNotEmpty) {
      phoneError =
          await TdPhoneNumberInput.validatePhoneAsync(phoneValue, 'NG');
    }

    // If there's a phone error, set it on the controller and trigger validation
    if (phoneError != null) {
      _controllers['cus_phone']?.setError(phoneError);
      return false;
    } else {
      _controllers['cus_phone']?.clearError();
    }

    return isFormValid && phoneError == null;
  }

  void _registerControllers() {
    final customer = widget.customer;
    _controllers['cus_name'] = TdTextController(
      initialValue: customer?.name ?? '',
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _controllers['cus_phone'] = TdTextController(
      initialValue: customer?.phoneNumber ?? '',
      validators: [
        Validators.required(),
      ],
    );

    _controllers['cus_email'] = TdTextController(
      initialValue: customer?.emailAddress ?? '',
      validators: [
        Validators.email(),
      ],
    );

    _controllers['cus_address'] =
        TdTextController(initialValue: customer?.address?.address ?? '');

    _customerType.value = customer?.customerType ?? CustomerType.individual;
  }
}

class CustomerTypeButton extends StatelessWidget {
  final CustomerType label;
  final String svgPath;
  final bool isSelected;
  final ValueChanged<CustomerType> onTap;

  const CustomerTypeButton({
    super.key,
    required this.label,
    required this.svgPath,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: () => onTap(label),
      style: OutlinedButton.styleFrom(
        backgroundColor: isSelected ? const Color(0xFFFFF7ED) : Colors.white,
        side: BorderSide(color: isSelected ? primaryColor : borderColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            svgPath,
            color: isSelected ? primaryColor : const Color(0xFF374151),
          ),
          const XMargin(8),
          Text(
            label.name,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: isSelected ? primaryColor : const Color(0xFF374151),
                  fontWeight: FontWeight.w400,
                ),
          ),
        ],
      ),
    );
  }
}
