import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_invoices_cubit/customer_invoices_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/invoice_list_item.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class PastInvoicesScreen extends StatefulWidget {
  const PastInvoicesScreen({
    super.key,
    this.resetInvoiceFields = true,
  });

  final bool resetInvoiceFields;

  @override
  State<PastInvoicesScreen> createState() => PastInvoicesScreenState();
}

class PastInvoicesScreenState extends State<PastInvoicesScreen> {
  final _textEditingController = TextEditingController();
  final _loading = ValueNotifier<bool>(false);
  bool searching = false;
  Timer? _debounce;
  late CustomerInvoicesCubit _cubit;
  CustomerInvoicesQuery query = CustomerInvoicesQuery();
  final ScrollController _scrollController = ScrollController();
  String? get searchText => _textEditingController.text.isNotEmpty
      ? _textEditingController.text
      : null;

  @override
  void initState() {
    _cubit = context.read<CustomerInvoicesCubit>();
    _scrollController.addListener(_onScroll);
    _cubit.fetchInvoices(query);
    super.initState();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _cubit.fetchMoreInvoices();
    }
  }

  Future _handleSearch(String searchTerm) async {
    if (searchTerm.isEmpty) {
      searching = false;
    } else {
      searching = true;
    }

    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _loading.value = true;
      _cubit.fetchInvoices(
        CustomerInvoicesQuery(
          searchText: searchTerm.isEmpty ? null : searchTerm,
          fetchType: FetchType.search,
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return WillPopScope(
      onWillPop: () async {
        if (widget.resetInvoiceFields) {
          resetCreateInvoiceFields(context);
        }

        return true;
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: ShopAppBar.invoiceAppBar(
          context,
          title: 'Past Invoice',
          titleStyle: textTheme.titleLarge
              ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
          centerTitle: false,
        ),
        body: BlocConsumer<CustomerInvoicesCubit, CustomerInvoicesState>(
          bloc: _cubit,
          listener: (context, state) {
            if (state is CustomerInvoiceLoaded) {
              query = state.result.query;
              _loading.value = false;
            }

            if (state is CustomerInvoiceError) {
              _loading.value = false;
            }
          },
          builder: (context, state) {
            return CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        const YMargin(11),
                        ValueListenableBuilder<bool>(
                          valueListenable: _loading,
                          builder: (context, loading, _) {
                            return SearchTextField(
                              loading: loading,
                              loadingColor: primaryColor,
                              textEditingController: _textEditingController,
                              svgPath: kSvgCustomerSearchIcon,
                              filled: true,
                              fillColor: Colors.white,
                              hintText: 'Search invoices or customers',
                              hintStyle: textTheme.bodyLarge?.copyWith(
                                color: const Color(0xFFADAEBC),
                                fontWeight: FontWeight.w400,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    const BorderSide(color: borderColor),
                              ),
                              onSearch: _handleSearch,
                              triggerSearchOnSubmit: false,
                            );
                          },
                        ),
                        const YMargin(24),
                      ],
                    ),
                  ),
                ),
                if (state is CustomerInvoiceLoading)
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        // final fakeInvoice = CustomerInvoice(id: 'id');
                        return Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16)
                              .copyWith(bottom: 16),
                          child: InvoiceListItemSkeleton(),
                        );
                      },
                      childCount: 5,
                    ),
                  )
                else if (state is CustomerInvoiceLoaded)
                  if (state.result.invoices.isEmpty)
                    SliverFillRemaining(
                      child: Column(
                          //mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(
                              height: 100,
                            ),
                            Icon(
                              Icons.list_alt,
                              size: 96,
                              color: Theme.of(context)
                                  .iconTheme
                                  .color
                                  ?.withValues(alpha: 0.6),
                            ),
                            const SizedBox(
                              height: 24.0,
                            ),
                            Text(
                              searching
                                  ? 'No matching results'
                                  : 'No Past Invoices',
                              style: textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ]),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (!state.hasReachedMax &&
                              index >= state.result.invoices.length) {
                            return KLoader();
                          }

                          final invoice = state.result.invoices[index];

                          return invoice.isDraft
                              ? Dismissible(
                                  key: Key(invoice.id),
                                  direction: DismissDirection.endToStart,
                                  background: Container(
                                    alignment: Alignment.centerRight,
                                    margin: EdgeInsets.symmetric(horizontal: 16)
                                        .copyWith(bottom: 16, right: 20),
                                    padding: EdgeInsets.only(right: 20),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: SvgPicture.asset(
                                        kSvgDeleteInvoiceItem,
                                        color: Colors.white),
                                  ),
                                  onDismissed: (direction) {
                                    _cubit.deleteInvoice(invoice.id);
                                  },
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16)
                                            .copyWith(
                                      bottom: 16,
                                    ),
                                    child: InvoiceListItem(invoice,
                                        resetInvoiceFields:
                                            widget.resetInvoiceFields),
                                  ),
                                )
                              : Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16)
                                      .copyWith(
                                    bottom: 16,
                                  ),
                                  child: InvoiceListItem(invoice,
                                      resetInvoiceFields:
                                          widget.resetInvoiceFields),
                                );
                        },
                        childCount: state.hasReachedMax
                            ? state.result.invoices.length
                            : state.result.invoices.length + 1,
                      ),
                    )
                else if (state is CustomerInvoiceError)
                  SliverFillRemaining(
                    child: Center(
                      child: KErrorScreen(
                          state.error,
                          () => _cubit.fetchInvoices(
                              query.copyWith(page: 1, searchText: searchText))),
                    ),
                  )
                else
                  SliverToBoxAdapter(child: const SizedBox.shrink()),
                SliverToBoxAdapter(
                  child: YMargin(20),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounce?.cancel();
    _textEditingController.dispose();
    super.dispose();
  }
}
