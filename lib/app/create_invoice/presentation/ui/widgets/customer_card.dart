import 'package:flutter/material.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/homepage/presentation/logic/utils/method.dart';
import 'package:shop/src/res/extensions/strings.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class CustomerCard extends StatelessWidget {
  const CustomerCard(this.customer, {super.key});

  final Customer customer;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 70,
      padding: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Color(0xFFFFF7ED),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(0xFFE5E7EB),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[300],
            child: Text(getInitials(customer.name)),
          ),
          XMargin(12),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer.name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodyLarge,
                ),
                Text(
                  customer.phoneNumber.formatInternationally(),
                  style: textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
