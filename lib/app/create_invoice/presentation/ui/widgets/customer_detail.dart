// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
// import 'package:shop/app/create_invoice/presentation/ui/widgets/customer_autocomplete.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
// import 'package:shop/src/components/src/form/form.dart';
// import 'package:shop/src/components/src/form/src/td_phone_number_field.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_src/td_flutter_src.dart';

// class CustomerDetail extends StatelessWidget {
//   const CustomerDetail({
//     super.key,
//     required Map<String, TdTextController> customerController,
//     required this.mounted,
//     // required this.address,
//   }) : _customerController = customerController;

//   final Map<String, TdTextController> _customerController;
//   final bool mounted;

//   @override
//   Widget build(BuildContext context) {
//     final customerCubit = context.read<CustomerCubit>();
//     return BlocConsumer<CustomerCubit, CustomerState>(
//       listener: (context, state) {
//         if (state is AddNewCustomerSuccess) {
//           SnackBarHelper.success('Customer added successfully', context);
//         }
//         if (state is AddNewCustomerError) {
//           SnackBarHelper.customError(state.errorMessage, context);
//         }
//       },
//       builder: (context, state) {
//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             YMargin(16),
//             Text(
//               "Customer Details",
//               style: KTextStyle.bodyText2.copyWith(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),
//             YMargin(4),
//             CustomerAutoComplete(
//               fetchSuggestions: customerCubit.fetchCustomers,
//               controller: _customerController['cus_name']!,
//               onSelected: (customer) {
//                 customerCubit.selectedExistingCustomer(customer: customer);
//               },
//               onAddNew: (newCustomer) {
//                 customerCubit.newCustomerName(newCustomer);
//                 _customerController['cus_name']?.controller?.text =
//                     newCustomer.name;
//               },
//             ),
//             if (state is NewCustomerName || state is AddNewCustomerError) ...[
//               Container(
//                 margin: EdgeInsets.zero,
//                 padding: const EdgeInsets.all(12.0),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.all(
//                     Radius.circular(10),
//                   ),
//                   border: Border.all(color: kColorStormGrey.withValues(alpha: 0.1)),
//                 ),
//                 child: Column(
//                   children: [
//                     TdPhoneNumberField(
//                       title: 'Phone Number',
//                       textController: _customerController['cus_phone'],
//                       inputFormatters: [validInput()],
//                       errorMessageRight: true,
//                       onSubmitted: () {
//                         if (mounted) FocusScope.of(context).nextFocus();
//                       },
//                       isPrefixConstant: true,
//                     ),
//                     SizedBox(
//                       child: KButtonPrimary.outlined(
//                         context,
//                         text: 'Add Customer',
//                         width: 1,
//                         isLoading: (state is AddNewCustomerLoading),
//                         onTap: () => customerCubit.addNewCustomer(
//                           customerController: _customerController,
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               YMargin(16),
//             ],
//           ],
//         );
//       },
//     );
//   }
// }
