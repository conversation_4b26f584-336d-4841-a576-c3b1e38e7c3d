import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';

class KInputWidget extends StatelessWidget {
  const KInputWidget({
    super.key,
    required this.title,
    this.isPrice = false,
    this.hintText,
    required this.controller,
    this.textInputType,
    this.inputFormatters,
    this.focusNode,
    this.onSubmitted,
    this.decoration,
    this.postFix,
    this.readOnly = false,
  });
  final String title;
  final String? hintText;
  final bool isPrice;
  final TdTextController controller;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final VoidCallback? onSubmitted;
  final InputDecoration? decoration;
  final Widget? postFix;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final hintStyle = textTheme.bodyLarge?.copyWith(
      fontSize: 20,
      color: const Color(0xFFADAEBC),
    );

    return Container(
      height: 85,
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 0,
      ).copyWith(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: const Color(0xFF6B7280),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TdTextField(
                  height: 37,
                  textController: controller,
                  textAlign: isPrice ? TextAlign.right : null,
                  keyboardType: textInputType,
                  inputFormatters: inputFormatters,
                  focusNode: focusNode,
                  onSubmitted: onSubmitted,
                  readonly: readOnly,
                  inputDecoration: decoration ??
                      InputDecoration(
                        hintText: hintText,
                        border: InputBorder.none,
                        hoverColor: Colors.transparent,
                        prefixIcon: isPrice
                            ? Padding(
                                padding: const EdgeInsets.only(top: 7),
                                child: Text(
                                    CurrencyItem.currencySymbol(context,
                                        UserCubit.instance!.currencyCode),
                                    style: hintStyle),
                              )
                            : null,
                        hintStyle: hintStyle,
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 0),
                      ),
                ),
              ),
              if (postFix != null) postFix!,
            ],
          ),
        ],
      ),
    );
  }
}
