import 'package:flutter/material.dart';

class KTooltip extends StatelessWidget {
  const KTooltip({super.key, required this.message, required this.child});

  final String message;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: message,
      verticalOffset: 8,
      preferBelow: false,
      textAlign: TextAlign.end,
      triggerMode: TooltipTriggerMode.tap,
      margin: const EdgeInsets.only(left: 100),
      child: child,
    );
  }
}
