import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import 'colors.dart';

class PaymentMethodItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String icon;
  // final Color? iconBgColor;
  final bool isSelected;
  final VoidCallback onTap;

  const PaymentMethodItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.isSelected,
    required this.onTap,
    // this.iconBgColor,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 76,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1),
            blurRadius: 2,
            color: Colors.black.withValues(alpha: 0.05),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.symmetric(
              // vertical: 18,
              horizontal: 16,
            ),
            child: Row(
              children: [
                CircleAvatar(
                  // backgroundColor: iconBgColor ?? Color(0xFFF3F4F6),
                  child: SvgPicture.asset(
                    icon,
                  ),
                ),
                XMargin(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        maxLines: 1,
                        style: textTheme.bodyLarge?.copyWith(
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        subtitle,
                        overflow: TextOverflow.ellipsis,
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w400,
                          color: labelColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 20,
                  width: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? primaryColor : Colors.black,
                      width: 0.5,
                    ),
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.circle,
                          size: 16,
                          color: primaryColor,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class PaymentMethodItemSkeleton extends StatelessWidget {
  const PaymentMethodItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    // Base skeleton colors.
    final baseColor = Colors.grey.shade300;
    final highlightColor = Colors.grey.shade100;

    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        height: 76,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          // Minimal shadow for separation.
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 2,
              color: Colors.black.withValues(alpha: 0.05),
            ),
          ],
        ),
        child: Row(
          children: [
            // Placeholder for the icon (CircleAvatar).
            CircleAvatar(
              backgroundColor: baseColor,
              radius: 18,
            ),
            const XMargin(12),
            // Placeholder for title and subtitle.
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title placeholder.
                  Container(
                    width: double.infinity,
                    height: 16,
                    color: baseColor,
                  ),
                  const SizedBox(height: 6),
                  // Subtitle placeholder.
                  Container(
                    width: 120,
                    height: 14,
                    color: baseColor,
                  ),
                ],
              ),
            ),
            // Placeholder for selection indicator.
            Container(
              height: 20,
              width: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: baseColor,
                  width: 0.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}




// class SelectableItem extends StatelessWidget {
//   final bool? isLastIndex;
//   final String leadingIcon;
//   final IconData trailingIcon;
//   final String title;
//   final String subtitle;
//   final bool isSelected;
//   final VoidCallback onTap;

//   const SelectableItem({
//     Key? key,
//     this.isLastIndex = false,
//     required this.leadingIcon,
//     required this.trailingIcon,
//     required this.title,
//     required this.subtitle,
//     required this.isSelected,
//     required this.onTap,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return Container(
//       margin: EdgeInsets.only(bottom: isLastIndex! ? 0 : 12),
//       decoration:
//           BoxDecoration(borderRadius: BorderRadius.circular(16), boxShadow: [
//         BoxShadow(
//             offset: Offset(0, 1),
//             blurRadius: 2,
//             color: Colors.black.withValues(alpha: 0.05)),
//       ]),
//       child: Material(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8),
//         child: InkWell(
//           borderRadius: BorderRadius.circular(8),
//           onTap: onTap,
//           child: ListTile(
//             contentPadding:
//                 const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//             leading: SvgPicture.asset(leadingIcon),
//             title: Text(
//               title,
//               style:
//                   textTheme.bodyLarge?.copyWith(color: const Color(0xFF1F2937)),
//             ),
//             subtitle: Text(
//               subtitle,
//               style: textTheme.bodyMedium?.copyWith(color: labelColor),
//             ),
//             trailing: Icon(
//               trailingIcon,
//               size: 16,
//               color: isSelected ? primaryColor : Colors.grey,
//             ),
//             // Removed onTap from ListTile to let InkWell handle taps.
//           ),
//         ),
//       ),
//     );
//   }
// }