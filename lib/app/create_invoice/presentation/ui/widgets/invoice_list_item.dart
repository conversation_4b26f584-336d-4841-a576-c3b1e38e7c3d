import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/presentation/ui/screens/share_invoice.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class InvoiceListItem extends StatelessWidget {
  const InvoiceListItem(
    this.invoice, {
    super.key,
    required this.resetInvoiceFields,
  });

  final CustomerInvoice invoice;
  final bool resetInvoiceFields;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1),
            blurRadius: 2,
            color: Colors.black.withValues(alpha: 0.05),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            context.pushNamed(ShareInvoicePath,
                extra: ShareInvoiceScreenArgs(
                    invoice: invoice, resetInvoiceFields: resetInvoiceFields));
          },
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            invoice.customerName ?? '',
                            // maxLines: 1,
                            style: textTheme.bodyLarge?.copyWith(
                              color: Color(0xFF111827),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          // Gap(6),
                          Text(
                            "Invoice #${invoice.invoiceNumber?.toString() ?? ''}",
                            overflow: TextOverflow.ellipsis,
                            style: textTheme.bodyMedium?.copyWith(
                              color: Color(0xFF6B7280),
                              fontWeight: FontWeight.w400,
                            ),
                          )
                        ],
                      ),
                    ),
                    if (invoice.isDraft)
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFFF3F4F6),
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: Text(
                          capitalize(invoice.status),
                          style: textTheme.bodyMedium?.copyWith(
                            color: Color(0xFF374151),
                          ),
                        ),
                      ),
                  ],
                ),
                YMargin(12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        CurrencyItem.value(
                            context,
                            invoice.subTotal ?? 0,
                            invoice.currency?.iso ??
                                UserCubit.instance!.currencyCode),
                        style: textTheme.bodyLarge?.copyWith(
                            color: Color(0xFF111827),
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    Flexible(
                      child: Text(
                        invoice.createdAt?.toMonthYear() ?? '',
                        style: textTheme.bodyMedium?.copyWith(
                          color: Color(0xFF6B7280),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // ListTile(
    //   title: Row(
    //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //     children: [
    //       Text(storeName),
    //       if (status != null)
    //         Container(
    //           padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    //           decoration: BoxDecoration(
    //             color: Colors.grey[200],
    //             borderRadius: BorderRadius.circular(4),
    //           ),
    //           child: Text(
    //             status!,
    //             style: TextStyle(
    //               fontSize: 12,
    //               color: Colors.grey[600],
    //             ),
    //           ),
    //         ),
    //     ],
    //   ),
    //   subtitle: Column(
    //     crossAxisAlignment: CrossAxisAlignment.start,
    //     children: [
    //       Text(invoiceId),
    //       SizedBox(height: 4),
    //       Row(
    //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //         children: [
    //           Text(
    //             '₦${amount.toStringAsFixed(2)}',
    //             style: TextStyle(
    //               color: Colors.black,
    //               fontWeight: FontWeight.bold,
    //             ),
    //           ),
    //           Text(
    //             date,
    //             style: TextStyle(color: Colors.grey[600]),
    //           ),
    //         ],
    //       ),
    //     ],
    //   ),
    //   onTap: () {
    //     // Handle invoice tap
    //     // Navigator.push(
    //     //   context,
    //     //   MaterialPageRoute(builder: (_) => InvoiceReviewScreen()),
    //     // );
    //   },
    // );
  }
}

class InvoiceListItemSkeleton extends StatelessWidget {
  const InvoiceListItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    // Base skeleton colors.
    final baseColor = Colors.grey.shade300;
    final highlightColor = Colors.grey.shade100;

    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 2,
              color: Colors.black.withValues(alpha: 0.05),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                // Simulated text block for outlet business name and invoice number.
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Outlet business name placeholder.
                      Container(
                        width: double.infinity,
                        height: 16,
                        color: baseColor,
                      ),
                      const SizedBox(height: 6),
                      // Invoice number placeholder.
                      Container(
                        width: 120,
                        height: 14,
                        color: baseColor,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Status placeholder.
                Container(
                  width: 80,
                  height: 20,
                  color: baseColor,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Placeholder for invoice amount.
                Container(
                  width: 100,
                  height: 16,
                  color: baseColor,
                ),
                // Placeholder for invoice date.
                Container(
                  width: 80,
                  height: 16,
                  color: baseColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
