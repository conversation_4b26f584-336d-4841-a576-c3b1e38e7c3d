import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/use_case/search_invoice_items.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/input_widget.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:td_flutter_core/config/DI/di.dart';

class ItemSearchAutocompleteInput extends StatefulWidget {
  const ItemSearchAutocompleteInput({
    super.key,
    required this.controller,
    this.title,
    this.hint,
    this.onSelected,
    this.autofocus = false,
    this.enabled = true,
    this.height = 34.0,
    this.borderRadius = 6.0,
    this.focusNode,
    this.onSubmitted,
  });

  final TdTextController controller;
  final String? title;
  final String? hint;
  final void Function(InvoiceItem? variant)? onSelected;
  final bool autofocus;
  final bool enabled;
  final double height;
  final double borderRadius;
  final FocusNode? focusNode;
  final VoidCallback? onSubmitted;

  @override
  State<ItemSearchAutocompleteInput> createState() =>
      _ItemSearchAutocompleteInputState();
}

class _ItemSearchAutocompleteInputState
    extends State<ItemSearchAutocompleteInput> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  Timer? _debounceTimer;
  List<InvoiceItem> _suggestions = [];
  bool _isLoading = false;
  int _selectedIndex = -1;
  late final _focusNode = widget.focusNode ?? FocusNode();
  InvoiceItem? _selectedVariant;

  @override
  void initState() {
    super.initState();
    widget.controller.controller?.addListener(_onSearchChanged);
    (_focusNode..addListener(_onFocusChanged)).requestFocus();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _hideOverlay();
    widget.controller.controller?.removeListener(_onSearchChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideOverlay();
    } else if (widget.controller.text.isNotEmpty && _selectedVariant == null) {
      _onSearchChanged();
    }
  }

  void _clearSelection() {
    setState(() {
      _selectedVariant = null;
      widget.controller.controller?.clear();
    });
    widget.onSelected?.call(null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNode.canRequestFocus) {
        _focusNode.requestFocus();
      }
    });
  }

  void _onSearchChanged() {
    if (!_focusNode.hasFocus || _selectedVariant != null) return;

    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      if (widget.controller.text.isEmpty) {
        _hideOverlay();
        return;
      }

      setState(() => _isLoading = true);

      try {
        final res = await locator.get<SearchInvoiceItems>().call(
              SearchParams(
                  term: widget.controller.text,
                  retailOutletId: UserCubit.instance?.currentOutlet?.id ?? ''),
            );

        res.when(
          success: (items) {
            setState(() {
              _suggestions = items;
              _isLoading = false;
              _selectedIndex = -1;
            });
            if (_suggestions.isNotEmpty && _focusNode.hasFocus) {
              _showOverlay();
            } else {
              _hideOverlay();
            }
          },
          apiFailure: (error, _) {
            setState(() {
              _suggestions = [];
              _isLoading = false;
            });
            _hideOverlay();
          },
        );
      } catch (e) {
        setState(() {
          _suggestions = [];
          _isLoading = false;
        });
        _hideOverlay();
      }
    });
  }

  void _showOverlay() {
    if (_overlayEntry == null) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    } else {
      _updateOverlay();
    }
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    _overlayEntry?.markNeedsBuild();
  }

  void _onVariantSelected(InvoiceItem item) {
    _hideOverlay();

    setState(() {
      _selectedVariant = item;
      widget.controller.controller?.text = item.name ?? '';
    });
    widget.onSelected?.call(item);
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 4),
          child: Material(
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 200,
                minWidth: size.width,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: Border.all(color: borderColor),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black.withValues(alpha: 0.05),
                  ),
                ],
              ),
              child: _buildSuggestionsList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionsList() {
    if (_suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    // _suggestions = [
    //   InvoiceItem(name: widget.controller.controller?.text),
    //   ..._suggestions
    // ];

    return ListView.separated(
      padding: EdgeInsets.zero,
      itemCount: _suggestions.length,
      shrinkWrap: true,
      separatorBuilder: (context, index) =>
          const Divider(height: 1, color: borderColor),
      itemBuilder: (context, index) {
        final variant = _suggestions[index];
        final isSelected = index == _selectedIndex;

        return ListTile(
          onTap: () => _onVariantSelected(variant),
          hoverColor: kColorOrange.withValues(alpha: 0.05),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  variant.name?.toTitleCase() ?? '-',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isSelected ? kColorOrange : kBrandBlack,
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.normal,
                        overflow: TextOverflow.ellipsis,
                      ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                variant.price != null
                    ? CurrencyItem.value(
                        context,
                        variant.price!,
                        UserCubit.instance!.currencyCode,
                      )
                    : '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isSelected ? kColorOrange : kBrandBlack,
                      fontWeight:
                          isSelected ? FontWeight.w500 : FontWeight.normal,
                      overflow: TextOverflow.ellipsis,
                    ),
              ),
            ],
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10),
        );
      },
    );
  }

  void _handleKeyEvent(KeyEvent event) {
    if (_suggestions.isEmpty) return;

    if (event is KeyDownEvent) {
      switch (event.logicalKey.keyLabel) {
        case 'Arrow Down':
          setState(() {
            _selectedIndex = (_selectedIndex + 1) % _suggestions.length;
          });
          _updateOverlay();
          break;
        case 'Arrow Up':
          setState(() {
            _selectedIndex = _selectedIndex <= 0
                ? _suggestions.length - 1
                : _selectedIndex - 1;
          });
          _updateOverlay();
          break;
        case 'Enter':
          if (_selectedIndex >= 0 && _selectedIndex < _suggestions.length) {
            _onVariantSelected(_suggestions[_selectedIndex]);
          }
          break;
        case 'Escape':
          if (_selectedVariant != null) {
            _clearSelection();
          } else {
            _hideOverlay();
          }
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: _handleKeyEvent,
        child: Stack(
          children: [
            KInputWidget(
              title: widget.title ?? '',
              hintText: widget.hint,
              controller: widget.controller,
              onSubmitted: widget.onSubmitted,
              focusNode: _focusNode,
              readOnly: _selectedVariant != null,
              postFix: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : null,
              decoration: _selectedVariant != null
                  ? InputDecoration(
                      hintText: widget.hint,
                      suffixIcon: IconButton(
                        icon: Icon(
                          Icons.close,
                          size: 20,
                          color: kColorOutline,
                        ),
                        onPressed: _clearSelection,
                        splashRadius: 16,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 0,
                        vertical: 0,
                      ),
                      border: InputBorder.none,
                      hoverColor: Colors.transparent,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}
