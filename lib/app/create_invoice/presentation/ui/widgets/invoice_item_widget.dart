import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/item.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class InvoiceItemWidget extends StatelessWidget {
  final InvoiceItem item;
  final int index;
  final bool isLast;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const InvoiceItemWidget({
    super.key,
    required this.item,
    required this.index,
    required this.isLast,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Material(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              onTap: onTap,
              child: Padding(
                padding: EdgeInsets.all(13),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name.toTitleCase(),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '${item.quantity} units × ${CurrencyItem.value(context, item.price, UserCubit.instance!.currencyCode)}',
                            // overflow: TextOverflow.ellipsis,
                            style: textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: SvgPicture.asset(kSvgDeleteInvoiceItem),
                      onPressed: onDelete,
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        YMargin(isLast ? 8 : 16),
      ],
    );
  }
}
