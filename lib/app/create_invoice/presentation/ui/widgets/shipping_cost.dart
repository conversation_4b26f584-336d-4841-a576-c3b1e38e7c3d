import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/shipping_cost.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import 'colors.dart';
import 'footer_button_widget.dart';

class ShippingCostWidget extends StatefulWidget {
  const ShippingCostWidget({super.key, this.initial, required this.onSave});

  final Shipping? initial;
  final ValueChanged<Shipping> onSave;

  static void open(
    BuildContext context, {
    Shipping? initial,
    required ValueChanged<Shipping> onSave,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      builder: (_) => ShippingCostWidget(
        initial: initial,
        onSave: onSave,
      ),
    );
  }

  @override
  State<ShippingCostWidget> createState() => _ShippingCostWidgetState();
}

class _ShippingCostWidgetState extends State<ShippingCostWidget> {
  final _controllers = <String, TdTextController>{};
  late ValueNotifier<bool> _disabled;
  Shipping? shippingCost;

  @override
  void initState() {
    shippingCost = widget.initial;
    _registerControllers();
    _disabled = ValueNotifier<bool>(_isDisabled());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final currencyCode = context.read<UserCubit>().currencyCode;
    return Stack(
      children: [
        SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 60,
            ),
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16)
                          .copyWith(top: 24),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Edit Shipping Cost',
                          style: textTheme.titleLarge?.copyWith(fontSize: 18),
                        ),
                      ),
                      InkWell(
                        child: const Icon(Icons.close),
                        onTap: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                Divider(color: borderColor),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      TdTextField(
                        height: 50,
                        title: 'Amount',
                        keyboardType: TextInputType.numberWithOptions(),
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(15),
                          CurrencyInputFormatter(decimalPlaces: 2),
                        ],
                        textController: _controllers['amount'],
                        onTextChanged: (text) {
                          shippingCost = Shipping(
                              amount:
                                  num.tryParse(text.replaceAll(",", "")) ?? 0,
                              note: shippingCost?.note);
                          _disabled.value = _isDisabled();
                        },
                        onSubmitted: () {
                          FocusScope.of(context).nextFocus();
                        },
                        inputDecoration: inputDecoration('0', context,
                            prefixIcon: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                CurrencyItem.currencySymbol(
                                    context, currencyCode),
                                style: textTheme.bodyLarge?.copyWith(
                                  fontFamily: 'roboto',
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF6B7280),
                                ),
                              ),
                            )),
                      ),
                      YMargin(16),
                      TdTextField(
                        height: 98,
                        expands: true,
                        textAlignVertical: TextAlignVertical.top,
                        title: 'Additional Notes',
                        keyboardType: TextInputType.text,
                        textController: _controllers['note'],
                        onTextChanged: (text) {
                          shippingCost = Shipping(
                              note: text, amount: shippingCost?.amount);
                          _disabled.value = _isDisabled();
                        },
                        onSubmitted: () {
                          FocusScope.of(context).nextFocus();
                        },
                        inputDecoration: inputDecoration(
                            'Enter delivery instructions...', context),
                      ),
                    ],
                  ),
                ),
                YMargin(60),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          left: 0,
          right: 0,
          child: FooterButtonWidget(
            child: ValueListenableBuilder(
              valueListenable: _disabled,
              builder: (context, disabled, _) {
                return KButtonPrimary(
                  disabled: disabled,
                  disabledColor: Color(0xFFD1D5DB),
                  textColor: disabled ? btnTextColor : Colors.white,
                  text: 'Save Changes',
                  elevation: 0,
                  onTap: () {
                    widget.onSave(shippingCost!);
                    shippingCost = null;
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void _registerControllers() {
    final amount = widget.initial?.amount ?? 0;
    final amountText = amount > 0 ? amount.toString() : '';
    _controllers['amount'] = TdTextController(initialValue: amountText);

    _controllers['note'] = TdTextController(initialValue: widget.initial?.note);
  }

  bool _isDisabled() {
    if (!(widget.initial?.isCleared ?? true)) {
      return false;
    }
    final amountText = _controllers['amount']?.text ?? '';
    final noteText = _controllers['note']?.text ?? '';
    return amountText.isEmpty && noteText.isEmpty;
  }
}
