import 'package:flutter/material.dart';
import 'package:safe_insets/safe_area_wrap.dart';

import 'colors.dart';

class FooterButtonWidget extends StatelessWidget {
  const FooterButtonWidget({
    super.key,
    this.child,
  });
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: borderColor),
        ),
      ),
      child: SafeArea(
        child: SafeAreaWrap(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: child,
          ),
        ),
      ),
    );
  }
}
