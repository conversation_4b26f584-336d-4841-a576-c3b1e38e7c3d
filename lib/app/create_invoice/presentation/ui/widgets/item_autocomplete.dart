// ignore_for_file: unused_field

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/td_text_field_error.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class ItemAutoComplete extends StatefulWidget {
  final Future<List<String>> Function(String) fetchSuggestions;

  final void Function(String) onSelected;
  final void Function(String) onAddNew;
  final TdTextController controller;
  final ValueNotifier item;

  const ItemAutoComplete({
    super.key,
    required this.fetchSuggestions,
    required this.onSelected,
    required this.onAddNew,
    required this.controller,
    required this.item,
  });

  @override
  ItemAutoCompleteState createState() => ItemAutoCompleteState();
}

class ItemAutoCompleteState extends State<ItemAutoComplete> {
  List<String> _suggestions = [];
  bool _isLoading = false;

  late Debouncer _debouncer;

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _debouncer = Debouncer(milliseconds: 500);

    // Add a listener to detect when the TextField gains or loses focus
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged(String query) {
    widget.item.value = query;
    if (query.isEmpty) {
      setState(() {
        _suggestions = [];
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });
  }

  late final OutlineInputBorder outlineInputBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: Theme.of(context).colorScheme.outline,
    ),
    borderRadius: BorderRadius.circular(8),
  );

  @override
  Widget build(BuildContext context) {
    late final OutlineInputBorder outlineInputBorder = OutlineInputBorder(
      borderSide: BorderSide(
        color: Theme.of(context).colorScheme.outline,
      ),
      borderRadius: BorderRadius.circular(8),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Column(
          children: [
            Autocomplete<String>(
              optionsBuilder: (TextEditingValue textEditingValue) async {
                if (textEditingValue.text.trim().isEmpty ||
                    textEditingValue.text.length < 3) {
                  return [];
                }

                return await _debouncer.run(
                  () async {
                    final suggestions =
                        await widget.fetchSuggestions(textEditingValue.text);

                    return [
                      ...suggestions,
                      'Save "${textEditingValue.text}"',
                    ];
                  },
                );
              },
              displayStringForOption: (String itemName) => itemName,
              fieldViewBuilder: (context, textEditingController, focusNode,
                  onFieldSubmitted) {
                widget.controller.controller = textEditingController;
                return TextField(
                  decoration: InputDecoration(
                    border: outlineInputBorder,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 15.0, horizontal: 12),
                    enabledBorder: outlineInputBorder,
                    focusedBorder: outlineInputBorder,
                    focusedErrorBorder: outlineInputBorder,
                    hintText: 'Add an item to the invoice',
                    hintStyle: KTextStyle.bodyText2.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                    constraints: BoxConstraints.tight(Size(388, 48)),
                  ),
                  inputFormatters: [validInput()],
                  controller: widget.controller.controller,
                  focusNode: focusNode,
                  style: KTextStyle.bodyText2.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                  onChanged: _onTextChanged,
                );
              },
              optionsViewBuilder: (context, onSelected, options) {
                if (widget.controller.text.isEmpty) {
                  return Offstage();
                }
                return Align(
                  alignment: Alignment.topLeft,
                  child: Material(
                    elevation: 4.0,
                    borderRadius: BorderRadius.circular(6),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                          maxHeight: 250,
                          maxWidth: MediaQuery.of(context).size.width - 30),
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: options.length,
                        itemBuilder: (BuildContext context, int index) {
                          final suggestion = options.elementAt(index);
                          if (index == options.length - 1 &&
                              suggestion.trim().isNotEmpty) {
                            // Add New Customer option
                            return InkWell(
                              onTap: () {
                                final newItem = widget.controller.text;

                                widget.onAddNew(newItem);

                                onSelected(newItem);
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  'Save item: ${widget.controller.text}',
                                  style: TextStyle(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            );
                          }

                          return InkWell(
                            onTap: () {
                              widget.onSelected(suggestion);
                              onSelected(suggestion);
                            },
                            child: Builder(builder: (context) {
                              final bool highlight =
                                  AutocompleteHighlightedOption.of(context) ==
                                      index;
                              if (highlight) {
                                SchedulerBinding.instance
                                    .addPostFrameCallback((Duration timeStamp) {
                                  Scrollable.ensureVisible(context,
                                      alignment: 0.5);
                                });
                              }

                              return Container(
                                color: highlight ? kNeutral100 : null,
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  suggestion,
                                  style: KTextStyle.bodyText2
                                      .copyWith(color: kNeutral600),
                                ),
                              );
                            }),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
            TdTextFieldError(
              padding: EdgeInsets.zero,
              textController: widget.controller,
            ),
          ],
        ),
      ],
    );
  }
}

class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  Future<T> run<T>(Future<T> Function() action) async {
    _timer?.cancel();
    final completer = Completer<T>();
    _timer = Timer(Duration(milliseconds: milliseconds), () async {
      completer.complete(await action());
    });
    return completer.future;
  }
}
