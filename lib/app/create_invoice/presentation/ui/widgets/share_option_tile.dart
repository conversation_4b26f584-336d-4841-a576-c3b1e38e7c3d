import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class ShareOptionTile extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback onTap;

  const ShareOptionTile({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: Color(0xFFE5E7EB),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(17),
            child: Row(
              children: [
                SvgPicture.asset(
                  icon,
                  height: 24,
                ),
                XMargin(12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Color(
                            0xFF1F2937,
                          ),
                        ),
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Color(0xFFE5E7EB),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
