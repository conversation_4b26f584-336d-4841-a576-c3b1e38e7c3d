import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class ContactsPicker {
  static const String _permissionDialogShownKey = 'permissionDialogShown';

  static Future<bool> invoke(BuildContext context) async {
    final sp = await SharedPreferences.getInstance();
    final dialogShown = sp.getBool(_permissionDialogShownKey) ?? false;

    if (!dialogShown) {
      final result = await _showPermissionDialog(context);
      if (!result) return false;
      await sp.setBool(_permissionDialogShownKey, true);
    }

    final status = await Permission.contacts.status;
    return await _handlePermissionStatus(status);
  }

  static Future<bool> _handlePermissionStatus(PermissionStatus status) async {
    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      final requestStatus = await Permission.contacts.request();
      if (requestStatus.isGranted) {
        return true;
      } else if (requestStatus.isPermanentlyDenied) {
        await openAppSettings();
      }
    } else if (status.isPermanentlyDenied) {
      await openAppSettings();
    }
    return false;
  }

  static Future<bool> _showPermissionDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            final theme = Theme.of(context);
            final textTheme = theme.textTheme;
            return AlertDialog(
              insetPadding: EdgeInsets.symmetric(horizontal: 16),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
              ),
              title: Text(
                'Access Contacts',
                textAlign: TextAlign.center,
              ),
              content: Text(
                'Allow this app to access your contacts to easily import customer information',
                textAlign: TextAlign.center,
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFFE5E7EB),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          side: BorderSide(
                            color: const Color(0xFFE5E7EB),
                            // width: 2.0,
                          ),
                          elevation: 0,
                        ),
                        onPressed: () => Navigator.of(context).pop(false),
                        child: Text(
                          'Not Now',
                          style: textTheme.bodyMedium
                              ?.copyWith(color: Color(0xFF374151)),
                        ),
                      ),
                    ),
                    XMargin(8),
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(horizontal: 0),
                          foregroundColor: theme.colorScheme.onPrimary,
                          backgroundColor: theme.colorScheme.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          side: BorderSide.none,
                          elevation: 0,
                        ),
                        onPressed: () => Navigator.of(context).pop(true),
                        child: Text(
                          'Allow Access',
                          style: textTheme.bodyMedium
                              ?.copyWith(color: Colors.white),
                        ),
                      ),
                    )
                  ],
                )
              ],
            );
          },
        ) ??
        false;
  }

  static Future<List<Contact>> loadContacts() async {
    try {
      return await FlutterContacts.getContacts(
          sorted: true, withProperties: true);
    } catch (e) {
      throw Exception('Failed to load contacts: ${e.toString()}');
    }
  }
}
