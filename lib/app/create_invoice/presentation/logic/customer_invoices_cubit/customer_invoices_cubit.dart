import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/use_case/delete_customer_invoice.dart';
import 'package:shop/app/create_invoice/domain/use_case/fetch_customer_invoices.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'customer_invoices_state.dart';

class CustomerInvoicesCubit extends Cubit<CustomerInvoicesState>
    implements OnBeforeLogout {
  final FetchCustomerInvoices _fetchCustomerInvoices;
  final DeleteCustomerInvoice _deleteCustomerInvoice;

  CustomerInvoicesCubit(
    this._fetchCustomerInvoices,
    this._deleteCustomerInvoice,
  ) : super(CustomerInvoiceInitial());

  void fetchInvoices(CustomerInvoicesQuery params) {
    // if (state is CustomerInvoiceLoaded) {
    //   final result = (state as CustomerInvoiceLoaded).result;
    //   if (result.invoices.isNotEmpty) {
    //     return;
    //   }
    // }

    emit(CustomerInvoiceLoading());
    updateState(params);
  }

  void fetchMoreInvoices() {
    if (state is CustomerInvoiceLoaded) {
      final query = (state as CustomerInvoiceLoaded).result.query;

      if (query.page == query.totalPages) {
        return;
      }

      updateState(query.copyWith(page: query.page! + 1));
    }
  }

  Future updateState(CustomerInvoicesQuery params) async {
    final res = await _fetchCustomerInvoices.call(params);

    res.when(
      success: (result) {
        List<CustomerInvoice> invoices = result.invoices;

        if (state is CustomerInvoiceLoaded) {
          final previousInvoices =
              (state as CustomerInvoiceLoaded).result.invoices;
          invoices = [...previousInvoices, ...invoices];
        }

        emit(
          CustomerInvoiceLoaded(result.copyWith(invoices: invoices)),
        );
      },
      apiFailure: (e, _) {
        emit(
          CustomerInvoiceError(
            ApiExceptions.getErrorMessage(e),
          ),
        );
      },
    );
  }

  Future<void> deleteInvoice(String id) async {
    final res = await _deleteCustomerInvoice.call(id);
    res.when(
      success: (_) {},
      apiFailure: (error, _) {},
    );
    // if (state is CustomerInvoiceLoaded) {
    //   final result = (state as CustomerInvoiceLoaded).result;
    //   final invoices = result.invoices;
    //   final index = invoices.indexWhere((element) => element.id == id);

    //   if (index != -1) {
    //     invoices.removeAt(index);
    //     emit(CustomerInvoiceLoaded(result.copyWith(invoices: invoices)));
    //   }
    // }
  }

  @override
  Future<void> onBeforeLogout() async {
    emit(CustomerInvoiceInitial());
  }
}
