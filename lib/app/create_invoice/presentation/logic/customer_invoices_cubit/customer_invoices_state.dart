part of 'customer_invoices_cubit.dart';

sealed class CustomerInvoicesState extends Equatable {
  const CustomerInvoicesState();

  @override
  List<Object> get props => [];
}

class CustomerInvoiceInitial extends CustomerInvoicesState {}

class CustomerInvoiceLoading extends CustomerInvoicesState {}

class CustomerInvoiceLoaded extends CustomerInvoicesState {
  final CustomerInvoicesResult result;

  const CustomerInvoiceLoaded(this.result);

  bool get hasReachedMax => result.query.page == result.query.totalPages;  

  @override
  List<Object> get props => [result];
}

class CustomerInvoiceError extends CustomerInvoicesState {
  final String error;

  const CustomerInvoiceError(this.error);

  @override
  List<Object> get props => [error];
}
