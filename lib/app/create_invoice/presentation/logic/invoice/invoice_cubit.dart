import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_invoice.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'invoice_state.dart';

class InvoiceViewerCubit extends Cubit<InvoiceState> implements OnBeforeLogout {
  final GetInvoice _getInvoice;

  InvoiceViewerCubit(
    this._getInvoice,
  ) : super(InvoiceInitial());

  Future<void> getInvoice(String params) async {
    emit(InvoiceLoading());

    final res = await _getInvoice.call(params);

    res.when(
      success: (invoice) {
        emit(
          InvoiceLoaded(invoice),
        );
      },
      apiFailure: (e, _) {
        emit(
          InvoiceError(
            ApiExceptions.getErrorMessage(e),
          ),
        );
      },
    );
  }

  @override
  Future<void> onBeforeLogout() async {
    emit(InvoiceInitial());
  }
}
