part of 'invoice_cubit.dart';

sealed class InvoiceState extends Equatable {
  const InvoiceState();

  @override
  List<Object> get props => [];
}

class InvoiceInitial extends InvoiceState {}

class InvoiceLoading extends InvoiceState {}

class InvoiceLoaded extends InvoiceState {
  final InvoiceFile invoiceFile;

  const InvoiceLoaded(this.invoiceFile);

  @override
  List<Object> get props => [invoiceFile];
}

class InvoiceError extends InvoiceState {
  final String error;

  const InvoiceError(this.error);

  @override
  List<Object> get props => [error];
}
