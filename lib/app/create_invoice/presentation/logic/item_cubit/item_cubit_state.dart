part of 'item_cubit_cubit.dart';

sealed class ItemState extends Equatable {
  const ItemState();

  @override
  List<Object?> get props => [];
}

final class ItemInitial extends ItemState {}

final class ManageItems extends ItemState {
  final List<InvoiceItem> items;
  final DateTime? updatedAt;

  const ManageItems(this.items, {this.updatedAt});

  @override
  List<Object?> get props => [items, updatedAt];
}

final class SelectedItem extends ItemState {
  final bool isNew;

  const SelectedItem(this.isNew);

  @override
  List<Object> get props => [];
}

final class ItemError extends ItemState {
  final String errorMessage;

  const ItemError(this.errorMessage);

  @override
  List<Object> get props => [];
}
