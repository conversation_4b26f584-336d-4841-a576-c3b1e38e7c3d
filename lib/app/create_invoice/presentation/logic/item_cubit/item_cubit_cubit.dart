import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/create_invoice/data/model/item.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/use_case/add_item.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_items.dart';
import 'package:shop/src/components/src/form/form.dart';

part 'item_cubit_state.dart';

class ItemCubit extends Cubit<ItemState> implements OnBeforeLogout {
  final AddItem _addItem;
  final GetItem _getItem;
  ItemCubit(
    this._addItem,
    this._getItem,
  ) : super(ItemInitial());

  List<InvoiceItem> items = [];

  void addInvoiceItem(Map<String, TdTextController> itemController,
      [int? editingIndex]) {
    final item = InvoiceItem(
      name: itemController['item_name']!.controller!.text.toString().trim(),
      price: num.tryParse(
        itemController['item_price']!.controller!.text.replaceAll(",", ""),
      ) as num,
      quantity:
          num.tryParse(itemController['item_qty']!.controller!.text) as num,
    );

    if (editingIndex != null &&
        editingIndex >= 0 &&
        editingIndex < items.length) {
      items[editingIndex] = item;
    } else {
      items = [...items, item];
    }

    itemController['item_name']!.controller!.clear();
    itemController['item_price']!.controller!.clear();
    itemController['item_qty']!.controller!.clear();

    _saveInvoiceItem(AddItemParams(name: item.name, price: item.price));

    emit(
      ManageItems(items, updatedAt: DateTime.now()),
    );
  }

  void removeInvoiceItem(InvoiceItem item) {
    items = items.where((i) => i != item).toList();
    emit(ManageItems(items));
  }

  Future<List<String>> fetchItems(String query) async {
    final res = await _getItem.call(query);

    return res.when(
      success: (items) {
        return items;
      },
      apiFailure: (e, s) {
        return [];
      },
    );
  }

  Future<void> _saveInvoiceItem(AddItemParams params) async {
    await _addItem.call(params);
    return;
  }

  void selectedExistingItem(String item) {
    emit(const SelectedItem(false));
  }

  void clear() {
    items = [];
    emit(ManageItems(items));
  }

  @override
  Future<void> onBeforeLogout() async {
    clear();
  }
}
