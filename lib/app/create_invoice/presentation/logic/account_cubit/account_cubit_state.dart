part of 'account_cubit_cubit.dart';

sealed class AccountState extends Equatable {
  const AccountState();

  @override
  List<Object> get props => [];
}

final class AccountInitial extends AccountState {}

final class AccountLoading extends AccountState {}

final class ExistingAccount extends AccountState {
  final List<WalletBank> getBankAccounts;

  const ExistingAccount(this.getBankAccounts);

  @override
  List<Object> get props => [getBankAccounts];
}

final class ListBankState extends AccountState {
  final List<SettlementBank> getBanks;

  const ListBankState(this.getBanks);

  @override
  List<Object> get props => [getBanks];
}

final class AccountError extends AccountState {
  final String errorMessage;
  const AccountError(this.errorMessage);

  @override
  List<Object> get props => [errorMessage];
}
