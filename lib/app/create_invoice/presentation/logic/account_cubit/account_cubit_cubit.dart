import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_bank_account.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_banks.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'account_cubit_state.dart';

class AccountCubit extends Cubit<AccountState> implements OnBeforeLogout {
  final GetBankAccounts _getBankAccounts;
  final GetBanks _getBanks;
  AccountCubit(
    this._getBankAccounts,
    this._getBanks,
  ) : super(AccountInitial());

  Future<void> fetchAccounts() async {
    emit(AccountLoading());

    final res = await _getBankAccounts.call(NoParams());

    res.when(
      success: (banks) {
        emit(ExistingAccount(banks));
      },
      apiFailure: (e, s) {
        emit(AccountError(ApiExceptions.getErrorMessage(e)));
      },
    );
  }

  Future<List<SettlementBank>> fetchBanks() async {
    final res = await _getBanks.call(NoParams());
    return res.when(
      success: (banks) {
        emit(ListBankState(banks));
        return banks;
      },
      apiFailure: (e, s) {
        emit(const ListBankState([]));
        return [];
      },
    );
  }

  @override
  Future<void> onBeforeLogout() async {
    emit(AccountInitial());
  }
}
