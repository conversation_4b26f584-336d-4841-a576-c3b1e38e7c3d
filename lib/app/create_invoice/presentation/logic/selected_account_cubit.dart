import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/create_invoice/presentation/ui/screens/create_invoice.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';

import 'customer_cubit/customer_cubit.dart';
import 'item_cubit/item_cubit_cubit.dart';

class SelectedAccountCubit extends Cubit<WalletBank?> {
  SelectedAccountCubit() : super(null);

  /// Updates the selected account.
  void updateAccount(WalletBank? account) {
    emit(account);
  }

  void reset() {
    emit(null);
  }
}

void resetCreateInvoiceFields(BuildContext context) {
  context.read<CustomerCubit>().reset();
  context.read<SelectedAccountCubit>().reset();
  context.read<ItemCubit>().clear();
  shipping.value = null;
}
