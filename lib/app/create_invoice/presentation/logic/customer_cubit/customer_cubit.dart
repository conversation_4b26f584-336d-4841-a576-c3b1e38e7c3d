import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/use_case/add_customer.dart';
import 'package:shop/app/create_invoice/domain/use_case/fetch_my_customers.dart';
import 'package:shop/app/create_invoice/domain/use_case/update_customer.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

part 'customer_state.dart';

class CustomerCubit extends Cubit<CustomerState> implements OnBeforeLogout {
  final AddCustomer addCustomer;
  final UpdateCustomer _updateCustomerUseCase;
  final FetchMyCustomers fetchMyCustomers;

  CustomerCubit(
    this.addCustomer,
    this._updateCustomerUseCase,
    this.fetchMyCustomers,
  ) : super(CustomerInitial());

  Customer? _customer;

  Customer? get selectedCustomer {
    return _customer;
  }

  void newCustomerName(Customer customer) {
    emit(NewCustomerName());
  }

  Future<void> addNewCustomer({
    required Map<String, TdTextController> customerController,
    required CustomerType customerType,
    TdAddress? address,
  }) async {
    if (!customerController.validate()) return;

    final data = customerController.data();

    final newCustomer = Customer(
      id: DateTime.now().toIso8601String(),
      name: data['cus_name'],
      phoneNumber: data['cus_phone'],
      emailAddress: data['cus_email'],
      address: address,
      customerType: customerType,
    );

    emit(AddNewCustomerLoading());
    final res = await addCustomer.call(newCustomer);
    res.when(
      success: (_) {
        // _customer = _;
        emit(AddNewCustomerSuccess(_));
        _updateLocalCustomerListAsync(_.customer);
      },
      apiFailure: (e, s) {
        String errMsg = ApiExceptions.getErrorMessage(e);
        emit(
          AddNewCustomerError(errorMessage: errMsg),
        );
      },
    );
  }

  Future<void> updateCustomer({
    required Map<String, TdTextController> customerController,
    required CustomerType customerType,
    required String customerId,
    TdAddress? address,
  }) async {
    if (!customerController.validate()) return;

    final data = customerController.data();

    final updatedCustomer = Customer(
      id: customerId,
      name: data['cus_name'],
      phoneNumber: data['cus_phone'],
      emailAddress: data['cus_email'],
      address: address,
      customerType: customerType,
      isEditing: true,
    );

    emit(UpdateCustomerLoading());
    final res = await _updateCustomerUseCase.call(updatedCustomer);
    res.when(
      success: (customer) {
        emit(UpdateCustomerSuccess(customer));
        _updateLocalCustomerListAsync(customer);
      },
      apiFailure: (e, s) {
        String errMsg = ApiExceptions.getErrorMessage(e);
        emit(
          UpdateCustomerError(errorMessage: errMsg),
        );
      },
    );
  }

  void selectedExistingCustomer({required Customer customer}) {
    _customer = customer;
    emit(SelectedExistingCustomer());
    _updateLocalCustomerListAsync(customer);
  }

  Future<List<Customer>> getRecentCustomers() async {
    List<Customer> customers = [];
    try {
      final outletId = UserCubit.instance?.currentOutlet?.id;
      if (outletId == null) return customers;
      final sp = await SharedPreferences.getInstance();
      final String? customersString =
          sp.getString(Keys.recentInvoiceCustomers(outletId));
      if (customersString != null && customersString.isNotEmpty) {
        final List<dynamic> jsonList = jsonDecode(customersString);
        customers =
            jsonList.map((e) => Customer.fromMap(jsonDecode(e))).toList();
      }
    } catch (e, _) {}

    return customers;
  }

  Future<void> _updateLocalCustomerListAsync(Customer customer) async {
    try {
      final outletId = UserCubit.instance?.currentOutlet?.id;
      if (outletId == null) return;
      final sp = await SharedPreferences.getInstance();
      final String? customersString =
          sp.getString(Keys.recentInvoiceCustomers(outletId));
      List<Customer> customers = [];

      if (customersString != null && customersString.isNotEmpty) {
        final List<dynamic> customersList = jsonDecode(customersString);
        customers =
            customersList.map((e) => Customer.fromMap(jsonDecode(e))).toList();
        final existingIndex = customers.indexWhere((c) => c.id == customer.id);
        if (existingIndex != -1) {
          customers.removeAt(existingIndex);
        }
        customers.insert(0, customer);
      } else {
        customers = [customer];
      }

      // Ensure the list has no more than 10 items.
      if (customers.length > 10) {
        customers = customers.take(10).toList();
      }

      await _updateLocalCustomerList(customers, sp, outletId);
    } catch (e, _) {
      // Optionally handle errors here.
    }
  }

  Future<void> _updateLocalCustomerList(
      List<Customer> customers, SharedPreferences sp, String outletId) async {
    final String value = jsonEncode(customers.map((e) => e.toJson()).toList());
    await sp.setString(Keys.recentInvoiceCustomers(outletId), value);
  }

  Future<List<Customer>> fetchCustomers(String query) async {
    final res = await fetchMyCustomers.call(query);

    return res.when(
      success: (_) {
        return _;
      },
      apiFailure: (e, s) {
        return [];
      },
    );
  }

  void reset() {
    _customer = null;
    emit(CustomerInitial());
  }

  @override
  Future<void> onBeforeLogout() async {
    reset();
  }
}
