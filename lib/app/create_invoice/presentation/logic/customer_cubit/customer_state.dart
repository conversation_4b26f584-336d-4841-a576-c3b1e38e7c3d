part of 'customer_cubit.dart';

sealed class CustomerState {
  const CustomerState();
}

final class CustomerInitial extends CustomerState {}

final class NewCustomerName extends CustomerState {}

final class SelectedExistingCustomer extends CustomerState {}

final class AddNewCustomerLoading extends CustomerState {}

final class AddNewCustomerSuccess extends CustomerState {
  final CustomerResult customerResult;

  const AddNewCustomerSuccess(this.customerResult);

  List<Object> get props => [customerResult];
}

final class AddNewCustomerError extends CustomerState {
  final String errorMessage;

  const AddNewCustomerError({required this.errorMessage});

  List<Object> get props => [errorMessage];
}

final class UpdateCustomerLoading extends CustomerState {}

final class UpdateCustomerSuccess extends CustomerState {
  final Customer customer;

  const UpdateCustomerSuccess(this.customer);

  List<Object> get props => [customer];
}

final class UpdateCustomerError extends CustomerState {
  final String errorMessage;

  const UpdateCustomerError({required this.errorMessage});

  List<Object> get props => [errorMessage];
}
