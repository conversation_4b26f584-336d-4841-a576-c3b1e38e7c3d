import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class SearchInvoiceItems
    with UseCases<ApiResult<List<InvoiceItem>>, SearchParams> {
  SearchInvoiceItems(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<List<InvoiceItem>>> call(SearchParams params) =>
      _repo!.searchInvoiceItems(params);
}
