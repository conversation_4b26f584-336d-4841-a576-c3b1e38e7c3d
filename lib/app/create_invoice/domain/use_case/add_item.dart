import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class AddItem with UseCases<ApiResult<dynamic>, AddItemParams> {
  AddItem(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<dynamic>> call(AddItemParams params) => _repo!.addItem(params);
}
