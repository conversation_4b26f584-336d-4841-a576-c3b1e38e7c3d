import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetBanks with UseCases<ApiResult<List<SettlementBank>>, NoParams> {
  GetBanks(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<List<SettlementBank>>> call(NoParams params) =>
      _repo!.getBanks();
}
