import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class FetchCustomerInvoices
    with UseCases<ApiResult<CustomerInvoicesResult>, CustomerInvoicesQuery> {
  final CreateInvoiceRepo? _repo;
  FetchCustomerInvoices(this._repo);

  @override
  Future<ApiResult<CustomerInvoicesResult>> call(
          CustomerInvoicesQuery params) =>
      _repo!.fetchCustomerInvoices(params);
}
