import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

///Get Downloaded PDF
class GetInvoice with UseCases<ApiResult<InvoiceFile>, String> {
  final CreateInvoiceRepo? _repo;
  GetInvoice(this._repo);

  @override
  Future<ApiResult<InvoiceFile>> call(String params) =>
      _repo!.getInvoice(params);
}
