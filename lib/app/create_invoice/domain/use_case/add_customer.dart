import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class AddCustomer with UseCases<ApiResult<CustomerResult>, Customer> {
  AddCustomer(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<CustomerResult>> call(Customer params) =>
      _repo!.addCustomer(params);
}
