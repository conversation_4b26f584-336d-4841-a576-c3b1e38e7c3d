import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class UpdateCustomer with UseCases<ApiResult<Customer>, Customer> {
  UpdateCustomer(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<Customer>> call(Customer params) =>
      _repo!.updateCustomer(params);
}