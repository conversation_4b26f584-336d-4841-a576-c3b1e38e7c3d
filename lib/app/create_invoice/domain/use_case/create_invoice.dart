import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CreateInvoiceUseCase
    with UseCases<ApiResult<CustomerInvoice>, CreateInvoiceParams> {
  CreateInvoiceUseCase(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<CustomerInvoice>> call(CreateInvoiceParams params) =>
      _repo!.createInvoice(params);
}
