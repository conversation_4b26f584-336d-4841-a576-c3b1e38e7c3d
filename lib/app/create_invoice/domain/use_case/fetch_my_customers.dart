import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchMyCustomers with UseCases<ApiResult<List<Customer>>, String> {
  FetchMyCustomers(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<List<Customer>>> call(String params) =>
      _repo!.fetchMyCustomer(params);
}
