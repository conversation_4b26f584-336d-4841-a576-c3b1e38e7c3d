import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class LinkAccount with UseCases<ApiResult<bool>, LinkBankParams> {
  LinkAccount(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<bool>> call(LinkBankParams params) =>
      _repo!.linkBank(params);
}
