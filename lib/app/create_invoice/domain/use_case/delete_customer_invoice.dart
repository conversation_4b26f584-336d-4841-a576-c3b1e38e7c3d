import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class DeleteCustomerInvoice with UseCases<ApiResult<dynamic>, String> {
  final CreateInvoiceRepo? _repo;
  DeleteCustomerInvoice(this._repo);

  @override
  Future<ApiResult<dynamic>> call(String id) =>
      _repo!.deleteCustomerInvoice(id);
}
