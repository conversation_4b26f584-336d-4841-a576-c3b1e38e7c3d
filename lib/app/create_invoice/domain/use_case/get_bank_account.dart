import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetBankAccounts with UseCases<ApiResult<List<WalletBank>>, NoParams> {
  GetBankAccounts(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<List<WalletBank>>> call(NoParams params) =>
      _repo!.getBankAccounts();
}
