import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ValidateAccount
    with
        UseCases<ApiResult<(String name, String? bvn)>, ValidateAccountParams> {
  ValidateAccount(this._repo);

  final CreateInvoiceRepo? _repo;

  @override
  Future<ApiResult<(String name, String? bvn)>> call(
          ValidateAccountParams params) =>
      _repo!.validateAccount(params);
}
