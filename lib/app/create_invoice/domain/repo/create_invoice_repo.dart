import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';

abstract class CreateInvoiceRepo {
  Future<ApiResult<List<Customer>>> fetchMyCustomer(String params);
  Future<ApiResult<CustomerResult>> addCustomer(Customer customer);
  Future<ApiResult<Customer>> updateCustomer(Customer customer);
  Future<ApiResult<dynamic>> addItem(AddItemParams params);
  Future<ApiResult<List<SettlementBank>>> getBanks();
  Future<ApiResult<bool>> linkBank(LinkBankParams params);
  Future<ApiResult<(String name, String? bvn)>> validateAccount(
      ValidateAccountParams params);
  Future<ApiResult<List<String>>> getItems(String term);
  Future<ApiResult<CustomerInvoice>> createInvoice(CreateInvoiceParams params);
  Future<ApiResult<List<WalletBank>>> getBankAccounts();
  Future<ApiResult<InvoiceFile>> getInvoice(String params);
  Future<ApiResult<CustomerInvoicesResult>> fetchCustomerInvoices(
      CustomerInvoicesQuery params);
  Future<ApiResult<dynamic>> deleteCustomerInvoice(String id);
  Future<ApiResult<List<InvoiceItem>>> searchInvoiceItems(SearchParams params);
}
