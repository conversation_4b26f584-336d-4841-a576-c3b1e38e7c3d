import 'dart:convert';

import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/data/model/item.dart';
import 'package:shop/app/create_invoice/data/model/shipping_cost.dart';

class Address {
  String address;
  String state;
  String country;

  Address({
    required this.address,
    required this.state,
    required this.country,
  });

  // Convert Address to Map
  Map<String, dynamic> toMap() {
    return {
      'address': address,
      'state': state,
      'country': country,
    };
  }
}

class CreateInvoiceParams {
  List<InvoiceItem> items;
  String bankAccountId;
  String customerId;
  String? status;
  Shipping? shipping;

  CreateInvoiceParams({
    required this.items,
    required this.bankAccountId,
    required this.customerId,
    this.status,
    this.shipping,
  });

  CreateInvoiceParams copyWith({
    String? status,
  }) {
    return CreateInvoiceParams(
      items: items,
      bankAccountId: bankAccountId,
      customerId: customerId,
      status: status ?? this.status,
      shipping: shipping,
    );
  }

  // Convert Data to Map
  Map<String, dynamic> toMap() {
    return {
      'items': items.map((item) => item.toMap()).toList(),
      'bankAccountId': bankAccountId,
      'customerId': customerId,
      'status': status,
      if (shipping?.amount != null && shipping!.amount! > 0)
        'shippingCost': shipping!.amount,
      if (shipping?.note != null && shipping!.note!.isNotEmpty)
        'note': shipping!.note,
    };
  }

  @override
  String toString() => '${toMap()}';
}

class ValidateAccountParams {
  String code;
  String number;
  ValidateAccountParams({
    required this.code,
    required this.number,
  });
}

class CustomerInvoicesResult {
  final List<CustomerInvoice> invoices;
  final CustomerInvoicesQuery query;
  CustomerInvoicesResult({
    required this.invoices,
    required this.query,
  });

  CustomerInvoicesResult copyWith({
    List<CustomerInvoice>? invoices,
  }) {
    return CustomerInvoicesResult(
      invoices: invoices ?? this.invoices,
      query: query,
    );
  }
}

enum FetchType { all, search }

class CustomerInvoicesQuery {
  final String? searchText;
  final String? status;
  final String? type;
  final num? page;
  final num? perPage;
  final num? totalPages;
  final FetchType fetchType;

  CustomerInvoicesQuery({
    this.searchText,
    this.status, // active or draft
    this.type = 'customer',
    this.page = 1,
    this.perPage = 10,
    this.totalPages,
    this.fetchType = FetchType.all,
  });

  CustomerInvoicesQuery copyWith({
    String? searchText,
    String? status,
    String? type,
    num? page,
    num? perPage,
    num? totalPages,
    FetchType? fetchType,
  }) {
    return CustomerInvoicesQuery(
      searchText: searchText ?? this.searchText,
      status: status ?? this.status,
      type: type ?? this.type,
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
      fetchType: fetchType ?? this.fetchType,
    );
  }

  Map<String, dynamic>? toMap() {
    return {
      if (searchText != null) 'searchText': searchText,
      if (status != null) 'status': status,
      'type': type,
      'page': page,
      'perPage': perPage,
    };
  }

  factory CustomerInvoicesQuery.fromMap(Map<String, dynamic> map) {
    return CustomerInvoicesQuery(
      searchText: map['searchText'],
      status: map['status'],
      type: map['type'],
      page: map['page']?.toInt(),
      perPage: map['perPage']?.toInt(),
    );
  }

  String toJson() => json.encode(toMap());

  factory CustomerInvoicesQuery.fromJson(String source) =>
      CustomerInvoicesQuery.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

enum ResultType { success, apiFailure }

class CustomerResult {
  final Customer customer;
  final ResultType type;
  CustomerResult({
    required this.customer,
    required this.type,
  });
}


class SearchParams {
  final String type;
  final String term;
  final String retailOutletId;

  const SearchParams({
    this.type = 'retailproducts',
    required this.term,
    required this.retailOutletId,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'term': term,
      'retailOutletId': retailOutletId,
    };
  }

  SearchParams copyWith({
    String? type,
    String? term,
    String? retailOutletId,
  }) {
    return SearchParams(
      type: type ?? this.type,
      term: term ?? this.term,
      retailOutletId: retailOutletId ?? this.retailOutletId,
    );
  }
}

class AddItemParams {
  final String name;
  final num price;

  const AddItemParams({
    required this.name,
    required this.price,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'price': price,
    };
  }
}
