import 'package:shop/app/create_invoice/data/data_source/create_invoice_data_source.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CreateInvoiceRepoImpl implements CreateInvoiceRepo {
  final CreateInvoiceDataSource _dataSource;

  CreateInvoiceRepoImpl(this._dataSource);

  @override
  Future<ApiResult<List<Customer>>> fetchMyCustomer(String term) async {
    return dioInterceptor(
      () => _dataSource.fetchMyCustomer(term),
    );
  }

  @override
  Future<ApiResult<CustomerResult>> addCustomer(Customer customer) async {
    return await dioInterceptor(
      () => _dataSource.addCustomer(customer),
      doNotReport: [400],
      dioErrorFunc: (DioError error) {
        if (error.response?.statusCode != null &&
            error.response!.statusCode == 400) {
          final data = error.response?.data?['data']?['customer'];
          return ApiResult.success(
              data: CustomerResult(
                  customer: Customer.fromMap(data),
                  type: ResultType.apiFailure));
        }

        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(error));
      },
    );
  }

  @override
  Future<ApiResult<dynamic>> addItem(AddItemParams params) async {
    return await dioInterceptor(() => _dataSource.addItem(params),
        doNotReport: [400]); //400: Product Exists
  }

  @override
  Future<ApiResult<List<SettlementBank>>> getBanks() async {
    return dioInterceptor(() => _dataSource.getBanks());
  }

  @override
  Future<ApiResult<bool>> linkBank(LinkBankParams params) async {
    return dioInterceptor(() => _dataSource.linkBank(params));
  }

  @override
  Future<ApiResult<(String name, String? bvn)>> validateAccount(
      ValidateAccountParams params) async {
    return dioInterceptor(() => _dataSource.validateAccount(params));
  }

  @override
  Future<ApiResult<List<String>>> getItems(String term) async {
    return dioInterceptor(
      () => _dataSource.getItems(term),
    );
  }

  @override
  Future<ApiResult<CustomerInvoice>> createInvoice(
      CreateInvoiceParams params) async {
    return dioInterceptor(
      () => _dataSource.createInvoice(params),
    );
  }

  @override
  Future<ApiResult<List<WalletBank>>> getBankAccounts() {
    return dioInterceptor(() => _dataSource.getBankAccounts());
  }

  @override
  Future<ApiResult<InvoiceFile>> getInvoice(String params) {
    return dioInterceptor(() => _dataSource.getInvoice(params));
  }

  @override
  Future<ApiResult<CustomerInvoicesResult>> fetchCustomerInvoices(
      CustomerInvoicesQuery params) async {
    return dioInterceptor(() => _dataSource.fetchCustomerInvoices(params));
  }

  @override
  Future<ApiResult> deleteCustomerInvoice(String id) async {
    return dioInterceptor(() => _dataSource.deleteCustomerInvoice(id));
  }

  @override
  Future<ApiResult<List<InvoiceItem>>> searchInvoiceItems(
      SearchParams params) async {
    return dioInterceptor(() => _dataSource.searchInvoiceItems(params));
  }

  @override
  Future<ApiResult<Customer>> updateCustomer(Customer customer) async {
    return dioInterceptor(
      () => _dataSource.updateCustomer(customer),
    );
  }
}
