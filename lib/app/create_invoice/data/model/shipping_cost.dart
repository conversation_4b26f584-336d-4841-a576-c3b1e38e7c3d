import 'dart:convert';

import 'package:equatable/equatable.dart';

class Shipping extends Equatable {
  final num? amount;
  final String? note;

  const Shipping({
    this.amount,
    this.note,
  });

  Shipping copyWith({
    num? amount,
    String? note,
  }) {
    return Shipping(
      amount: amount ?? this.amount,
      note: note ?? this.note,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'note': note,
    };
  }

  factory Shipping.fromMap(Map<String, dynamic> map) {
    return Shipping(
      amount: map['amount'],
      note: map['note'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Shipping.fromJson(String source) =>
      Shipping.fromMap(json.decode(source));

  bool get isCleared =>
      (amount == null || amount! <= 0) && (note == null || note!.isEmpty);

  @override
  String toString() => 'ShippingCost(amount: $amount, note: $note)';

  @override
  List<Object?> get props => [amount, note];
}
