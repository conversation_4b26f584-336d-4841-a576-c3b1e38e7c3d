import 'dart:convert';

import 'package:equatable/equatable.dart';
// import 'package:shop/app/order/data/models/order_data.dart';
import 'package:td_commons_flutter/utils/methods.dart';

import 'invoice.dart';

class CustomerInvoice extends Equatable {
  final String id;
  final List<InvoiceItem>? items;
  final String? retailOutletId;
  final String? outletBusinessName;
  final String? invoiceType;
  final num? shippingCosts;
  final num? processingCost;
  final String? status;
  final Currency? currency;
  final BankAccount? bankAccount;
  final String? customerId;
  final int? invoiceNumber;
  final DateTime? issuedAt;
  final DateTime? createdAt;
  final String? userId;
  final num? exchangeRate;
  final num? shippingDiscount;
  final num? subTotal;
  final num? taxes;
  final num? total;
  final num? amount;
  final String? createdBy;
  final ShippingAddress? shippingAddress;
  final List<History>? history;
  final String? customerName;
  const CustomerInvoice({
    required this.id,
    this.items = const [],
    this.retailOutletId,
    this.outletBusinessName,
    this.invoiceType,
    this.shippingCosts,
    this.processingCost,
    this.status,
    this.currency,
    this.bankAccount,
    this.customerId,
    this.invoiceNumber,
    this.issuedAt,
    this.createdAt,
    this.userId,
    this.exchangeRate,
    this.shippingDiscount,
    this.subTotal,
    this.taxes,
    this.total,
    this.amount,
    this.createdBy,
    this.shippingAddress,
    this.history,
    this.customerName,
  });

  CustomerInvoice copyWith({
    String? id,
    List<InvoiceItem>? items,
    String? retailOutletId,
    String? outletBusinessName,
    String? invoiceType,
    num? shippingCosts,
    num? processingCost,
    String? status,
    Currency? currency,
    BankAccount? bankAccount,
    String? customerId,
    int? invoiceNumber,
    DateTime? issuedAt,
    DateTime? createdAt,
    String? userId,
    num? exchangeRate,
    num? shippingDiscount,
    num? subTotal,
    num? taxes,
    num? total,
    num? amount,
    String? createdBy,
    ShippingAddress? shippingAddress,
    List<History>? history,
    String? customerName,
  }) {
    return CustomerInvoice(
      id: id ?? this.id,
      items: items ?? this.items,
      retailOutletId: retailOutletId ?? this.retailOutletId,
      outletBusinessName: outletBusinessName ?? this.outletBusinessName,
      invoiceType: invoiceType ?? this.invoiceType,
      shippingCosts: shippingCosts ?? this.shippingCosts,
      processingCost: processingCost ?? this.processingCost,
      status: status ?? this.status,
      currency: currency ?? this.currency,
      bankAccount: bankAccount ?? this.bankAccount,
      customerId: customerId ?? this.customerId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      issuedAt: issuedAt ?? this.issuedAt,
      createdAt: createdAt ?? this.createdAt,
      userId: userId ?? this.userId,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      shippingDiscount: shippingDiscount ?? this.shippingDiscount,
      subTotal: subTotal ?? this.subTotal,
      taxes: taxes ?? this.taxes,
      total: total ?? this.total,
      amount: amount ?? this.amount,
      createdBy: createdBy ?? this.createdBy,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      history: history ?? this.history,
      customerName: customerName ?? this.customerName,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'items': items?.map((x) => x.toMap()).toList(),
      'retailOutletId': retailOutletId,
      'outletBusinessName': outletBusinessName,
      'invoiceType': invoiceType,
      'shippingCosts': shippingCosts,
      'processingCost': processingCost,
      'status': status,
      'currency': currency?.toMap(),
      'bankAccount': bankAccount?.toMap(),
      'customerId': customerId,
      'invoiceNumber': invoiceNumber,
      'issuedAt': issuedAt?.millisecondsSinceEpoch,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'userId': userId,
      'exchangeRate': exchangeRate,
      'shippingDiscount': shippingDiscount,
      'subTotal': subTotal,
      'taxes': taxes,
      'total': total,
      'amount': amount,
      'createdBy': createdBy,
      'shippingAddress': shippingAddress?.toMap(),
      'history': history?.map((x) => x.toMap()).toList(),
      'customerName': customerName,
    };
  }

  factory CustomerInvoice.fromMap(Map<String, dynamic> map) {
    return CustomerInvoice(
      id: map['_id'] ?? '',
      items: map['items'] != null
          ? List<InvoiceItem>.from(
              map['items']?.map((x) => InvoiceItem.fromMap(x)))
          : [],
      retailOutletId: map['retailOutletId'],
      outletBusinessName: map['outletBusinessName'],
      invoiceType: map['invoiceType'],
      shippingCosts: map['shippingCosts'],
      processingCost: map['processingCost'],
      status: map['status'],
      currency:
          map['currency'] != null ? Currency.fromMap(map['currency']) : null,
      bankAccount: map['bankAccount'] != null
          ? BankAccount.fromMap(map['bankAccount'])
          : null,
      customerId: map['customerId'],
      invoiceNumber: map['invoiceNumber']?.toInt(),
      issuedAt: parseDate(map['issuedAt']),
      createdAt: parseDate(map['createdAt']),
      userId: map['userId'],
      exchangeRate: map['exchangeRate'],
      shippingDiscount: map['shippingDiscount'],
      subTotal: map['subTotal'],
      taxes: map['taxes'],
      total: map['total'],
      amount: map['amount'],
      createdBy: map['createdBy'],
      shippingAddress: map['shippingAddress'] != null
          ? ShippingAddress.fromMap(map['shippingAddress'])
          : null,
      history: map['history'] != null
          ? List<History>.from(map['history']?.map((x) => History.fromMap(x)))
          : null,
      customerName: map['customerName'],
    );
  }

  String toJson() => json.encode(toMap());

  bool get isDraft => status?.toLowerCase() == 'draft';

  @override
  String toString() => '${toMap()}';

  factory CustomerInvoice.fromJson(String source) =>
      CustomerInvoice.fromMap(json.decode(source));

  @override
  List<Object?> get props {
    return [
      id,
      retailOutletId,
      outletBusinessName,
      invoiceType,
      shippingCosts,
      processingCost,
      status,
      currency,
      bankAccount,
      customerId,
      invoiceNumber,
      issuedAt,
      createdAt,
      userId,
      exchangeRate,
      shippingDiscount,
      subTotal,
      taxes,
      total,
      amount,
      createdBy,
      shippingAddress,
      history,
      customerName,
    ];
  }
}

class BankAccount {
  final String? bankName;
  final String? accountNumber;
  final String? accountName;
  BankAccount({
    this.bankName,
    this.accountNumber,
    this.accountName,
  });

  BankAccount copyWith({
    String? bankName,
    String? accountNumber,
    String? accountName,
  }) {
    return BankAccount(
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'bankName': bankName,
      'accountNumber': accountNumber,
      'accountName': accountName,
    };
  }

  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      bankName: map['bankName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      accountName: map['accountName'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory BankAccount.fromJson(String source) =>
      BankAccount.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}
