import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';

enum CustomerType {
  individual('Individual'),
  business('Business');

  final String name;
  const CustomerType(this.name);

  static CustomerType? fromString(String? value) {
    for (final type in CustomerType.values) {
      if (type.name.toLowerCase() == value?.toLowerCase()) {
        return type;
      }
    }
    return null;
  }
}

class Customer extends Equatable {
  final String? id;
  final String name;
  final String phoneNumber;
  final String? emailAddress;
  final TdAddress? address;
  final CustomerType? customerType;
  final bool isEditing;

  const Customer({
    this.id,
    required this.name,
    required this.phoneNumber,
    this.emailAddress,
    this.address,
    this.customerType,
    this.isEditing = false,
  });

  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      if (isEditing) '_id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      if (emailAddress != null) 'email': emailAddress,
      if (address != null) 'address': address?.toData(),
      if (customerType != null) 'customerType': customerType?.name.toLowerCase()
    };
  }

  Map<String, dynamic> toLocalMap() {
    return {
      '_id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': emailAddress,
      'address': address?.toMap(),
      'customerType': customerType?.name,
    };
  }

  String toJson() => json.encode(toLocalMap());

  // Create from Map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['_id'],
      name: map['name'],
      phoneNumber: map['phoneNumber'],
      emailAddress: map['email'],
      address:
          map['address'] != null ? TdAddress.fromMap(map['address']) : null,
      customerType: CustomerType.fromString(map['customerType']),
    );
  }

  @override
  List<Object?> get props => [name, phoneNumber, emailAddress];
}
