import 'package:equatable/equatable.dart';

class InvoiceItem extends Equatable {
  final String name;
  final num price;
  final num quantity;
  final int? index;

  const InvoiceItem({
    required this.name,
    required this.price,
    required this.quantity,
    this.index,
  });

  InvoiceItem copyWith({
    String? name,
    num? price,
    num? quantity,
    int? index,
  }) {
    return InvoiceItem(
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      index: index ?? this.index,
    );
  }

  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'price': price,
      'quantity': quantity,
    };
  }

  // Create from Map
  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      name: map['name'] as String,
      price: map['price'] as num,
      quantity: map['quantity'] as num,
    );
  }

  @override
  List<Object?> get props => [
        name,
        price,
        quantity,
        index,
      ];
}
