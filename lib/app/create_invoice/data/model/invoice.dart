import 'package:td_commons_flutter/utils/methods.dart';

class Invoice {
  final String id;
  final String retailOutletId;

  Invoice({
    required this.id,
    required this.retailOutletId,
  });

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['_id'],
      retailOutletId: map['retailOutletId'],
    );
  }
}

class BankAccount {
  final String? bankName;
  final String accountNumber;
  final String accountName;

  BankAccount({
    this.bankName,
    required this.accountNumber,
    required this.accountName,
  });

  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      bankName: map['bankName'],
      accountNumber: map['accountNumber'],
      accountName: map['accountName'],
    );
  }
}

class Currency {
  final String iso;
  final String symbol;

  Currency({
    required this.iso,
    required this.symbol,
  });

  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      iso: map['iso'],
      symbol: map['symbol'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'iso': iso,
      'symbol': symbol,
    };
  }

  @override
  String toString() => 'Currency(iso: $iso, symbol: $symbol)';
}

class History {
  final String? event;
  final String? userId;
  final String? newValue;
  final DateTime? createdAt;

  History({
    this.event,
    this.userId,
    this.newValue,
    this.createdAt,
  });

  factory History.fromMap(Map<String, dynamic> map) {
    return History(
      event: map['event'],
      userId: map['userId'],
      newValue: map['newValue'],
      createdAt: parseDate(map['createdAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'event': event,
      'userId': userId,
      'newValue': newValue,
      'createdAt': createdAt,
    };
  }

  @override
  String toString() =>
      'History(event: $event, userId: $userId, newValue: $newValue, createdAt: $createdAt)';
}

class InvoiceItem {
  final String? name;
  final num? price;
  final num? quantity;
  final String? id;
  final bool? isPromo;

  InvoiceItem({
    this.name,
    this.price,
    this.quantity,
    this.id,
    this.isPromo,
  });

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      name: map['name'],
      price: map['price'],
      quantity: map['quantity'],
      id: map['_id'],
      isPromo: map['isPromo'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'price': price,
      'quantity': quantity,
      'id': id,
      'isPromo': isPromo,
    };
  }

  Map<String, dynamic> toData() {
    return {
      'name': name,
      'price': price,
    };
  }

  @override
  String toString() {
    return 'InvoiceItem(name: $name, price: $price, quantity: $quantity, id: $id, isPromo: $isPromo)';
  }
}

class ShippingAddress {
  final String fullName;
  final String address1;
  final String state;
  final String country;
  final String id;

  ShippingAddress({
    required this.fullName,
    required this.address1,
    required this.state,
    required this.country,
    required this.id,
  });

  factory ShippingAddress.fromMap(Map<String, dynamic> map) {
    return ShippingAddress(
      fullName: map['fullName'],
      address1: map['address1'],
      state: map['state'],
      country: map['country'],
      id: map['_id'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fullName': fullName,
      'address1': address1,
      'state': state,
      'country': country,
      'id': id,
    };
  }

  @override
  String toString() {
    return 'ShippingAddress(fullName: $fullName, address1: $address1, state: $state, country: $country, id: $id)';
  }
}
