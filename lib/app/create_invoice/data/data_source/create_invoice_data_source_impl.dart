import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:td_flutter_core/services/api/td_api.dart';

import 'create_invoice_data_source.dart';

/// Implements [DealsDataSource] abstract class.
///
/// Makes network call.
class CreateInvoiceDataSourceImpl implements CreateInvoiceDataSource {
  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request.
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  CreateInvoiceDataSourceImpl(this._apiClient, this._firebaseServiceUrl);

  final String customerPath = '';
  final String _type = "serviceUrl";
  final String _monoInstitution = "https://api.withmono.com/v1/institutions";

  @override
  Future<CustomerResult> addCustomer(Customer customer) async {
    const path = 'v3/invoice/create-customer';
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": path,
        "method": "post",
        "data": customer.toMap(),
      },
    );

    final result = res.data['data'];
    return CustomerResult(
        customer: Customer.fromMap(result), type: ResultType.success);
  }

  @override
  Future<dynamic> addItem(AddItemParams params) async {
    const path = 'v3/invoice/create-product';
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": path,
        "method": "post",
        "data": params.toMap(),
      },
    );

    final result = res.data['data'];
    return result;
  }

  @override
  Future<(String name, String? bvn)> validateAccount(
      ValidateAccountParams params) async {
    final response =
        await _apiClient.post('$_firebaseServiceUrl/shop/v3/proxy', data: {
      'method': 'GET',
      "path": "get-account-name",
      "type": _type,
      "params": {"accountNumber": params.number, "bankCode": params.code}
    });
    final data = (response.data["data"] as Map);
    return (data["accountName"] as String, data["accountBVN"] as String?);
  }

  @override
  Future<List<SettlementBank>> getBanks() async {
    final response =
        await _apiClient.post('$_firebaseServiceUrl/shop/v3/proxy', data: {
      'method': 'GET',
      "path": "list-banks",
      "type": _type,
    });
    return (response.data["data"] as List)
        .map((e) => SettlementBank.fromMap(e))
        .toList();
  }

  @override
  Future<bool> linkBank(LinkBankParams params) async {
    await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'POST',
        "path": "link-bank-account",
        "type": _type,
        "data": params.toMap()
      },
    );
    return true;
  }

  @override
  Future<List<String>> getItems(String term) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        "path": "search-invoices",
        "type": "catalogueUrl",
        "data": {
          "term": term,
          "type": "retailproducts",
          "retailOutletId": UserCubit.instance?.currentOutlet?.id,
          "page": 1,
          "perPage": 30
        },
      },
    );

    final List result = res.data['body'];
    final items = result.map((e) => e['name'] as String).toList();
    return items;
  }

  @override
  Future<List<Customer>> fetchMyCustomer(String term) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        "path": "search-invoices",
        "type": "catalogueUrl",
        "data": {
          "term": term,
          "type": "invoicecustomers",
          "retailOutletId": UserCubit.instance?.currentOutlet?.id,
          "page": 1,
          "perPage": 30
        },
      },
    );
    final List myCustomers = res.data['body'];
    final result = myCustomers.map((cus) => Customer.fromMap(cus)).toList();
    return result;
  }

  @override
  Future<CustomerInvoice> createInvoice(CreateInvoiceParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "v3/invoice/create-invoice",
        "method": "post",
        "data": params.toMap(),
      },
    );

    final result = res.data['data']['invoice'];
    final invoice = CustomerInvoice.fromMap(result);
    return invoice;
  }

  @override
  Future<InvoiceFile> getInvoice(String invoiceId) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "v3/customer-invoice/$invoiceId",
        "method": "get",
      },
    );

    final String invoiceUrl = res.data['data']['invoiceUrl'];
    final fileUrlPath = Uri.parse(invoiceUrl);

    final getFile = await http.get(fileUrlPath);

    if (getFile.statusCode != 200) {
      throw Exception(
          'Unable to download pdf file, statusCode: ${getFile.statusCode}, Body: ${getFile.body}, invoiceUrl: $invoiceUrl');
    }

    var bytes = getFile.bodyBytes;
    var tempDir = await getTemporaryDirectory();
    File file = File('${tempDir.path}/invoice_$invoiceId.pdf');
    await file.writeAsBytes(bytes, flush: true);

    return InvoiceFile(
        file: file, urlPath: fileUrlPath.toString(), invoiceUrl: invoiceUrl);
  }

  Future<List<({String? bankName, String? icon})>> getMonoIcons() async {
    try {
      final result = await _apiClient.get(_monoInstitution);
      return (result.data as List<dynamic>)
          .map(
            (e) => (bankName: e["name"] as String?, icon: e["icon"] as String?),
          )
          .toList();
    } catch (_) {
      return [];
    }
  }

  @override
  Future<List<WalletBank>> getBankAccounts() async {
    const monoListAccountPath = "list-accounts";
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {"path": monoListAccountPath, "method": "GET", "type": _type},
    );
    final icons = await getMonoIcons();
    return (response.data["data"] as List<dynamic>)
        .map(
          (e) => (WalletBank.fromMap(e,
              icon: icons
                  .firstWhere(
                    (element) => element.bankName == e["bankName"],
                    orElse: () => (bankName: null, icon: null),
                  )
                  .icon)),
        )
        .toList();
  }

  @override
  Future<CustomerInvoicesResult> fetchCustomerInvoices(
      CustomerInvoicesQuery params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "v3/sales/invoices",
        "method": "get",
        "params": params.toMap(),
      },
    );

    if (res.data == null) {
      throw Exception("API response is null.");
    }

    final dynamic data = res.data is List ? res.data : res.data?['data'];

    // Extract invoices safely
    final List<dynamic> invoiceList = (data is List)
        ? data.cast<dynamic>()
        : (data?['invoices'] as List?) ?? [];

    final List<CustomerInvoice> invoices = invoiceList
        .map((e) => CustomerInvoice.fromMap(e as Map<String, dynamic>))
        .toList();

    // Extract pagination safely
    final pagination = (data is List || data?['pagination'] == null)
        ? Pagination()
        : Pagination.fromMap(data['pagination'] as Map<String, dynamic>);

    return CustomerInvoicesResult(
      invoices: invoices,
      query: params.copyWith(
        page: pagination.page ?? 1,
        totalPages: pagination.totalPages,
        perPage: pagination.perPage,
      ),
    );
  }

  @override
  Future deleteCustomerInvoice(String id) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "v3/invoice/delete-invoice/$id",
        "method": "post",
      },
    );
    return res.data;
  }

  @override
  Future<List<InvoiceItem>> searchInvoiceItems(SearchParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'POST',
        "path": "search-invoices",
        "type": 'catalogueUrl',
        "data": params.toMap(),
      },
    );

    final List results = res.data['body'];
    return results.map((x) => InvoiceItem.fromMap(x)).toList();
  }

  @override
  Future<Customer> updateCustomer(Customer customer) async {
    final path = 'v3/invoice/update-customer';
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": path,
        "method": "put",
        "data": customer.toMap(),
      },
    );

    final result = res.data['data'];
    return Customer.fromMap(result);
  }
}
