import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/data/model/invoice.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/credit/data/models/settlement_bank.dart';
import 'package:shop/app/credit/domain/params/link_bank_params.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';

/// Base class for [DealsDataSourceImplementation].
abstract class CreateInvoiceDataSource {
  Future<CustomerResult> addCustomer(Customer customer);
  Future<Customer> updateCustomer(Customer customer);
  Future<dynamic> addItem(AddItemParams params);
  Future<(String name, String? bvn)> validateAccount(
      ValidateAccountParams params);
  Future<bool> linkBank(LinkBankParams params);
  Future<List<SettlementBank>> getBanks();
  Future<List<String>> getItems(String term);
  Future<List<Customer>> fetchMyCustomer(String term);
  Future<CustomerInvoice> createInvoice(CreateInvoiceParams params);
  Future<List<WalletBank>> getBankAccounts();
  Future<InvoiceFile> getInvoice(String invoiceId);
  Future<CustomerInvoicesResult> fetchCustomerInvoices(
      CustomerInvoicesQuery params);
  Future<dynamic> deleteCustomerInvoice(String id);
  Future<List<InvoiceItem>> searchInvoiceItems(SearchParams params);
}
