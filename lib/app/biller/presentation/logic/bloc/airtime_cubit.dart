import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/biller/data/exceptions/bill_unvailable_exception.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/use_cases/get_airtime_bills.dart';
import 'package:shop/app/biller/domain/use_cases/get_category_bills.dart';
import 'package:shop/app/biller/domain/use_cases/get_payment_items.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'airtime_state.dart';

class AirtimeCubit extends Cubit<AirtimeState> {
  AirtimeCubit() : super(AirtimeInitial());

  static String _airtimeId = '';

  Future<void> loadAirtime(Environment env) async {
    if (state is AirtimeLoaded) return;

    emit(AirtimeLoading());

    final res =
        await locator.get<GetAirtimeBills>().call(await _getAirtimeId(env));

    res.when(
      success: (data) {
        emit(AirtimeLoaded(data.billers, data.minAmount));
      },
      apiFailure: (e, _) {
        emit(AirtimeError(ApiExceptions.getErrorMessage(e)));
      },
    );
  }

  Future<List<PaymentItem>?> loadItems(
    Biller bill,
    BuildContext context, [
    bool indicate = true,
  ]) async {
    final res = await locator.get<GetPaymentItems>().call(bill.billerId);

    return res.when(
      success: (data) {
        return data;
      },
      apiFailure: (e, _) {
        final message = ApiExceptions.getErrorMessage(e);
        if (message == '10001') {
          throw BillUnavailableException();
        }

        if (indicate) {
          Toast.error(
            InterSwitchErrorService.it.extract(
              context,
              message,
              biller: true,
            ),
            context,
          );
        }
        return null;
      },
    );
  }

  Future<String> _getAirtimeId(Environment env) async {
    int defaultVal = env == Environment.dev ? 4 : 3;
    if (_airtimeId.isNotEmpty) return _airtimeId;

    final res = await locator.get<GetCategoryBills>().call(null);
    return res.when(
      success: (data) {
        try {
          _airtimeId = data.firstWhere((element) => element.isAirtime).id;
          return _airtimeId;
        } catch (e) {
          return defaultVal.toString();
        }
      },
      apiFailure: (e, _) {
        return defaultVal.toString();
      },
    );
  }
}
