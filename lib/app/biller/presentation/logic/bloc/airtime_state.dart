part of 'airtime_cubit.dart';

abstract class AirtimeState extends Equatable {
  const AirtimeState();
}

class AirtimeInitial extends AirtimeState {
  @override
  List<Object?> get props => [];
}

class AirtimeLoading extends AirtimeState {
  @override
  List<Object?> get props => [];
}

class AirtimeLoaded extends AirtimeState {
  final num minAmount;
  final List<Biller> bills;

  const AirtimeLoaded(this.bills, this.minAmount);

  @override
  List<Object?> get props => [bills];
}

class AirtimeError extends AirtimeState {
  final String message;

  const AirtimeError(this.message);

  @override
  List<Object?> get props => [message];
}
