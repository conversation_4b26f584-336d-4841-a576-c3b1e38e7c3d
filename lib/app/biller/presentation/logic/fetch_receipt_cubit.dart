import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/biller/domain/use_cases/get_receipt.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

class FetchReceiptCubit extends Cubit<CubitState<String?>> {
  FetchReceiptCubit(this._getReceipt) : super(CubitState.init());

  final GetReceipt _getReceipt;

  Future fetchReceipt(String ref) async {
    emit(CubitState.loading(loading: true));
    final result = await _getReceipt(ref);
    result.when(
      success: (String? data) async {
        emit(CubitState.loading(loading: false));
        emit(CubitState.completed(model: data));
      },
      apiFailure: (error, _) async {
        emit(CubitState.loading(loading: false));
        emit(CubitState.error(
            errorMessage: ApiExceptions.getErrorMessage(error)));
      },
    );
  }

  Future init() async {
    emit(CubitState.init());
  }
}
