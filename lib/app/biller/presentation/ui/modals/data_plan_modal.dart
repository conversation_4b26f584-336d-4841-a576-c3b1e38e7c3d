import 'package:flutter/material.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

typedef PaymentAction = Future<bool> Function(String pin, BuildContext context);

class DataPlanModal extends StatefulWidget {
  final List<PaymentItem> dataPlans;

  const DataPlanModal({
    super.key,
    required this.dataPlans,
  });

  static Future<PaymentItem?> displayModal(
      BuildContext context, List<PaymentItem> dataPlans) async {
    return showModalBottomSheet<PaymentItem>(
      isScrollControlled: true,
      context: context,
      builder: (_) => DataPlanModal(dataPlans: dataPlans),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
      constraints: BoxConstraints.tightFor(
          height: MediaQuery.of(context).size.height * 0.85),
    );
  }

  @override
  _DataPlanModalState createState() => _DataPlanModalState();
}

class _DataPlanModalState extends State<DataPlanModal> {
  late final ValueNotifier<List<PaymentItem>> _dataPlansNotifier =
      ValueNotifier(widget.dataPlans);
  PaymentItem? dataPlan;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _dataPlansNotifier.dispose();
    super.dispose();
  }

  void _onSearch(String searchWord) {
    if (searchWord.isEmpty) {
      _dataPlansNotifier.value = widget.dataPlans;
    } else {
      final num? searchAmount = num.tryParse(searchWord);
      _dataPlansNotifier.value = searchAmount is num
          ? widget.dataPlans
              .where((element) => (element.amount / 100)
                  .toStringAsFixed(2)
                  .contains(searchWord))
              .toList()
          : widget.dataPlans
              .where((element) => element.paymentItemName
                  .toLowerCase()
                  .contains(searchWord.toLowerCase()))
              .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return Padding(
      padding: screenPadding,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Select Plan', style: theme.headlineSmall),
              IconButton(
                  onPressed: () => Navigator.pop(context),
                  constraints: BoxConstraints.tightFor(width: 35),
                  icon: Icon(Icons.close))
            ],
          ),
          YSpacing(10),
          SearchTextField(
            hintText: 'Search by  plan name or amount',
            onSearch: _onSearch,
          ),
          YSpacing(10),
          Expanded(
            child: ValueListenableBuilder<List<PaymentItem>>(
                valueListenable: _dataPlansNotifier,
                builder: (context, plans, _) {
                  if (plans.isEmpty) {
                    return Center(
                      child: Text("No data plan matches your search"),
                    );
                  }
                  return ListView.custom(
                    childrenDelegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          final plan = plans[index];
                          return ListTile(
                            title: Text(plan.paymentItemName,
                                style: theme.bodyMedium),
                            subtitle: CurrencyItem(
                              (plan.amount / 100),
                              UserCubit.instance!.currencyCode,
                              amountStyle: theme.bodyLarge,
                              decimalDigits:
                                  isInteger(plan.amount / 100) ? 0 : 2,
                            ),
                            trailing: Icon(
                              size: 20,
                              Icons.arrow_forward_ios_outlined,
                            ),
                            contentPadding:
                                EdgeInsets.only(bottom: 20, right: 5),
                            onTap: () => Navigator.pop(context, plan),
                          );
                        },
                        childCount: plans.length,
                        findChildIndexCallback: (Key key) {
                          final ValueKey<PaymentItem> valueKey =
                              key as ValueKey<PaymentItem>;
                          final PaymentItem data = valueKey.value;
                          return plans.indexOf(data);
                        }),
                  );
                }),
          ),
          const YMargin(30),
        ],
      ),
    );
  }

  bool isInteger(num value) => value is int || value == value.roundToDouble();
}
