import 'package:flutter/material.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/domain/use_cases/get_bills.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_option_screen.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';

class BillCategoryModal extends StatefulWidget {
  final String title;
  final String id;
  final String subtitle;
  static final _repo = <String, List<Biller>>{};

  const BillCategoryModal({
    super.key,
    required this.title,
    required this.id,
    required this.subtitle,
  });

  static void show(
    BuildContext context, {
    required String id,
    required String title,
    required String subtitle,
  }) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (_) => BillCategoryModal(
        id: id,
        title: title,
        subtitle: subtitle,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  State<BillCategoryModal> createState() => _BillCategoryModalState();
}

class _BillCategoryModalState extends State<BillCategoryModal> {
  bool _loading = false;
  String _error = '';
  List<Biller> _bills = [];

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      height: MediaQuery.of(context).size.height * 0.75,
      padding: const EdgeInsets.only(
        top: 29,
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: textStyleRegular(
              context,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            widget.subtitle,
            style: textStyleRegular(
              context,
            ),
            textAlign: TextAlign.center,
          ),
          Expanded(child: _buildList(context)),
        ],
      ),
    );
  }

  Widget _buildList(BuildContext context) {
    if (_loading) return Center(child: CircularProgressIndicator());

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _error,
              style: TextStyle(color: Colors.red),
            ),
            TextButton(
              onPressed: _loadItems,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 10),
      itemCount: _bills.length,
      separatorBuilder: (_, __) => Divider(height: 0),
      itemBuilder: (_, index) => _buildItem(_bills[index]),
    );
  }

  Widget _buildItem(Biller bill) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => BillOptionScreen(bill: bill)),
          );
          Segment.track(
            eventName: SegmentEvents.billPayCategoryItemSelected,
            properties: {
              'category_id': bill.categoryId,
              'category_name': bill.categoryName,
              'biller_id': bill.billerId,
              'biller_name': bill.billerName,
              'customer_field_1': bill.field1,
              'customer_field_2': bill.field2,
            },
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  bill.name,
                  style: textStyleRegular(
                    context,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 14,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _loadItems() async {
    if (BillCategoryModal._repo[widget.id] != null) {
      setState(() {
        _bills = BillCategoryModal._repo[widget.id]!;
      });
      return;
    }

    setState(() {
      _loading = true;
    });

    final res = await locator.get<GetBills>().call(widget.id);

    res.when(
      success: (data) {
        _bills = data;
        // cache
        BillCategoryModal._repo[widget.id] = data;
        _error = '';
      },
      apiFailure: (e, _) {
        _error = ApiExceptions.getErrorMessage(e);
      },
    );

    if (mounted) {
      setState(() {
        _loading = false;
      });
    }
  }

  // void _track(String mode, String value, Assignee assignee) {
  //   Segment.track(
  //     eventName: SegmentEvents.contactedManager,
  //     properties: {
  //       'mode': mode,
  //       'managerId': assignee.assigneeId,
  //       'name': assignee.assigneeName,
  //       'contact': value,
  //     },
  //   );
  // }
}
