import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/presentation/logic/fetch_receipt_cubit.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class BillSuccess extends StatelessWidget {
  final String info;
  final String title;
  final ChargeResponse? response;
  final Biller? biller;

  const BillSuccess(
      {super.key,
      required this.info,
      required this.title,
      this.response,
      this.biller});

  static Future<void> displayModal(
      BuildContext context, String info, String title,
      [ChargeResponse? response, Biller? biller]) async {
    showModalBottomSheet(
      context: context,
      builder: (_) => BillSuccess(
        info: info,
        title: title,
        response: response,
        biller: biller,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height *
            (response?.pin.isEmpty ?? true ? 0.5 : 0.7),
      ),
      isScrollControlled: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return WillPopScope(
        child: Padding(
          padding: screenPadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              YMargin(10),
              CircleAvatar(
                radius: 30,
                child: SvgPicture.asset(kSvgCheck),
              ),
              YMargin(40),
              Text(title, style: theme.headlineLarge),
              Text(info, textAlign: TextAlign.center, style: theme.bodyLarge),
              if (response?.pin.isNotEmpty ?? false) ...[
                YMargin(50),
                ListTile(
                  leading: SizedBox(
                    width: 40,
                    height: 40,
                    child: DecoratedBox(
                      decoration: BoxDecoration(),
                    ),
                  ),
                  title: Text(biller!.name, style: theme.bodyMedium),
                  subtitle: Text(
                    response!.pin,
                    style: theme.bodyLarge,
                  ),
                  contentPadding: EdgeInsets.zero,
                  trailing: SizedBox(
                    width: 90,
                    height: 36,
                    child: KButtonPrimary(
                      onTap: () => FlutterClipboard.copy(response!.pin).then(
                        (value) {
                          Toast.success(
                            "code copied",
                            context,
                            duration: 5,
                          );
                        },
                      ),
                      text: "Copy code",
                      padding: EdgeInsets.all(4),
                      textStyle: theme.bodyMedium,
                    ),
                  ),
                ),
              ],
              YMargin(50),
              if ((response?.reference.isNotEmpty ?? false) &&
                  !(response!.pending)) ...[
                BlocConsumer<FetchReceiptCubit, CubitState<String?>>(
                    builder: (context, state) => KButtonPrimary(
                          onTap: () => context
                              .read<FetchReceiptCubit>()
                              .fetchReceipt(response!.reference),
                          text: "Share Receipt",
                          isLoading: state.maybeWhen(
                              orElse: () => false, loading: (val) => val),
                        ),
                    listener: (context, state) {
                      state.maybeWhen(
                          orElse: () {},
                          error: (E) => Toast.error(E, context),
                          completed: (data) {
                            if (data != null) {
                              final RenderBox box =
                                  context.findRenderObject() as RenderBox;
                              Share.shareXFiles([
                                XFile(
                                  data,
                                  mimeType: 'application/pdf',
                                ),
                              ],
                                  subject:
                                      '${biller!.categoryName} Bill Receipt',
                                  text:
                                      data.substring(data.lastIndexOf("/") + 1),
                                  sharePositionOrigin:
                                      box.localToGlobal(Offset.zero) &
                                          box.size);
                            }
                          });
                    }),
                YSpacing(12),
              ],
              KButtonPrimary(
                onTap: () => context.goNamed(PaymentPath),
                text: 'Done',
              ),
            ],
          ),
        ),
        onWillPop: () async {
          context.goNamed(PaymentPath);
          return false;
        });
  }
}
