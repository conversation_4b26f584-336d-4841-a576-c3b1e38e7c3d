import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';

class ServiceErrorModal extends StatelessWidget {
  String _title(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    return locale.service_unavailable;
  }

  String _subtitle(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    return locale.unavailable_description;
  }

  const ServiceErrorModal({
    super.key,
  });

  static Future<bool?> show(BuildContext context) {
    return showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: false,
      context: context,
      builder: (_) => const ServiceErrorModal(),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      height: MediaQuery.of(context).size.height * 0.5,
      padding: const EdgeInsets.only(
        top: 29,
        left: 16,
        right: 16,
        bottom: 24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SvgPicture.asset(kSvgBillFailed),
          Text(
            _title(context),
            style: textStyleRegular(
              context,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.55,
            child: Text(
              _subtitle(context),
              style: textStyleRegular(
                context,
                fontColor: Theme.of(context).disabledColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(),
          const SizedBox(),
          const SizedBox(),
          const SizedBox(),
          _buildButtons(context),
        ],
      ),
    );
  }

  Widget _buildButtons(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    return KButton(
      showIcon: false,
      text: locale.go_back,
      onPressed: () {
        Navigator.of(context).pop(true);
      },
    );
  }
}
