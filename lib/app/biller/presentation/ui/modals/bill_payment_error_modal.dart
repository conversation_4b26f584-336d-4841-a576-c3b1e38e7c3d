import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class BillPaymentErrorModal extends StatelessWidget {
  final String subtitle;
  final bool funds;

  const BillPaymentErrorModal({
    super.key,
    required this.funds,
    required this.subtitle,
  });

  static Future<bool?> displayModal(
      BuildContext context, String subtitle, bool funds) async {
    return showModalBottomSheet<bool?>(
      context: context,
      builder: (_) => BillPaymentErrorModal(
        subtitle: subtitle,
        funds: funds,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      isScrollControlled: true,
      constraints: const BoxConstraints.tightFor(height: 400),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    final locale = AppLocalizations.of(context)!;
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.7,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const CircleAvatar(
              radius: 30,
              child: Icon(
                Icons.error_outline,
              ),
            ),
            const YMargin(40),
            Text(_title(context), style: theme.headlineLarge),
            Text(_subtitle(context),
                textAlign: TextAlign.center, style: theme.bodyLarge),
            const YMargin(60),
            funds ? _topUp(context, locale) : _tryAgain(context, locale)
          ],
        ),
      ),
    );
  }

  String _title(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    if (funds) {
      return locale.insufficient_funds;
    }
    return locale.transaction_failed;
  }

  String _subtitle(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    if (funds) {
      return locale.insufficient_funds_description;
    }

    if (subtitle.toLowerCase().contains('failed request')) {
      return locale.api_error_message;
    }

    if (subtitle.isNotEmpty) return subtitle;

    return locale.api_error_message;
  }

  Widget _tryAgain(BuildContext context, AppLocalizations locale) {
    return KButtonPrimary(
      text: locale.try_again,
      onTap: () {
        Navigator.of(context).pop(true);
      },
    );
  }

  Widget _topUp(BuildContext context, AppLocalizations locale) {
    return KButtonPrimary(
      text: locale.top_up_account,
      onTap: () {
        context.goNamed(TopUpPath);
      },
    );
  }
}
