import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/biller/data/exceptions/bill_unvailable_exception.dart';
import 'package:shop/app/biller/data/models/bill_argument.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/app/biller/domain/use_cases/validate_bill.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/biller/presentation/ui/modals/service_error_modal.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_pin_screen.dart';
import 'package:shop/app/homepage/presentation/logic/utils/method.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/form/src/select_menu_2.dart';
import 'package:shop/src/components/src/form/src/select_menu_item.dart';
import 'package:shop/src/components/src/form/src/text_field_2.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/res/extensions/extensions.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/scaler/src/extensions.dart';

class BillOptionScreen extends StatefulWidget {
  final Biller bill;

  const BillOptionScreen({super.key, required this.bill});

  @override
  _BillOptionScreenState createState() => _BillOptionScreenState();
}

class _BillOptionScreenState extends State<BillOptionScreen> {
  final _amountController = TextEditingController();
  final _chargeController = TextEditingController(text: '0');
  final _inputController = TextEditingController();
  final _phoneController = TextEditingController();

  bool _sending = false;
  bool _loadingOptions = false;
  String _to = '';
  num _charges = 0;
  bool _validMeterLength = false;

  SelectMenuItem<PaymentItem> _selectedOption = SelectMenuItem(
    title: '',
    value: PaymentItem.fromMap({}),
  );
  List<PaymentItem> _options = [];

  bool get includePhone => !widget.bill.field1.toLowerCase().contains('phone');

  @override
  void initState() {
    super.initState();
    _loadOptions();

    _inputController.addListener(() {
      if (_inputController.text.length == 11) {
        setState(() {
          _validMeterLength = true;
        });
      } else {
        setState(() {
          _validMeterLength = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            const SizedBox(height: 20),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 20,
                ),
                children: [
                  _buildForm(),
                  const SizedBox(height: 20),
                  _buildIndicator(),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 10,
              ),
              child: KButton(
                onPressed: _sending ? null : _proceed,
                isLoading: _sending,
                text: 'Next',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    if (!_selectedOption.isValid) return const SizedBox.shrink();

    num amount = 0;
    try {
      amount = num.parse(_amountController.text.replaceAll(',', ''));
    } catch (e) {}

    amount += _charges;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 20,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _to,
            style: textStyleRegular(
              context,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '₦ ',
                        style: TextStyle(fontFamily: 'Roboto'),
                      ),
                      TextSpan(
                        text: amount.toStringAsFixed(2),
                      ),
                    ],
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 1),
                    child: Text(
                      '${widget.bill.name} ${_selectedOption.value.paymentItemName}',
                      style: textStyleRegular(
                        context,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  void _proceed() async {
    // validate

    if (!_selectedOption.isValid) {
      Toast.error('Please select a bill option', context);
      return;
    }

    final inputAmount = _amountController.text;
    if (inputAmount.isEmpty) {
      Toast.error('Please enter an amount', context);
      return;
    }

    late final num amount;
    try {
      amount = num.parse(inputAmount.replaceAll(',', ''));
    } catch (e) {
      Toast.error('Invalid amount', context);
      return;
    }

    if (amount < 1) {
      Toast.error('Please enter a valid amount', context);
      return;
    }

    final input = _inputController.text;

    if (input.isEmpty) {
      Toast.error('Please enter your ${widget.bill.field1}', context);
      return;
    }

    if (!_validMeterLength) {
      Toast.error('Please enter a valid 11 digits meter number', context);
      return;
    }

    String phone = _phoneController.text.replaceAll(' ', '');

    if (includePhone) {
      if (phone.startsWith('234')) {
        phone = '+$phone';
      }

      if (phone.startsWith('+')) {
        // Remove + at the beginning
        // requested by backend
        phone = phone.replaceFirst('+234', '0');
      }

      if (phone.isEmpty) {
        Toast.error('Please enter a phone number', context);
        return;
      }

      if (phone.length != 11) {
        Toast.error('Invalid phone number', context);
        return;
      }
    }

    _to = '';

    // validate
    setState(() {
      _sending = true;
    });

    hideKeyboard(context);

    Segment.track(
      eventName: SegmentEvents.billPayInitiated,
      properties: {
        'amount': amount,
        'charges': _charges,
        'biller': widget.bill.toMap(),
        'payment_code': _selectedOption.value.paymentCode,
        'input': input,
        'name': _selectedOption.value.paymentItemName,
        'customer_phone': phone,
      },
    );

    final res = await locator.get<ValidateBill>().call(
          ValidateParam(
            input: input,
            paymentCode: _selectedOption.value.paymentCode,
          ),
        );

    res.when(
      success: (CustomerInfo data) async {
        setState(() {
          _to = data.fullName;
        });

        await Future.delayed(Duration(milliseconds: 2500));

        setState(() {
          _sending = false;
        });

        BillPinScreen.show(
          context,
          BillArgument(
            amount: amount,
            charges: _charges,
            customerName: _to,
            biller: widget.bill,
            paymentCode: _selectedOption.value.paymentCode,
            input: input,
            name: _selectedOption.value.paymentItemName,
            customerPhone: phone,
          ),
        );
      },
      apiFailure: (e, _) {
        Toast.error(
          InterSwitchErrorService.it.extract(
            context,
            ApiExceptions.getErrorMessage(e),
          ),
          context,
        );
        setState(() {
          _sending = false;
        });
      },
    );
  }

  Widget _buildForm() {
    return Column(
      children: [
        Stack(
          children: [
            SelectMenu2<PaymentItem>(
              enabled: !_loadingOptions && _options.isNotEmpty,
              onChanged: (val) {
                if (val == null) return;

                _setOption(val);
              },
              title: 'Option',
              hint: 'Select option',
              value: _selectedOption,
              items: _options
                  .map(
                    (e) => SelectMenuItem<PaymentItem>(
                      title: e.paymentItemName,
                      subtitle: e.amount < 1
                          ? ''
                          : (e.amount / 100).toStringAsFixed(2),
                      value: e,
                    ),
                  )
                  .toList(),
            ),
            if (_loadingOptions)
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 10),
        TextField2(
          enabled: !_selectedOption.value.isAmountFixed,
          amount: true,
          controller: _amountController,
          title: 'Enter amount',
        ),
        const SizedBox(height: 10),
        TextField2(
          controller: _inputController,
          title: 'Enter ${widget.bill.field1}',
          onChanged: (_) {
            if (_to.isEmpty) return;

            setState(() {
              _to = '';
            });
          },
        ),
        if (includePhone)
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: _buildPhoneInput(),
          ),
        const SizedBox(height: 10),
        TextField2(
          enabled: false,
          controller: _chargeController,
          title: 'Service charge',
          amount: true,
        ),
      ],
    );
  }

  Widget _buildPhoneInput() {
    return TextField(
      keyboardType: TextInputType.numberWithOptions(),
      style: TextStyle(
        fontSize: context.fontSize(14),
      ),
      controller: _phoneController,
      decoration: InputDecoration(
        hintText: 'Customer Phone Number',
        contentPadding: const EdgeInsets.only(
          left: 16,
          right: 16,
          top: 26,
          bottom: 10,
        ),
        enabledBorder: KTextStyle.outlineInputBorder,
        focusedBorder: KTextStyle.outlineInputBorder,
        border: KTextStyle.outlineInputBorder,
/*        suffixIcon: kIsWeb
            ? null
            : InkWell(
                onTap: () async {
                  return showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        content: SizedBox(
                          height: 180,
                          width: double.maxFinite,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Access to your contacts is required to complete this action. We take data privacy seriously and will not be shared with third parties. Do you want to proceed?",
                                style: KTextStyle.bodyText2.copyWith(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: Text('NO'),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop();
                              final contact =
                                  await FlutterContactPicker.pickPhoneContact();
                              if (contact.phoneNumber != null) {
                                _phoneController.text = contact
                                    .phoneNumber!.number
                                    .toString()
                                    .replaceAll(
                                      ' ',
                                      '',
                                    );
                              }
                              // contact.phoneNumber
                            },
                            child: Text('YES'),
                          ),
                        ],
                        titleTextStyle:
                            KTextStyle.semiBold24.copyWith(fontSize: 20),
                        contentTextStyle:
                            KTextStyle.bodyText2.copyWith(fontSize: 16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        contentPadding:
                            EdgeInsets.only(left: 30, bottom: 15, right: 30),
                        titlePadding:
                            EdgeInsets.only(left: 30, top: 15, right: 30),
                        actionsPadding: EdgeInsets.zero,
                      );
                    },
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: SvgPicture.asset(
                    kSvgContactIcon,
                  ),
                ),
              ),*/
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[\d+]')),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: KBackButton(
                black: true,
                legacy: true,
              ),
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(top: 20, bottom: 5),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width * .6,
                  child: Text(
                    'Pay ${widget.bill.name} Bill',
                    style: textStyleRegular(
                      context,
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ),
        Text(
          "Enter details",
          textAlign: TextAlign.center,
          style: textStyleRegular(
            context,
          ),
        ),
      ],
    );
  }

  void _loadOptions() async {
    if (_loadingOptions) return;

    setState(() {
      _loadingOptions = true;

      // reset previous selection option
      _selectedOption = SelectMenuItem(
        title: '',
        value: PaymentItem.fromMap({}),
      );
    });

    //  load options
    try {
      final res = await BlocProvider.of<AirtimeCubit>(context).loadItems(
        widget.bill,
        context,
      );

      _loadingOptions = false;

      if (res == null) {
        // failed
      } else {
        _options = res;
      }
    } on BillUnavailableException catch (_) {
      if (!mounted) return;
      setState(() {
        _loadingOptions = false;
      });

      await ServiceErrorModal.show(context);
      Navigator.pop(context);
      return;
    }

    if (!mounted) return;

    setState(() {});
  }

  void _setOption(SelectMenuItem<PaymentItem> val) {
    if (val.value.amount > 0) {
      _amountController.text = (val.value.amount / 100).toStringAsFixed(2);
    }

    _charges = val.value.itemFee.koboToNaira();

    _chargeController.text = val.value.itemFee.koboToNaira().toStringAsFixed(2);

    setState(() {
      _selectedOption = val;
    });
  }
}
