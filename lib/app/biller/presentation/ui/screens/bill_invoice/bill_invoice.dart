import 'package:flutter/cupertino.dart';
import 'package:shop/app/biller/data/models/invoice_data.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_invoice/mobile/mobile_bill_invoice.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class BillInvoice extends StatelessWidget {
  final InvoiceData invoiceData;

  const BillInvoice({super.key, required this.invoiceData});

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      smallScreen: MobileBillInvoice(
        invoiceData: invoiceData,
      ),
    );
  }
}
