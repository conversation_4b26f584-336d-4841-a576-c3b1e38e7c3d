import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/data/models/invoice_data.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/use_cases/charge_bill.dart';
import 'package:shop/app/biller/presentation/ui/modals/bill_payment_error_modal.dart';
import 'package:shop/app/biller/presentation/ui/modals/bill_success.dart';
import 'package:shop/app/payments/presentation/ui/modals/payment_pin_modal.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/extensions/extensions.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

const insufficientFundsErrMsg = "Insufficient Balance";

class MobileBillInvoice extends StatelessWidget {
  final InvoiceData invoiceData;

  MobileBillInvoice({super.key, required this.invoiceData});

  late final Map<String, Object> list = invoiceData.billType == BillType.bills
      ? {
          if (invoiceData.recipientName.isNotEmpty)
            "RECIPIENT": invoiceData.recipientName,
          "AMOUNT": invoiceData.amount,
          "PROVIDER": invoiceData.biller.name,
          "PLAN": invoiceData.plan,
          "FEE": invoiceData.fee,
          "TOTAL": invoiceData.totalAmount,
        }
      : {
          "AMOUNT": invoiceData.amount,
          if (invoiceData.plan.isNotEmpty) "PLAN": invoiceData.plan,
          "NETWORK": invoiceData.biller.name,
          "FEE": invoiceData.fee,
          "TOTAL": invoiceData.totalAmount,
        };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    final currencyCode = context.read<UserCubit>().currentOutlet!.currencyCode;
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: false,
        title: Padding(
          padding: EdgeInsets.only(left: 5),
          child: Text("Confirm payment"),
        ),
        elevation: 0.5,
        actions: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close),
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: screenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                    text:
                        'Confirm ${invoiceData.billType == BillType.bills ? invoiceData.biller.name : invoiceData.billType == BillType.airtime ? "airtime" : "data"} purchase for ',
                    style: theme.headlineLarge,
                    children: [
                      TextSpan(
                          text: invoiceData.inputOption,
                          style: theme.headlineLarge),
                    ]),
              ),
              YSpacing(10),
              Text("Review details and confirm payment",
                  style: theme.bodyLarge),
              YSpacing(40),
              Divider(),
              YSpacing(40),
              ...list.entries
                  .map(
                    (e) => InvoiceListTile(
                        label: e.key,
                        value: e.value,
                        currencyCode: currencyCode),
                  )
                  ,
              YSpacing(100),
              KButtonPrimary(
                onTap: () => PaymentPinModal.show(context, confirm),
                text: 'Confirm',
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> confirm(String pin, BuildContext context) async {
    TdLoader.show(context);
    final res = await locator.get<ChargeBill>().call(
          ChargeParam(
            retailOutletId: UserCubit.instance?.currentOutlet?.id ?? '',
            pin: pin,
            amount: invoiceData.amount,
            paymentCode: invoiceData.paymentCode,
            customerName: invoiceData.recipientName,
            customerId: invoiceData.inputOption,
            billerId: invoiceData.biller.billerId,
            billerName: invoiceData.biller.billerName,
            categoryId: invoiceData.biller.categoryId,
            categoryName: invoiceData.biller.categoryName,
            customerPhone: invoiceData.recipientPhone,
          ),
        );

    TdLoader.hide();
    res.when(
      success: (ChargeResponse e) {
        String title, eventName;
        String total =
            CurrencyItem.formattedAmount(context, invoiceData.totalAmount);
        switch (invoiceData.billType) {
          case BillType.airtime:
            title = "Your airtime purchase of NGN $total is on its way";
            eventName = SegmentEvents.airtimePayCompleted;
            break;
          case BillType.data:
            title = "Your data purchase of NGN $total is on its way";
            eventName = SegmentEvents.billPayCompleted;
            break;
          case BillType.bills:
            title =
                "Your have successfully purchased NGN $total for ${invoiceData.biller.name}";
            eventName = SegmentEvents.billPayCompleted;
            break;
        }
        Segment.track(
          eventName: eventName,
          properties: {
            'amount': invoiceData.amount,
            'payment_code': invoiceData.paymentCode,
            'customer_id': invoiceData.inputOption,
            'biller_id': invoiceData.biller.billerId,
            'biller_name': invoiceData.biller.billerName,
            'category_id': invoiceData.biller.categoryId,
            'category_name': invoiceData.biller.categoryName,
            'customer_phone': invoiceData.recipientPhone,
            'charges': invoiceData.fee,
            'status': e.status,
          },
        );
        Navigator.pop(context);
        BillSuccess.displayModal(
            context,
            title,
            "Purchase ${e.pending ? 'Pending' : 'Successful'}",
            e,
            invoiceData.biller);
        UserCubit.instance?.updateOutlet();
      },
      apiFailure: (e, _) async {
        final message = ApiExceptions.getErrorMessage(e);

        if (_ == 422) {
          Navigator.pop(context);
          Toast.error(message, context);
          return;
        }
        Navigator.pop(context);
        BillPaymentErrorModal.displayModal(
            context,
            message.contains(RegExp(r'\d'))
                ? InterSwitchErrorService.it.extract(context, message)
                : message,
            _ == 400 && message == insufficientFundsErrMsg);

        /*      if (retry == true) {
          confirm(pin, context);
        }*/
      },
    );
  }
}

class InvoiceListTile extends StatelessWidget {
  const InvoiceListTile({
    super.key,
    required this.currencyCode,
    required this.label,
    required this.value,
  });

  final String currencyCode;
  final String label;
  final Object value;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            child: Text(label, style: theme.bodySmall),
          ),
          value is num
              ? CurrencyItem(
                  value as num,
                  currencyCode,
                  amountStyle: theme.headlineSmall?.copyWith(fontSize: 18),
                )
              : Flexible(
                  flex: 2,
                  child: Text(
                    value as String,
                    textAlign: TextAlign.end,
                    style: theme.headlineSmall?.copyWith(fontSize: 18),
                  ),
                )
        ],
      ),
    );
  }
}
