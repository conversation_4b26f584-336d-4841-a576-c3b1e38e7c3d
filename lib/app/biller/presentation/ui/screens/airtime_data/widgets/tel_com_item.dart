import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/colors/colors.dart';

class TelComItem extends StatelessWidget {
  final Biller telCom;
  final bool isSelected;
  final ValueChanged<Biller> onTap;
  const TelComItem(
      {super.key,
      required this.telCom,
      required this.isSelected,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 70,
      child: Column(
        children: [
          InkWell(
            child: SizedBox(
              height: 64,
              width: 64,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                      color: isSelected ? kBrandBlue : Colors.transparent),
                ),
                child: Padding(
                  padding: EdgeInsets.all(10),
                  child: CachedNetworkImage(
                    imageUrl: telCom.image,
                    progressIndicatorBuilder: (_, __, ___) => Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    ),
                    errorWidget: (_, __, ___) => Text(telCom.name),
                  ),
                ),
              ),
            ),
            onTap: () => onTap(telCom),
          ),
          YSpacing(8),
          Text(
            telCom.name,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context)
                .textTheme
                .labelLarge
                ?.copyWith(color: isSelected ? kBrandBlue : k585858),
          ),
        ],
      ),
    );
  }
}
