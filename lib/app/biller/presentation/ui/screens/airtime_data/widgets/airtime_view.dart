import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/invoice_data.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/tel_com_item.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/wallet_balance_chip.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/src/td_form_field.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/services/local_storage/src/shared_pref_service.dart';

const _amount = [200, 500, 1000, 2000, 2500, 5000];

class AirtimeView extends StatefulWidget {
  const AirtimeView({
    super.key,
  });

  static final GlobalKey<AirtimeViewState> globalKey = GlobalKey();
  @override
  State<StatefulWidget> createState() {
    return AirtimeViewState();
  }
}

class AirtimeViewState extends State<AirtimeView> {
  final ValueNotifier<num?> _amountNotifier = ValueNotifier(null);
  late final _currencyCode =
      context.read<UserCubit>().currentOutlet!.currencyCode;
  late final TextEditingController _amountTextController =
      TextEditingController(text: sp.read(Keys.defaultAmount));
  late final TextEditingController _phoneTextController =
      TextEditingController(text: sp.read(Keys.defaultNumber));
  late final theme = Theme.of(context).textTheme;
  final ValueNotifier<Biller?> _telComNotifier = ValueNotifier(null);
  late final ValueNotifier<bool> _switchDefaultAmountNotifier =
      ValueNotifier(sp.check(Keys.defaultAmount));
  late final ValueNotifier<bool> _switchDefaultNumberNotifier =
      ValueNotifier(sp.check(Keys.defaultNumber));
  late final _airtimeBloc = context.read<AirtimeCubit>();
  List<PaymentItem> _options = [];
  late final SharedPrefService sp;
  late final _retailOutlet = context.read<UserCubit>().currentOutlet!;
  Biller _selectTelCom(Biller telCom) => _telComNotifier.value = telCom;

  @override
  void dispose() {
    _amountNotifier.dispose();
    _amountTextController.dispose();
    _phoneTextController.dispose();
    _switchDefaultAmountNotifier.dispose();
    _switchDefaultNumberNotifier.dispose();
    _telComNotifier.dispose();
    super.dispose();
  }

  void _selectAmount(num amount) {
    _amountNotifier.value = amount;
    _amountTextController.text =
        CurrencyItem.formattedAmount(context, amount, 0);
  }

  @override
  void initState() {
    sp = locator.get<SharedPrefService>();
    _airtimeBloc.loadAirtime(context.read<AppConfig>().environment!);
    _telComNotifier.addListener(() {
      _options = [];
      _loadOptions(false);
    });
    super.initState();
  }

/*  void _pickContact() async {
    final contact = await FlutterContactPicker.pickPhoneContact();
    if (contact.phoneNumber != null) {
      _phoneTextController.text =
          contact.phoneNumber!.number?.replaceAll(' ', '') ?? '';
    }
  }*/

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverOverlapInjector(
          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
        ),
        SliverToBoxAdapter(
          child: Column(
            children: [
              YSpacing(8),
              WalletBalanceChip(retailOutlet: _retailOutlet),
              YSpacing(20),
              BlocBuilder<AirtimeCubit, AirtimeState>(builder: (_, state) {
                if (state is AirtimeError) {
                  return Center(
                    child: Text(state.message),
                  );
                }
                if (state is AirtimeLoaded) {
                  return ValueListenableBuilder<Biller?>(
                    valueListenable: _telComNotifier,
                    builder: (context, selectedTelCom, _) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: state.bills
                            .map(
                              (e) => TelComItem(
                                telCom: e,
                                isSelected: selectedTelCom == e,
                                onTap: _selectTelCom,
                              ),
                            )
                            .toList(),
                      );
                    },
                  );
                }

                return Center(
                  child: CircularProgressIndicator(),
                );
              }),
              YSpacing(40),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Column(
            children: [
              SizedBox(
                height: 30,
                child: ValueListenableBuilder<num?>(
                  valueListenable: _amountNotifier,
                  builder: (context, selectedAmount, _) {
                    return ListView(
                      scrollDirection: Axis.horizontal,
                      children: _amount
                          .map(
                            (e) => AirtimeAmount(
                              amount: e,
                              currencyCode: _currencyCode,
                              isSelected: selectedAmount == e,
                              onTap: _selectAmount,
                            ),
                          )
                          .toList(),
                    );
                  },
                ),
              ),
              YSpacing(50),
              Column(
                children: [
                  TdFormField(
                    label: 'Enter amount',
                    widget: TextFormField(
                      controller: _amountTextController,
                      keyboardType: TextInputType.numberWithOptions(),
                      style: theme.bodyLarge,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(8),
                        CurrencyInputFormatter(decimalPlaces: 2)
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Save as default amount',
                        style: theme.bodyMedium,
                      ),
                      ValueListenableBuilder<bool>(
                          valueListenable: _switchDefaultAmountNotifier,
                          builder: (context, switched, _) {
                            return Switch(
                                value: switched,
                                onChanged: (value) {
                                  _switchDefaultAmountNotifier.value = value;
                                });
                          }),
                    ],
                  ),
                  YSpacing(20),
                  TdFormField(
                    label: 'Phone number',
                    widget: TextFormField(
                      decoration: InputDecoration(
                          /*                 suffixIconConstraints: BoxConstraints.tight(
                          Size(30, 34),
                        ),
                        suffixIcon: kIsWeb
                            ? null
                            : InkWell(
                                onTap: () async {
                                  if (await FlutterContactPicker
                                      .hasPermission()) {
                                    _pickContact();
                                  } else {
                                    await ContactPicker.displayDialog(
                                        context, _pickContact);
                                  }
                                },
                                child: Padding(
                                  child: SvgPicture.asset(kSvgContacts),
                                  padding: EdgeInsets.only(right: 12),
                                ),
                              ),*/
                          ),
                      controller: _phoneTextController,
                      keyboardType: TextInputType.numberWithOptions(),
                      style: theme.bodyLarge,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[\d+]')),
                      ],
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Save as default number',
                    style: theme.bodyMedium,
                  ),
                  ValueListenableBuilder<bool>(
                      valueListenable: _switchDefaultNumberNotifier,
                      builder: (context, switched, _) {
                        return Switch(
                            value: switched,
                            onChanged: (value) {
                              _switchDefaultNumberNotifier.value = value;
                            });
                      }),
                ],
              ),
              YSpacing(20)
            ],
          ),
        ),
      ],
    );
  }

  Future<void> buy() async {
    if (_telComNotifier.value == null) {
      Toast.error("Select a service provider", context);
      return;
    }
    final phone = await _phoneTextController.text.isValidNGPhoneNumber;

    if (phone == null) {
      Toast.error("Invalid Phone Number", context);
      return;
    }

    final inputAmount = _amountTextController.text;

    if (inputAmount.isEmpty) {
      Toast.error('Please enter an amount', context);
      return;
    }

    late final num amount;
    try {
      amount = num.parse(inputAmount.replaceAll(',', ''));
    } catch (e) {
      Toast.error('Invalid amount', context);
      return;
    }

    if (amount < 1) {
      Toast.error('Please enter a valid amount', context);
      return;
    }

    if (_options.isEmpty) {
      await _loadOptions(true);
      if (_options.isEmpty) return;
    }

    if (_airtimeBloc.state is AirtimeLoaded) {
      final minAmount = (_airtimeBloc.state as AirtimeLoaded).minAmount;
      if (amount < minAmount) {
        Toast.error('Min airtime amount is NGN$minAmount', context);
        return;
      } else {
        final invoice = InvoiceData(
            inputOption: phone,
            recipientPhone: phone,
            amount: amount,
            paymentCode: _options.first.paymentCode,
            billType: BillType.airtime,
            fee: _options.first.itemFee.koboToNaira(),
            biller: _telComNotifier.value!);
        context.pushNamed(BillInvoicePath, extra: invoice);
        if (_switchDefaultAmountNotifier.value) {
          sp.save(Keys.defaultAmount, _amountTextController.text);
        } else {
          sp.remove(Keys.defaultAmount);
        }
        if (_switchDefaultNumberNotifier.value) {
          sp.save(Keys.defaultNumber, _phoneTextController.text);
        } else {
          sp.remove(Keys.defaultNumber);
        }
      }
    }
  }

  Future<void> _loadOptions(bool indicate) async {
    if (_telComNotifier.value == null) return;

    if (indicate) {
      /*setState(() {
        _sending = true;
      });*/
    }

    //  load options
    final res = await _airtimeBloc.loadItems(
      _telComNotifier.value!,
      context,
      indicate,
    );

    if (res != null) {
      _options = res;
    }

    if (indicate) {
/*      setState(() {
        _sending = false;
      });*/
    }
  }
}

class AirtimeAmount extends StatelessWidget {
  final num amount;
  final String currencyCode;
  final bool isSelected;
  final ValueChanged<num> onTap;

  const AirtimeAmount({
    super.key,
    required this.amount,
    required this.currencyCode,
    required this.onTap,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return InkWell(
      child: Padding(
        padding: EdgeInsets.only(right: 8),
        child: SizedBox(
          height: 28,
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onPrimary,
              borderRadius: BorderRadius.circular(28),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 6, horizontal: 10),
              child: CurrencyItem(
                amount,
                currencyCode,
                decimalDigits: 0,
                amountStyle: theme.textTheme.bodySmall?.copyWith(
                    color: isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onPrimaryContainer),
              ),
            ),
          ),
        ),
      ),
      onTap: () => onTap(amount),
    );
  }
}
