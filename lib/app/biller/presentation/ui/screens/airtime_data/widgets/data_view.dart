import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/exceptions/bill_unvailable_exception.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/invoice_data.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/app/biller/domain/use_cases/get_bills.dart';
import 'package:shop/app/biller/domain/use_cases/validate_bill.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/biller/presentation/ui/modals/data_plan_modal.dart';
import 'package:shop/app/biller/presentation/ui/modals/service_error_modal.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/tel_com_item.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/wallet_balance_chip.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/src/td_form_field.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_core/services/local_storage/src/shared_pref_service.dart';

class DataView extends StatefulWidget {
  const DataView({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return DataViewState();
  }
}

class DataViewState extends State<DataView> {
  late final theme = Theme.of(context).textTheme;
  late final TextEditingController _planTextController =
      TextEditingController();
  late final TextEditingController _phoneTextController =
      TextEditingController(text: sp.read(Keys.defaultDataNumber));

  final ValueNotifier<Biller?> _telComNotifier = ValueNotifier(null);

  final ValueNotifier<CubitState<List<PaymentItem>>> _dataPlanOptions =
      ValueNotifier(CubitState.init());

  final ValueNotifier<CubitState<List<Biller>>> _phoneDataProviders =
      ValueNotifier(CubitState.init());

  late final ValueNotifier<bool> _switchDefaultNumberNotifier =
      ValueNotifier(sp.check(Keys.defaultDataNumber));
  static List<Biller>? _dataProviders;
  PaymentItem? selectedPaymentOption;
  late final SharedPrefService sp;
  late final _retailOutlet = context.read<UserCubit>().currentOutlet!;

  @override
  void initState() {
    sp = locator.get<SharedPrefService>();
    _loadItems();
    _telComNotifier.addListener(() {
      _planTextController.text = "";
      _dataPlanOptions.value = CubitState.init();
      _loadOptions();
    });
    super.initState();
  }

  @override
  dispose() {
    _phoneTextController.dispose();
    _planTextController.dispose();
    _telComNotifier.dispose();
    _phoneDataProviders.dispose();
    _dataPlanOptions.dispose();
    _switchDefaultNumberNotifier.dispose();
    // _switchDefaultPlanNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverOverlapInjector(
          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
        ),
        SliverToBoxAdapter(
          child: Column(
            children: [
              YSpacing(8),
              WalletBalanceChip(retailOutlet: _retailOutlet),
              YSpacing(20),
              ValueListenableBuilder<CubitState<List<Biller>>>(
                  valueListenable: _phoneDataProviders,
                  builder: (context, state, _) {
                    return state.when(
                      init: () => Center(
                        child: CircularProgressIndicator(),
                      ),
                      loading: (loading) => Center(
                        child: CircularProgressIndicator(),
                      ),
                      completed: (providers) => ValueListenableBuilder<Biller?>(
                        valueListenable: _telComNotifier,
                        builder: (context, selectedTelCom, _) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: providers
                                .map(
                                  (e) => TelComItem(
                                    telCom: e,
                                    isSelected: selectedTelCom == e,
                                    onTap: (e) => _telComNotifier.value = e,
                                  ),
                                )
                                .toList(),
                          );
                        },
                      ),
                      error: (error) => Center(
                        child: Text(error),
                      ),
                    );
                  }),
              YSpacing(40),
            ],
          ),
        ),
        SliverToBoxAdapter(
          child: Column(
            children: [
              TdFormField(
                label: 'Phone number',
                widget: TextField(
                  decoration: InputDecoration(
       /*             suffixIconConstraints: BoxConstraints.tight(
                      Size(30, 34),
                    ),
                    suffixIcon: kIsWeb
                        ? null
                        : InkWell(
                            onTap: () async {
                              if (await FlutterContactPicker.hasPermission()) {
                                _pickContact();
                              } else {
                                await ContactPicker.displayDialog(
                                    context, _pickContact);
                              }
                            },
                            child: Padding(
                              child: SvgPicture.asset(kSvgContacts),
                              padding: EdgeInsets.only(right: 12),
                            ),
                          ),*/
                  ),
                  controller: _phoneTextController,
                  keyboardType: TextInputType.numberWithOptions(),
                  style: theme.bodyLarge,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[\d+]')),
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Save as default number',
                    style: theme.bodyMedium,
                  ),
                  ValueListenableBuilder<bool>(
                      valueListenable: _switchDefaultNumberNotifier,
                      builder: (context, switched, _) {
                        return Switch(
                            value: switched,
                            onChanged: (value) {
                              _switchDefaultNumberNotifier.value = value;
                            });
                      }),
                ],
              ),
              YSpacing(20),
              ValueListenableBuilder<CubitState<List<PaymentItem>>>(
                  valueListenable: _dataPlanOptions,
                  builder: (context, options, _) {
                    return TextField(
                      decoration: InputDecoration(
                        hintText: "Select a data plan",
                        hintStyle: theme.bodySmall,
                        suffixIconConstraints:
                            BoxConstraints.tightFor(height: 30, width: 40),
                        suffixIcon: options.maybeWhen(
                          orElse: () =>
                              Icon(Icons.keyboard_arrow_down_outlined),
                          loading: (load) => Padding(
                            padding: EdgeInsets.only(right: 10),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          error: (_) => Icon(
                            Icons.error_outline_outlined,
                          ),
                        ),
                      ),
                      controller: _planTextController,
                      style: theme.bodyLarge,
                      onTap: () async {
                        _dataPlanOptions.value.maybeWhen(orElse: () {
                          if (_telComNotifier.value == null) {
                            Toast.error("Select a network provider", context);
                          } else {
                            Toast.error("No data plan is available, try again",
                                context);
                            _loadOptions();
                          }
                        }, completed: (options) async {
                          final res = await DataPlanModal.displayModal(
                              context, options);
                          if (res == null) return;
                          selectedPaymentOption = res;
                          _planTextController.text = res.paymentItemName;
                        });
                      },
                      readOnly: true,
                    );
                  }),
              /*     YSpacing(10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Save as default number',
                    style: theme.bodyMedium,
                  ),
                  Switch(value: false, onChanged: (res) {})
                ],
              ),*/
              YSpacing(20)
            ],
          ),
        ),
      ],
    );
  }
/*
  void _pickContact() async {
    final contact = await FlutterContactPicker.pickPhoneContact();
    if (contact.phoneNumber != null) {
      _phoneTextController.text =
          contact.phoneNumber!.number?.replaceAll(' ', '') ?? '';
    }
  }*/

  void _loadItems() async {
    if (DataViewState._dataProviders != null) {
      _phoneDataProviders.value =
          CubitState.completed(model: DataViewState._dataProviders!);
      return;
    }
    _phoneDataProviders.value = CubitState.loading(loading: true);

    final res = await locator.get<GetBills>().call(4.toString());

    res.when(
      success: (data) {
        _phoneDataProviders.value = CubitState.completed(model: data);
        // cache
        DataViewState._dataProviders = data;
      },
      apiFailure: (e, _) {
        _phoneDataProviders.value =
            CubitState.error(errorMessage: ApiExceptions.getErrorMessage(e));
      },
    );
  }

  void _loadOptions() async {
    if (_dataPlanOptions.value == CubitState.loading(loading: true)) return;
    _dataPlanOptions.value = CubitState.loading(loading: true);
    try {
      final res = await BlocProvider.of<AirtimeCubit>(context).loadItems(
        _telComNotifier.value!,
        context,
      );
      if (res == null) {
        _dataPlanOptions.value =
            CubitState.error(errorMessage: "Failed to fetch data plans");
        Toast.error("Failed to fetch data plans", context);
      } else {
        _dataPlanOptions.value = CubitState.completed(
            model: res.where((element) => element.amount > 0).toList());
      }
    } on BillUnavailableException catch (_) {
      if (!mounted) return;
      _dataPlanOptions.value =
          CubitState.error(errorMessage: "Failed to fetch data plans");
      await ServiceErrorModal.show(context);
      Navigator.pop(context);
      return;
    }
  }

  Future<void> buy() async {
    if (_telComNotifier.value == null) {
      Toast.error("Select a service provider", context);
      return;
    }
    final phone = await _phoneTextController.text.isValidNGPhoneNumber;

    if (phone == null) {
      Toast.error("Invalid Phone Number", context);
      return;
    }

    _dataPlanOptions.value.maybeWhen(orElse: () {
      _loadOptions();
      Toast.error('Preferred data plan has not been selected', context);
    }, completed: (plans) async {
      if (selectedPaymentOption == null) {
        Toast.error('Please select a data plan', context);
      } else {
        TdLoader.show(context);
        Segment.track(
          eventName: SegmentEvents.billPayInitiated,
          properties: {
            'amount': selectedPaymentOption!.amount / 100,
            'biller': _telComNotifier.value?.toMap(),
            'payment_code': selectedPaymentOption!.paymentCode,
            'input': phone,
            'name': selectedPaymentOption!.paymentItemName,
            'customer_phone': phone,
          },
        );

        final res = await locator.get<ValidateBill>().call(
              ValidateParam(
                input: phone,
                paymentCode: selectedPaymentOption!.paymentCode,
              ),
            );

        res.when(
          success: (CustomerInfo data) async {
            final invoice = InvoiceData(
                inputOption: phone,
                plan: selectedPaymentOption!.paymentItemName,
                recipientPhone: phone,
                amount: selectedPaymentOption!.amount / 100,
                paymentCode: selectedPaymentOption!.paymentCode,
                fee: selectedPaymentOption!.itemFee.koboToNaira(),
                billType: BillType.data,
                biller: _telComNotifier.value!);
            TdLoader.hide();
            context.pushNamed(BillInvoicePath, extra: invoice);
            if (_switchDefaultNumberNotifier.value) {
              sp.save(Keys.defaultDataNumber, _phoneTextController.text);
            } else {
              sp.remove(Keys.defaultDataNumber);
            }
          },
          apiFailure: (e, _) {
            TdLoader.hide();
            Toast.error(
              InterSwitchErrorService.it.extract(
                context,
                ApiExceptions.getErrorMessage(e),
              ),
              context,
            );
          },
        );
      }
    });
  }
}
