import 'package:flutter/material.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/res/extensions/extensions.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_commons_flutter/models/retailer.dart';

class WalletBalanceChip extends StatelessWidget {
  const WalletBalanceChip({
    super.key,
    required RetailOutlet retailOutlet,
  }) : _retailOutlet = retailOutlet;

  final RetailOutlet _retailOutlet;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return SizedBox(
      height: 36,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(42),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: RichText(
            text: TextSpan(
                text: 'Wallet balance: ',
                children: [
                  TextSpan(
                      text: CurrencyItem.currencySymbol(
                          context, _retailOutlet.currencyCode),
                      style: theme.labelLarge
                          ?.copyWith(fontFamily: DEFAULT_SYMBOL_FONT)),
                  TextSpan(
                    text: CurrencyItem.formattedAmount(
                        context, _retailOutlet.walletAccount?.currentBalance ?? 0),
                  )
                ],
                style: theme.labelLarge),
          ),
        ),
      ),
    );
  }
}
