import 'package:flutter/material.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/airtime_view.dart';
import 'package:shop/app/biller/presentation/ui/screens/airtime_data/widgets/data_view.dart';
import 'package:shop/src/components/src/buttons/src/k_back_icon_button.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';

class MobileAirtimeData extends StatefulWidget {
  const MobileAirtimeData({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MobileAirtimeData();
  }
}

class _MobileAirtimeData extends State<MobileAirtimeData>
    with SingleTickerProviderStateMixin {
  late final theme = Theme.of(context).textTheme;
  final GlobalKey<AirtimeViewState> _airtimeKey = GlobalKey();
  final GlobalKey<DataViewState> _dataKey = GlobalKey();
  late final TabController _tabController =
      TabController(length: 2, vsync: this);
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      child: Scaffold(
        appBar: AppBar(
          title: Text("Airtime & Data"),
          leading: KBackIconButton(),
          elevation: 0.5,
          actions: [
            Padding(
              padding: EdgeInsets.all(10),
              child: InkWell(
                child: SizedBox(
                  width: 84,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(38),
                    ),
                    child: Center(
                      child: Text(
                        'Buy',
                        textAlign: TextAlign.center,
                        style: theme.labelLarge,
                      ),
                    ),
                  ),
                ),
                onTap: () {
                  if (_tabController.index == 0) {
                    _airtimeKey.currentState?.buy();
                  } else {
                    _dataKey.currentState?.buy();
                  }
                },
              ),
            ),
            XSpacing(7)
          ],
        ),
        body: NestedScrollView(
          headerSliverBuilder: _buildHeaderSlivers,
          body: TabBarView(controller: _tabController, children: [
            Builder(
              builder: (BuildContext context) {
                return Padding(
                  padding: screenPadding,
                  child: AirtimeView(key: _airtimeKey),
                );
              },
            ),
            Builder(
              builder: (BuildContext context) {
                return Padding(
                  padding: screenPadding,
                  child: DataView(
                    key: _dataKey,
                  ),
                );
              },
            ),
          ]),
        ),
      ),
    );
  }

  List<Widget> _buildHeaderSlivers(
      BuildContext context, bool innerBoxIsScrolled) {
    return <Widget>[
      SliverOverlapAbsorber(
        handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
        sliver: SliverPersistentHeader(
          pinned: true,
          delegate: _TabBarDelegate(_tabController),
        ),
      ),
    ];
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabController tabController;
  _TabBarDelegate(this.tabController);

  @override
  double get minExtent => 68;

  @override
  double get maxExtent => 68;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return SizedBox(
      height: 68,
      child: Padding(
        padding: EdgeInsets.only(bottom: 20, top: 20),
        child: DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
              padding: EdgeInsets.all(3),
              child: TabBar(
                controller: tabController,
                tabs: [Text('Airtime'), Text('Data')],
              )),
        ),
      ),
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
