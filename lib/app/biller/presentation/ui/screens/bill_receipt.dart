import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/domain/use_cases/load_receipt.dart';
import 'package:shop/app/biller/presentation/logic/fetch_receipt_cubit.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

class BillReceipt extends StatefulWidget {
  final String ref;
  final String? pin;
  const BillReceipt(this.ref, {super.key, this.pin});

  @override
  State<StatefulWidget> createState() {
    return _BillReceipt();
  }
}

class _BillReceipt extends State<BillReceipt> {
  ChargeReceipt? data;
  late final Map<String, String> receipt = {
    if (data!.customerName?.isNotEmpty ?? false)
      "Customer Name": data!.customerName!,
    if (data!.category?.isNotEmpty ?? false) "Payment Type": data!.category!,
    "Date & Time": DateFormat().format(data!.createdAt),
    if (data!.merchant?.isNotEmpty ?? false) "Merchant": data!.merchant!,
    if (data!.customerId?.isNotEmpty ?? false) "Customer ID": data!.customerId!,
    //"Meter Number": "",
    if (widget.pin?.isNotEmpty ?? false) "Received Token": widget.pin!,
    "Amount": "${data!.amount}",
    if (data!.transactionRef?.isNotEmpty ?? false)
      "Reference": data!.transactionRef ?? ""
  };
  late final Map<String, String> utilityReceipt = {
    if (data!.customerName?.isNotEmpty ?? false)
      "Customer Name": data!.customerName!,
    if (data!.category?.isNotEmpty ?? false) "Payment Type": data!.category!,
    "Date & Time": DateFormat().format(data!.createdAt),
    if (data!.merchant?.isNotEmpty ?? false) "Merchant": data!.merchant!,
    if (data!.customerId?.isNotEmpty ?? false)
      "Meter Number": data!.customerId!,
    //"Meter Number": "",
    if (widget.pin?.isNotEmpty ?? false) "Received Token": widget.pin!,
    "Amount": "${data!.amount}",
    if (data!.transactionRef?.isNotEmpty ?? false)
      "Reference": data!.transactionRef ?? ""
  };

  late final Map<String, String> airtimeReceipt = {
    if (data!.customerName?.isNotEmpty ?? false)
      "Customer Name": data!.customerName!,
    if (data!.category?.isNotEmpty ?? false) "Payment Type": data!.category!,
    "Date & Time": DateFormat().format(data!.createdAt),
    if (data!.merchant?.isNotEmpty ?? false) "Merchant": data!.merchant!,
    if (data!.customerId?.isNotEmpty ?? false)
      "Phone Number": data!.customerId!,
    "Amount": "${data!.amount}",
    if (data!.transactionRef?.isNotEmpty ?? false)
      "Reference": data!.transactionRef ?? ""
  };

  late final billReceipt = data!.categoryId == "1"
      ? utilityReceipt
      : data!.categoryId == "3"
          ? airtimeReceipt
          : receipt;

  bool _loading = true;
  String? errMsg;

  @override
  void initState() {
    loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(left: 15, bottom: 30, right: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(kSvgLogo,
                  width: 70.0, height: 70, alignment: Alignment.centerLeft),
              YSpacing(20),
              Text(
                "Payment Receipt",
                style: KTextStyle.regular14.copyWith(fontSize: 20),
              ),
              YSpacing(20),
              _loading
                  ? Center(
                      child: KLoader(),
                    )
                  : (data?.id.isEmpty ?? true)
                      ? Center(
                          child: Text(
                              errMsg ?? "Error Occurred During This Process"),
                        )
                      : Column(
                          children: billReceipt.entries
                              .map(
                                (e) => Column(
                                  children: [
                                    ListTile(
                                      title: Row(
                                        children: [
                                          Text(
                                            e.key,
                                            maxLines: 1,
                                            style: KTextStyle.medium14
                                                .copyWith(fontSize: 16),
                                          ),
                                          XSpacing(10),
                                          Flexible(
                                            child: Align(
                                              alignment: Alignment.centerRight,
                                              child: e.key.startsWith("Amount")
                                                  ? CurrencyItem(
                                                      num.parse(e.value),
                                                      data!.currencyCode,
                                                      amountStyle: KTextStyle
                                                          .regular14
                                                          .copyWith(
                                                              fontSize: 16),
                                                      symbolStyle: KTextStyle
                                                          .regular14
                                                          .copyWith(
                                                              fontSize: 16),
                                                    )
                                                  : Text(
                                                      e.value,
                                                      style: KTextStyle
                                                          .regular14
                                                          .copyWith(
                                                              fontSize: 16),
                                                      textAlign:
                                                          TextAlign.right,
                                                    ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(
                                      height: 1,
                                      thickness: 1,
                                    )
                                  ],
                                ),
                              )
                              .toList(),
                        ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.all(10),
        child: BlocConsumer<FetchReceiptCubit, CubitState<String?>>(
            builder: (context, state) => KButton(
                  onPressed: () => context
                      .read<FetchReceiptCubit>()
                      .fetchReceipt(widget.ref),
                  text: "Share",
                  isDisabled: _loading || (data?.id.isEmpty ?? true),
                  isLoading: state.maybeWhen(
                      orElse: () => false, loading: (val) => val),
                ),
            listener: (context, state) {
              state.maybeWhen(
                  orElse: () {},
                  error: (E) => Toast.error(E, context),
                  completed: (data) {
                    if (data != null) {
                      final RenderBox box =
                          context.findRenderObject() as RenderBox;
                      Share.shareXFiles([
                        XFile(
                          data,
                          mimeType: 'application/pdf',
                        )
                      ],
                          subject: '${this.data!.category!} Bill Receipt',
                          text: data.substring(data.lastIndexOf("/") + 1),
                          sharePositionOrigin:
                              box.localToGlobal(Offset.zero) & box.size);
                    }
                  });
            }),
      ),
    );
  }

  void loadData() async {
    final res = await locator
        .get<LoadReceipt>()
        .fetch(widget.ref, UserCubit.instance?.currentOutlet?.id ?? "");
    res.when(
        success: (data) => this.data = data,
        apiFailure: (e, _) => errMsg = ApiExceptions.getErrorMessage(e));
    _loading = false;
    setState(() {});
  }
}
