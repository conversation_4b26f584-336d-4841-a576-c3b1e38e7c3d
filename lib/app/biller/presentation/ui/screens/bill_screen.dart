import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/domain/use_cases/get_category_bills.dart';
import 'package:shop/app/biller/presentation/ui/modals/bill_category_modal.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class BillScreen extends StatefulWidget {
  const BillScreen({super.key});

  @override
  _BillScreenState createState() => _BillScreenState();
}

class _BillScreenState extends State<BillScreen> {
  List<BillCategory> _categories = [];
  bool _loading = false;
  String _error = '';
  late final Environment flavor;

  @override
  void initState() {
    super.initState();
    flavor = context.read<AppConfig>().environment ?? Environment.dev;
    _load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            const SizedBox(height: 20),
            Expanded(
              child: _buildItems(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItems() {
    if (_loading) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          children: [
            Text(_error),
            const SizedBox(height: 7),
            TextButton(
              onPressed: _load,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_categories.isEmpty) {
      return Center(
        child: Column(
          children: [
            Text("There are no categories available at the moment"),
            const SizedBox(height: 7),
            TextButton(
              onPressed: _load,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      itemBuilder: (BuildContext context, int index) {
        final item = _categories[index];

        /*final placeHolder = CircleAvatar(
          child: Text(item.name[0].toUpperCase()),
          radius: 16,
        );*/

        return InkWell(
          onTap: () {
            BillCategoryModal.show(
              context,
              id: item.id,
              title: item.name,
              subtitle: item.subtitle,
            );

            Segment.track(
              eventName: SegmentEvents.billPayCategoryClicked,
              properties: {
                'category_id': item.id,
                'category_name': item.name,
              },
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            child: Row(
              children: [
                SvgPicture.network(flavor == Environment.dev
                    ? "$devS3Bucket${item.id}.svg"
                    : "$prodS3Bucket${item.id}.svg"),
                /*   CachedNetworkImage(
                  imageUrl: item.image,
                  progressIndicatorBuilder: (_, __, ___) => placeHolder,
                  errorWidget: (_, __, ___) => placeHolder,
                  height: 32,
                  width: 32,
                ),*/
                const SizedBox(width: 12),
                Text(item.name),
              ],
            ),
          ),
        );
      },
      separatorBuilder: (_, __) => Divider(height: 0),
      itemCount: _categories.length,
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Stack(
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: KBackButton(
            black: true,
            legacy: true,
          ),
        ),
        Center(
          child: Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Text(
              'Pay bills',
              style: textStyleRegular(
                context,
                fontSize: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _load() async {
    setState(() {
      _loading = true;
      _error = '';
    });

    final res = await locator.get<GetCategoryBills>().call(NoParams());

    _loading = false;

    res.when(
      success: (data) {
        setState(() {
          _categories = data.where((element) => !(element.isAirtime)).toList();
        });
      },
      apiFailure: (e, _) {
        setState(() {
          _error = ApiExceptions.getErrorMessage(e);
        });
      },
    );
  }
}
