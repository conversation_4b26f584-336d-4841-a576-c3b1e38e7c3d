import 'package:flutter/material.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/presentation/ui/widgets/biller_item.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/src/components/src/buttons/src/k_back_icon_button.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';

class MobileBills extends StatefulWidget {
  final BillCategory billCategory;

  const MobileBills({super.key, required this.billCategory});
  @override
  State<StatefulWidget> createState() {
    return _MobileBills();
  }
}

class _MobileBills extends State<MobileBills> {
  late final ValueNotifier<List<Biller>> _billerNotifier =
      ValueNotifier(widget.billCategory.billers!);
  @override
  void dispose() {
    _billerNotifier.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: KBackIconButton(),
        title: Text(widget.billCategory.name),
      ),
      body: Padding(
        padding: screenPadding,
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: SearchTextField(
                  onSearch: _onSearch, hintText: "Search by  biller name"),
            ),
            SliverPadding(
              padding: EdgeInsets.only(bottom: 40),
            ),
            ValueListenableBuilder<List<Biller>>(
                valueListenable: _billerNotifier,
                builder: (context, billers, _) {
                  if (billers.isEmpty) {
                    return SliverToBoxAdapter(
                      child: Text("No biller matches your search"),
                    );
                  }
                  return SliverGrid(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final biller = billers[index];
                        return BillerItem(biller: biller);
                      },
                      childCount: billers.length,
                    ),
                    gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                        maxCrossAxisExtent: 70,
                        mainAxisSpacing: 40,
                        crossAxisSpacing: 20,
                        mainAxisExtent: 70),
                  );
                })
          ],
        ),
      ),
    );
  }

  void _onSearch(String searchWord) {
    if (searchWord.isEmpty) {
      _billerNotifier.value = widget.billCategory.billers!;
    } else {
      _billerNotifier.value = widget.billCategory.billers!
          .where(
            (element) =>
                element.name.toLowerCase().contains(searchWord.toLowerCase()),
          )
          .toList();
    }
  }
}
