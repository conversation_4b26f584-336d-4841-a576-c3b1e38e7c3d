import 'package:flutter/cupertino.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/presentation/ui/screens/bills/mobile/mobile_bills.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class Bills extends StatelessWidget {
  final BillCategory billCategory;

  const Bills({super.key, required this.billCategory});

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      smallScreen: MobileBills(billCategory: billCategory),
    );
  }
}
