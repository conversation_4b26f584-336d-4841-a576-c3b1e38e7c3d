import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safe_insets/index.dart';
import 'package:shop/app/biller/data/models/bill_argument.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_pin_screen.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/form/src/text_field_2.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_src/scaler/src/extensions.dart';

class AirtimeScreen extends StatefulWidget {
  const AirtimeScreen({super.key});

  @override
  _AirtimeScreenState createState() => _AirtimeScreenState();
}

class _AirtimeScreenState extends State<AirtimeScreen> {
  num _minAmount = 50;

  Biller? _selectedBill;
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();

  bool _sending = false;

  List<PaymentItem> _options = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<AirtimeCubit>(context)
        .loadAirtime(context.read<AppConfig>().environment!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  const SizedBox(height: 20),
                  _buildItems(),
                  const SizedBox(height: 30),
                  _buildForm(),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 10,
              ),
              child: SafeAreaWrap(
                KButton(
                  onPressed: _sending ? null : _proceed,
                  isLoading: _sending,
                  text: 'Next',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _proceed() async {
    // validate
    if (_selectedBill == null) {
      Toast.error('Please select a network', context);
      return;
    }

    String phone = _phoneController.text.replaceAll(' ', '');
    if (phone.startsWith('0')) {
      phone = phone.replaceFirst('0', '+234');
    }
    if (phone.startsWith('234')) {
      phone = '+$phone';
    }

    if (phone.startsWith('+')) {
      // Remove + at the beginning
      // requested by backend
      phone = phone.replaceFirst('+', '');
    }

    if (phone.isEmpty) {
      Toast.error('Please enter a phone number', context);
      return;
    }

    if (phone.length != 13) {
      Toast.error('Invalid phone number', context);
      return;
    }

    final inputAmount = _amountController.text;

    if (inputAmount.isEmpty) {
      Toast.error('Please enter an amount', context);
      return;
    }

    late final num amount;
    try {
      amount = num.parse(inputAmount.replaceAll(',', ''));
    } catch (e) {
      Toast.error('Invalid amount', context);
      return;
    }

    if (amount < 1) {
      Toast.error('Please enter a valid amount', context);
      return;
    }

    if (amount < _minAmount) {
      Toast.error('Min airtime amount is NGN$_minAmount', context);
      return;
    }

    if (_options.isEmpty) {
      await _loadOptions(true);
      if (_options.isEmpty) return;
    }

    Segment.track(
      eventName: SegmentEvents.airtimePayInitiated,
      properties: {
        'network': _selectedBill?.billerName,
        'phone_number': phone,
        'amount': amount,
        'biller': _selectedBill?.toMap(),
      },
    );

    BillPinScreen.show(
      context,
      BillArgument(
        amount: amount,
        biller: _selectedBill!,
        input: phone,
        name: _phoneController.text,
        paymentCode: _options.first.paymentCode,
      ),
    );
  }

  Widget _buildForm() {
    return Column(
      children: [
        TextField2(
          amount: true,
          controller: _amountController,
          title: 'Enter amount',
        ),
        const SizedBox(height: 10),
        TextField(
          keyboardType: TextInputType.numberWithOptions(),
          style: TextStyle(
            fontSize: context.fontSize(14),
          ),
          controller: _phoneController,
          decoration: InputDecoration(
            hintText: 'Phone number',
            contentPadding: const EdgeInsets.only(
              left: 16,
              right: 16,
              top: 26,
              bottom: 10,
            ),
            enabledBorder: KTextStyle.outlineInputBorder,
            focusedBorder: KTextStyle.outlineInputBorder,
            border: KTextStyle.outlineInputBorder,
           /* suffixIcon: kIsWeb
                ? null
                : InkWell(
                    onTap: () async {
                      return showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            content: SizedBox(
                              height: 180,
                              width: double.maxFinite,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Access to your contacts is required to complete this action. We take data privacy seriously and will not be shared with third parties. Do you want to proceed?",
                                    style: KTextStyle.bodyText2.copyWith(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: Text('NO'),
                              ),
                              TextButton(
                                onPressed: () async {
                                  Navigator.of(context).pop();
                                  final contact = await FlutterContactPicker
                                      .pickPhoneContact();
                                  if (contact.phoneNumber != null) {
                                    _phoneController.text = contact
                                        .phoneNumber!.number
                                        .toString()
                                        .replaceAll(' ', '');
                                  }
                                },
                                child: Text('YES'),
                              ),
                            ],
                            titleTextStyle:
                                KTextStyle.semiBold24.copyWith(fontSize: 20),
                            contentTextStyle:
                                KTextStyle.bodyText2.copyWith(fontSize: 16),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8)),
                            contentPadding: EdgeInsets.only(
                                left: 30, bottom: 15, right: 30),
                            titlePadding:
                                EdgeInsets.only(left: 30, top: 15, right: 30),
                            actionsPadding: EdgeInsets.zero,
                          );
                        },
                      ); // contact.phoneNumber
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: SvgPicture.asset(
                        kSvgContactIcon,
                      ),
                    ),
                  ),*/
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[\d+]')),
          ],
        ),
      ],
    );
  }

  Widget _buildItems() {
    return BlocBuilder<AirtimeCubit, AirtimeState>(builder: (_, state) {
      if (state is AirtimeError) {
        return Center(
          child: Text(state.message),
        );
      }

      if (state is AirtimeLoaded) {
        _minAmount = state.minAmount;
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: state.bills.map((e) => _button(e)).toList(),
        );
      }

      return Center(
        child: CircularProgressIndicator(),
      );
    });
  }

  Widget _button(Biller bill) {
    return GestureDetector(
      onTap: () {
        _select(bill);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: _selectedBill == bill
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
          ),
        ),
        child: CachedNetworkImage(
          imageUrl: bill.image,
          progressIndicatorBuilder: (_, __, ___) => Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
          errorWidget: (_, __, ___) => Text(bill.name),
          height: 50,
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: KBackButton(
                black: true,
                legacy: true,
              ),
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(top: 20),
                child: Text(
                  'Buy Airtime',
                  style: textStyleRegular(
                    context,
                    fontSize: 20,
                  ),
                ),
              ),
            ),
          ],
        ),
        Text(
          "Choose a network",
          textAlign: TextAlign.center,
          style: textStyleRegular(
            context,
          ),
        ),
      ],
    );
  }

  void _select(Biller bill) async {
    if (bill == _selectedBill) return;

    setState(() {
      _selectedBill = bill;
      _options = [];
    });

    _loadOptions(false);
  }

  Future<void> _loadOptions(bool indicate) async {
    if (_selectedBill == null) return;

    if (indicate) {
      setState(() {
        _sending = true;
      });
    }

    //  load options
    final res = await BlocProvider.of<AirtimeCubit>(context).loadItems(
      _selectedBill!,
      context,
      indicate,
    );

    if (res == null) {
      // failed
    } else {
      _options = res;
    }

    if (indicate) {
      setState(() {
        _sending = false;
      });
    }
  }
}
