import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/data/models/bill_argument.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/use_cases/charge_bill.dart';
import 'package:shop/app/biller/presentation/ui/modals/bill_payment_error_modal.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_completed_screen.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/form/src/k_passcode_field.dart';
import 'package:shop/src/components/src/widgets/k_keyboard.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

const insufficientFundsErrMsg = "Insufficient Balance";

class BillPinScreen extends StatefulWidget {
  final BillArgument argument;

  const BillPinScreen({
    super.key,
    required this.argument,
  });

  static Future<void> show(BuildContext context, BillArgument argument) {
    return Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BillPinScreen(
          argument: argument,
        ),
      ),
    );
  }

  @override
  _BillPinScreenState createState() => _BillPinScreenState();
}

class _BillPinScreenState extends State<BillPinScreen> {
  bool _sending = false;
  final _passcodeCtrl = TextEditingController();

  late final int length =
      Provider.of<UserCubit>(context).currentUser?.pinLength ?? 4;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            _buildSubtitle(),
            Expanded(
              child: KKeyPad(
                activatePeriod: false,
                onKeyTap: _addToText,
                onRemoveTap: _removeLastDigit,
                disabled: _sending,
              ),
            ),
            YMargin(10),
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 10,
              ),
              child: KButton(
                showIcon: false,
                onPressed: _sending ? null : _proceed,
                text: 'Send',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _proceed() async {
    final pin = _passcodeCtrl.text;
    if (pin.length != length) {
      Toast.error('Please enter your pin', context);
      return;
    }

    setState(() {
      _sending = true;
    });

    TdLoader.show(context);

    final res = await locator.get<ChargeBill>().call(
          ChargeParam(
            retailOutletId: UserCubit.instance?.currentOutlet?.id ?? '',
            pin: pin,
            amount: widget.argument.amount,
            paymentCode: widget.argument.paymentCode,
            customerName: widget.argument.customerName ?? "",
            customerId: widget.argument.input,
            billerId: widget.argument.biller.billerId,
            billerName: widget.argument.biller.billerName,
            categoryId: widget.argument.biller.categoryId,
            categoryName: widget.argument.biller.categoryName,
            customerPhone: widget.argument.customerPhone,
          ),
        );

    TdLoader.hide();

    res.when(
      success: (ChargeResponse e) {
        String title = "You have successfully purchased\n"
            "NGN ${widget.argument.total} "
            "for ${widget.argument.name}";

        String eventName = '';

        if (widget.argument.biller.isAirtime) {
          title = "Your airtime purchase was\nsuccessful";
          eventName = SegmentEvents.airtimePayCompleted;
        } else {
          eventName = SegmentEvents.billPayCompleted;
        }

        Segment.track(
          eventName: eventName,
          properties: {
            'amount': widget.argument.amount,
            'payment_code': widget.argument.paymentCode,
            'customer_id': widget.argument.input,
            'biller_id': widget.argument.biller.billerId,
            'biller_name': widget.argument.biller.billerName,
            'category_id': widget.argument.biller.categoryId,
            'category_name': widget.argument.biller.categoryName,
            'customer_phone': widget.argument.customerPhone,
            'charges': widget.argument.charges,
            'status': e.status,
          },
        );

        BillCompletedScreen.show(
          context,
          title,
          e,
        );
      },
      apiFailure: (e, _) async {
        final message = ApiExceptions.getErrorMessage(e);

        if (_ == 422) {
          Toast.error(message, context);
          return;
        }
        final retry = await BillPaymentErrorModal.displayModal(
          context,
          message.contains(RegExp(r'\d'))
              ? InterSwitchErrorService.it.extract(context, message)
              : message,
          _ == 400 && message == insufficientFundsErrMsg,
        );

        if (retry == true) {
          _proceed();
        }
      },
    );

    setState(() {
      _sending = false;
    });
  }

  Widget _buildSubtitle() {
    return Column(
      children: [
        YMargin(40),
        Text(
          'Enter your transaction PIN',
          style: textStyleRegular(
            context,
            fontSize: 16,
          ),
        ),
        YMargin(20),
        Center(
          child: IgnorePointer(
            ignoring: true,
            child: KCodeInput(
              controller: _passcodeCtrl,
              length: length,
              builder: CodeInputBuilders.lightCircle(
                context: context,
                emptyRadius: 10,
                filledRadius: 10,
                totalRadius: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Stack(
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: KBackButton(
            black: true,
            legacy: true,
          ),
        ),
        Center(
          child: Padding(
            padding: const EdgeInsets.only(top: 25),
            child: RichText(
              text: _headerText(),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  TextSpan _headerText() {
    if (widget.argument.biller.isAirtime) {
      return TextSpan(
        children: [
          TextSpan(text: 'Buying '),
          TextSpan(
            text: '₦',
            style: TextStyle(fontFamily: 'Roboto'),
          ),
          TextSpan(text: '${widget.argument.total} '),
          TextSpan(text: widget.argument.biller.name),
          TextSpan(text: ' airtime for\n'),
          TextSpan(text: widget.argument.name),
        ],
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 16,
              color: Colors.black,
            ),
      );
    }
    return TextSpan(
      children: [
        TextSpan(text: 'Paying '),
        TextSpan(
          text: '₦',
          style: TextStyle(fontFamily: 'Roboto'),
        ),
        TextSpan(text: widget.argument.total),
        TextSpan(text: ' to\n'),
        TextSpan(text: widget.argument.biller.name),
        TextSpan(text: '\n'),
        TextSpan(
          text: widget.argument.name,
          style: TextStyle(
            color: Theme.of(context).disabledColor,
          ),
        ),
      ],
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: 16,
            color: Colors.black,
          ),
    );
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }
    // auto submit pin
    if (_passcodeCtrl.text.length == length) {
      _proceed();
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }
  }
}
