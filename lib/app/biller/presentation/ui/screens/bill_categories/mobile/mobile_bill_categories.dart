import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/domain/use_cases/get_category_bills.dart';
import 'package:shop/app/biller/presentation/ui/screens/bills/bills.dart';
import 'package:shop/app/biller/presentation/ui/widgets/biller_item.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/app/payments/presentation/ui/widgets/search_text_field.dart';
import 'package:shop/src/components/src/buttons/src/k_back_icon_button.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class MobileBillCategories extends StatefulWidget {
  const MobileBillCategories({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MobileBillCategories();
  }
}

class _MobileBillCategories extends State<MobileBillCategories> {
  final ValueNotifier<List<BillCategory>> _categoryNotifier =
      ValueNotifier(_cachedCategories ?? []);
  final ValueNotifier<CubitState<List<BillCategory>>> _stateCategoryNotifier =
      ValueNotifier(_cachedCategories == null
          ? CubitState.init()
          : CubitState.completed(model: _cachedCategories!));
  late final flavor = context.read<AppConfig>().environment ?? Environment.dev;

  static List<BillCategory>? _cachedCategories;

  @override
  void dispose() {
    _categoryNotifier.dispose();
    _stateCategoryNotifier.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _load();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: AppBar(
        leading: KBackIconButton(),
        title: Text('Choose a Service'),
      ),
      body: Padding(
        padding: screenPadding,
        child: ValueListenableBuilder<CubitState<List<BillCategory>>>(
            valueListenable: _stateCategoryNotifier,
            builder: (context, state, _) {
              return state.when(
                  init: () => SizedBox.shrink(),
                  loading: (loading) => Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      ),
                  completed: (billCategories) {
                    if (billCategories.isEmpty) {
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                              "There are no categories available at the moment"),
                          const SizedBox(height: 7),
                          TextButton(
                            onPressed: _load,
                            child: Text('Retry'),
                          ),
                        ],
                      );
                    }
                    return CustomScrollView(
                      slivers: [
                        SliverToBoxAdapter(
                          child: SearchTextField(
                              onSearch: _onSearch,
                              hintText: "Search by  biller name"),
                        ),
                        SliverPadding(
                          padding: EdgeInsets.only(bottom: 40),
                        ),
                        ValueListenableBuilder<List<BillCategory>>(
                          valueListenable: _categoryNotifier,
                          builder: (context, categories, _) {
                            if (categories.isEmpty) {
                              return SliverToBoxAdapter(
                                child: Text("No category matches your search"),
                              );
                            }
                            final crossAxisCount =
                                ((MediaQuery.of(context).size.width - 40) / 85)
                                    .truncate();
                            return SliverFixedExtentList(
                                delegate: SliverChildBuilderDelegate(
                                  (context, index) {
                                    final category = categories[index];
                                    return Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            SvgPicture.network(flavor ==
                                                    Environment.dev
                                                ? "$devS3Bucket${category.id}.svg"
                                                : "$prodS3Bucket${category.id}.svg"),
                                            const SizedBox(width: 4),
                                            Text(category.name,
                                                style: theme.bodySmall),
                                            Spacer(),
                                            if ((category.billers!.length) >
                                                crossAxisCount)
                                              InkWell(
                                                child: Text("See all",
                                                    style: theme.bodySmall
                                                        ?.copyWith(
                                                            color: Theme.of(
                                                                    context)
                                                                .colorScheme
                                                                .primary)),
                                                onTap: () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => Bills(
                                                      billCategory: category,
                                                    ),
                                                  ),
                                                ),
                                              )
                                          ],
                                        ),
                                        YSpacing(12),
                                        Divider(),
                                        YSpacing(40),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: category.billers!
                                              .getRange(
                                                  0,
                                                  (category.billers!.length) >
                                                          crossAxisCount
                                                      ? crossAxisCount
                                                      : category
                                                          .billers!.length)
                                              .map(
                                                (e) => Padding(
                                                  padding: EdgeInsets.only(
                                                      right: 15),
                                                  child: BillerItem(biller: e),
                                                ),
                                              )
                                              .toList(),
                                        ),
                                      ],
                                    );
                                  },
                                  childCount: categories.length,
                                ),
                                itemExtent: 200);
                          },
                        )
                      ],
                    );
                  },
                  error: (error) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(error),
                        const SizedBox(height: 7),
                        TextButton(
                          onPressed: _load,
                          child: Text('Retry'),
                        ),
                      ],
                    );
                  });
            }),
      ),
    );
  }

  void _onSearch(String searchWord) {
    if (searchWord.isEmpty) {
      _categoryNotifier.value = _stateCategoryNotifier.value
          .maybeWhen(orElse: () => [], completed: (categories) => categories);
    } else {
      _categoryNotifier.value = _stateCategoryNotifier.value.maybeWhen(
        orElse: () => [],
        completed: (categories) =>
            categories.fold<List<BillCategory>>([], (previousValue, element) {
          final searchBillers = element.billers!
              .where(
                (element) => element.name
                    .toLowerCase()
                    .contains(searchWord.toLowerCase()),
              )
              .toList();
          return searchBillers.isEmpty
              ? previousValue
              : [
                  ...previousValue,
                  BillCategory(element.id, element.name, element.description,
                      searchBillers)
                ];
        }),
      );
    }
  }

  void _load() async {
    if (_cachedCategories != null) return;
    _stateCategoryNotifier.value = CubitState.loading(loading: true);
    final res = await locator.get<GetCategoryBills>().call(NoParams());

    res.when(success: (data) {
      if (mounted) {
        _categoryNotifier.value =
            data.where((element) => !(element.isAirtimeData)).toList();
        _stateCategoryNotifier.value =
            CubitState.completed(model: _categoryNotifier.value);
        _cachedCategories = _categoryNotifier.value;
      }
    }, apiFailure: (e, _) {
      if (mounted) {
        _stateCategoryNotifier.value =
            CubitState.error(errorMessage: ApiExceptions.getErrorMessage(e));
      }
    });
  }
}
