import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/biller/data/exceptions/bill_unvailable_exception.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/invoice_data.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/app/biller/domain/use_cases/validate_bill.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/biller/presentation/ui/modals/bill_plan_modal.dart';
import 'package:shop/app/biller/presentation/ui/modals/service_error_modal.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_back_icon_button.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/extensions/index.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/services/interswitch_error_service.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';
import 'package:shop/src/components/src/form/src/td_form_field.dart';

class MobileBillPlan extends StatefulWidget {
  final Biller biller;

  const MobileBillPlan({super.key, required this.biller});

  @override
  State<StatefulWidget> createState() {
    return _MobileBillPlan();
  }
}

class _MobileBillPlan extends State<MobileBillPlan> {
  final TextEditingController _amountTextController = TextEditingController();
  final TextEditingController _accountTextController = TextEditingController();
  final TextEditingController _planTextController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final ValueNotifier<CubitState<List<PaymentItem>>> _billPlanOptions =
      ValueNotifier(CubitState.init());
  final ValueNotifier<bool> _isFixedAmountNotifier = ValueNotifier(false);
  final TextEditingController _phoneTextController = TextEditingController();

  PaymentItem? selectedPaymentOption;

  @override
  void dispose() {
    _amountTextController.dispose();
    _accountTextController.dispose();
    _phoneTextController.dispose();
    _billPlanOptions.dispose();
    _nameController.dispose();
    _planTextController.dispose();
    _isFixedAmountNotifier.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _loadOptions();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    return AppScreen(
      child: Scaffold(
        appBar: AppBar(
          leading: KBackIconButton(),
          title: Text(widget.biller.name),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: screenPadding,
            child: Column(
              children: [
                TdFormField(
                  label: widget.biller.field1,
                  widget: TextField(
                    style: theme.bodyLarge,
                    inputFormatters: [validInput()],
                    controller: _accountTextController,
                  ),
                ),
                YSpacing(20),
                ValueListenableBuilder<CubitState<List<PaymentItem>>>(
                    valueListenable: _billPlanOptions,
                    builder: (context, options, _) {
                      return TextField(
                        decoration: InputDecoration(
                          hintText: "Select a plan",
                          hintStyle: theme.bodySmall,
                          suffixIconConstraints:
                              BoxConstraints.tightFor(height: 30, width: 40),
                          suffixIcon: options.maybeWhen(
                            orElse: () =>
                                Icon(Icons.keyboard_arrow_down_outlined),
                            loading: (load) => Padding(
                              padding: EdgeInsets.only(right: 10),
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            ),
                            error: (_) => Icon(
                              Icons.error_outline_outlined,
                            ),
                          ),
                        ),
                        controller: _planTextController,
                        style: theme.bodyLarge,
                        onTap: () async {
                          _billPlanOptions.value.maybeWhen(orElse: () {
                            Toast.error(
                                "No plan is available, try again", context);
                            _loadOptions();
                          }, completed: (options) async {
                            if (options.isEmpty) {
                              Toast.error(
                                  "No data plan is available, try again",
                                  context);
                            } else {
                              final res = await BillPlanModal.displayModal(
                                  context, options);
                              if (res == null) return;
                              selectedPaymentOption = res;
                              _planTextController.text = res.paymentItemName;
                              if (res.isAmountFixed) {
                                _isFixedAmountNotifier.value = true;
                                _amountTextController.text =
                                    CurrencyItem.formattedAmount(
                                        context, (res.amount / 100));
                              }
                            }
                          });
                        },
                        readOnly: true,
                      );
                    }),
                YSpacing(20),
                TdFormField(
                  label: "Amount",
                  widget: ValueListenableBuilder<bool>(
                    valueListenable: _isFixedAmountNotifier,
                    builder: (context, isFixed, _) => TextField(
                      decoration: InputDecoration(filled: isFixed),
                      style: theme.bodyLarge,
                      inputFormatters: [
                        //  FilteringTextInputFormatter.allow(RegExp(r'[\d+.]')),
                        CurrencyInputFormatter(decimalPlaces: 2)
                      ],
                      controller: _amountTextController,
                      keyboardType: TextInputType.numberWithOptions(),
                      readOnly: isFixed,
                    ),
                  ),
                ),
                YSpacing(20),
                TdFormField(
                  label: 'Customer Phone number',
                  widget: TextField(
                    decoration: InputDecoration(
    /*                  suffixIconConstraints: BoxConstraints.tight(
                        Size(30, 34),
                      ),
                      suffixIcon: kIsWeb
                          ? null
                          : InkWell(
                              onTap: () async {
                                if (await FlutterContactPicker
                                    .hasPermission()) {
                                  _pickContact();
                                } else {
                                  await ContactPicker.displayDialog(
                                      context, _pickContact);
                                }
                              },
                              child: Padding(
                                child: SvgPicture.asset(kSvgContacts),
                                padding: EdgeInsets.only(right: 12),
                              ),
                            ),*/
                    ),
                    controller: _phoneTextController,
                    keyboardType: TextInputType.numberWithOptions(),
                    style: theme.bodyLarge,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[\d+]')),
                    ],
                  ),
                ),
                YSpacing(20),
                TdFormField(
                  label: "Customer Name",
                  widget: TextField(
                    decoration: InputDecoration(
                      enabledBorder:
                          OutlineInputBorder(borderSide: BorderSide.none),
                      border: OutlineInputBorder(borderSide: BorderSide.none),
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 0, horizontal: 12),
                      focusedBorder:
                          OutlineInputBorder(borderSide: BorderSide.none),
                      filled: true,
                    ),
                    style: theme.bodyLarge,
                    readOnly: true,
                    controller: _nameController,
                  ),
                ),
                YMargin(40),
                KButtonPrimary(
                  text: "Continue",
                  onTap: confirm,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _loadOptions() async {
    if (_billPlanOptions.value == CubitState.loading(loading: true)) return;
    _billPlanOptions.value = CubitState.loading(loading: true);
    try {
      final res = await context.read<AirtimeCubit>().loadItems(
            widget.biller,
            context,
          );
      if (res == null) {
        _billPlanOptions.value =
            CubitState.error(errorMessage: "Failed to fetch data plans");
        Toast.error("Failed to fetch data plans", context);
      } else {
        _billPlanOptions.value = CubitState.completed(model: res);
      }
    } on BillUnavailableException catch (_) {
      if (!mounted) return;
      _billPlanOptions.value =
          CubitState.error(errorMessage: "Failed to fetch data plans");
      await ServiceErrorModal.show(context);
      Navigator.pop(context);
      return;
    }
  }

  Future<void> confirm() async {
    _billPlanOptions.value.maybeWhen(orElse: () {
      _loadOptions();
      Toast.error('Preferred plan has not been selected', context);
    }, completed: (plans) async {
      if (selectedPaymentOption == null) {
        Toast.error('Please select a plan', context);
        return;
      }

      final input = _accountTextController.text;

      if (input.isEmpty) {
        Toast.error('Please enter your ${widget.biller.field1}', context);
        return;
      }
      //  if (!(input.length == 11)) {
      //   Toast.error('Please enter a valid 11 digits meter number', context);
      //   return;
      //  }

      final inputAmount = _amountTextController.text;
      if (inputAmount.isEmpty) {
        Toast.error('Please enter an amount', context);
        return;
      }

      late final num amount;
      try {
        amount = num.parse(inputAmount.replaceAll(',', ''));
      } catch (e) {
        Toast.error('Invalid amount', context);
        return;
      }

      if (amount < 1) {
        Toast.error('Please enter a valid amount', context);
        return;
      }

      final phone = await _phoneTextController.text.isValidNGPhoneNumber;

      if (phone == null) {
        Toast.error("Invalid Phone Number", context);
        return;
      }
      _nameController.text = "";
      TdLoader.show(context);
      Segment.track(
        eventName: SegmentEvents.billPayInitiated,
        properties: {
          'amount': selectedPaymentOption!.amount / 100,
          'biller': widget.biller.toMap(),
          'payment_code': selectedPaymentOption!.paymentCode,
          'input': input,
          'name': selectedPaymentOption!.paymentItemName,
        },
      );

      final res = await locator.get<ValidateBill>().call(
            ValidateParam(
              input: input,
              paymentCode: selectedPaymentOption!.paymentCode,
            ),
          );

      res.when(
        success: (CustomerInfo data) async {
          _nameController.text = data.fullName;
          final invoice = InvoiceData(
              inputOption: input,
              plan: selectedPaymentOption!.paymentItemName,
              amount: amount,
              fee: selectedPaymentOption!.itemFee.koboToNaira(),
              recipientPhone: phone,
              recipientName: data.fullName,
              paymentCode: selectedPaymentOption!.paymentCode,
              billType: BillType.bills,
              biller: widget.biller);
          await Future.delayed(Duration(milliseconds: 2500));
          TdLoader.hide();
          context.pushNamed(BillInvoicePath, extra: invoice);
        },
        apiFailure: (e, _) {
          TdLoader.hide();
          Toast.error(
            InterSwitchErrorService.it.extract(
              context,
              ApiExceptions.getErrorMessage(e),
            ),
            context,
          );
        },
      );
    });
  }

/*  void _pickContact() async {
    final contact = await FlutterContactPicker.pickPhoneContact();
    if (contact.phoneNumber != null) {
      _phoneTextController.text =
          contact.phoneNumber!.number?.replaceAll(' ', '') ?? '';
    }
  }*/
}
