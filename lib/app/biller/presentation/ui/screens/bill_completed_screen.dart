import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import 'bill_receipt.dart';

class BillCompletedScreen extends StatelessWidget {
  final String title;
  final ChargeResponse response;

  const BillCompletedScreen({
    super.key,
    required this.title,
    required this.response,
  });

  static void show(
    BuildContext context,
    String title,
    ChargeResponse response,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BillCompletedScreen(
          title: title,
          response: response,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: response.pending
          ? theme.colorScheme.onPrimaryContainer
          : theme.colorScheme.primary,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(),
                  _buildContent(context),
                  if (response.pin.isNotEmpty) ...[
                    const SizedBox(),
                    _buildTab(context),
                  ],
                  const SizedBox(),
                  const SizedBox(),
                ],
              ),
            ),
            _buildFooter(context),
            const SizedBox(height: 15),
            if (!response.pending) _buildReceipt(context),
            const YSpacing(20)
          ],
        ),
      ),
    );
  }

  Widget _buildReceipt(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: KButton(
        color: Colors.transparent,
        isOutline: true,
        showIcon: false,
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => BillReceipt(
                response.reference,
                pin: response.pin,
              ),
            ),
          );
        },
        text: 'View Receipt',
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final locale = AppLocalizations.of(context)!;

    return Column(
      children: [
        Image.asset(
          response.pending ? kImageBillPending : kImageBillDone,
          height: 100,
        ),
        const YMargin(30),
        Text(
          response.pending
              ? locale.payment_processing
              : locale.payment_successful,
          style: textStyleRegular(
            context,
            fontSize: 24,
          ),
        ),
        const YMargin(16),
        Text(
          response.pending ? locale.processing_description : title,
          style: textStyleRegular(
            context,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: KButton(
        showIcon: false,
        onPressed: () {
          Navigator.of(context).popUntil((route) => route.isFirst);
        },
        text: 'Done',
      ),
    );
  }

  Widget _buildTab(BuildContext context) {
    final locale = AppLocalizations.of(context)!;

    return Center(
      child: GestureDetector(
        onTap: () {
          FlutterClipboard.copy(response.pin).then(
            (value) {
              Toast.success(
                locale.pin_copied,
                context,
                duration: 5,
              );
            },
          );
        },
        child: Container(
          width: MediaQuery.of(context).size.width * 0.73,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Row(
            children: [
              Expanded(
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '${locale.your_pin}: ',
                        style: textStyleRegular(
                          context,
                          fontSize: 16,
                        ),
                      ),
                      TextSpan(
                        text: response.pin,
                        style: textStyleSemiBold(
                          context,
                          fontSize: 16,
                        ),
                      ),
                    ],
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(),
                  ),
                ),
              ),
              SvgPicture.asset(kSvgBillCopy),
            ],
          ),
        ),
      ),
    );
  }
}
