import 'package:flutter/material.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/presentation/ui/screens/bill_plan/bill_plan.dart';
import 'package:shop/src/components/src/widgets/margin.dart';

class BillerItem extends StatelessWidget {
  final Biller biller;

  const BillerItem({super.key, required this.biller});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: SizedBox(
        width: 70,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 50,
              height: 50,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  border: Border.all(),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            YSpacing(4),
            Text(
              biller.name,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => Bill<PERSON><PERSON>(
            biller: biller,
          ),
        ),
      ),
    );
  }
}
