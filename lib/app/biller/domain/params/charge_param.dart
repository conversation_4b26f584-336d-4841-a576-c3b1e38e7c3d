import 'package:shop/src/components/src/utils/utils.dart';

class ChargeParam {
  final String retailOutletId;
  final String pin;
  final num amount;
  final String paymentCode;
  final String customerId;
  final String billerId;
  final String billerName;
  final String categoryId;
  final String customerName;
  final String categoryName;
  final String customerPhone;

  const ChargeParam({
    required this.retailOutletId,
    required this.pin,
    required this.amount,
    required this.paymentCode,
    required this.customerId,
    required this.billerId,
    required this.billerName,
    required this.customerName,
    required this.categoryId,
    required this.categoryName,
    required this.customerPhone,
  });

  Map<String, dynamic> toMap() {
    return {
      "retailOutletId": retailOutletId,
      "pin": pin,
      "amount": amount,
      "paymentCode": paymentCode,
      "customerId": customerId,
      "billerId": billerId,
      "customerName": customerName,
      "billerName": billerName,
      "categoryId": categoryId,
      "categoryName": categoryName,
      "extChannel": getExtChannel(),
      'customerMobile': customerPhone,
    };
  }
}
