import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ChargeBill with UseCases<ApiResult<ChargeResponse>, ChargeParam> {
  ChargeBill(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Charge a biller
  @override
  Future<ApiResult<ChargeResponse>> call(ChargeParam param) {
    return _repo.charge(param);
  }
}
