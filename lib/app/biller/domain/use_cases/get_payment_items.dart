import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetPaymentItems with UseCases<ApiResult<List<PaymentItem>>, String> {
  GetPaymentItems(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Returns a list of nearby agents.
  @override
  Future<ApiResult<List<PaymentItem>>> call(String id) {
    return _repo.getPaymentItems(id);
  }
}
