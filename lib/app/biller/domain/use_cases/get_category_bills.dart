import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetCategoryBills with UseCases<ApiResult<List<BillCategory>>, NoParams> {
  GetCategoryBills(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Returns a list of nearby agents.
  @override
  Future<ApiResult<List<BillCategory>>> call(NoParams? id) {
    return _repo.getCategoryBills();
  }
}
