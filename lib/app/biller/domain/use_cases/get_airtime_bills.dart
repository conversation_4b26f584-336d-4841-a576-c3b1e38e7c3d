import 'package:shop/app/biller/data/models/airtime_response.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetAirtimeBills with UseCases<ApiResult<AirtimeResponse>, String> {
  GetAirtimeBills(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Returns a list of nearby agents.
  @override
  Future<ApiResult<AirtimeResponse>> call(String id) {
    return _repo.getAirtimeBills(id);
  }
}
