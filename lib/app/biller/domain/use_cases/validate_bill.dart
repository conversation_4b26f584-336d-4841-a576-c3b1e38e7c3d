import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ValidateBill with UseCases<ApiResult<CustomerInfo>, ValidateParam> {
  ValidateBill(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Charge a biller
  @override
  Future<ApiResult<CustomerInfo>> call(ValidateParam param) {
    return _repo.validate(param);
  }
}
