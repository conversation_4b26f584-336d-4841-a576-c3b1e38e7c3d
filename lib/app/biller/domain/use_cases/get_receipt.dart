import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:shop/app/pay/domain/repo/pay_repo.dart';
import 'package:td_flutter_core/service_result/service_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class GetReceipt with UseCases<ApiResult<String?>, String> {
  GetReceipt(this._repo);

  /// Instance of [PayRepo].
  final BillerRepo _repo;

  /// Returns agent detail.
  @override
  Future<ApiResult<String?>> call(String id) {
    return _repo.getReceipt(id);
  }
}
