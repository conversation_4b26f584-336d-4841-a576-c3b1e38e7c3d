import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetBills with UseCases<ApiResult<List<Biller>>, String> {
  GetBills(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Returns a list of nearby agents.
  @override
  Future<ApiResult<List<Biller>>> call(String id) {
    return _repo.getCategories(id);
  }
}
