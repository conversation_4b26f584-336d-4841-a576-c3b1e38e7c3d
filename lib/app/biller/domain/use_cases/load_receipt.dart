import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class LoadReceipt with UseCases<ApiResult<ChargeReceipt>, String> {
  LoadReceipt(this._repo);

  /// Instance of [BillerRepo].
  final BillerRepo _repo;

  /// Returns Bill Receipt Data.
  Future<ApiResult<ChargeReceipt>> fetch(String id, String outlet) {
    return _repo.loadReceipt(id, outlet);
  }

  @override
  Future<ApiResult<ChargeReceipt>> call(String params) {
    throw UnimplementedError();
  }
}
