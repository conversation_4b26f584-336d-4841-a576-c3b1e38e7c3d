import 'package:shop/app/biller/data/models/airtime_response.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

abstract class BillerRepo {
  Future<ApiResult<AirtimeResponse>> getAirtimeBills(String id);

  Future<ApiResult<List<BillCategory>>> getCategoryBills();

  Future<ApiResult<List<Biller>>> getCategories(String id);

  Future<ApiResult<List<PaymentItem>>> getPaymentItems(String id);

  Future<ApiResult<ChargeResponse>> charge(ChargeParam param);

  Future<ApiResult<CustomerInfo>> validate(ValidateParam param);

  Future<ApiResult<String?>> getReceipt(String params);

  Future<ApiResult<ChargeReceipt>> loadReceipt(String orderId, String outletId);
}
