import 'package:shop/app/biller/data/data_source/biller_remote_data_source.dart';
import 'package:shop/app/biller/data/data_source/impl/biller_remote_data_source_impl.dart';
import 'package:shop/app/biller/data/repos_impl/biller_repo_impl.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:shop/app/biller/domain/use_cases/charge_bill.dart';
import 'package:shop/app/biller/domain/use_cases/get_airtime_bills.dart';
import 'package:shop/app/biller/domain/use_cases/get_bills.dart';
import 'package:shop/app/biller/domain/use_cases/get_category_bills.dart';
import 'package:shop/app/biller/domain/use_cases/get_payment_items.dart';
import 'package:shop/app/biller/domain/use_cases/get_receipt.dart';
import 'package:shop/app/biller/domain/use_cases/validate_bill.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'domain/use_cases/load_receipt.dart';

void registerBillerDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<BillerRemoteDataSource>(
    () => BillerRemoteDataSourceImpl(
      locator(),
      config.firebaseServiceUrl!,
    ),
  );

  // Repositories
  locator.registerLazySingleton<BillerRepo>(() => BillerRepoImpl(locator()));

  // Use cases
  locator.registerLazySingleton(() => GetAirtimeBills(locator()));

  locator.registerLazySingleton(() => GetCategoryBills(locator()));

  locator.registerLazySingleton(() => GetBills(locator()));

  locator.registerLazySingleton(() => GetReceipt(locator()));

  locator.registerLazySingleton(() => LoadReceipt(locator()));

  locator.registerLazySingleton(() => GetPaymentItems(locator()));

  locator.registerLazySingleton(() => ChargeBill(locator()));

  locator.registerLazySingleton(() => ValidateBill(locator()));
}
