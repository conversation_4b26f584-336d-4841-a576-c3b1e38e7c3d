import 'package:shop/app/biller/data/models/airtime_response.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';

abstract class BillerRemoteDataSource {
  Future<AirtimeResponse> getAirtimeBills(String id);

  Future<List<BillCategory>> getBillCategories();

  Future<List<Biller>> getCategories(String id);

  Future<List<PaymentItem>> getPaymentItems(String id);

  Future<ChargeResponse> charge(ChargeParam param);

  Future<CustomerInfo> validate(ValidateParam param);

  Future<String?> getReceipt(String id);

  Future<ChargeReceipt> loadReceipt(String orderId, String outletId);
}
