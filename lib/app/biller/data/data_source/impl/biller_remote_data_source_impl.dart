import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:shop/app/biller/data/data_source/biller_remote_data_source.dart';
import 'package:shop/app/biller/data/models/airtime_response.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/src/components/src/utils/download_file/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class BillerRemoteDataSourceImpl implements BillerRemoteDataSource {
  static const _categoryPath = 'v3/wallet/get_billers_by_category/:id';
  static const _paymentItemsPath = 'v3/wallet/get_biller_payment_items/:id';
  static const _chargePath = 'v3/wallet/bill_charge';
  static const _validatePath = 'v3/wallet/customer_validation';
  static const _mainCategoryPath = 'v3/wallet/get_all_billers';
  static const _receiptPath = 'v3/wallet/get_transaction';

  final String _type = "claimsUrl";

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request.
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  BillerRemoteDataSourceImpl(this._apiClient, this._firebaseServiceUrl);

  @override
  Future<AirtimeResponse> getAirtimeBills(String id) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'get',
        'path': _categoryPath.replaceFirst(':id', id),
      },
    );

    return AirtimeResponse.fromMap(res.data['data']);
  }

  @override
  Future<List<BillCategory>> getBillCategories() async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'get',
        'path': _mainCategoryPath,
      },
    );
    if (res.data is! Map) {
      throw FirebaseException(
          plugin: 'connect', message: "server connection error");
    }
    return BillCategory.resolveList(res.data['data']);
  }

  @override
  Future<List<Biller>> getCategories(String id) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'get',
        'path': _categoryPath.replaceFirst(':id', id),
      },
    );

    return Biller.resolveList(res.data['data']['billers']);
  }

  @override
  Future<List<PaymentItem>> getPaymentItems(String id) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'get',
        'path': _paymentItemsPath.replaceFirst(':id', id),
      },
    );

    return PaymentItem.resolveList(res.data['data']['paymentitems']);
  }

  @override
  Future<ChargeResponse> charge(ChargeParam param) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'path': _chargePath,
        'data': param.toMap(),
      },
    );

    return ChargeResponse.fromMap(res.data);
  }

  @override
  Future<CustomerInfo> validate(ValidateParam param) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'path': _validatePath,
        'data': param.toMap(),
      },
    );

    return CustomerInfo.fromMap(res.data['data']['Customers'][0]);
  }

  @override
  Future<String?> getReceipt(String id) async {
    final response =
        await _apiClient.post('$_firebaseServiceUrl/shop/v3/proxy', data: {
      "path": "generate-receipt",
      "method": "get",
      "data": "",
      "params": {"reference": id, "type": "bills"},
      "type": _type
    });
    final res = await http.get(Uri.parse(response.data["data"] as String));
    return downloadFile(res.bodyBytes, id);
  }

  @override
  Future<ChargeReceipt> loadReceipt(String orderId, String outletId) async {
    final res = await _apiClient.post('$_firebaseServiceUrl/shop/v3/proxy',
        data: {
          'method': 'get',
          'path': "$_receiptPath?id=$orderId&outletId=$outletId"
        });
    return ChargeReceipt.fromJson(res.data["data"]);
  }
}
