import 'package:equatable/equatable.dart';

class PaymentItem extends Equatable {
  final String categoryId;
  final String billerId;
  final bool isAmountFixed;
  final String paymentItemId;
  final String paymentItemName;
  final num amount;
  final String billerType;
  final String code;
  final String paymentCode;
  final num itemFee;

  const PaymentItem({
    required this.categoryId,
    required this.billerId,
    required this.isAmountFixed,
    required this.paymentItemId,
    required this.paymentItemName,
    required this.amount,
    required this.billerType,
    required this.code,
    required this.paymentCode,
    required this.itemFee,
  });

  factory PaymentItem.fromMap(Map<String, dynamic> item) {
    return PaymentItem(
      categoryId: item['categoryid'] ?? '',
      billerId: item['billerid'] ?? '',
      isAmountFixed: item['isAmountFixed'] ?? false,
      paymentItemId: item['paymentitemid'] ?? '',
      paymentItemName: item['paymentitemname'] ?? '',
      amount: num.parse(item['amount'] ?? '0'),
      billerType: item['billerType'] ?? '',
      code: item['code'] ?? '',
      paymentCode: item['paymentCode'] ?? '',
      itemFee: num.parse(item['itemFee'] ?? '0'),
    );
  }

  static List<PaymentItem> resolveList(List<dynamic> items) {
    return items.map((e) => PaymentItem.fromMap(e)).toList();
  }

  @override
  List<Object?> get props => [];
}
