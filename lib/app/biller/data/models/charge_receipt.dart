class ChargeReceipt {
  final String id;
  final String currency;
  final num amount;
  final String? merchant;
  final String? phoneNumber;
  final String? transactionRef;
  final String? reference;
  final String? transactionType;
  final String? extChannel;
  final String? userId;
  final String? retailOutletId;
  final String? loanRepaymentId;
  final String? category;
  final String currencyCode;
  final String? customerName;
  final String? categoryId;
  final String? payChannel;
  final String? customerId;
  final DateTime createdAt;

  ChargeReceipt(
      this.id,
      this.currency,
      this.amount,
      this.phoneNumber,
      this.reference,
      this.transactionType,
      this.extChannel,
      this.customerName,
      this.categoryId,
      this.transactionRef,
      this.userId,
      this.merchant,
      this.retailOutletId,
      this.category,
      this.currencyCode,
      this.createdAt,
      this.customerId,
      this.loanRepaymentId,
      this.payChannel);

  ChargeReceipt.fromJson(Map<String, dynamic> data)
      : id = data["_id"] as String,
        category = data["category"] as String?,
        categoryId = data["extTransaction"]["categoryId"] as String?,
        customerName = data["extTransaction"]["customerName"] as String?,
        amount = data["amount"] as num,
        phoneNumber = data["phoneNumber"] as String?,
        merchant = data["extTransaction"]["accountName"] as String?,
        customerId = data["extTransaction"]["customerId"] as String?,
        transactionRef = data["extTransaction"]["transactionRef"] as String?,
        reference = data["reference"] as String?,
        transactionType = data["transactionType"] as String?,
        extChannel = data["extChannel"] as String?,
        userId = data["userId"] as String?,
        retailOutletId = data["retailOutletId"] as String?,
        loanRepaymentId = data["loanRepaymentId"] as String?,
        currency = data["currency"]["symbol"] as String,
        currencyCode = data["currency"]["iso"] as String,
        payChannel = data["payChannel"] as String?,
        createdAt = DateTime.parse(data["createdAt"] as String);
}
