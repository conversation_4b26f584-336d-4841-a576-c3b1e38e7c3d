import 'package:equatable/equatable.dart';

class CustomerInfo extends Equatable {
  final String paymentCode;
  final String customerId;
  final String responseCode;
  final String fullName;
  final String amount;
  final String amountType;
  final String amountTypeDescription;

  const CustomerInfo({
    required this.paymentCode,
    required this.customerId,
    required this.responseCode,
    required this.fullName,
    required this.amount,
    required this.amountType,
    required this.amountTypeDescription,
  });

  factory CustomerInfo.fromMap(Map<String, dynamic> item) {
    return CustomerInfo(
      paymentCode: item['paymentCode'] ?? '',
      customerId: item['customerId'] ?? '',
      responseCode: item['responseCode'] ?? '',
      fullName: item['fullName'] ?? '',
      amount: item['amount'] ?? '',
      amountType: item['amountType'] ?? '',
      amountTypeDescription: item['amountTypeDescription'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'paymentCode': paymentCode,
      'customerId': customerId,
      'responseCode': responseCode,
      'fullName': fullName,
      'amount': amount,
      'amountType': amountType,
      'amountTypeDescription': amountTypeDescription,
    };
  }

  @override
  List<Object?> get props => [paymentCode, fullName, amount];
}
