import 'package:equatable/equatable.dart';

class ChargeResponse extends Equatable {
  final String status;
  final String pin;
  final String message;
  final String reference;

  bool get pending => status.toLowerCase().contains('pending');

  bool get successful => status.toLowerCase().contains('success');

  const ChargeResponse({
    required this.status,
    required this.message,
    required this.reference,
    required this.pin,
  });

  factory ChargeResponse.fromMap(Map<String, dynamic> item) {
    return ChargeResponse(
      status: item['status'],
      reference: item['data']?['reference'] ?? "",
      message: item['data']?['message'] ?? '',
      pin: item['data']?['pin'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'data': {
        'message': message,
        'pin': pin,
        'reference': reference,
      },
    };
  }

  @override
  List<Object?> get props => [status, message, pin, reference];
}
