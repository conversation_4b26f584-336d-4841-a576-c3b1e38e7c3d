import 'package:shop/app/biller/data/models/biller.dart';

enum BillType { airtime, data, bills }

class InvoiceData {
  final Biller biller;
  final String paymentCode;
  final String plan;
  final num fee;
  final String name;
  final String inputOption;
  final num amount;
  final BillType billType;
  final String recipientName;
  final String recipientPhone;

  num get totalAmount => amount + fee;

  InvoiceData(
      {required this.biller,
      required this.paymentCode,
      required this.inputOption,
      required this.amount,
      required this.billType,
      this.name = '',
      this.plan = '',
      this.fee = 0,
      this.recipientName = '',
      this.recipientPhone = ''});
}
