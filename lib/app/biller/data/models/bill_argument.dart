import 'package:equatable/equatable.dart';
import 'package:shop/app/biller/data/models/biller.dart';

class BillArgument extends Equatable {
  final num amount;
  final num charges;
  final String input;
  final String name;
  final Biller biller;
  final String? customerName;
  final String paymentCode;
  final String customerPhone;

  num get totalAmount => amount + charges;

  String get total => totalAmount.toStringAsFixed(2);

  const BillArgument({
    required this.amount,
    required this.biller,
    required this.paymentCode,
    required this.input,
    this.customerName,
    required this.name,
    this.customerPhone = '',
    this.charges = 0,
  });

  @override
  List<Object?> get props => [amount, paymentCode];
}
