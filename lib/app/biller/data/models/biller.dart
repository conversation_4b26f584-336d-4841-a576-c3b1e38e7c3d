import 'package:equatable/equatable.dart';

class Biller extends Equatable {
  final String categoryId;
  final String categoryName;
  final String billerId;
  final String billerName;
  final String field1;
  final String field2;

  String get name => isAirtime ? billerName.split(' ').first : billerName;

  String get image =>
      'https://td-prod-img.s3.amazonaws.com/billers/$billerId.png';

  bool get isAirtime => categoryName.toLowerCase().contains('mobile recharge');

  const Biller({
    required this.categoryId,
    required this.categoryName,
    required this.billerId,
    required this.billerName,
    required this.field1,
    required this.field2,
  });

  factory Biller.fromMap(Map<String, dynamic> item) {
    return Biller(
      categoryId: item['categoryid'] ?? '',
      categoryName: item['categoryname'] ?? '',
      billerId: item['billerid'] ?? '',
      billerName: item['billername'] ?? '',
      field1: item['customerfield1'] ?? '',
      field2: item['customerfield2'] ?? '',
    );
  }

  static List<Biller> resolveList(List<dynamic> items) {
    return items.map((e) => Biller.fromMap(e)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'categoryid': categoryId,
      'categoryname': categoryName,
      'billerid': billerId,
      'billername': billerName,
      'customerfield1': field1,
      'customerfield2': field2,
    };
  }

  @override
  List<Object?> get props => [billerId];
}
