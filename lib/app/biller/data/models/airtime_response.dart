import 'package:equatable/equatable.dart';
import 'package:shop/app/biller/data/models/biller.dart';

class AirtimeResponse extends Equatable {
  final num minAmount;
  final List<Biller> billers;

  const AirtimeResponse(this.minAmount, this.billers);

  factory AirtimeResponse.fromMap(Map<String, dynamic> item) {
    return AirtimeResponse(
      item['minAmount'] ?? 50,
      Biller.resolveList(item['billers'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'minAmount': minAmount,
      'billers': billers.map((biller) => biller.toMap()).toList(),
    };
  }

  @override
  List<Object?> get props => [minAmount, billers];
}
