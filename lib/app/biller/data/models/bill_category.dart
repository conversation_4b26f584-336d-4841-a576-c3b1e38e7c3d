import 'package:equatable/equatable.dart';
import 'package:shop/app/biller/data/models/biller.dart';

const devS3Bucket = "https://td-dev-img.s3.amazonaws.com/biller_categories/";
const prodS3Bucket = "https://td-prod-img.s3.amazonaws.com/biller_categories/";

class BillCategory extends Equatable {
  final String id;
  final String name;
  final String description;
  final List<Biller>? billers;

  bool get isAirtime => name.toLowerCase().contains('mobile recharge');

  bool get isAirtimeData =>
      isAirtime ||
      name.toLowerCase().contains('phone bills') ||
      name.toLowerCase().contains('airtime and data');

  bool get isTransport => name.toLowerCase().contains('transport');

  //String get image =>
  //  'https://td-prod-img.s3.amazonaws.com/biller_categories/$id.svg';

  String get subtitle {
    if (isAirtime) return 'Choose a Network';
    if (isTransport) return 'Choose a transport biller';
    return 'Choose a provider';
  }

  const BillCategory(this.id, this.name, this.description, [this.billers]);

  factory BillCategory.fromMap(Map<String, dynamic> item) {
    return BillCategory(item['categoryid'], item['categoryname'],
        item['categorydescription'], Biller.resolveList(item['billers'] ?? []));
  }

  Map<String, dynamic> toMap() {
    return {
      'categoryid': id,
      'categoryname': name,
      'categorydescription': description,
      'billers': billers
    };
  }

  static List<BillCategory> resolveList(List<dynamic> items) {
    return items.map((e) => BillCategory.fromMap(e)).toList();
  }

  @override
  List<Object?> get props => [];
}
