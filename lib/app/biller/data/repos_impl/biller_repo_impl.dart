import 'package:shop/app/biller/data/data_source/biller_remote_data_source.dart';
import 'package:shop/app/biller/data/models/airtime_response.dart';
import 'package:shop/app/biller/data/models/bill_category.dart';
import 'package:shop/app/biller/data/models/biller.dart';
import 'package:shop/app/biller/data/models/charge_receipt.dart';
import 'package:shop/app/biller/data/models/charge_response.dart';
import 'package:shop/app/biller/data/models/customer_info.dart';
import 'package:shop/app/biller/data/models/payment_item.dart';
import 'package:shop/app/biller/domain/params/charge_param.dart';
import 'package:shop/app/biller/domain/params/validate_param.dart';
import 'package:shop/app/biller/domain/repos/biller_repo.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class BillerRepoImpl implements BillerRepo {
  /// Instance of [BillerRemoteDataSource].
  final BillerRemoteDataSource _remoteDataSource;

  const BillerRepoImpl(this._remoteDataSource);

  @override
  Future<ApiResult<AirtimeResponse>> getAirtimeBills(String id) {
    return dioInterceptor(
      () => _remoteDataSource.getAirtimeBills(id),
    );
  }

  @override
  Future<ApiResult<List<BillCategory>>> getCategoryBills() {
    return dioInterceptor(
      () => _remoteDataSource.getBillCategories(),
    );
  }

  @override
  Future<ApiResult<List<PaymentItem>>> getPaymentItems(String id) {
    return dioInterceptor(
      () => _remoteDataSource.getPaymentItems(id),
    );
  }

  @override
  Future<ApiResult<ChargeResponse>> charge(ChargeParam param) {
    return dioInterceptor(
      () => _remoteDataSource.charge(param),
    );
  }

  @override
  Future<ApiResult<CustomerInfo>> validate(ValidateParam param) async {
    try {
      return await dioInterceptor(
        () => _remoteDataSource.validate(param),
      );
    } catch (e, s) {
      ErrorHandler.report(e, s);
      return ApiResult<CustomerInfo>.apiFailure(
        error: ApiExceptions.defaultError(
          'Unable to process transaction. Please try again later',
        ),
      );
    }
  }

  @override
  Future<ApiResult<String?>> getReceipt(String params) {
    return dioInterceptor(
      () => _remoteDataSource.getReceipt(params),
    );
  }

  @override
  Future<ApiResult<ChargeReceipt>> loadReceipt(
      String orderId, String outletId) {
    return dioInterceptor(
        () => _remoteDataSource.loadReceipt(orderId, outletId));
  }

  @override
  Future<ApiResult<List<Biller>>> getCategories(String id) {
    return dioInterceptor(
      () => _remoteDataSource.getCategories(id),
    );
  }
}
