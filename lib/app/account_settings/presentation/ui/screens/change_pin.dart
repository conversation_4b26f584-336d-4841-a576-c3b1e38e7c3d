import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:safe_insets/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/use_cases/change_pin.dart';
import 'package:shop/app/account_settings/domain/use_cases/verify_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/homepage/presentation/ui/widgets/user_intro.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/widgets/k_keyboard.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

enum ChangePinType { oldPin, newPin, confirmPin, migrate }

class ChangePinScreen extends StatefulWidget {
  final ChangePinArgs args;

  const ChangePinScreen({
    super.key,
    required this.args,
  });

  @override
  _ChangePinScreenState createState() => _ChangePinScreenState();
}

class _ChangePinScreenState extends State<ChangePinScreen> {
  final _passcodeCtrl = TextEditingController();
  String _errorMessage = '';
  bool _loading = false;
  bool canContinue = false;
  late int length;
  late final pageType = widget.args.pageType;
  late final bool relatedActionText = pageType == ChangePinType.oldPin ||
      pageType == ChangePinType.migrate ||
      pageType == ChangePinType.newPin;
  final ValueNotifier<bool> hasElevation = ValueNotifier(false);

  @override
  void initState() {
    final migrated = context.read<UserCubit>().currentUser?.migrated ?? false;
    length = (migrated || (widget.args.oldPin != null)) ? 6 : 4;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (!_loading) {
          return true;
        }
        return false;
      },
      child: Scaffold(
        appBar: ElevatedAppBar(
          hasElevation,
          leading: widget.args.applyLeading!
              ? InkWell(
                  onTap: () {
                    if (!_loading) {
                      context.pop();
                    }
                  },
                  child: Icon(
                    Icons.arrow_back,
                  ),
                )
              : SizedBox.shrink(),
          middle: Text(
            'Change PIN',
            style: KTextStyle.bodyText2
                .copyWith(fontWeight: FontWeight.w600, fontSize: 18),
          ),
        ),
        body: Center(
          child: Column(
            children: [
              const YMargin(30),
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: defaultHorizontalContentPadding),
                  child: Text(
                    getPageTitle(pageType),
                    style: KTextStyle.bodyText2.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              YMargin(30),
              _errorMessage.isNotEmpty
                  ? Center(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(
                          color: Colors.red,
                        ),
                      ),
                    )
                  : SizedBox.shrink(),
              const YMargin(50),
              IgnorePointer(
                ignoring: true,
                child: KCodeInput(
                  controller: _passcodeCtrl,
                  length: length,
                  builder: CodeInputBuilders.darkRectangle(
                    context: context,
                    emptySize: Size(40.0, 50.0),
                  ),
                ),
              ),
              const YMargin(20),
              Expanded(
                child: KKeyPad(
                  activatePeriod: false,
                  onKeyTap: _addToText,
                  onRemoveTap: _removeLastDigit,
                  disabled: false,
                ),
              ),
              const YMargin(16),
              // Spacer(),
              SafeArea(
                child: SafeAreaWrap(
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: defaultHorizontalContentPadding),
                    child: KButtonPrimary(
                      text: relatedActionText ? 'Continue' : 'Confirm',
                      onTap:
                          canContinue ? () => onButtonClicked(pageType) : null,
                      disabled: !canContinue,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void onButtonClicked(ChangePinType type) {
    switch (type) {
      case ChangePinType.oldPin:
        _enterOldPin();
        break;
      case ChangePinType.newPin:
      case ChangePinType.migrate:
        _enterNewPin();
        break;
      case ChangePinType.confirmPin:
        confirmPin();
        break;
    }
  }

  void _enterNewPin() {
    context.pushNamed(
      ChangePinPath,
      extra: widget.args.copyWith(
        pageType: ChangePinType.confirmPin,
        oldPin: widget.args.oldPin,
        pin: _passcodeCtrl.text,
        applyLeading: true,
      ),
    );
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }

    if (_passcodeCtrl.text.length == length) {
      setState(() {
        canContinue = true;
      });
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }

    if (canContinue) {
      setState(() {
        canContinue = false;
      });
    }
  }

  void _enterOldPin() async {
    if (!_loading) {
      setState(() {
        _errorMessage = '';
        _loading = true;
      });
      TdLoader.show(context);
      final res = await locator.get<VerifyPin>().call(
            VerifyPinParams(pin: _passcodeCtrl.text),
          );

      res.when(
        success: (val) {
          setState(() {
            _loading = false;
          });
          TdLoader.hide();
          context.pushNamed(
            ChangePinPath,
            extra: widget.args.copyWith(
              pageType: ChangePinType.newPin,
              oldPin: _passcodeCtrl.text,
            ),
          );
        },
        apiFailure: (ApiExceptions e, _) {
          TdLoader.hide();
          setState(() {
            // clear pin inputted
            _passcodeCtrl.text = '';
            canContinue = false;
            _loading = false;
          });

          final msg = ApiExceptions.getErrorMessage(e);
          Toast.error(msg, context);
        },
      );
    }
  }

  void confirmPin() {
    if (_passcodeCtrl.text != widget.args.pin) {
      setState(() {
        _errorMessage = 'Pin does not match';
      });
      return;
    } else {
      _createPin();
    }
  }

  void _createPin() async {
    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    ApiResult res;
    TdLoader.show(context);
    res = await locator.get<ChangePin>().call(
          ChangePinParams(widget.args.oldPin, widget.args.pin),
        );

    res.when(
      success: (_) async {
        final sp = await SharedPreferences.getInstance();
        sp.remove(Keys.migratePin);
        TdLoader.hide();
        _trackSegment(1);
        if (widget.args.migrated!) {
          final userCubit = context.read<UserCubit>();
          final migratedUser = userCubit.currentUser?.copyWith(migrated: true);
          if (migratedUser != null) {
            userCubit.updatingUser(migratedUser, dry: true);
            await goHomeMobile(context);
          } else {
            context.goNamed(OnBoardPath);
          }
        } else {
          int count = 0;
          Navigator.of(context).popUntil((_) => count++ >= 3);
          Toast.success(
            'Pin Changed Successfully',
            context,
            duration: 5,
          );
          if (mounted) {
            setState(() {
              _loading = false;
            });
          }
        }
      },
      apiFailure: (e, _) {
        _passcodeCtrl.text = '';
        canContinue = false;
        TdLoader.hide();
        _trackSegment(0);
        if (mounted) {
          setState(() {
            _errorMessage = ApiExceptions.getErrorMessage(e);
            _loading = false;
          });
        }
      },
    );
  }

  void _trackSegment(int status) {
    Segment.track(
      eventName: SegmentEvents.changePin,
      properties: {
        'status': status,
      },
    );
  }

  String getPageTitle(ChangePinType type) {
    switch (type) {
      case ChangePinType.oldPin:
        return 'Enter old PIN';
      case ChangePinType.newPin:
        return 'Enter your new PIN';
      case ChangePinType.confirmPin:
        return 'Confirm PIN';
      case ChangePinType.migrate:
        return "You're required to upgrade to a 6 digit PIN, Please enter your new PIN";
    }
  }
}

class ChangePinArgs {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final String? oldPin;
  final ChangePinType pageType;
  final bool? migrated;
  final bool? applyLeading;

  const ChangePinArgs(
    this.pageType, {
    Key? key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.oldPin,
    this.migrated = false,
    this.applyLeading = true,
  });

  ChangePinArgs copyWith({
    ChangePinType? pageType,
    bool? confirm,
    String? pin,
    String? name,
    String? phone,
    String? oldPin,
    bool? migrated,
    bool? applyLeading,
  }) {
    return ChangePinArgs(
      pageType ?? this.pageType,
      confirm: confirm ?? this.confirm,
      pin: pin ?? this.pin,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      oldPin: oldPin ?? this.oldPin,
      migrated: migrated ?? this.migrated,
      applyLeading: applyLeading ?? this.applyLeading,
    );
  }
}
