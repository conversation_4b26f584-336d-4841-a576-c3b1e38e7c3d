import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app_config.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:td_commons_flutter/app_config.dart';

class SmileWebViewArgs {
  final String token;
  final String jobType;
  final ValueChanged<String?>? onSuccess;
  final VoidCallback? onClose;
  final VoidCallback? onFailed;

  SmileWebViewArgs(this.token, this.jobType,
      {this.onSuccess, this.onClose, this.onFailed});
}

class SmileWebView extends StatefulWidget {
  final SmileWebViewArgs args;
  const SmileWebView({super.key, required this.args});

  @override
  _SmileWebViewState createState() => _SmileWebViewState();
}

class _SmileWebViewState extends State<SmileWebView> {
  final ValueNotifier<bool> _loader = ValueNotifier<bool>(false);
  final GlobalKey webViewKey = GlobalKey();
  final String handler = 'SmileResult';

  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera",
      iframeAllowFullscreen: true);

  Widget _buildLoader() {
    return ConstrainedBox(
      constraints:
          const BoxConstraints(maxHeight: 2.0, minWidth: double.infinity),
      child: const LinearProgressIndicator(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            InAppWebView(
              key: webViewKey,
              initialSettings: settings,
              initialUrlRequest: URLRequest(
                url: WebUri.uri(
                  Uri.parse("https://sandbox.shoptopup.com/smile.html"),
                ),
              ),
              onPermissionRequest: (controller, request) async {
                return PermissionResponse(
                    resources: request.resources,
                    action: PermissionResponseAction.GRANT);
              },
              onWebViewCreated: (controller) {
                webViewController = controller;
                controller.addJavaScriptHandler(
                    handlerName: handler,
                    callback: (args) {
                      SmileResult result =
                          SmileResult.values.byName(args.first);
                      switch (result) {
                        case SmileResult.success:
                          widget.args.onSuccess!(result.name);
                          break;
                        case SmileResult.close:
                          widget.args.onClose!();

                          break;
                        case SmileResult.error:
                          widget.args.onFailed!();
                          break;
                      }
                    });
              },
              onLoadStart: (controller, url) {
                if (mounted) _loader.value = true;
              },
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                var uri = navigationAction.request.url!;

                if (![
                  "http",
                  "https",
                  "file",
                  "chrome",
                  "data",
                  "javascript",
                  "about"
                ].contains(uri.scheme)) {
                  if (await canLaunchUrl(uri)) {
                    // Launch the App
                    await launchUrl(
                      uri,
                    );
                    // and cancel the request
                    return NavigationActionPolicy.CANCEL;
                  }
                }

                return NavigationActionPolicy.ALLOW;
              },
              onLoadStop: (controller, url) async {
                if (mounted) _loader.value = false;
                controller.evaluateJavascript(source: script);
              },
              onReceivedError: (controller, request, error) {
                if (mounted) _loader.value = false;
              },
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint(consoleMessage.toString());
              },
            ),
            ValueListenableBuilder(
              valueListenable: _loader,
              builder: (context, dynamic loading, child) {
                return AnimatedSwitcher(
                  duration: kThemeAnimationDuration,
                  child: loading ? child : const SizedBox.shrink(),
                );
              },
              child: _buildLoader(),
            )
          ],
        ),
      ),
    );
  }

  late final script = """
  SmileIdentity({
  token: "${widget.args.token}",
  product: "biometric_kyc",
  callback_url: "${config.smileWebCallBackUrl}",
  consent_required: {'NG': ['${widget.args.jobType}']},
  id_selection: {'NG': ['${widget.args.jobType}']},
  environment: "${config.environment == Environment.dev ? "sandbox" : "live"}",
  partner_details: {
  partner_id: "2481",
  name: "TradeDepot",
  logo_url: "https://tradedepot.co/images/logo-alt.svg",
  policy_url: "https://shoptopup.com/privacy",
  theme_color: '#FF8D06'
  },
  onSuccess: () => { window.flutter_inappwebview
                      .callHandler('$handler',"success"); },
  onClose: () => { window.flutter_inappwebview
                      .callHandler('$handler', "close");},
  onError: () => { window.flutter_inappwebview
                      .callHandler('$handler',"error"); }
  });
 
  """;
}

/*    "NG": ["BVN", "NIN_V2"]
},
         consent_required: {
    NG: ['BVN', 'NIN_V2']
  }, */
