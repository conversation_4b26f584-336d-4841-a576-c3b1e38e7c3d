import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/kyc_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/kyc_state.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class AccountSettingsDp extends StatelessWidget {
  const AccountSettingsDp({super.key});

  @override
  Widget build(BuildContext context) {
    KycState kycState = Provider.of<KycCubit>(context).state;
    UserCubit userCubit = Provider.of<UserCubit>(context);
    bool isNgUser = userCubit.isNgUser;

    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const KBackButton(),
            Padding(
              padding: screenPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildIntro(context),
                  settingItems(
                    assetUrl: kSvgChangePin,
                    title: AppLocalizations.of(context)!.change_pin,
                    onTap: () => context.pushNamed(ChangePinPath),
                    isFirst: true,
                  ),
                  isNgUser
                      ? settingItems(
                          assetUrl: kSvgKYC,
                          title: AppLocalizations.of(context)!.kyc_validation,
                          kycSetupCompleted: kycState.kycSetupCompleted,
                          onTap: kycState.kycSetupCompleted
                              ? null
                              : () => context.pushNamed(SelectKYCPath),
                        )
                      : const SizedBox.shrink(),
                  isNgUser
                      ? settingItems(
                          assetUrl: kSvgCardManagement,
                          title: AppLocalizations.of(context)!.card_management,
                          onTap: () => context.pushNamed(CardManagementPath),
                        )
                      : const SizedBox.shrink(),
                  settingItems(
                    assetUrl: kSvgTermsAndCondition,
                    title: AppLocalizations.of(context)!.terms_and_condition,
                    onTap: openTermsUri,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Column buildIntro(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.account_setting,
          style: KTextStyle.headerTitleText,
        ),
        SizedBox(
          height: screenHeight(context, percent: 0.03),
        ),
      ],
    );
  }

  InkWell settingItems({
    required String assetUrl,
    required String title,
    VoidCallback? onTap,
    bool kycSetupCompleted = false,
    bool isFirst = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            isFirst
                ? const Divider(
                    indent: 2,
                  )
                : const SizedBox.shrink(),
            Padding(
              padding: const EdgeInsets.only(
                  top: defaultHorizontalContentPadding,
                  bottom: defaultHorizontalContentPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        left: defaultHorizontalContentPadding),
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          assetUrl,
                          width: 24,
                          height: 30,
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        Text(
                          title,
                          style: KTextStyle.drawerNavItemText,
                        ),
                      ],
                    ),
                  ),
                  kycSetupCompleted
                      ? Padding(
                          padding: const EdgeInsets.only(
                              right: defaultHorizontalContentPadding),
                          child: SvgPicture.asset(
                            kSvgVerified,
                            width: 24,
                            height: 30,
                          ),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
            const Divider(
              indent: 2,
            ),
          ],
        ),
      ),
    );
  }
}
