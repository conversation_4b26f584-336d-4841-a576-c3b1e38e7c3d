import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/use_cases/verify_pin.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/change_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/buttons/src/k_small_flat_button.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/k_keyboard.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class VerifyPinScreen extends StatefulWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;

  const VerifyPinScreen({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
  });

  @override
  _VerifyPinScreenState createState() => _VerifyPinScreenState();
}

class _VerifyPinScreenState extends State<VerifyPinScreen> {
  final _passcodeCtrl = TextEditingController();
  String _errorMessage = '';
  bool _loading = false;

  Null get error => null;
  // bool _isVerify = false;

  // bool get loggingIn => widget.phone != null;
  late final int length =
      Provider.of<UserCubit>(context).currentUser?.pinLength ?? 4;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTop(),
            YMargin(40),
            Center(
              child: IgnorePointer(
                ignoring: true,
                child: KCodeInput(
                  controller: _passcodeCtrl,
                  length: length,
                  builder: CodeInputBuilders.lightCircle(
                    context: context,
                    emptyRadius: 10,
                    filledRadius: 10,
                    totalRadius: 20,
                  ),
                ),
              ),
            ),
            YMargin(10),
            Center(
              child: _buildVerifyPasswordButton(),
            ),
            Expanded(
              child: KKeyPad(
                activatePeriod: false,
                onKeyTap: _addToText,
                onRemoveTap: _removeLastDigit,
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        KBackButton(),
        YMargin(25),
        Padding(
          padding: context.insetsSymetric(
            horizontal: defaultHorizontalContentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildIntro(context, 'Change Pin', 'Enter your Old Pin'),
              YMargin(30),
              Padding(
                padding: context.insetsOnly(right: 40),
                child: _errorMessage.isNotEmpty
                    ? Text(
                        _errorMessage,
                        style: TextStyle(
                          color: Colors.red,
                        ),
                      )
                    : SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildIntro(BuildContext context, title, subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: KTextStyle.headerTitleText,
        ),
        SizedBox(
          height: screenHeight(context, percent: 0.03),
        ),
        Text(
          subtitle,
          style: KTextStyle.subtitleTitleText,
          textAlign: TextAlign.justify,
        ),
      ],
    );
  }

  void _enterPin(String pin) async {
    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    final res = await locator.get<VerifyPin>().call(
          VerifyPinParams(pin: pin),
        );

    res.maybeWhen(
      success: (val) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChangePinScreen(
              args: ChangePinArgs(
                ChangePinType.oldPin,
                oldPin: pin,
              ),
            ),
          ),
        );
      },
      apiFailure: (e, _) {
        setState(() {
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
      },
      orElse: () {
        setState(() {
          _errorMessage =
              'An unexpected error occurred. Please try again later';
        });
      },
    );

    setState(() {
      _loading = false;
    });
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }
  }

  Widget _buildVerifyPasswordButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != length
          ? null
          : () {
              // submit
              _enterPin(_passcodeCtrl.text);
            },
      text: _loading ? 'Loading...' : 'INPUT OLD PIN',
    );
  }
}
