import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/use_cases/change_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/buttons/src/k_small_flat_button.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/widgets/k_keyboard.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class ChangePinScreenDp extends StatefulWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final String? oldPin;

  const ChangePinScreenDp({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.oldPin,
  });

  @override
  _ChangePinScreenDpState createState() => _ChangePinScreenDpState();
}

class _ChangePinScreenDpState extends State<ChangePinScreenDp> {
  final _passcodeCtrl = TextEditingController();
  String _errorMessage = '';
  bool _loading = false;
  // bool _isVerify = false;

  late final int length = context.read<UserCubit>().currentUser?.pinLength ?? 4;

  // bool get loggingIn => widget.phone != null;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTop(),
            YMargin(40),
            Center(
              child: IgnorePointer(
                ignoring: true,
                child: KCodeInput(
                  controller: _passcodeCtrl,
                  length: length,
                  builder: CodeInputBuilders.lightCircle(
                    context: context,
                    emptyRadius: 10,
                    filledRadius: 10,
                    totalRadius: 20,
                  ),
                ),
              ),
            ),
            YMargin(10),
            Center(
              child: _buildCreatePasswordButton(),
            ),
            Expanded(
              child: KKeyPad(
                activatePeriod: false,
                onKeyTap: _addToText,
                onRemoveTap: _removeLastDigit,
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        KBackButton(),
        YMargin(25),
        Padding(
          padding: context.insetsSymetric(
            horizontal: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildIntro(context, 'Create New Pin', _subtitle),
              YMargin(30),
              Padding(
                padding: context.insetsOnly(right: 40),
                child: _errorMessage.isNotEmpty
                    ? Text(
                        _errorMessage,
                        style: TextStyle(
                          color: Colors.red,
                        ),
                      )
                    : SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildIntro(BuildContext context, title, subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: KTextStyle.headerTitleText,
        ),
        SizedBox(
          height: screenHeight(context, percent: 0.03),
        ),
        Text(
          subtitle,
          style: KTextStyle.subtitleTitleText,
          textAlign: TextAlign.justify,
        ),
      ],
    );
  }

  void _trackSegment(int status) {
    Segment.track(
      eventName: SegmentEvents.changePin,
      properties: {
        'status': status,
      },
    );
  }

  void _createPin() async {
    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    ApiResult res;

    res = await locator.get<ChangePin>().call(
          ChangePinParams(widget.oldPin, widget.pin),
        );

    res.maybeWhen(
      success: (_) {
        _trackSegment(1);
        int count = 0;
        Navigator.of(context).popUntil((_) => count++ >= 3);
        Toast.success(
          'Pin Changed Successfully',
          context,
          duration: 5,
        );
      },
      apiFailure: (e, _) {
        _trackSegment(0);
        setState(() {
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
      },
      orElse: () {
        _trackSegment(0);
        setState(() {
          _errorMessage =
              'An unexpected error occurred. Please try again later';
        });
      },
    );

    setState(() {
      _loading = false;
    });
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }
  }

  String get _subtitle {
    if (widget.confirm) {
      return "Confirm your New Pin.";
    }
    return 'Enter your New Pin';
  }

  Widget _buildCreatePasswordButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != length
          ? null
          : () async {
              if (!widget.confirm) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ChangePinScreenDp(
                      confirm: true,
                      oldPin: widget.oldPin,
                      pin: _passcodeCtrl.text,
                    ),
                  ),
                );
              }

              if (widget.confirm) {
                if (_passcodeCtrl.text != widget.pin) {
                  setState(() {
                    _errorMessage = 'Pin does not match';
                  });
                  return;
                }
              }

              // submit
              if (widget.confirm) _createPin();
            },
      text: _loading
          ? 'Creating...'
          : widget.confirm
              ? 'Confirm Pin'
              : 'CREATE PIN',
    );
  }
}
