import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/smile_web.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_verification_modal.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/permission_dialog.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/credit/presentation/logic/methods/events_tracking.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/js/helper.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../homepage/presentation/ui/widgets/user_intro.dart';

class KYCValidation extends StatefulWidget {
  const KYCValidation({super.key});

  @override
  State<KYCValidation> createState() => _KYCValidationState();
}

class _KYCValidationState extends State<KYCValidation> with EventsTracking {
  final ValueNotifier<bool> hasElevation = ValueNotifier(false);
  final _formKey = GlobalKey<FormState>();
  TextEditingController bvnController = TextEditingController();
  final _nameController = <String, TdTextController>{};
  String? selectedDate;
  bool isScreenBusy = false;
  late SmileJobCubit _smileJobCubit;

  @override
  void initState() {
    super.initState();
    selectedDate =
        UserCubit.instance?.currentUser?.retailOutlets?.first.identity?.dob;
    _smileJobCubit = BlocProvider.of<SmileJobCubit>(context, listen: false);
    _registerTextControllers();
  }

  void _registerTextControllers() {
    _nameController['firstName'] = TdTextController(
      initialValue: UserCubit.instance?.currentUser?.firstName,
      validators: [Validators.required()],
    );
    _nameController['lastName'] = TdTextController(
      initialValue: UserCubit.instance?.currentUser?.lastName,
      validators: [Validators.required()],
    );
  }

  void handleSuccess(String jobId) {
    _smileJobCubit.validateSmileJob(jobId);
    context.pop();
    context.pop();
    KYCVerificationModal.show(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return AppScreen(
      child: BlocListener<SmileJobCubit, SmileJobState>(
        listener: (context, state) async {
          if (state is SmileJobLoading) {
            TdLoader.show(context);
          } else {
            TdLoader.hide();
          }

          if (state is SmileJobFailed) {
            // TdLoader.hide();
            Toast.error(state.message, context);
          }

          if (state is SmileJobThreeTrialsWarning) {
            // TdLoader.hide();
            Toast.error(state.message, context);
          }

          if (state is SmileJobProcessInitiated) {
            final helper = JSHelper();
            bvnUpdateInit();

            if (state.token == null) {
              Toast.error("Invalid token received: ${state.token}", context);
              return;
            }

            final res = await helper.requestCameraAndAudioPermissions(
              () {
                final jobType = state.jobType == 'nin' ? "NIN_V2" : "BVN";
                if (kIsWeb) {
                  final env = config.environment == Environment.dev
                      ? "sandbox"
                      : "live";

                  return helper.configureSmileIdentityWebIntegration(
                    state.token!,
                    config.smileWebCallBackUrl!,
                    env,
                    jobType,
                    (String type) {
                      SmileResult result = SmileResult.values.byName(type);
                      switch (result) {
                        case SmileResult.success:
                          handleSuccess(state.jobId);

                          break;
                        case SmileResult.close:
                          // do nothing
                          break;
                        case SmileResult.error:
                          // render error message if necessary
                          break;
                      }
                    },
                  );
                }
                context.pushNamed(
                  smileWebPath,
                  extra: SmileWebViewArgs(state.token!, jobType,
                      onSuccess: (value) async {
                    handleSuccess(state.jobId);
                  }, onClose: () {
                    context.pop();
                    Toast.error("Verification process exited", context);
                  }, onFailed: () {
                    context.pop();
                    Toast.error("Verification process Failed", context);
                  }),
                );
              },
              state.enhancedKyc,
            );

            if (!res) {
              showDialog(
                  context: context,
                  builder: (ctx) {
                    return PermissionDialog();
                  });
            }
          }

          if (state is SmileJobCompleted) {
            context.pop();
            // TdLoader.hide();
            KYCVerificationModal.show(context);
          }
        },
        child: Scaffold(
          appBar: ElevatedAppBar(
            hasElevation,
            leading: InkWell(
              onTap: () {
                if (!isScreenBusy) {
                  Navigator.pop(context);
                }
              },
              child: Icon(
                Icons.arrow_back,
              ),
            ),
            middle: Text(
              'KYC Validation',
              style: KTextStyle.bodyText2.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: Theme.of(context).colorScheme.onSurface),
            ),
          ),
          body: Padding(
            padding: screenPadding,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Complete your KYC',
                    style: KTextStyle.bodyText2
                        .copyWith(fontWeight: FontWeight.w600, fontSize: 18),
                  ),
                  YMargin(20),
                  Text(
                    'Enter your 11-digit Bank Verification Number and Date of Birth to verify your account',
                    style: TextStyle(color: theme.hintColor),
                  ),
                  if (config.environment == Environment.dev)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 20,
                        ),
                        TdTextField(
                          autoFocus: false,
                          title: 'First Name',
                          hint: 'Enter your first name',
                          textController: _nameController['firstName'],
                          inputFormatters: [
                            validInput(),
                          ],
                          keyboardType: TextInputType.name,
                        ),
                        TdTextField(
                          autoFocus: false,
                          title: 'Last Name',
                          hint: 'Enter your last name',
                          textController: _nameController['lastName'],
                          inputFormatters: [
                            validInput(),
                          ],
                          keyboardType: TextInputType.name,
                        ),
                        Text('Date of Birth'),
                        Column(
                          children: [
                            SizedBox(
                              height: 5,
                            ),
                            InkWell(
                              onTap: () => _selectDate(context),
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 14),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all()),
                                child: selectedDate != null
                                    ? Text(selectedDate!)
                                    : Text(
                                        'Enter your D.O.B',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(color: theme.hintColor),
                                      ),
                              ),
                            ),
                          ],
                        ),
                        YMargin(20),
                      ],
                    ),
                  YMargin(config.environment == Environment.dev ? 10 : 40),
                  Text(
                    'Enter your BVN',
                    style: KTextStyle.bodyText2
                        .copyWith(fontWeight: FontWeight.w500, fontSize: 13),
                  ),
                  YMargin(10),
                  Form(
                    key: _formKey,
                    child: TextFormField(
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      controller: bvnController,
                      validator: Validators.isNinOrBvnValid(),
                      decoration: InputDecoration(
                        errorStyle: TextStyle(),
                        contentPadding:
                            EdgeInsets.symmetric(vertical: 14, horizontal: 10),
                        isDense: true,
                        isCollapsed: true,
                        focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide()),
                        errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide()),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide()),
                        enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide()),
                      ),
                    ),
                  ),
                  YMargin(40),
                  Text(
                    'WHY WE NEED YOUR BVN',
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(color: theme.hintColor),
                  ),
                  YMargin(5),
                  Divider(),
                  YMargin(10),
                  Text(
                    'We only require your BVN once to confirm your identity, here’s what we have access to',
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(color: theme.hintColor),
                  ),
                  YMargin(25),
                  customRow(kSvgProfile, 'Your full, legal name', context),
                  YMargin(25),
                  customRow(kSvgBvn, 'BVN-linked phone number', context),
                  YMargin(25),
                  customRow(kSvgDob, 'Your Date of Birth', context),
                  YMargin(100),
                  BlocBuilder<SmileJobCubit, SmileJobState>(
                    builder: (_, state) {
                      return KButtonPrimary(
                        onTap: () async {
                          if (config.environment == Environment.dev) {
                            if (selectedDate == null) {
                              Toast.error('Enter Dob', context);
                              return;
                            }
                            if (_nameController.validate() &&
                                _formKey.currentState!.validate()) {
                              _initiateSmile();
                            }
                          } else {
                            if (_formKey.currentState!.validate()) {
                              _initiateSmile();
                            }
                          }
                        },
                        text: 'Continue',
                      );
                    },
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null) {
      setState(() {
        selectedDate = pickedDate.toString().substring(0, 10).trim();
      });
    }
  }

  Widget customRow(String icon, String text, BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          icon,
          color: Theme.of(context).colorScheme.primary,
          width: 17.5,
        ),
        SizedBox(width: 10),
        Text(
          text,
          style: Theme.of(context)
              .textTheme
              .bodyMedium!
              .copyWith(fontWeight: FontWeight.w400),
        )
      ],
    );
  }

  Future<void> _initiateSmile() async {
    await _smileJobCubit.initiateJob(
      InitiateJobRequest(
          jobtype: 'bvn',
          idNumber: bvnController.text,
          field: config.environment == Environment.dev
              ? Field(
                  firstName: _nameController["firstName"]?.controller?.text,
                  lastName: _nameController["lastName"]?.controller?.text,
                  dob: selectedDate)
              : null),
    );
  }

  Future<void> permissionCheck(VoidCallback action, bool isEnhancedKyc) async {
    if (isEnhancedKyc) {
      action();
      return;
    }

    PermissionStatus status = await Permission.camera.request();
    if (status.isGranted) {
      action();
    } else if (status.isDenied) {
      permissionCheck(() {
        action();
      }, isEnhancedKyc);
    } else if (status.isPermanentlyDenied) {
      showDialog(
          context: context,
          builder: (ctx) {
            return PermissionDialog();
          });

      return;
    }
  }
}

class KYCValidationArgs {
  PhoneValidateCubit bloc;
  KYCValidationArgs(this.bloc);
}
