import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_modal.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_stripe_modal.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class AccountSettings extends StatefulWidget {
  const AccountSettings({super.key});

  @override
  State<AccountSettings> createState() => AccountSettingsState();
}

class AccountSettingsState extends State<AccountSettings> {
  static GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    final appHost = userCubit.domain;
    return Scaffold(
      key: scaffoldKey,
      appBar: ShopAppBar.shopAppBar(context, title: 'Manage Account'),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: defaultHorizontalContentPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /*         BlocListener<SmileJobCubit, SmileJobState>( @codesUntold todo
                listener: (context, state) {
                  if (state is SmileJobFailed) {
                    Toast.error(state.message, context);
                  }
                },
                child: SizedBox.shrink(),
              ),*/
              SizedBox(
                height: screenHeight(context, percent: 0.04),
              ),
              buildDrawerItems(
                  assetUrl: kSvgEditProfile,
                  title: 'Edit profile',
                  onTap: () {
                    showModalBottomSheet(
                      isScrollControlled: true,
                      context: context,
                      builder: (_) => Container(
                        padding: screenPadding,
                        height: 300,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Edit Profile',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge!
                                      .copyWith(fontWeight: FontWeight.w600),
                                ),
                                InkWell(
                                    onTap: () => context.pop(),
                                    child: Icon(Icons.clear))
                              ],
                            ),
                            YMargin(20),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                context.pushNamed(PersonalInfoPath,
                                    extra:
                                        context.read<UserCubit>().currentUser);
                              },
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Manage Personal Profile',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 10,
                                  )
                                ],
                              ),
                            ),
                            YMargin(20),
                            Divider(
                              height: null,
                            ),
                            YMargin(20),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                context.pushNamed(ShopInfoPath,
                                    extra:
                                        context.read<UserCubit>().currentUser);

                                try {
                                  Segment.track(
                                    eventName: SegmentEvents.storeProfileViewed,
                                    properties: {},
                                  );
                                } catch (_) {}
                              },
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Manage Store Profile',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 10,
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(30),
                        ),
                      ),
                      clipBehavior: Clip.antiAlias,
                    );
                  }),
              Visibility(
                visible: appHost == AppHost.shopTopUp,
                child: SizedBox(
                  height: 5,
                ),
              ),
              // Visibility(
              //   visible: appHost == AppHost.shopTopUp,
              //   child: buildDrawerItems(
              //       assetUrl: kSvgManageAccountIcon,
              //       title: 'Change Pin',
              //       onTap: () {
              //         context.pushNamed(
              //           ChangePinPath,
              //           extra: ChangePinArgs(
              //             ChangePinType.oldPin,
              //             migrated: true,
              //           ),
              //         );
              //       }),
              // ),
              // SizedBox(
              //   height: 5,
              // ),
              context.read<UserCubit>().isGhUser ||
                      context.read<UserCubit>().isZaUser
                  ? SizedBox.shrink()
                  : Column(
                      children: [
                        buildDrawerItems(
                            assetUrl: kSvgShoppingPrefsIcon,
                            title: 'KYC Validation',
                            onTap: () {
                              showModalBottomSheet(
                                isScrollControlled: true,
                                context: context,
                                builder: (_) => SizedBox(
                                    height: screenHeight(context, percent: 0.6),
                                    child: appHost == AppHost.shopTopUp
                                        ? KycModal()
                                        : KycStripeModal()),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(30),
                                  ),
                                ),
                                clipBehavior: Clip.antiAlias,
                              );
                            }),
                        SizedBox(
                          height: 5,
                        ),
                      ],
                    ),
              /* buildDrawerItems(
                  assetUrl: kSvgTermsAndCondition,
                  title: 'Terms & Conditions',
                  onTap: openTermsUri),
              SizedBox(
                height: 5,
              ),
              buildDrawerItems(
                  assetUrl: kSvgRewardIcon,
                  title: 'Privacy ',
                  onTap: openPrivacyUri),*/
              YSpacing(5),
              buildDrawerItems(
                assetUrl: kSvgDeleteIcon,
                title: 'Delete Account',
                onTap: () => confirmationModal(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Future<void> confirmationModal(BuildContext context) async {
    final theme = Theme.of(context).textTheme;
    showModalBottomSheet<void>(
      isScrollControlled: true,
      context: context,
      builder: (_) => Padding(
        padding: screenPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(kSvgAlert),
            YSpacing(30),
            Text(
              "Are you sure you want to delete your account?",
              style: theme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            Text(
              "Deleting your account will erase your data and you will no longer have access to TradeDepot Wholesale.",
              style: theme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            Spacer(),
            KButtonPrimary(
              onTap: () {
                Navigator.pop(context);
                context.pushNamed(DeleteAccountPath);
              },
              text: 'Yes, I want to delete my account',
            ),
            YSpacing(10),
            KButtonPrimary.inverted(
              context,
              onTap: () => Navigator.pop(context),
              text: 'Cancel',
            ),
          ],
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      constraints: BoxConstraints.tightFor(
          height: MediaQuery.of(context).size.height * 0.6),
      clipBehavior: Clip.antiAlias,
    );
  }

  Widget customRow(bool isVerified, ThemeData theme) {
    final ColorScheme successColorScheme =
        ColorScheme.fromSeed(seedColor: Color(0XFF08AB5D));
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
              color: isVerified ? null : successColorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(4),
              border: isVerified
                  ? Border.all(color: successColorScheme.primary)
                  : null),
          child: Text(
            isVerified ? 'VERIFIED' : 'NOT STARTED',
            style: theme.textTheme.bodySmall!.copyWith(
                color: isVerified
                    ? successColorScheme.onPrimaryContainer
                    : theme.disabledColor),
          ),
        ),
        XMargin(20),
        Icon(
          Icons.arrow_forward_ios,
          size: 10,
        ),
      ],
    );
  }

  InkWell buildDrawerItems(
      {required String assetUrl,
      required String title,
      VoidCallback? onTap,
      VoidCallback? onLongPress,
      double? assetSize}) {
    return InkWell(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        // margin: EdgeInsets.only(left: 50),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: 16, bottom: 16, right: defaultHorizontalContentPadding),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: SvgPicture.asset(
                            assetUrl,
                            width: assetSize ?? 24,
                            // height: 30,
                          ),
                        ),
                        SizedBox(
                          width: 20,
                        ),
                        Text(
                          title,
                          style: KTextStyle.drawerNavItemText,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
