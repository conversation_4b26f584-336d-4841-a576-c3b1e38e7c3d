import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';

enum VerificationType { identity, company }

class StripeWebViewArgs {
  final String verificationUrl;
  VerificationType type;

  StripeWebViewArgs({required this.verificationUrl, required this.type});
}

class StripeWebView extends StatefulWidget {
  final StripeWebViewArgs args;
  const StripeWebView({super.key, required this.args});

  @override
  _StripeWebViewState createState() => _StripeWebViewState();
}

class _StripeWebViewState extends State<StripeWebView> {
  final ValueNotifier<bool> _loader = ValueNotifier<bool>(false);
  final GlobalKey webViewKey = GlobalKey();

  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera",
      iframeAllowFullscreen: true);

  Widget _buildLoader() {
    return ConstrainedBox(
      constraints:
          const BoxConstraints(maxHeight: 2.0, minWidth: double.infinity),
      child: const LinearProgressIndicator(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ShopAppBar.shopAppBar(context, title: ''),
      body: SafeArea(
        child: Stack(
          children: [
            InAppWebView(
              key: webViewKey,
              initialSettings: settings,
              initialUrlRequest: URLRequest(
                url: WebUri.uri(
                  Uri.parse(widget.args.verificationUrl),
                ),
              ),
              onPermissionRequest: (controller, request) async {
                return PermissionResponse(
                    resources: request.resources,
                    action: PermissionResponseAction.GRANT);
              },
              onWebViewCreated: (controller) {
                webViewController = controller;
              },
              onLoadStart: (controller, url) {
                if (mounted) _loader.value = true;
              },
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                var uri = navigationAction.request.url!;
                if (uri.toString().contains('https://tradedepot.com')) {
                  if (widget.args.type == VerificationType.identity) {
                    context.pop();
                  }
                  if (widget.args.type == VerificationType.company) {
                    context.pop();
                    Navigator.pop(context);
                    context.pop();
                  }
                  return NavigationActionPolicy.CANCEL;
                }

                return NavigationActionPolicy.ALLOW;
              },
              onLoadStop: (controller, url) async {
                if (mounted) _loader.value = false;
              },
              onReceivedError: (controller, request, error) {
                if (mounted) _loader.value = false;
              },
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint(consoleMessage.toString());
              },
            ),
            ValueListenableBuilder(
              valueListenable: _loader,
              builder: (context, dynamic loading, child) {
                return AnimatedSwitcher(
                  duration: kThemeAnimationDuration,
                  child: loading ? child : const SizedBox.shrink(),
                );
              },
              child: _buildLoader(),
            )
          ],
        ),
      ),
    );
  }
}
