import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_verification_modal.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_state.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/sign_up_user/sign_up_user_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/app/loan/domain/params/loan_params.dart';
import 'package:shop/app/loan/domain/use-cases/verify_bvn.dart';
import 'package:shop/app/loan/presentation/logic/bloc/base_loan_cubit.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../../../route_constants.dart';
import '../../../../../../../src/components/src/buttons/src/k_button_primary.dart';
import '../../../../../../../src/components/src/form/form.dart';
import '../../../../../../../src/res/values/analytics/segment_events.dart';
import '../../../../../../../src/res/values/config/keys.dart';
import '../../../../../../../src/res/values/styles/text_style.dart';
import '../../../../../../../src/services/url_service.dart';

class MobileVerifyBvn extends StatefulWidget {
  final SendOTParams otpParams;
  final AuthPageType pageType;
  final bool forgotPin;
  final PhoneValidateCubit? bloc;

  final BvnParams? bvnParams;

  const MobileVerifyBvn({
    super.key,
    required this.otpParams,
    required this.pageType,
    required this.forgotPin,
    this.bloc,
    this.bvnParams,
  });

  @override
  State<MobileVerifyBvn> createState() => _MobileVerifyBvnState();
}

class _MobileVerifyBvnState extends State<MobileVerifyBvn> {
  final _otpMode = ValueNotifier<PhoneAuthMode>(PhoneAuthMode.WhatsApp);
  late String ussdCode = context.read<UserCubit>().ussdCode;
  bool isLoading = false;
  // TextEditingController _controller = TextEditingController();
  // final _signUpBloc = VerifyPhoneCubit(locator(), locator());
  // final _countDownController = CountdownController(autoStart: true);
  // late final ChargeOtpCubit _chargeOtpBloc = ChargeOtpCubit(locator());

  @override
  void initState() {
    // fetchUssdCode();
    super.initState();
  }

  // Future<void> fetchUssdCode() async {
  //   final config = await RemoteConfigService.fetchString(
  //       SHOP_V3_OTP_USSD, jsonEncode(DEFAULT_OTP_USSD));

  //   final code = formatConfigString(config, DEFAULT_OTP_USSD)[
  //       countryCodeFromPhone(widget.otpParams.phoneNumber!)];

  //   ussdCode = code;

  //   if (mounted) {
  //     setState(() {
  //       ussdCode = code;
  //     });
  //   }
  // }

  final Map<String, TdTextController> controller = {
    'otp': TdTextController(
      validators: [Validators.required()],
    ),
  };

  void _verify() async {
    final otp = controller.data()['otp'];

    final params = VerifyOTParams(
      phoneNumber: widget.otpParams.phoneNumber!,
      token: otp,
      countryCode: countryCodeFromPhone(widget.otpParams.phoneNumber!),
      url: '${config.firebaseServiceUrl!}/$VERIFY_OTP_PATH',
    );

    await widget.bloc!.verifyOTP(
      params,
      widget.pageType,
    );
  }

  String get maskedPhone {
    final phone = widget.otpParams.phoneNumber;
    final prefix = phone!.substring(0, 4);
    final postfix = phone.substring(phone.length - 4);
    final mask = 'xxxxxx';
    return '$prefix$mask$postfix';
  }

  @override
  Widget build(BuildContext context) {
    final style = KTextStyle.bodyText2
        .copyWith(fontSize: 16, color: Color.fromRGBO(113, 122, 142, 1));
    final theme = Theme.of(context);

    return BlocListener<PhoneValidateCubit, PhoneValidateState>(
      bloc: widget.bloc,
      listener: (_, state) async {
        if (state is ValidatingOTPState) {
          TdLoader.show(context);
          isLoading = true;
        } else {
          TdLoader.hide();
          isLoading = false;
        }

        if (state is FailedToValidateOTP) {
          Toast.error(state.errorMessage, _);
        }

        if (state is ValidatedAfterLoginState) {
          // try {
          //   final info = await deviceInfo();
          //   await saveDeviceInfo(info);
          // } catch (e, s) {
          //   ErrorHandler.report(e, s);
          // }

          // Remove the login OTP prompt on app load.
          SharedPreferences.getInstance().then((sp) {
            sp.remove(Keys.loginOtp);
          });

          TdLoader.hide();
          isLoading = false;
          context.goNamed(HomePath);
          return;
        }

        if (state is ValidatedBVNState) {
          final res = await locator.get<VerifyBvn>().call(VerifyBvnParams(
              outletId: widget.bvnParams!.outletId,
              bvn: widget.bvnParams!.bvn,
              isValidated: widget.bvnParams!.isValidated,
              phoneNumber: widget.bvnParams!.phoneNumber));

          res.when(
            success: (data) {
              if (widget.bvnParams?.fromLoan == true) {
                UserCubit.instance?.updateOutlet();

                context
                    .read<BaseLoanCubit>()
                    .reloadState(UserCubit.instance?.currentOutlet);
                Segment.track(
                  eventName: SegmentEvents.kycValidationCompleted,
                  properties: {
                    'Status': 1,
                  },
                );

                TdLoader.hide();
                isLoading = false;

                Navigator.pop(context);
              } else {
                TdLoader.hide();
                isLoading = false;
                Navigator.of(context)
                  ..pop()
                  ..pop();
                showModalBottomSheet(
                  isScrollControlled: true,
                  context: context,
                  builder: (_) => KYCVerificationModal(),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(30),
                    ),
                  ),
                  clipBehavior: Clip.antiAlias,
                );
                UserCubit.instance?.updateOutlet();
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              isLoading = false;
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
        }

        if (state is ValidatedOTPState) {
          if (widget.pageType == AuthPageType.resetPinBeforeLogin) {
            context.pushNamed(
              ResetPinPath,
              extra: EnterPinScreenArgs(
                pageType: widget.pageType,
                resetPinParams: ResetPinParams(
                  null,
                  widget.otpParams.phoneNumber!,
                  state.accessToken,
                ),
              ),
            );
            return;
          }

          // next page
          context.pushNamed(
            SignUpUserPath,
            extra: SignUpUserArgs(
              accessToken: state.accessToken,
              phoneNumber: widget.otpParams.phoneNumber!,
              countryCode: state.countryCode,
            ),
          );
        }

        if (state is PhoneValidateUserLoggedIn) {
          if (widget.pageType == AuthPageType.login) {
            SharedPreferences.getInstance().then((sp) {
              sp.setBool(Keys.createPin, true);
            });

            // user wants to create pin
            context.pushNamed(
              EnterPinPath,
              extra: EnterPinScreenArgs(
                name: state.firstName,
                pageType: widget.pageType,
                // resetPinParams: ResetPinParams(
                //   null,
                //   widget.otpParams.phoneNumber,
                //   state.accessToken,
                // ),
              ),
            );
          } else {
            context.goNamed(HomePath);
          }
        }
      },
      child: PopScope(
        canPop: !isLoading,
        child: SafeArea(
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: defaultHorizontalContentPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      onPressed: () {
                        if (!isLoading) {
                          if (widget.pageType == AuthPageType.proceedLogin) {
                            context.read<UserCubit>().logout();
                          } else {
                            context.pop();
                          }
                        }
                      },
                      icon: const Icon(Icons.clear_rounded),
                      iconSize: 30,
                      color: const Color.fromRGBO(18, 34, 66, 1),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enter OTP Code',
                        style: KTextStyle.bodyText2.copyWith(
                          fontSize: 28,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const YMargin(18),
                      Text(
                        "Enter the OTP code sent to your BVN linked phone number to complete your verification",
                        style: KTextStyle.bodyText2.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.hintColor,
                        ),
                      ),
                      const YMargin(20),
                      // TdPhoneNumberField(
                      //   title: 'Phone Number',
                      //   textController: controller['phone'],
                      //   inputFormatters: [validInput()],
                      // ),
                      TdTextField(
                        autoFocus: true,
                        title: 'Enter Code',
                        hint: '000000',
                        textController: controller['otp'],
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          validInput(),
                          FilteringTextInputFormatter.allow(RegExp(r"\d+")),
                          // maximum of 6 numbers for OTP
                          LengthLimitingTextInputFormatter(6),
                        ],
                      ),
                    ],
                  ),
                  Center(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          WidgetSpan(
                            child: Text(
                              "Not getting a code?",
                              style: style,
                            ),
                          ),
                          WidgetSpan(
                            child: InkWell(
                              onTap: () => _resendOtp(context),
                              child: Text(
                                " Resend ",
                                style: style.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                          /*
                          WidgetSpan(
                            child: Text(
                              "or ",
                              style: _style,
                            ),
                          ),
                          WidgetSpan(
                            child: InkWell(
                              onTap: () => OtpMethods.show(
                                context,
                                title: 'Choose a verification method',
                                subtitle:
                                    'Select an option to complete verifying your account',
                                otpMode: _otpMode,
                                phoneNumber: widget.otpParams.phoneNumber!,
                                handleTap: (mode) async {
                                  Navigator.pop(context);
                                  if (mode == PhoneAuthMode.SMS ||
                                      mode == PhoneAuthMode.WhatsApp) {
                                    final networkConnection =
                                        locator.get<NetworkConnection>();

                                    if (await networkConnection
                                        .isDeviceConnected) {
                                      return _resendOtp(context);
                                    }
                                    Toast.error(
                                        'No internet connection', context);
                                  }
                                },
                              ),
                              child: Text(
                                "Change Method",
                                style: _style.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                          */
                        ],
                      ),
                      style: style,
                    ),
                  ),
                  Spacer(),
                  if (ussdCode.isNotEmpty) ...[
                    ussdButton(),
                    const YMargin(30),
                  ],
                  KButtonPrimary(
                    text: 'Verify',
                    onTap: _verifyPhone,
                  ),
                  const YMargin(20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget ussdButton() {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints.tight(Size(300, 36)),
        child: TextButton(
          onPressed: () {
            UrlService.it.call(Uri.encodeComponent(ussdCode));
          },
          style: TextButton.styleFrom(
            elevation: 0,
            side: BorderSide.none,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(42.0),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Get OTP by dialling',
                style: KTextStyle.bodyText2.copyWith(
                  color: Theme.of(context).hintColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                ' $ussdCode',
                style: KTextStyle.bodyText2.copyWith(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _verifyPhone() async {
    if (!controller.validate()) return;
    _verify();
  }

  Future<void> _resendOtp(BuildContext context) async {
    TdLoader.show(context);
    isLoading = true;

    final params = SendOTParams(
      mode: _otpMode.value,
      phoneNumber: widget.otpParams.phoneNumber,
      url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
    );

    controller['otp']?.controller?.text = '';

    final res = await locator.get<SendOTP>().call(params);

    res.when(
      success: (data) {
        TdLoader.hide();
        isLoading = false;
        controller['otp']?.controller?.text = '';
        Toast.show('OTP code has been sent', context);
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        isLoading = false;
        final msg = ApiExceptions.getErrorMessage(error);
        Toast.error(msg, context);
      },
    );
  }
}
