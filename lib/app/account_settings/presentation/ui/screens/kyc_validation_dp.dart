// import 'dart:html' as webPermission;
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:provider/provider.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/verify_nin_screen.dart';
// import 'package:shop/app/loan/presentation/logic/bloc/kyc_cubit.dart';
// import 'package:shop/app/loan/presentation/logic/bloc/kyc_state.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:shop/src/nav_router/routes/src/routes.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:shop/src/services/app_navigator.dart';
// import 'package:td_flutter_src/scaler/scaler.dart';

// class KycValidation extends StatelessWidget {
//   final bool showIcons;
//   final bool title;
//   final bool fromLoan;

//   const KycValidation({
//     Key? key,
//     this.showIcons = true,
//     this.title = false,
//     this.fromLoan = false,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     KycState _kycState = Provider.of<KycCubit>(context).state;
//     final name =
//         Provider.of<UserCubit>(context, listen: false).currentUser?.name;
//     return Scaffold(
//       body: SafeArea(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             KBackButton(),
//             Padding(
//               padding: EdgeInsets.only(left: 20, top: 20),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   buildIntro(context),
//                   settingItems(
//                       assetUrl: kSvgKYC,
//                       title: 'Validate BVN',
//                       isBvnVerified: _kycState.isBvnVerified,
//                       onTap: _kycState.isBvnVerified
//                           ? null
//                           : () =>
//                               context.navigator.pushNamed(kycPageConfig)),
//                   settingItems(
//                     assetUrl: kSvgKYC,
//                     title: 'Validate NIN',
//                     isBvnVerified: _kycState.isNinVerified,
//                     onTap: _kycState.isNinVerified
//                         ? null
//                         : () => context.navigator.pushNamed(verifyNINPageConfig,
//                             arguments: VerifyNinArgs(
//                                 name: name,
//                                 showBackButton: true,
//                                 showSkipButton: false,
//                                 isFromSettings: true)),
//                   )
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Column buildIntro(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Kyc Setup',
//           style: KTextStyle.headerTitleText,
//         ),
//         SizedBox(
//           height: screenHeight(context, percent: 0.03),
//         ),
//       ],
//     );
//   }

//   InkWell settingItems({
//     required String assetUrl,
//     required String title,
//     VoidCallback? onTap,
//     bool isBvnVerified = false,
//     bool isNinVerified = false,
//     bool isFirst = false,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: [
//             isFirst
//                 ? Divider(
//                     indent: 2,
//                   )
//                 : SizedBox.shrink(),
//             Padding(
//               padding: const EdgeInsets.only(top: 15, bottom: 15),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.only(left: 20.0),
//                     child: Row(
//                       children: [
//                         SvgPicture.asset(
//                           assetUrl,
//                           width: 24,
//                           height: 30,
//                         ),
//                         SizedBox(
//                           width: 20,
//                         ),
//                         Text(
//                           title,
//                           style: KTextStyle.drawerNavItemText,
//                         ),
//                       ],
//                     ),
//                   ),
//                   (isBvnVerified || isNinVerified)
//                       ? Padding(
//                           padding: const EdgeInsets.only(right: 10.0),
//                           child: SvgPicture.asset(
//                             kSvgVerified,
//                             width: 24,
//                             height: 30,
//                           ),
//                         )
//                       : SizedBox.shrink(),
//                 ],
//               ),
//             ),
//             Divider(
//               indent: 2,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
