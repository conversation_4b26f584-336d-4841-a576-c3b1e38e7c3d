import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class PermissionDialog extends StatelessWidget {
  final String? permission;
  const PermissionDialog({super.key, this.permission});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(
            10.0,
          ),
        ),
      ),
      content: SizedBox(
        height: 200,
        child: Column(
          children: [
            const SizedBox(
              height: 20,
            ),
            Expanded(
              child: Column(
                children: [
                  Text(
                    'Permission Required',
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  YMargin(20),
                  Text(
                    'Your action cannot be completed unless you allow access to ${permission ?? 'the device camera'}. Proceed?',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  )
                ],
              ),
            ),
            KButtonPrimary(
              onTap: () {
                Navigator.pop(context);
                AppSettings.openAppSettings(type: AppSettingsType.settings);
              },
              text: 'Go to Settings',
            ),
          ],
        ),
      ),
    );
  }

  static void display(BuildContext context, String permission) => showDialog(
      context: context,
      builder: (ctx) {
        return PermissionDialog(permission: permission);
      });

  /*AlertDialog(
      title: Text('Camera Permission'),
      content: Text('Allow this app to access your camera?'),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('Deny'),
        ),
        TextButton(
          onPressed: () {
            requestCameraPermission();
          },
          child: Text('Allow'),
        ),
      ],
    );*/
}

/*AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(
            10.0,
          ),
        ),
      ),
      content: SizedBox(
        height: 200,
        child: Column(
          children: [
            const SizedBox(
              height: 20,
            ),
            Expanded(
                child: Column(
              children: [
                Text(
                  'Permission Required',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
                YMargin(20),
                Text(
                  'Your action cannot be completed unless you allow access to the device camera. Proceed?',
                  style: Theme.of(context).textTheme.bodyMedium?,
                )
              ],
            )),
            KButtonPrimary(
              onTap: () {
                Navigator.pop(context);
                OpenAppSettings.openAppSettings();
              },
              text: 'Go to Settings',
            ),
          ],
        ),
      ),
    ); */
