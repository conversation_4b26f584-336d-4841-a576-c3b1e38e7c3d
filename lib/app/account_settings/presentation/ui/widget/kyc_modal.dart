import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/smile_web.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_shimmer_widget.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_verification_modal.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/permission_dialog.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_job.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_status_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_status_state.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/verify_nin_screen.dart';
import 'package:shop/app/credit/presentation/logic/methods/events_tracking.dart';
import 'package:shop/app/loan/presentation/logic/bloc/kyc_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/kyc_state.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/js/helper.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class KycModal extends StatefulWidget {
  const KycModal({super.key, this.bvnOnly = false});
  final bool bvnOnly;
  @override
  State<KycModal> createState() => _KycModalState();

  static Future showKycModal(BuildContext context,
      [bool bvnOnly = true, bool useRootNavigator = false]) {
    return showModalBottomSheet(
      isScrollControlled: true,
      useRootNavigator: useRootNavigator,
      context: context,
      builder: (_) => SizedBox(
        //  padding: screenPadding,
        height: screenHeight(context, percent: 0.6),
        child: KycModal(
          bvnOnly: bvnOnly,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }
}

class _KycModalState extends State<KycModal> with EventsTracking {
  late SmileJobStatusCubit smileJobStatusCubit;
  late SmileJobCubit _smileJobCubit;
  final _processingBvnJob = ValueNotifier<bool>(false);
  final _processingNinJob = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    smileJobStatusCubit = BlocProvider.of(context, listen: false);
    _smileJobCubit = BlocProvider.of(context, listen: false);
    smileJobStatusCubit.checkJobStatus();
  }

  @override
  Widget build(BuildContext context) {
    KycState kycState = Provider.of<KycCubit>(context).state;
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(Icons.clear),
        ),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BlocBuilder<SmileJobStatusCubit, SmileJobStatusState>(
            builder: (context, state) {
              Widget child = SizedBox.shrink();

              if (state is SmileJobStatusFetchFailed) {
                child = KErrorScreen(state.message, () {
                  smileJobStatusCubit.checkJobStatus();
                });
              } else if (state is SmileJobStatusFetching) {
                child = SizedBox(
                  height: screenHeight(context, percent: 0.5),
                  child: Center(
                    child: Padding(
                      padding: screenPadding,
                      child: KycShimmerWidget(),
                    ),
                  ),
                );
              } else {
                if (state is SmileJobStatusFetchcompleted) {
                  if (state.currentJob.bvnJob.isNotEmpty) {
                    if (state.currentJob.bvnJob.first.status!.toLowerCase() ==
                        "success") {
                      bvnUpdateCompleted();
                    } else {
                      bvnUpdateFailed();
                    }
                  }

                  if (state.currentJob.ninJob.isNotEmpty) {
                    if (state.currentJob.ninJob.first.status!.toLowerCase() ==
                        "success") {
                      ninUpdateCompleted();
                    } else {
                      ninUpdateFailed();
                    }
                  }
                  child = Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: screenPadding,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Complete KYC',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(fontWeight: FontWeight.w600),
                            ),
                            YMargin(20),
                            Text(
                                'To complete KYC and enjoy the full TradeDepot experience, please complete the following actions',
                                style: Theme.of(context).textTheme.bodyMedium!),
                          ],
                        ),
                      ),
                      //  YMargin(30),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                if (!kycState.isBvnVerified) {
                                  if (state.currentJob.bvnJob.isEmpty) {
                                    if (state.currentJob.runEnhanced == null ||
                                        state.currentJob.runEnhanced == false) {
                                      initiateBiometricVerification("bvn");
                                    } else {
                                      Navigator.pop(context);
                                      context.pushNamed(KYCValidationPath);
                                    }
                                  } else {
                                    if (state.currentJob.bvnJob.first.status
                                                ?.toLowerCase() !=
                                            'running' &&
                                        state.currentJob.bvnJob.first.status
                                                ?.toLowerCase() !=
                                            "success") {
                                      if (state.currentJob.runEnhanced ==
                                              null ||
                                          state.currentJob.runEnhanced ==
                                              false) {
                                        initiateBiometricVerification("bvn");
                                      } else {
                                        Navigator.pop(context);
                                        context.pushNamed(KYCValidationPath);
                                      }
                                    }
                                  }
                                }
                              },
                              child: kycState.isBvnVerified
                                  ? ListTile(
                                      title: Text(
                                        'Verify your BVN',
                                      ),
                                      trailing: defaultCustomRowVerified(),
                                    )
                                  : state.currentJob.bvnJob.isEmpty
                                      ? ListTile(
                                          title: Text(
                                            'Verify your BVN',
                                          ),
                                          trailing:
                                              defaultCustomRowUnVerified(),
                                        )
                                      : ListTile(
                                          title: Text(
                                            'Verify your BVN',
                                            maxLines: state.currentJob.bvnJob
                                                        .first.status
                                                        ?.toLowerCase() ==
                                                    "failed"
                                                ? null
                                                : 1,
                                          ),
                                          trailing: customRow(
                                              state.currentJob, context, true),
                                          isThreeLine: state.currentJob.bvnJob
                                                      .first.status
                                                      ?.toLowerCase() ==
                                                  "failed"
                                              ? true
                                              : false,
                                          subtitle: state.currentJob.bvnJob
                                                      .first.status
                                                      ?.toLowerCase() ==
                                                  "failed"
                                              ? Text(
                                                  state.currentJob.bvnJob.first
                                                          .resultText ??
                                                      'We are unable to verify your identity with the information provided',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall!
                                                      .copyWith(
                                                          color:
                                                              theme.hintColor),
                                                )
                                              : null),
                            ),
                          ),
                          ValueListenableBuilder(
                              valueListenable: _processingBvnJob,
                              builder: (context, value, _) {
                                return value
                                    ? ConstrainedBox(
                                        constraints: BoxConstraints(
                                            minHeight: 12,
                                            minWidth: 12,
                                            maxHeight: 30,
                                            maxWidth: 30),
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                              right: 16, top: 16),
                                          child: CircularProgressIndicator(),
                                        ))
                                    : SizedBox.shrink();
                              })
                        ],
                      ),
                      // YMargin(20),
                      //  YMargin(20),
                      widget.bvnOnly
                          ? SizedBox.shrink()
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: InkWell(
                                    onTap: () async {
                                      if (!kycState.isNinVerified) {
                                        if (state.currentJob.ninJob.isEmpty) {
                                          if (state.currentJob.runEnhanced ==
                                                  null ||
                                              state.currentJob.runEnhanced ==
                                                  false) {
                                            initiateBiometricVerification(
                                                "nin");
                                          } else {
                                            Navigator.pop(context);
                                            context.pushNamed(
                                              DrawerVerifyNINPath,
                                              extra: VerifyNinScreenArgs(
                                                  AuthPageType.verifyKycNin),
                                            );
                                          }
                                        } else {
                                          if (state.currentJob.ninJob.first
                                                      .status
                                                      ?.toLowerCase() !=
                                                  'running' &&
                                              state.currentJob.ninJob.first
                                                      .status
                                                      ?.toLowerCase() !=
                                                  "success") {
                                            if (state.currentJob.runEnhanced ==
                                                    null ||
                                                state.currentJob.runEnhanced ==
                                                    false) {
                                              initiateBiometricVerification(
                                                  "nin");
                                            } else {
                                              Navigator.pop(context);
                                              context.pushNamed(
                                                DrawerVerifyNINPath,
                                                extra: VerifyNinScreenArgs(
                                                    AuthPageType.verifyKycNin),
                                              );
                                            }
                                          }
                                        }
                                      }
                                    },
                                    child: kycState.isNinVerified
                                        ? ListTile(
                                            title: Text(
                                              'Verify your NIN',
                                            ),
                                            trailing:
                                                defaultCustomRowVerified(),
                                          )
                                        : state.currentJob.ninJob.isEmpty
                                            ? ListTile(
                                                title: Text(
                                                  'Verify your NIN',
                                                ),
                                                trailing:
                                                    defaultCustomRowUnVerified(),
                                              )
                                            : ListTile(
                                                title: Text(
                                                  'Verify your NIN',
                                                  maxLines: state
                                                              .currentJob
                                                              .ninJob
                                                              .first
                                                              .status
                                                              ?.toLowerCase() ==
                                                          "failed"
                                                      ? null
                                                      : 1,
                                                ),
                                                isThreeLine: state.currentJob
                                                        .ninJob.first.status
                                                        ?.toLowerCase() ==
                                                    "failed",
                                                trailing: customRow(
                                                    state.currentJob,
                                                    context,
                                                    false),
                                                subtitle: state.currentJob
                                                            .ninJob.first.status
                                                            ?.toLowerCase() ==
                                                        "failed"
                                                    ? Text(
                                                        state
                                                                .currentJob
                                                                .ninJob
                                                                .first
                                                                .resultText ??
                                                            'We are unable to verify your identity with the information provided',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall!
                                                            .copyWith(
                                                                color: theme
                                                                    .hintColor),
                                                      )
                                                    : null,
                                              ),
                                  ),
                                ),
                                ValueListenableBuilder(
                                    valueListenable: _processingNinJob,
                                    builder: (context, value, _) {
                                      return value
                                          ? ConstrainedBox(
                                              constraints: BoxConstraints(
                                                  minHeight: 12,
                                                  minWidth: 12,
                                                  maxHeight: 30,
                                                  maxWidth: 30),
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 16, top: 16),
                                                child:
                                                    CircularProgressIndicator(),
                                              ))
                                          : SizedBox.shrink();
                                    })
                              ],
                            ),
                      contactSupportButton(state.currentJob)
                    ],
                  );
                }
              }

              return child;
            },
          ),
          BlocListener<SmileJobCubit, SmileJobState>(
            listener: (context, state) {
              //fetch fresh copy of smile job after validation
              if (state is SmileJobStatusRefresh) {
                smileJobStatusCubit.checkJobStatus();
              }
            },
            child: SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  void handleJobLoader(String jobType, bool value) {
    if (jobType == "nin") {
      _processingNinJob.value = value;
    }
    if (jobType == "bvn") {
      _processingBvnJob.value = value;
    }
  }

  Future<void> initiateBiometricVerification(String jobType) async {
    if (jobType == "nin") {
      ninUpdateInit();
    }
    if (jobType == "bvn") {
      bvnUpdateInit();
    }
    handleJobLoader(jobType, true);
    final res = await locator
        .get<InitiateJob>()
        .call(InitiateJobRequest(jobtype: jobType));

    res.maybeWhen(
      success: (response) async {
        handleJobLoader(jobType, false);

        final flavorIsDevelopment = config.environment == Environment.dev;
        final helper = JSHelper();
        final permissionResponse =
            await helper.requestCameraAndAudioPermissions(() {
          final usuableJobType = jobType == 'nin' ? "NIN_V2" : "BVN";
          if (kIsWeb) {
            final env = flavorIsDevelopment ? "sandbox" : "live";
            Navigator.pop(context);
            return helper.configureSmileIdentityWebIntegration(
              response.data!.token!,
              config.smileWebCallBackUrl!,
              env,
              usuableJobType,
              (String type) {
                SmileResult result = SmileResult.values.byName(type);
                switch (result) {
                  case SmileResult.success:
                    _smileJobCubit.validateSmileJob(response.data!.jobId);
                    context.pop();
                    KYCVerificationModal.show(context);
                    break;
                  case SmileResult.close:
                    // do nothing
                    break;
                  case SmileResult.error:
                    // TODO: render error message if necessary
                    break;
                }
              },
            );
          }

          //   Navigator.pop(context);
          if (response.data?.token != null && response.data?.jobId != null) {
            context.pushNamed(smileWebPath,
                extra: SmileWebViewArgs(response.data!.token!, usuableJobType,
                    onSuccess: (value) async {
                  _smileJobCubit.validateSmileJob(response.data!.jobId);
                  context.pop();
                  Navigator.pop(context);
                  KYCVerificationModal.show(context);
                }, onClose: () {
                  context.pop(); 
                  Toast.error("Verification process exited", context);
                }, onFailed: () {
                  context.pop();
                  Toast.error("Verification process Failed", context);
                }));
          } else {
            Toast.error("Invalid response data", context);
          }
        }, false);

        if (!permissionResponse) {
          showDialog(
              context: context,
              builder: (ctx) {
                return PermissionDialog();
              });
        }
      },
      apiFailure: (e, _) {
        handleJobLoader(jobType, false);
        Toast.apiError(e, context);
      },
      orElse: () {
        handleJobLoader(jobType, false);
        Toast.error("Error occured", context);
      },
    );
  }

  Widget contactSupportButton(CurrentJob job) {
    bool? bvnNeedSupport;
    bool? ninNeedSupport;
    if (job.bvnJob.isNotEmpty) {
      bvnNeedSupport = job.bvnJob.first.support;
    }

    if (job.ninJob.isNotEmpty) {
      ninNeedSupport = job.ninJob.first.support;
    }

    if (bvnNeedSupport == true || ninNeedSupport == true) {
      return InkWell(
        onTap: () {
          Intercom.instance.displayMessageComposer(
            'Hello, I am having issues with verifying my KYC, please can you assist me?',
          );
        },
        child: Container(
          margin: EdgeInsets.only(
              top: defaultVerticalContentPadding,
              left: defaultHorizontalContentPadding,
              right: defaultHorizontalContentPadding),
          padding: EdgeInsets.symmetric(
            vertical: defaultVerticalContentPadding,
          ),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                  width: 0.5, color: Theme.of(context).colorScheme.error)),
          child: Center(
              child: Text('Contact Support',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(color: Theme.of(context).colorScheme.error))),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget customRow(CurrentJob job, BuildContext context, bool isBvn) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
              color: getStatusTextBgColor(
                  isBvn ? job.bvnJob.first.status! : job.ninJob.first.status!),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                  color: getStatusTextBorderColor(isBvn
                      ? job.bvnJob.first.status!
                      : job.ninJob.first.status!))),
          child: Text(
            getstatusText(
                isBvn ? job.bvnJob.first.status! : job.ninJob.first.status!),
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: getStatusTextColor(isBvn
                    ? job.bvnJob.first.status!
                    : job.ninJob.first.status!)),
          ),
        ),
        XMargin(20),
        Icon(
          Icons.arrow_forward_ios,
          size: 10,
          color: Theme.of(context).hintColor,
        ),
      ],
    );
  }

  Widget defaultCustomRowUnVerified() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(4),
            /* border: Border.all(
                  color: getStatusTextBorderColor(isBvn
                      ? job.bvnJob.first.status
                      : job.ninJob.first.status))*/
          ),
          child: Text('NOT STARTED',
              style: Theme.of(context).textTheme.bodySmall!),
        ),
        XMargin(20),
        Icon(Icons.arrow_forward_ios,
            size: 10, color: Theme.of(context).colorScheme.onPrimaryContainer),
      ],
    );
  }

  Widget defaultCustomRowVerified() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: defaultColorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(4),
            /* border: Border.all(
                  color: getStatusTextBorderColor(isBvn
                      ? job.bvnJob.first.status
                      : job.ninJob.first.status))*/
          ),
          child: Text(
            'VERIFIED',
            style: Theme.of(context)
                .textTheme
                .bodySmall!
                .copyWith(color: defaultColorScheme.primary),
          ),
        ),
        XMargin(20),
        Icon(
          Icons.arrow_forward_ios,
          size: 10,
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
      ],
    );
  }

  // successColorScheme.surfaceVariant

  String getstatusText(String text) {
    switch (text.toLowerCase()) {
      case "running":
        return 'IN REVIEW';
      case "success":
        return 'VERIFIED';
      case 'failed':
        return 'FAILED';
      default:
        return "NOT STARTED";
    }
  }

  Color getStatusTextColor(String text) {
    switch (text.toLowerCase()) {
      case "running":
        return runningColorScheme.primary;
      case "success":
        return successColorScheme.primary;
      case 'failed':
        return errorColorScheme.primary;
      default:
        return defaultColorScheme.primary;
    }
  }

  Color getStatusTextBgColor(String text) {
    switch (text.toLowerCase()) {
      case "running":
        return runningColorScheme.surfaceContainerHighest;
      case "success":
        return successColorScheme.surfaceContainerHighest;
      case 'failed':
        return errorColorScheme.surfaceContainerHighest;
      default:
        return defaultColorScheme.surfaceContainerHighest;
    }
  }

  Color getStatusTextBorderColor(String text) {
    switch (text.toLowerCase()) {
      case "running":
        return Color(0XFFFEF4EB);
      case "success":
        return successColorScheme.primary;
      case 'failed':
        return Color(0XFFFEEBEB);
      default:
        return Color(0XFFEBF0FE);
    }
  }
}
