import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class KycShimmerWidget extends StatelessWidget {
  const KycShimmerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Shimmer.fromColors(
            baseColor: Colors.grey.withValues(alpha: 0.5),
            highlightColor: Theme.of(context).colorScheme.primaryContainer,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 25,
                  width: MediaQuery.of(context).size.width * 0.6,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
                <PERSON><PERSON><PERSON><PERSON>(10),
                Container(
                  height: 10,
                  width: MediaQuery.of(context).size.width,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
                <PERSON><PERSON><PERSON><PERSON>(10),
                Container(
                  height: 10,
                  width: MediaQuery.of(context).size.width,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
                <PERSON><PERSON><PERSON><PERSON>(60),
                Container(
                  height: 25,
                  width: MediaQuery.of(context).size.width,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
                YMargin(10),
                YMargin(20),
                Container(
                  height: 25,
                  width: MediaQuery.of(context).size.width,
                  color: Colors.grey.withValues(alpha: 0.5),
                ),
              ],
            )),
      ],
    );
  }
}
