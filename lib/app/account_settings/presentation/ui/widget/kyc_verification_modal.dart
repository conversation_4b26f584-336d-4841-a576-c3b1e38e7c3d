import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class KYCVerificationModal extends StatelessWidget {
  const KYCVerificationModal({super.key});

  static Future<void> show(context) {
    return showModalBottomSheet(
      isScrollControlled: true,
      useRootNavigator: true,
      context: context,
      builder: (_) => KYCVerificationModal(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: screenPadding,
      height: 300,
      child: Column(
        children: [
          SvgPicture.asset(
            kSvgKycVerified,
            width: 70,
          ),
          YMargin(40),
          Text(
            'KYC Completed',
            style: Theme.of(context)
                .textTheme
                .titleLarge!
                .copyWith(fontWeight: FontWeight.w500),
          ),
          YMargin(10),
          Text(
            'You will be notified once verification review has been concluded',
            style: Theme.of(context).textTheme.bodyMedium!,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
