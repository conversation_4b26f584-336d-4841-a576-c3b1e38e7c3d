import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/stripe_web.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_shimmer_widget.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_state.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_status_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_status_state.dart';
import 'package:shop/app/credit/presentation/logic/methods/events_tracking.dart';
import 'package:shop/app/credit/presentation/ui/screens/business_verification/mobile/td_business_verification.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class KycStripeModal extends StatefulWidget {
  const KycStripeModal({super.key, this.bvnOnly = false});
  final bool bvnOnly;
  @override
  State<KycStripeModal> createState() => _KycStripeModalState();

  static Future showKycModal(BuildContext context,
      [bool bvnOnly = true, bool useRootNavigator = false]) {
    return showModalBottomSheet(
      isScrollControlled: true,
      useRootNavigator: useRootNavigator,
      context: context,
      builder: (_) => SizedBox(
        width: screenWidth(context),
        // padding: screenPadding,
        height: screenHeight(context, percent: 0.6),
        child: KycStripeModal(
          bvnOnly: bvnOnly,
        ),
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }
}

class _KycStripeModalState extends State<KycStripeModal> with EventsTracking {
  late StripeStatusCubit stripeStatusCubit;
  late StripeCubit stripeCubit;

  @override
  void initState() {
    super.initState();
    stripeStatusCubit = BlocProvider.of(context, listen: false);
    stripeCubit = BlocProvider.of(context, listen: false);
    stripeStatusCubit.getVerificationStatus();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StripeStatusCubit, StripeStatusState>(
      builder: (context, stateStatus) {
        Widget widget = const SizedBox.shrink();

        if (stateStatus is StripeStatusLoading) {
          widget =
              const Padding(padding: screenPadding, child: KycShimmerWidget());
        }

        if (stateStatus is StripeVerificationStatusFailed) {
          widget = KErrorScreen(stateStatus.msg, () {
            stripeStatusCubit.getVerificationStatus();
          });
        }

        if (stateStatus is StripeVerificationStatusRetrieved) {
          String idStatus = stateStatus.response.identityVerification != null
              ? stateStatus.response.identityErrorCode != null
                  ? 'failed'
                  : stateStatus.response.identityVerification!
              : "";
          widget = Scaffold(
            appBar: AppBar(
                leading: InkWell(
              onTap: () => Navigator.pop(context),
              child: const Icon(Icons.clear),
            )),
            body: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: screenPadding,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Complete your account setup',
                            style: Theme.of(context)
                                .textTheme
                                .bodyLarge!
                                .copyWith(fontWeight: FontWeight.w600),
                          ),
                          const YMargin(20),
                          Text(
                              'To access all features on TradeDepot Wholesale please complete the following actions',
                              style: Theme.of(context).textTheme.bodyMedium!),
                        ],
                      ),
                    ),
                    BlocBuilder<StripeCubit, StripeState>(
                      builder: (context, status) {
                        Widget currentWidget = const SizedBox.shrink();

                        if (status is StripeVerificationLoading) {
                          currentWidget = Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: customRow('Verify your identity',
                                        idStatus, context),
                                  ),
                                  Row(
                                    children: [
                                      const XMargin(10),
                                      ConstrainedBox(
                                          constraints:
                                              const BoxConstraints.tightFor(
                                                  width: 10, height: 10),
                                          child:
                                              const CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ))
                                    ],
                                  )
                                ],
                              ),
                              stateStatus.response.identityRejectionReason !=
                                      null
                                  ? errorText(stateStatus
                                      .response.identityRejectionReason!)
                                  : const SizedBox.shrink()
                            ],
                          );
                        } else if (status is StripeVerificationInitiated) {
                          stripeCubit.setStateToInitial();
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            context.pushNamed(stripeWebPath,
                                extra: StripeWebViewArgs(
                                    verificationUrl:
                                        status.response.verificationUrl!,
                                    type: VerificationType.identity));
                            Navigator.pop(context);
                          });

                          currentWidget = InkWell(
                              onTap: () {
                                if (stateStatus.response.identityVerification
                                        ?.toLowerCase() ==
                                    'verified') {
                                  return;
                                }
                                if (stateStatus.response.identityVerification
                                        ?.toLowerCase() ==
                                    'pending') {
                                  if (stateStatus.response.verificationSession
                                          ?.kyc.stripeStatus ==
                                      "requires_input") {
                                    context.pushNamed(stripeWebPath,
                                        extra: StripeWebViewArgs(
                                            verificationUrl: stateStatus
                                                .response
                                                .verificationSession!
                                                .kyc
                                                .url!,
                                            type: VerificationType.identity));
                                    Navigator.pop(context);
                                  }
                                  if (stateStatus.response.verificationSession
                                          ?.kyc.stripeStatus ==
                                      "processing") {
                                    Toast.info(
                                        "Verification under review", context);
                                  }
                                  return;
                                }
                                stripeCubit.initiateVerification(
                                    InitiateStripeRequest(
                                        redirectUrl: "https://tradedepot.com"));
                              },
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  customRow(
                                      'Verify your identity', idStatus, context,
                                      errorwidget: stateStatus.response
                                                  .identityRejectionReason !=
                                              null
                                          ? errorText(stateStatus.response
                                              .identityRejectionReason!)
                                          : null),
                                ],
                              ));
                        } else {
                          currentWidget = InkWell(
                              onTap: () {
                                if (stateStatus.response.identityVerification
                                        ?.toLowerCase() ==
                                    'verified') {
                                  return;
                                }
                                if (stateStatus.response.identityVerification
                                        ?.toLowerCase() ==
                                    'pending') {
                                  if (stateStatus.response.verificationSession
                                          ?.kyc.stripeStatus ==
                                      "requires_input") {
                                    context.pushNamed(stripeWebPath,
                                        extra: StripeWebViewArgs(
                                            verificationUrl: stateStatus
                                                .response
                                                .verificationSession!
                                                .kyc
                                                .url!,
                                            type: VerificationType.identity));
                                    Navigator.pop(context);
                                  }
                                  if (stateStatus.response.verificationSession
                                          ?.kyc.stripeStatus ==
                                      "processing") {
                                    Toast.info(
                                        "Verification under review", context);
                                  }
                                  return;
                                }
                                stripeCubit.initiateVerification(
                                    InitiateStripeRequest(
                                        redirectUrl: "https://tradedepot.com"));
                              },
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: customRow(
                                            'Verify your identity', idStatus, context,
                                            errorwidget: stateStatus.response
                                                            .identityErrorCode !=
                                                        null ||
                                                    stateStatus.response.identityRejectionReason !=
                                                        null
                                                ? errorText(AppLocalizations.of(context)!.error_description(stateStatus.response.identityErrorCode.toString()) !=
                                                        stateStatus.response
                                                            .identityErrorCode
                                                            .toString()
                                                    ? AppLocalizations.of(context)!
                                                        .error_description(
                                                            stateStatus.response.identityErrorCode.toString())
                                                    : stateStatus.response.identityRejectionReason ?? '')
                                                : null),
                                      ),
                                    ],
                                  ),
                                ],
                              ));
                        }
                        return currentWidget;
                      },
                    ),
                    InkWell(
                        onTap: () {
                          if (stateStatus.response.companyVerification
                                  ?.toLowerCase() ==
                              'verified') {
                            return;
                          }
                          if (stateStatus.response.companyVerification
                                  ?.toLowerCase() ==
                              'pending') {
                            if (stateStatus.response.verificationSession?.kyb
                                    .stripeStatus ==
                                "requires_input") {
                              context.pushNamed(stripeWebPath,
                                  extra: StripeWebViewArgs(
                                      verificationUrl: stateStatus.response
                                          .verificationSession!.kyb.url!,
                                      type: VerificationType.identity));
                              Navigator.pop(context);
                            }
                            if (stateStatus.response.verificationSession?.kyb
                                    .stripeStatus ==
                                'processing') {
                              Toast.info("Verification under review", context);
                            }
                            return;
                          }

                          context.pushNamed(tdBusinessVerificationPath,
                              extra: TdBusinessVerificationArgs(
                                  director: stateStatus.response
                                      .lastVerificationSession?.director,
                                  companyDetails:
                                      stateStatus.response.company));
                          Navigator.pop(context);
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            customRow(
                                'Verify your business',
                                stateStatus.response.companyVerification != null
                                    ? stateStatus.response.companyErrorCode !=
                                            null
                                        ? 'failed'
                                        : stateStatus
                                            .response.companyVerification!
                                    : "",
                                context,
                                errorwidget:
                                    stateStatus.response.companyErrorCode !=
                                                null ||
                                            stateStatus.response
                                                    .companyRejectionReason !=
                                                null
                                        ? errorText(stateStatus
                                            .response.companyRejectionReason!)
                                        : null),
                          ],
                        )),
                  ],
                ),
              ],
            ),
          );
        }
        return widget;
      },
    );
  }

  Widget customRow(String text, String status, BuildContext context,
      {Widget? errorwidget}) {
    return ListTile(
      subtitle: errorwidget,
      isThreeLine: false,
      title: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: getStatusTextBgColor(status),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              getstatusText(status),
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: getStatusTextColor(status)),
            ),
          ),
          const XMargin(20),
          Icon(
            Icons.arrow_forward_ios,
            size: 10,
            color: Theme.of(context).hintColor,
          ),
        ],
      ),
    );
  }

  String getstatusText(String text) {
    switch (text.toLowerCase()) {
      case "pending":
        return 'PENDING';
      case "verified":
        return 'VERIFIED';
      case 'failed':
        return 'FAILED';
      default:
        return "NOT STARTED";
    }
  }

  Color getStatusTextColor(String text) {
    switch (text.toLowerCase()) {
      case "pending":
        return runningColorScheme.primary;
      case "verified":
        return successColorScheme.primary;
      case 'failed':
        return errorColorScheme.primary;
      default:
        return defaultColorScheme.primary;
    }
  }

  Color getStatusTextBgColor(String text) {
    switch (text.toLowerCase()) {
      case "pending":
        return runningColorScheme.surfaceContainerHighest;
      case "verified":
        return successColorScheme.surfaceContainerHighest;
      case 'failed':
        return errorColorScheme.surfaceContainerHighest;
      default:
        return defaultColorScheme.surfaceContainerHighest;
    }
  }

  Widget errorText(String reason) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const YMargin(5),
        Text(
          reason.trim(),
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(color: Theme.of(context).colorScheme.error),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }
}
