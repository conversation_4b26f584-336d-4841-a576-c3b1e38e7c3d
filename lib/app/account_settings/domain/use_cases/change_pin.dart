import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/repos/change_pin_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ChangePin
    with UseCases<ApiResult<Map<String, dynamic>?>, ChangePinParams> {
  const ChangePin(this.repo);

  /// Instance of [ChangePinRepo].
  final ChangePinRepo? repo;

  /// Change a user pin.
  ///
  /// Pin is used for login and other authorisation and validation requests.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(ChangePinParams params) {
    return repo!.changePin(params);
  }
}
