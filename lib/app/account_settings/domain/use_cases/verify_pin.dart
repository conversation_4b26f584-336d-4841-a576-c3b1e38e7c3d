import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/repos/change_pin_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class VerifyPin
    with UseCases<ApiResult<Map<String, dynamic>?>, VerifyPinParams> {
  const VerifyPin(this.repo);

  /// Instance of [ChangePinRepo].
  final ChangePinRepo? repo;

  /// Validate a user pin.
  ///
  /// Pin is used for login and other authorisation and validation requests.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(VerifyPinParams params) {
    return repo!.verifyPin(params);
  }
}
