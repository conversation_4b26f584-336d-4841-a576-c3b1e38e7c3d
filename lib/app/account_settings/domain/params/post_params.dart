class ChangePinParams {
  final String? newPin;
  final oldPin;

  ChangePinParams(this.oldPin, this.newPin);

  Map<String, dynamic> toMap() {
    return {
      'oldPin': oldPin,
      'newPin': newPin,
    };
  }
}

class VerifyPinParams {
  final String pin;
  VerifyPinParams({
    required this.pin,
  });

  Map<String, dynamic> toMap() {
    return {
      'pin': pin,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

enum SmileResult { success, error, close }