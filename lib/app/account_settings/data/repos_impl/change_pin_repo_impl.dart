import 'package:shop/src/components/src/utils/dio.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/connectivity/src/network_connection.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/repos/change_pin_repo.dart';
import 'package:shop/app/account_settings/data/data_source/change_pin_remote_data_source.dart';

/// Implements the [ChangePinRepo] abstract class.
///
/// [ChangePinRepo] make calls to [ChangePinRemoteDataSource].
class ChangePinRepoImpl implements ChangePinRepo {
  /// Instance of [ChangePinRemoteDataSource].
  final ChangePinRemoteDataSource? _changePinRemoteDataSource;

  /// Returns network connectivity state.
  final NetworkConnection? networkConnection;

  ChangePinRepoImpl(this._changePinRemoteDataSource, this.networkConnection);

  /// Call [ChangePinRemoteDataSource] to change a user pin.
  @override
  Future<ApiResult<Map<String, dynamic>?>> changePin(
    ChangePinParams params,
  ) async {
    try {
      if (await networkConnection!.isDeviceConnected) {
        final res = await _changePinRemoteDataSource!.changePin(params);
        return ApiResult.success(data: res);
      } else {
        return ApiResult.apiFailure(
            error: ApiExceptions.noInternetConnection());
      }
    } catch (e, s) {
      ErrorHandler.report(e, s);
      return ApiResult.apiFailure(
        error: ApiExceptions.getDioException(e),
      );
    }
  }

  /// Call [ChangePinRemoteDataSource] to validate user inputted pin.
  @override
  Future<ApiResult<Map<String, dynamic>?>> verifyPin(
      VerifyPinParams params) async {
    if (await networkConnection!.isDeviceConnected) {
      return dioInterceptor(
        () => _changePinRemoteDataSource!.verifyPin(params),
      );
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }
}
