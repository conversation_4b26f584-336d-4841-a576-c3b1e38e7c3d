import 'package:shop/app/account_settings/data/data_source/change_pin_remote_data_source.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Implements [ChangePinRemoteDataSource] abstract class.
///
/// Makes network call.
class ChangePinRemoteDataSourceImpl implements ChangePinRemoteDataSource {
  static const String _changePinPath = 'shop/v4/changePin';
  static const String _verifyPinPath = 'shop/v4/verifyPin';

  /// The API Client instance.
  ///
  /// Handles all http network request
  final TdApiClient? _apiClient;

  /// API base url
  final String? _firebaseServiceUrl;

  const ChangePinRemoteDataSourceImpl(
    this._apiClient,
    this._firebaseServiceUrl,
  );

  /// change a user pin.
  @override
  Future<Map<String, dynamic>?> changePin(ChangePinParams params) async {
    final res = await _apiClient!.post(
      '$_firebaseServiceUrl/$_changePinPath',
      data: params.toMap(),
    );
    return res.data;
  }

  /// Validate user inputted pin
  @override
  Future<Map<String, dynamic>?> verifyPin(VerifyPinParams params) async {
    final res = await _apiClient!.post(
      '$_firebaseServiceUrl/$_verifyPinPath',
      data: params.toMap(),
    );
    return res.data;
  }
}
