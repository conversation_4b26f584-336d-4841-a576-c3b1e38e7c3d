class UserStatus {
  final String? firstName;
  final bool? pinEnabled;
  final bool hasRetailStore;

  const UserStatus({
    required this.firstName,
    required this.pinEnabled,
    required this.hasRetailStore,
  });

  factory UserStatus.fromMap(Map<String, dynamic> data) {
    return UserStatus(
      firstName: data['firstName'],
      pinEnabled: data['pinEnabled'],
      hasRetailStore: data['hasRetailStore'] ?? false,
    );
  }
}
