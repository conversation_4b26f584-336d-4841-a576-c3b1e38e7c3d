class Country {
  final String? name;
  final String? countryCode;
  final String? dialCode;
  final String flagUri;
  final String? isoCode;
  final String? hint;

  Country({
    required this.name,
    required this.countryCode,
    required this.dialCode,
    required this.flagUri,
    required this.isoCode,
    required this.hint,
  });

  factory Country.fromMap(Map<String, dynamic> data) {
    return Country(
      name: data['en_short_name'],
      countryCode: data['alpha_2_code'],
      dialCode: data['dial_code'],
      isoCode: data['alpha_3_code'],
      hint: data['hint'],
      flagUri: 'assets/images/flags/${data['alpha_2_code'].toLowerCase()}.png',
    );
  }
}
