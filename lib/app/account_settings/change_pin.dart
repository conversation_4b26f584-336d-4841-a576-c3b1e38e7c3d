import 'package:shop/app/account_settings/data/data_source/impl/change_pin_remote_data_source_impl.dart';
import 'package:shop/app/account_settings/data/data_source/change_pin_remote_data_source.dart';
import 'package:shop/app/account_settings/domain/repos/change_pin_repo.dart';
import 'package:shop/app/account_settings/data/repos_impl/change_pin_repo_impl.dart';
import 'package:shop/app/account_settings/domain/use_cases/change_pin.dart';
import 'package:shop/app/account_settings/domain/use_cases/verify_pin.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

void registerChangePinDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<ChangePinRemoteDataSource>(
    () => ChangePinRemoteDataSourceImpl(
      locator(),
      config.firebaseServiceUrl,
    ),
  );

  // Repositories
  locator.registerLazySingleton<ChangePinRepo>(
    () => ChangePinRepoImpl(locator(), locator()),
  );

  // Use cases
  locator.registerLazySingleton(() => ChangePin(locator()));
  locator.registerLazySingleton(() => VerifyPin(locator()));
}
