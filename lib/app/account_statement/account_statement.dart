import 'package:shop/app/account_statement/data/data_source/account_statement_remote_data_source.dart';
import 'package:shop/app/account_statement/data/data_source/impl/account_statement_remote_data_source_impl.dart';
import 'package:shop/app/account_statement/data/repos_impl/account_statement_repo_impl.dart';
import 'package:shop/app/account_statement/domain/repos/account_statement_repo.dart';
import 'package:shop/app/account_statement/domain/use_cases/get_account_statement.dart';
import 'package:shop/app/account_statement/domain/use_cases/send_account_statement.dart';
import 'package:shop/app/account_statement/domain/use_cases/view_account_statement.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

void registerAccountStatementDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<AccountStatementRemoteDataSource>(
    () => AccountStatementRemoteDataSourceImpl(
      locator(),
      config.firebaseServiceUrl!,
    ),
  );

  // Repositories
  locator.registerLazySingleton<AccountStatementRepo>(
      () => AccountStatementRepoImpl(locator()));

  // Use cases

  locator.registerLazySingleton(() => GetAccountStatement(locator()));
  locator.registerLazySingleton(() => ViewAccountStatement(locator()));
  locator.registerLazySingleton(() => SendAccountStatement(locator()));
}
