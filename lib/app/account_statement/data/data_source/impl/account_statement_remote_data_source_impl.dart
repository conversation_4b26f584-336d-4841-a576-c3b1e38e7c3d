import 'dart:io';

import 'package:shop/app/account_statement/data/data_source/account_statement_remote_data_source.dart';
import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

/// Implements [TransactionsRemoteDataSource] abstract class.
///
/// Makes network call.
class AccountStatementRemoteDataSourceImpl
    implements AccountStatementRemoteDataSource {
  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request.
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  AccountStatementRemoteDataSourceImpl(
    this._apiClient,
    this._firebaseServiceUrl,
  );

  @override
  Future<AccountStatement> getAccountStatement(
      AccountStatementParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "account-statement",
        "method": "get",
        "params": params.toMap(),
        "type": "claimsUrl",
      },
    );

    final statement = res.data['data'];
    return AccountStatement.fromMap(statement);
  }

  @override
  Future<AccountStatementFile> viewAccountStatement(
      AccountStatementParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "account-statement",
        "method": "get",
        "params": params.toMap(),
        "type": "claimsUrl",
      },
    );

    final fileUrlPath = Uri.parse(res.data['data']['url']);

    final getFile = await http.get(fileUrlPath);

    if (getFile.statusCode != 200) {
      throw Error();
    }

    var bytes = getFile.bodyBytes;
    var tempDir = await getTemporaryDirectory();
    File file = File('${tempDir.path}/invoice.pdf');
    await file.writeAsBytes(bytes, flush: true);

    return AccountStatementFile(file: file, urlPath: fileUrlPath.toString());
  }

  @override
  Future<bool> sendAccountStatement(AccountStatementParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "account-statement",
        "method": "get",
        "params": params.toMap(),
        "type": "claimsUrl",
      },
    );

    if (res.statusCode == 200) {
      return true;
    }
    return false;
  }
}
