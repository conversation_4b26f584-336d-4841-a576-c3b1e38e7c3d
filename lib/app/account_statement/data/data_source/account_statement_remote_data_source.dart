import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';

/// Base class for [OrderRemoteDataSourceImpl].
abstract class AccountStatementRemoteDataSource {
  Future<AccountStatement> getAccountStatement(AccountStatementParams params);
  Future<bool> sendAccountStatement(AccountStatementParams params);
  Future<AccountStatementFile> viewAccountStatement(
      AccountStatementParams params);
}
