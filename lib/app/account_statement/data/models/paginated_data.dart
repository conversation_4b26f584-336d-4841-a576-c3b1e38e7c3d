import 'package:equatable/equatable.dart';

class PaginatedData<T> extends Equatable {
  final int page;
  final int pageCount;
  final List<T> data;
  final bool isLoading;
  final String error;

  const PaginatedData({
    this.page = 0,
    this.pageCount = 0,
    this.data = const [],
    this.isLoading = false,
    this.error = '',
  });

  bool get hasNext {
    return page < pageCount;
  }

  bool get hasData {
    return data.isNotEmpty;
  }

  int get next {
    return page + 1;
  }

  @override
  toString() {
    return "page => $page, pageCount => $pageCount, data => $data, next => $next, hasNext => $hasNext, isLoading => $isLoading, error => $error";
  }

  PaginatedData<T> copyWith({
    int? page,
    int? pageCount,
    List<T>? data,
    bool? isLoading,
    String? error,
  }) {
    return PaginatedData(
      page: page ?? this.page,
      pageCount: pageCount ?? this.pageCount,
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  PaginatedData<T> addData(PaginatedData<T> pageData) {
    if (pageData.page == 0) {
      return pageData;
    }
    if (this == pageData) return this;
    final items = [...data, ...pageData.data];
    // final uniqueItems = items.toSet();

    return PaginatedData(
      page: pageData.page,
      pageCount: pageData.pageCount,
      data: items,
    );
  }

  PaginatedData<T> addSingleItem(T item, {bool unshift = true}) {
    final items = unshift ? [item, ...data] : [...data, item];
    return PaginatedData(
      page: page,
      pageCount: pageCount,
      data: items,
    );
  }

  @override
  List<Object?> get props => [
        page,
        pageCount,
        data,
        isLoading,
        isLoading,
        data.length,
        error,
      ];
}
