import 'dart:io';

import 'package:equatable/equatable.dart';

class AccountStatementFile extends Equatable {
  final File? file;
  final String? urlPath;
  const AccountStatementFile({
    this.file,
    this.urlPath,
  });

  AccountStatementFile copyWith({
    File? file,
    String? urlPath,
  }) {
    return AccountStatementFile(
      file: file ?? this.file,
      urlPath: urlPath ?? this.urlPath,
    );
  }

  @override
  String toString() => 'AccountStatementFile(file: $file, urlPath: $urlPath)';

  @override
  List<Object> get props => [];
}
