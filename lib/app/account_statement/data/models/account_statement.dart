import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:shop/app/transactions/data/models/order_data.dart';
import 'package:td_commons_flutter/models/order_item.dart';

class AccountStatement extends Equatable {
  final num? totalCredits;
  final num? totalDebits;
  final num? balance;
  final List<Transaction>? transactions;

  const AccountStatement({
    this.totalCredits,
    this.totalDebits,
    this.balance,
    this.transactions,
  });

  @override
  List<Object> get props => [];

  AccountStatement copyWith({
    num? totalCredits,
    num? totalDebits,
    num? balance,
    List<Transaction>? transactions,
  }) {
    return AccountStatement(
      totalCredits: totalCredits ?? this.totalCredits,
      totalDebits: totalDebits ?? this.totalDebits,
      balance: balance ?? this.balance,
      transactions: transactions ?? this.transactions,
    );
  }

  factory AccountStatement.fromMap(Map<String, dynamic> map) {
    return AccountStatement(
      totalCredits: map['totalCredits'],
      totalDebits: map['totalDebits'],
      balance: map['balance'],
      transactions: map['transactions'] != null
          ? List<Transaction>.from(
              map['transactions'].map(
                (item) => Transaction.fromMap(item),
              ),
            )
          : null,
    );
  }

  factory AccountStatement.fromJson(String source) =>
      AccountStatement.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AccountStatement(totalCredits: $totalCredits, totalDebits: $totalDebits, balance: $balance, transactions: $transactions)';
  }
}

class Transaction extends Equatable {
  final String? id;
  final List<OrderItem>? items;
  final String? invoiceType;
  final String? retailOutletId;
  final String? outletBusinessName;
  final String? shipmentId;
  final String? returnShipmentId;
  final String? paymentReference;
  final String? status;
  final Currency? currency;
  final num? processingCost;
  final num? shippingCosts;
  final int? invoiceNumber;
  final DateTime? issuedAt;
  final DateTime? createdAt;
  final String? userId;
  final num? exchangeRate;
  final num? shippingDiscount;
  final num? subTotal;
  final num? taxes;
  final num? total;
  final String? createdBy;
  final List<History>? history;
  final ShippingAddress? shippingAddress;
  final DateTime? updatedAt;

  const Transaction({
    this.id,
    this.items,
    this.invoiceType,
    this.retailOutletId,
    this.outletBusinessName,
    this.shipmentId,
    this.returnShipmentId,
    this.paymentReference,
    this.status,
    this.currency,
    this.processingCost,
    this.shippingCosts,
    this.invoiceNumber,
    this.issuedAt,
    this.createdAt,
    this.userId,
    this.exchangeRate,
    this.shippingDiscount,
    this.subTotal,
    this.taxes,
    this.total,
    this.createdBy,
    this.history,
    this.shippingAddress,
    this.updatedAt,
  });

  factory Transaction.fromMap(Map<String, dynamic> json) {
    return Transaction(
      id: json['_id'],
      items:
          List<OrderItem>.from(json['items'].map((x) => OrderItem.fromMap(x))),
      invoiceType: json['invoiceType'],
      retailOutletId: json['retailOutletId'],
      outletBusinessName: json['outletBusinessName'],
      shipmentId: json['shipmentId'],
      returnShipmentId: json['returnShipmentId'],
      paymentReference: json['paymentReference'],
      status: json['status'],
      currency: Currency.fromJson(json['currency']),
      processingCost: json['processingCost'],
      shippingCosts: json['shippingCosts'],
      invoiceNumber: json['invoiceNumber'],
      issuedAt:
          json['issuedAt'] != null ? DateTime.parse(json['issuedAt']) : null,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      userId: json['userId'],
      exchangeRate: json['exchangeRate'],
      shippingDiscount: json['shippingDiscount'],
      subTotal: json['subTotal'],
      taxes: json['taxes'],
      total: json['total'],
      createdBy: json['createdBy'],
      history:
          List<History>.from(json['history'].map((x) => History.fromJson(x))),
      shippingAddress: ShippingAddress.fromJson(json['shippingAddress']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  @override
  List<Object?> get props => [];
}

class Pagination extends Equatable {
  final num? page;
  final num? perPage;
  final num? totalPages;
  const Pagination({
    this.page = 1,
    this.perPage = 10,
    this.totalPages = 1,
  });

  Pagination copyWith({
    num? page,
    num? perPage,
    num? totalPages,
  }) {
    return Pagination(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (page != null) {
      result.addAll({'page': page});
    }
    if (perPage != null) {
      result.addAll({'perPage': perPage});
    }

    if (totalPages != null) {
      result.addAll({'totalPages': totalPages});
    }

    return result;
  }

  factory Pagination.fromMap(Map<String, dynamic> map) {
    return Pagination(
      page: num.parse(map['page'].toString()),
      perPage: num.parse(map['perPage'].toString()),
      totalPages: num.parse(map['totalPages'].toString()),
    );
  }

  String toJson() => json.encode(toMap());

  factory Pagination.fromJson(String source) =>
      Pagination.fromMap(json.decode(source));

  @override
  String toString() =>
      'Pagination(page: $page, perPage: $perPage, totalPages: $totalPages)';

  @override
  List<Object?> get props => [page, perPage, totalPages];
}
