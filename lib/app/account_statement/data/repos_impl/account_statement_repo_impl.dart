import 'package:shop/app/account_statement/data/data_source/account_statement_remote_data_source.dart';
import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/repos/account_statement_repo.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:td_flutter_core/service_result/service_result.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';

class AccountStatementRepoImpl implements AccountStatementRepo {
  /// Instance of [remoteDataSource].
  final AccountStatementRemoteDataSource _remoteDataSource;

  AccountStatementRepoImpl(this._remoteDataSource);

  /// Calls [remoteDataSource] to get the list of a user's orders.
  @override
  Future<ApiResult<AccountStatement>> getAccountStatement(
      AccountStatementParams params) {
    return dioInterceptor(
      () => _remoteDataSource.getAccountStatement(params),
    );
  }

  @override
  Future<ApiResult<AccountStatementFile>> viewAccountStatement(
      AccountStatementParams params) {
    return dioInterceptor(
      () => _remoteDataSource.viewAccountStatement(params),
    );
  }

  @override
  Future<ApiResult<bool>> sentAccountStatement(AccountStatementParams params) {
    return dioInterceptor(
      () => _remoteDataSource.sendAccountStatement(params),
    );
  }
}
