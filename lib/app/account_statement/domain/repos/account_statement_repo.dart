import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:td_flutter_core/service_result/service_result.dart';

abstract class AccountStatementRepo {
  Future<ApiResult<AccountStatement>> getAccountStatement(
      AccountStatementParams params);
  Future<ApiResult<AccountStatementFile>> viewAccountStatement(
      AccountStatementParams params);
  Future<ApiResult<bool>> sentAccountStatement(AccountStatementParams params);
}
