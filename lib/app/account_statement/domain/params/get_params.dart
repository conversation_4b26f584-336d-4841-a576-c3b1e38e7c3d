class AccountStatementParams {
  final int batch;
  final int limit;
  final DateTime? startDate;
  final DateTime? endDate;
  final RequestType? type;
  final String? email;
  AccountStatementParams({
    required this.batch,
    required this.limit,
    this.startDate,
    this.endDate,
    this.type,
    this.email,
  });

  AccountStatementParams copyWith({
    int? batch,
    int? limit,
    DateTime? startDate,
    DateTime? endDate,
    RequestType? type,
    String? email,
  }) {
    return AccountStatementParams(
      batch: batch ?? this.batch,
      limit: limit ?? this.limit,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      type: type ?? this.type,
      email: email ?? this.email,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'batch': batch});
    result.addAll({'limit': limit});
    if (startDate != null) {
      result.addAll({'startDate': startDate!.millisecondsSinceEpoch / 1000});
    }
    if (endDate != null) {
      result.addAll({'endDate': endDate!.millisecondsSinceEpoch / 1000});
    }
    if (type != null) {
      result.addAll({'type': type?.name ?? ''});
    }

    if (email != null) {
      result.addAll({'email': email ?? ''});
    }

    return result;
  }
}

enum RequestType { list, download, email }
