import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/repos/account_statement_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class SendAccountStatement
    with UseCases<ApiResult<bool>, AccountStatementParams> {
  final AccountStatementRepo? _repo;
  SendAccountStatement(this._repo);

  @override
  Future<ApiResult<bool>> call(AccountStatementParams params) =>
      _repo!.sentAccountStatement(params);
}
