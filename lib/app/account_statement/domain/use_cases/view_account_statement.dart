import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/repos/account_statement_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

///Get Downloaded PDF
class ViewAccountStatement
    with UseCases<ApiResult<AccountStatementFile>, AccountStatementParams> {
  final AccountStatementRepo? _repo;
  ViewAccountStatement(this._repo);

  @override
  Future<ApiResult<AccountStatementFile>> call(AccountStatementParams params) =>
      _repo!.viewAccountStatement(params);
}
