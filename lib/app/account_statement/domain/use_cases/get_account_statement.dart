import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/repos/account_statement_repo.dart';
import 'package:shop/app/order/domain/repos/order_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetAccountStatement
    with UseCases<ApiResult<AccountStatement>, AccountStatementParams> {
  GetAccountStatement(this._repo);

  /// Instance of [OrderRepo].
  final AccountStatementRepo? _repo;

  /// Returns the list of a user's orders.
  @override
  Future<ApiResult<AccountStatement>> call(AccountStatementParams params) {
    return _repo!.getAccountStatement(params);
  }
}
