import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safe_insets/safe_area_wrap.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/send_statement_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class SendAccountStatementModal extends StatefulWidget {
  final AccountStatementParams params;
  const SendAccountStatementModal({
    super.key,
    required this.params,
  });

  @override
  State<SendAccountStatementModal> createState() =>
      _SendAccountStatementModalState();
}

class _SendAccountStatementModalState extends State<SendAccountStatementModal> {
  @override
  Widget build(BuildContext context) {
    final user = context.read<UserCubit>().currentUser;
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Send to my Email',
                style: textStyleSemiBold(context),
              ),
              const Spacer(),
              CloseButton(onPressed: () => Navigator.pop(context)),
            ],
          ),
          YMargin(38),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Email address',
                style: TextStyle(color: theme.hintColor),
              ),
              YMargin(10),
              TextFormField(
                initialValue: user?.email ?? '',
                readOnly: true,
                decoration: InputDecoration(
                  errorStyle: TextStyle(),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 14, horizontal: 10),
                  isDense: true,
                  isCollapsed: true,
                  focusedErrorBorder:
                      borderStyling(Theme.of(context).colorScheme.error),
                  errorBorder:
                      borderStyling(Theme.of(context).colorScheme.error),
                  focusedBorder:
                      borderStyling(theme.colorScheme.onPrimaryContainer),
                  enabledBorder:
                      borderStyling(theme.colorScheme.onPrimaryContainer),
                ),
              ),
            ],
          )),
          SafeArea(
            child: SafeAreaWrap(
              BlocListener<SendStatementCubit, SendStatementState>(
                listener: (context, state) {
                  if (state is SendStatementLoaded) {
                    Navigator.pop(context);
                    Toast.success(
                      'Email sent successfully',
                      context,
                    );
                  }
                  if (state is SendStatementError) {
                    Navigator.pop(context);
                    Toast.error(
                      'Failed to sent email',
                      context,
                    );
                  }
                },
                child: BlocBuilder<SendStatementCubit, SendStatementState>(
                  builder: (context, state) {
                    return KButtonPrimary(
                      onTap: () async {
                        BlocProvider.of<SendStatementCubit>(context)
                            .sendAccountStatement(widget.params);
                      },
                      text: 'Send',
                      isLoading: state is SendStatementLoading,
                    );
                  },
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  OutlineInputBorder borderStyling(Color color) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(defaultBorderRadius),
      borderSide: BorderSide(color: color),
    );
  }
}
