import 'package:flutter/material.dart';
import 'package:shop/app/account_statement/data/models/paginated_data.dart';

typedef OnTapAsync = Future<void> Function();
typedef OnTap = void Function();

class KInfiniteScrollView<T> extends StatelessWidget {
  final PaginatedData data;
  final OnTap onScrollEnd;
  final OnTapAsync onRefresh;
  final ScrollView child;

  const KInfiniteScrollView({
    required this.data,
    required this.onScrollEnd,
    required this.onRefresh,
    required this.child,
    super.key,
  });

  bool overscrolled(ScrollNotification notifier) {
    if (child.physics is! BouncingScrollPhysics) {
      return notifier is OverscrollNotification && notifier.overscroll > 0;
    }
    return (notifier.metrics.outOfRange || notifier.metrics.atEdge) &&
        notifier.metrics.axisDirection == AxisDirection.down;
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notifier) {
        if (!data.isLoading && (data.hasNext && overscrolled(notifier))) {
          onScrollEnd();
        }
        return true;
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: RefreshIndicator(
              onRefresh: onRefresh,
              child: child,
            ),
          ),
          if (data.isLoading)
            const Align(
              alignment: Alignment.bottomCenter,
              child: CircularProgressIndicator(),
            )
        ],
      ),
    );
  }
}
