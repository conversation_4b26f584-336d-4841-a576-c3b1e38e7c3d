import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/homepage/presentation/ui/screens/home/<USER>/mobile_home_screen.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class EmptyStatementView extends StatelessWidget {
  const EmptyStatementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          YMargin(64),
          Column(
            children: [
              SvgPicture.asset(
                kSvgOrderEmpty,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 40),
              Text(
                "You don't have any statement yet.\n"
                "Why don't you start shopping\n"
                "to stock up your store",
                textAlign: TextAlign.center,
                style: textStyleRegular(
                  context,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25),
            child: KButtonPrimary(
              text: 'Start Shopping',
              onTap: () {
                HomeScreenState.drawerKey.currentState?.closeDrawer();
                context.goNamed(HomePath);
              },
            ),
          ),
          YMargin(16),
        ],
      ),
    );
  }
}
