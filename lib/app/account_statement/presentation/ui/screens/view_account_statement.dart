import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pdfx/pdfx.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/download_statement_cubit.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/src/services/url_service.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class AccountStatementViewerScreen extends StatefulWidget {
  const AccountStatementViewerScreen({
    super.key,
  });

  @override
  State<AccountStatementViewerScreen> createState() =>
      _AccountStatementViewerScreenState();
}

class _AccountStatementViewerScreenState
    extends State<AccountStatementViewerScreen> {
  late DownloadStatementCubit cubit;

  @override
  void initState() {
    cubit = context.read();
    cubit.downloadStatement();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ShopAppBar.shopAppBar(
        context,
        title: 'View Statement ',
      ),
      body: BlocBuilder<DownloadStatementCubit, DownloadStatementState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (state is DownloadStatementLoading)
                Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              if (state is DownloadStatementError)
                Expanded(
                  child: Center(
                    child: KErrorScreen(
                      null,
                      () async {
                        await cubit.downloadStatement();
                      },
                      displayErrorCode: true,
                    ),
                  ),
                ),
              if (state is DownloadStatementLoaded)
                Expanded(
                  child: SizedBox(
                    height: double.infinity,
                    child: Padding(
                      padding: context.insetsSymetric(horizontal: 20),
                      child: PdfViewPinch(
                        key: UniqueKey(),
                        controller: PdfControllerPinch(
                          document: PdfDocument.openFile(
                            state.accountStatementFile.file?.path ?? '',
                          ),
                        ),
                        padding: 0.1,
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
      bottomSheet: BlocBuilder<DownloadStatementCubit, DownloadStatementState>(
        builder: (context, state) {
          if (state is DownloadStatementLoaded) {
            return Padding(
              padding: const EdgeInsets.all(15.0),
              child: Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: screenHeight(context, percent: 0.05),
                      child: KButtonPrimary(
                        text: "Download",
                        textStyle: textStyleMedium(context, fontSize: 14),
                        onTap: () => UrlService.it
                            .urlLink(state.accountStatementFile.urlPath ?? ''),
                      ),
                    ),
                  ),
                  XMargin(8),
                  Expanded(
                    child: SizedBox(
                      height: screenHeight(context, percent: 0.05),
                      child: KButtonPrimary(
                        text: 'Share',
                        textStyle: textStyleMedium(context, fontSize: 14),
                        color: kColorLightOrange,
                        textColor: Theme.of(context).primaryColor,
                        onTap: () {
                          final RenderBox box =
                              context.findRenderObject() as RenderBox;
                          Share.shareXFiles(
                            [
                              XFile(state.accountStatementFile.file?.path ?? '',mimeType: 'application/pdf',)
                            ],
                            subject: 'Invoice',
                            sharePositionOrigin:
                                box.localToGlobal(Offset.zero) & box.size,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return Offstage();
        },
      ),
    );
  }
}
