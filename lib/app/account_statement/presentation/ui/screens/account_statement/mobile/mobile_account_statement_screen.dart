import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:shop/app/account_statement/presentation/ui/screens/widget/empty_statement_view.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/paginated_data.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/account_statement_cubit.dart';
import 'package:shop/app/account_statement/presentation/ui/modal/send_account_statement_modal.dart';
import 'package:shop/app/account_statement/presentation/ui/screens/widget/infinite_scroll_view.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/utils/error_mapper.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class MobileAccountStatementScreen extends StatefulWidget {
  const MobileAccountStatementScreen({super.key});

  @override
  _MobileAccountStatementScreenState createState() =>
      _MobileAccountStatementScreenState();
}

class _MobileAccountStatementScreenState
    extends State<MobileAccountStatementScreen> with ErrorMapper {
  ScrollController? _scrollController;
  int page = 1;

  ValueNotifier<String?> fromFormattedDate = ValueNotifier(null);
  ValueNotifier<String?> toFormattedDate = ValueNotifier(null);
  DateTime? fromDate;
  DateTime? toDate;

  bool _hasFilter = false;

  @override
  void initState() {
    super.initState();
    context.read<AccountStatementCubit>().loadAccountStatements(
          params: AccountStatementParams(
            batch: 1,
            limit: 10,
          ),
          firstLoad: true,
          forceRefresh: true,
        );

    _scrollController = ScrollController();
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    return Scaffold(
      appBar: ShopAppBar.shopAppBar(
        context,
        title: 'Account Statement',
        actions: [
          PopupMenuButton(
            padding: context.insetsAll(10),
            icon: SvgPicture.asset(
              kSvgMoreWithBg,
              // color: Theme.of(context).colorScheme.onSurface,
            ),
            itemBuilder: (context) {
              return [
                // PopupMenuItem(
                //   onTap: () {
                //     context.pushNamed(AccountStatementDownloadPath);
                //   },
                //   child: Row(
                //     children: [
                //       SvgPicture.asset(kSvgDownload),
                //       XMargin(10),
                //       Text(
                //         'Download',
                //         style: textStyleMedium(context, fontSize: 14),
                //       ),
                //     ],
                //   ),
                // ),
                PopupMenuItem(
                  onTap: () async {
                    await showModalBottomSheet(
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (context) {
                        return Container(
                          padding: context.insetsAll(8),
                          child: SendAccountStatementModal(
                            params: AccountStatementParams(
                              batch: 0,
                              limit: 0,
                              startDate: fromDate,
                              endDate: toDate,
                              type: RequestType.email,
                            ),
                          ),
                        );
                      },
                    );
                  },
                  child: Row(
                    children: [
                      SvgPicture.asset(kSvgMessageOutline),
                      XMargin(10),
                      Text(
                        'Send to my Email',
                        style: textStyleMedium(context, fontSize: 14),
                      ),
                    ],
                  ),
                ),

                PopupMenuItem(
                  onTap: () async {
                    context.read<AccountStatementCubit>().loadAccountStatements(
                          params: AccountStatementParams(
                            batch: 1,
                            limit: 10,
                          ),
                          firstLoad: true,
                          forceRefresh: true,
                        );
                  },
                  child: Row(
                    children: [
                      Icon(Icons.clear),
                      XMargin(10),
                      Text(
                        'Clear filter',
                        style: textStyleMedium(context, fontSize: 14),
                      ),
                    ],
                  ),
                )
              ];
            },
          ),
        ],
      ),
      body: BlocBuilder<AccountStatementCubit, AccountStatementState>(
        builder: (context, state) {
          if (state is AccountStatementLoading) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }
          if (state is AccountStatementError) {
            return Center(
              child: Padding(
                padding: context.insetsSymetric(horizontal: 20),
                child: KErrorScreen(
                  null,
                  () async {
                    await context
                        .read<AccountStatementCubit>()
                        .loadAccountStatements(
                          params: AccountStatementParams(batch: 1, limit: 10),
                          firstLoad: true,
                          forceRefresh: true,
                        );
                  },
                  displayErrorCode: true,
                ),
              ),
            );
          }
          if (state is AccountStatementLoaded) {
            return SafeArea(
              child: Padding(
                padding: context.insetsSymetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'BALANCE',
                      style: textStyleMedium(
                        fontSize: 14,
                        context,
                      ).copyWith(),
                    ),
                    CurrencyItem(
                      state.accountStatement.balance ?? 0,
                      userCubit.currencyCode,
                      amountStyle: textStyleSemiBold(
                        context,
                        fontSize: 34,
                      ),
                      symbolStyle: textStyleSemiBold(
                        context,
                        fontSize: 34,
                      ),
                    ),
                    YMargin(8),
                    Row(
                      children: [
                        Expanded(
                          child: TotalCard(
                            title: 'TOTAL CREDIT',
                            svgPath: kSvgTotalCredit,
                            amount: state.accountStatement.totalCredits ?? 0.0,
                            currency: userCubit.currencyCode,
                          ),
                        ),
                        XMargin(12),
                        Expanded(
                          child: TotalCard(
                            title: 'TOTAL DEBIT',
                            svgPath: kSvgTotalDebit,
                            amount:
                                -(state.accountStatement.totalDebits ?? 0.0),
                            currency: userCubit.currencyCode,
                          ),
                        ),
                      ],
                    ),
                    YMargin(16),
                    Text(
                      'Filter',
                      style: textStyleMedium(
                        context,
                        fontColor: kColorBlack,
                      ).copyWith(),
                    ),
                    YMargin(4),
                    Row(
                      children: [
                        Expanded(
                          child: dateFilter(
                            context: context,
                            title: 'From',
                          ),
                        ),
                        XMargin(8),
                        Expanded(
                          child: dateFilter(
                            context: context,
                            title: 'To',
                          ),
                        )
                      ],
                    ),
                    YMargin(16.0),
                    Expanded(child: _buildBody()),
                  ],
                ),
              ),
            );
          }
          return Offstage();
        },
      ),
    );
  }

  Widget dateFilter({
    required BuildContext context,
    required String title,
  }) {
    return Container(
      padding: context.insetsSymetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        border: Border.all(color: kColorGrey50),
      ),
      child: InkWell(
        onTap: () async {
          final res = await showDatePicker(
            context: context,
            fieldLabelText: 'Select date',
            initialDate:
                ((title == 'From') ? fromDate : toDate) ?? DateTime.now(),
            firstDate: DateTime(2000),
            lastDate: DateTime.now(),
          );

          if (res != null) {
            if (title == 'From') {
              fromDate = res.toLocal();
              fromFormattedDate.value =
                  DateFormat('MMM dd, y').format(fromDate!);
            } else {
              toDate = res.toLocal();
              toFormattedDate.value = DateFormat('MMM dd, y').format(toDate!);
            }

            _hasFilter = true;
            context.read<AccountStatementCubit>().loadAccountStatements(
                  params: AccountStatementParams(
                    batch: 1,
                    limit: 10,
                    startDate:
                        fromDate?.copyWith(hour: 0, minute: 0, second: 0),
                    endDate: toDate?.copyWith(hour: 24, minute: 59, second: 59),
                  ),
                  forceRefresh: true,
                );
          }
        },
        child: Container(
          child: Row(
            children: [
              Text(title),
              XMargin(8),
              SvgPicture.asset(
                kSvgCalendarOutline,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              XMargin(8),
              ValueListenableBuilder(
                  valueListenable:
                      (title == 'From') ? fromFormattedDate : toFormattedDate,
                  builder: (context, value, child) {
                    return Text(
                      value ?? 'Select date',
                    );
                  })
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final model = context.read<AccountStatementCubit>();
    final userCubit = context.read<UserCubit>();
    // final user = userCubit.currentUserStream.valueOrNull;
    return ValueListenableBuilder<PaginatedData<Transaction>>(
      builder: (context, value, child) {
        if (value.isLoading && (!value.hasData || value.page <= 0)) {
          return Center(
            child: CircularProgressIndicator(),
          );
        }
        if (value.data.isEmpty && value.error.isEmpty) {
          return Padding(
            padding: context.insetsOnly(
              left: 20,
              right: 20,
              bottom: 24,
            ),
            child: EmptyStatementView(),
          );
        }

        if (value.error.isNotEmpty && value.data.isEmpty) {
          return Center(
            child: Padding(
              padding: context.insetsSymetric(horizontal: 20),
              child: KErrorScreen(
                null,
                () async {
                  await model.loadAccountStatements(
                    params: AccountStatementParams(batch: 1, limit: 10),
                    forceRefresh: true,
                  );
                },
                displayErrorCode: true,
              ),
            ),
          );
        }

        return KInfiniteScrollView(
          data: value,
          onRefresh: () async {
            await model.loadAccountStatements(
              params: AccountStatementParams(
                batch: 1,
                limit: 10,
              ),
              forceRefresh: true,
            );
          },
          onScrollEnd: () {
            if (value.hasNext && !value.isLoading) {
              model.loadAccountStatements(
                params: AccountStatementParams(
                  batch: value.next,
                  startDate: _hasFilter
                      ? toDate?.copyWith(hour: 0, minute: 0, second: 0)
                      : null,
                  endDate: _hasFilter
                      ? fromDate?.copyWith(hour: 24, minute: 59, second: 59)
                      : null,
                  limit: 10,
                ),
              );
            }
          },
          child: CustomScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(child: child),
              ...model.transactionSegments.keys.map(
                (key) {
                  final values = model.transactionSegments[key] ?? [];
                  return SliverPadding(
                    padding: context.insetsOnly(
                      top: 8,
                      left: 0,
                      right: 0,
                      bottom: 16,
                    ),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, idx) {
                          if (idx == 0) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  key,
                                  style: textStyleMedium(
                                    context,
                                    fontSize: 12,
                                    fontColor: kBrandBlack60,
                                  ),
                                ),
                                Divider(
                                  color: kColorDividerGrey,
                                ),
                              ],
                            );
                          }
                          final transaction = values[idx - 1];

                          return ListTile(
                            contentPadding: context.insetsAll(0.0),
                            dense: true,
                            leading: transaction.invoiceType == 'Purchase'
                                ? SvgPicture.asset(kSvgDebit)
                                : SvgPicture.asset(kSvgCredit),
                            title: Text(
                                '${transaction.invoiceType} ${transaction.invoiceNumber}'),
                            trailing: CurrencyItem(
                              transaction.total ?? 0,
                              userCubit.currencyCode,
                              amountStyle: textStyleMedium(
                                context,
                              ),
                              symbolStyle: textStyleMedium(
                                context,
                              ),
                            ),
                            subtitle: Text(
                              transaction.status ?? '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context).hintColor,
                                  ),
                            ),
                          );
                        },
                        childCount: values.length + 1,
                      ),
                    ),
                  );
                },
              )
            ],
          ),
        );
      },
      valueListenable: model.transactions,
    );
  }
}

class TotalCard extends StatelessWidget {
  final String title;
  final String svgPath;
  final num amount;
  final String currency;
  const TotalCard({
    super.key,
    required this.title,
    required this.svgPath,
    required this.amount,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: context.insetsSymetric(horizontal: 24, vertical: 24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(10.0),
        ),
        border: Border.all(color: kColorGrey3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(svgPath),
          YMargin(4),
          Text(
            title,
            style: textStyleMedium(
              context,
              fontSize: 10,
              fontColor: kColorGrey3,
            ),
          ),
          CurrencyItem(
            amount,
            currency,
            amountStyle: textStyleSemiBold(
              context,
            ),
            symbolStyle: textStyleSemiBold(
              context,
            ),
          ),
        ],
      ),
    );
  }
}
