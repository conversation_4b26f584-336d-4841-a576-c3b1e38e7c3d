import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/use_cases/send_account_statement.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'send_statement_state.dart';

class SendStatementCubit extends Cubit<SendStatementState> {
  final SendAccountStatement _sendAccountStatement;
  SendStatementCubit(
    this._sendAccountStatement,
  ) : super(SendStatementInitial());

  Future sendAccountStatement(AccountStatementParams params) async {
    emit(SendStatementLoading());
    final res = await _sendAccountStatement.call(params);

    res.when(
      success: (_) {
        emit(
          SendStatementLoaded(),
        );
      },
      apiFailure: (e, s) {
        ErrorHandler.report(e, null);
        emit(
          SendStatementError(
            ApiExceptions.getErrorMessage(e),
          ),
        );
      },
    );
  }
}
