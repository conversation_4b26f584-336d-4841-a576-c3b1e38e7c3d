import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shop/app/account_statement/data/models/account_statement.dart';
import 'package:shop/app/account_statement/data/models/paginated_data.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/use_cases/get_account_statement.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'account_statement_state.dart';

class AccountStatementCubit extends Cubit<AccountStatementState>
    implements OnBeforeLogout {
  final GetAccountStatement _getAccountStatement;

  AccountStatementCubit(
    this._getAccountStatement,
  ) : super(AccountStatementInitial());

  ValueNotifier<PaginatedData<Transaction>> transactions =
      ValueNotifier(const PaginatedData());

  Map<String, List<Transaction>> _transactionSegments = {};

  Map<String, List<Transaction>> get transactionSegments =>
      _transactionSegments;

  String _getGroupName(int differenceInDays) {
    if (differenceInDays == -1) return "---";
    if (differenceInDays == 0) return "Today";
    if (differenceInDays == 1) return "Yesterday";
    final dateFromDifference = DateTime.now().subtract(
      Duration(days: differenceInDays),
    );
    return DateFormat("MMMM d, yyy").format(dateFromDifference);
  }

  void _groupTransactions(List<Transaction> items) {
    _transactionSegments = {};
    items.sort((prev, next) {
      return next.createdAt!.compareTo(prev.createdAt!);
    });

    for (var transaction in items) {
      final groupKey = _getGroupName(
        DateTime.now().difference(transaction.createdAt!).inDays,
      );
      if (_transactionSegments.containsKey(groupKey)) {
        _transactionSegments[groupKey]?.add(transaction);
      } else {
        _transactionSegments[groupKey] = [transaction];
      }
    }
  }

  Future loadAccountStatements({
    required AccountStatementParams params,
    forceRefresh = false,
    bool firstLoad = false,
  }) async {
    if (firstLoad) emit(AccountStatementLoading());

    var currentTransactionsHolder = transactions.value;

    final isLoading = currentTransactionsHolder.isLoading;

    final isInvalidPage =
        !forceRefresh && params.batch <= currentTransactionsHolder.page;

    if (isLoading || isInvalidPage) return;

    if (forceRefresh) {
      transactions.value = transactions.value.copyWith(
        isLoading: true,
        page: forceRefresh ? 0 : params.batch,
        data: [],
      );
    } else {
      transactions.value = transactions.value.copyWith(
        isLoading: true,
        page: forceRefresh ? 0 : params.batch,
      );
    }

    final res = await _getAccountStatement(params);

    res.when(
      success: (data) {
        PaginatedData<Transaction> paginatedData = PaginatedData<Transaction>(
          page: params.batch,
          pageCount: params.limit,
          data: data.transactions ?? [],
          error: '',
        );

        if (params.batch > 0) {
          paginatedData = transactions.value.addData(paginatedData);
        }

        _groupTransactions(paginatedData.data);
        transactions.value = paginatedData;
        emit(AccountStatementLoaded(data));
      },
      apiFailure: (e, s) {
        if (firstLoad) {
          emit(
            AccountStatementError(
              ApiExceptions.getErrorMessage(e),
            ),
          );
        }
        transactions.value = transactions.value.copyWith(
          isLoading: false,
          error: ApiExceptions.getErrorMessage(e),
          page: currentTransactionsHolder.page,
        );
        ErrorHandler.report(e, null);
      },
    );
  }

  @override
  Future<void> onBeforeLogout() async {
    emit(AccountStatementInitial());
  }
}
