part of 'send_statement_cubit.dart';

sealed class SendStatementState extends Equatable {
  const SendStatementState();

  @override
  List<Object> get props => [];
}

final class SendStatementInitial extends SendStatementState {}

class SendStatementLoaded extends SendStatementState {
  const SendStatementLoaded();

  @override
  List<Object> get props => [];
}

class SendStatementError extends SendStatementState {
  final String error;
  const SendStatementError(this.error);

  @override
  List<Object> get props => [error];
}

class SendStatementLoading extends SendStatementState {
  const SendStatementLoading();

  @override
  List<Object> get props => [];
}
