import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/account_statement/data/models/account_statement_file.dart';
import 'package:shop/app/account_statement/domain/params/get_params.dart';
import 'package:shop/app/account_statement/domain/use_cases/view_account_statement.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'download_statement_state.dart';

class DownloadStatementCubit extends Cubit<DownloadStatementState> {
  final ViewAccountStatement _viewAccountStatement;
  DownloadStatementCubit(
    this._viewAccountStatement,
  ) : super(DownloadStatementInitial());

  Future downloadStatement() async {
    emit(DownloadStatementLoading());
    final res = await _viewAccountStatement.call(
      AccountStatementParams(batch: 0, limit: 0, type: RequestType.download),
    );

    res.when(
      success: (AccountStatementFile file) {
        emit(
          DownloadStatementLoaded(file),
        );
      },
      apiFailure: (e, s) {
        ErrorHandler.report(e, null);
        emit(
          DownloadStatementError(
            ApiExceptions.getErrorMessage(e),
          ),
        );
      },
    );
  }
}
