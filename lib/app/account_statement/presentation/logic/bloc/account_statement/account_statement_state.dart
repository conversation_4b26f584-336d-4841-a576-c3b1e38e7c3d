part of 'account_statement_cubit.dart';

abstract class AccountStatementState extends Equatable {
  const AccountStatementState();
}

class AccountStatementInitial extends AccountStatementState {
  @override
  List<Object> get props => [];
}

class AccountStatementLoading extends AccountStatementState {
  final String? id;

  const AccountStatementLoading({this.id});
  @override
  List<Object> get props => [];
}

class AccountStatementLoaded extends AccountStatementState {
  final AccountStatement accountStatement;

  final bool completed;

  const AccountStatementLoaded(this.accountStatement, [this.completed = false]);

  @override
  List<Object?> get props => [completed, accountStatement];
}

class AccountStatementError extends AccountStatementState {
  final String error;

  const AccountStatementError(this.error);

  @override
  List<Object> get props => [error];
}
