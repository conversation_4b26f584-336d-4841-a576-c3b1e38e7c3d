part of 'download_statement_cubit.dart';

sealed class DownloadStatementState extends Equatable {
  const DownloadStatementState();

  @override
  List<Object> get props => [];
}

final class DownloadStatementInitial extends DownloadStatementState {}

class DownloadStatementLoaded extends DownloadStatementState {
  final AccountStatementFile accountStatementFile;
  const DownloadStatementLoaded(this.accountStatementFile);

  @override
  List<Object> get props => [accountStatementFile];
}

class DownloadStatementError extends DownloadStatementState {
  final String error;
  const DownloadStatementError(this.error);

  @override
  List<Object> get props => [error];
}

class DownloadStatementLoading extends DownloadStatementState {
  const DownloadStatementLoading();

  @override
  List<Object> get props => [];
}
