// import 'package:flutter/widgets.dart';
// import 'package:shop/app/card_management/presentation/ui/widget/card_details.dart';
// import 'package:shop/src/res/assets/assets.dart';

// Widget getCardImage(String cardType) {
//   switch (cardType.toLowerCase()) {
//     case MASTER:
//       return SizedBox(
//         width: 40,
//         child: Image(image: AssetImage(kMaster)),
//       );

//     case VISA:
//       return SizedBox(
//         width: 40,
//         child: Image(image: AssetImage(kVisa)),
//       );

//     case VERVE:
//       return SizedBox(
//         width: 40,
//         child: Image(
//           image: AssetImage(kVerve),
//         ),
//       );
//     default:
//       return SizedBox(
//         width: 40,
//       );
//   }
// }
