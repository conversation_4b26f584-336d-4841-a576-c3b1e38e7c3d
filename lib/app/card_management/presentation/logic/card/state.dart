// part of 'bloc.dart';

// @immutable
// class CardState {
//   final bool isLoading;
//   final bool? isError;
//   final String? errorMessage;
//   final String? errorCode;
//   final List<PaymentCard>? cards;
//   final PaymentCard? activeCard;

//   CardState({
//     required this.isLoading,
//     this.isError,
//     this.errorMessage,
//     this.errorCode,
//     this.cards,
//     this.activeCard,
//   });

//   factory CardState.initialState() {
//     return CardState(
//       isLoading: false,
//       isError: false,
//       errorMessage: null,
//       cards: null,
//       activeCard: null,
//     );
//   }

//   factory CardState.cardLoadError(
//     errorCode,
//     errorMessage,
//   ) {
//     return CardState(
//       isError: true,
//       isLoading: false,
//       errorMessage: errorMessage,
//       errorCode: errorCode,
//     );
//   }

//   CardState copyWith({
//     bool? isLoading,
//     bool? isError,
//     String? errorMessage,
//     String? errorCode,
//     List<PaymentCard>? cards,
//     PaymentCard? activeCard,
//   }) {
//     return CardState(
//       isLoading: isLoading ?? this.isLoading,
//       errorCode: errorCode ?? this.errorCode,
//       isError: isError ?? this.isError,
//       errorMessage: errorMessage ?? this.errorMessage,
//       cards: cards ?? this.cards,
//       activeCard: activeCard ?? this.activeCard,
//     );
//   }

//   @override
//   String toString() {
//     return '''Card state {
//       isLoading: $isLoading,
//       isError: $isError,
//       errorMessage: $errorMessage,
//       errorCode: $errorCode,
//       cards: $cards,
//       activeCard: $activeCard,
//     }''';
//   }
// }
