// import 'dart:async';

// import 'package:bloc/bloc.dart';
// import 'package:flutter/foundation.dart';
// import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
// import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
// import 'package:shop/app/card_management/data/model/payment_card.dart';
// import 'package:shop/app/card_management/domain/use_case/fetch_cards.dart';
// import 'package:td_commons_flutter/models/user.dart';
// import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

// part 'state.dart';

// const CARD_LOAD_CONNECTION_ERROR = "connection_error";
// const CARD_LOAD_UNKNOWN_ERROR = "unknown_error";
// const CARD_CONTEXT = 'CardBloc';

// class CardCubit extends Cubit<CardState> implements OnLogin, OnBeforeLogout {
//   final FetchCard? _fetchCard;
//   User? _user;

//   CardCubit(this._fetchCard) : super(CardState.initialState());

//   @override
//   Future<void> onLogin(User? user) async {
//     _user = user;
//     fetchCard(_user!.currentRetailOutlet?.id);
//   }

//   @override
//   Future<void> onBeforeLogout() async {
//     _user = null;
//     emit(CardState.initialState());
//   }

//   Future<void> fetchCard(String? outletId) async {
//     if (outletId == null) return;

//     final CardState currentState = state;
//     emit(currentState.copyWith(isLoading: true, isError: false));

//     final res = await _fetchCard!(outletId);
//     res.maybeWhen(
//       success: (cards) {
//         List<PaymentCard> myCards = cards
//           ..sort((a, b) => a.status!.compareTo(b.status!));
//         PaymentCard? activeCard;
//         if (cards.isNotEmpty) {
//           activeCard = cards.where((card) => card.active).first;
//         }
//         emit(currentState.copyWith(
//           isLoading: false,
//           cards: myCards,
//           activeCard: activeCard,
//           isError: false,
//         ));
//       },
//       apiFailure: (error, _) {
//         emit(CardState.cardLoadError(
//           ApiExceptions.getErrorMessage(error),
//           ApiExceptions.getErrorMessage(error),
//         ));
//       },
//       orElse: () => emit(currentState.copyWith(
//         errorMessage: 'Something went wrong',
//         isLoading: false,
//         isError: true,
//       )),
//     );
//   }

//   void updateCard(cardsList) {
//     final CardState currentState = state;
//     List<PaymentCard> cards = cardsList
//       ..sort((a, b) => a.status.compareTo(b.status));
//     PaymentCard? activeCard;

//     if (cards.isNotEmpty) {
//       activeCard = cards.where((card) => card.active).first;
//     }
//     emit(currentState.copyWith(
//       isLoading: false,
//       isError: false,
//       cards: cards,
//       activeCard: activeCard,
//     ));
//   }
// }
