// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:provider/provider.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/card_management/data/model/payment_card.dart';
// import 'package:shop/app/card_management/domain/use_case/delete_card.dart';
// import 'package:shop/app/card_management/domain/use_case/verify_card.dart';
// import 'package:shop/app/card_management/presentation/logic/card/bloc.dart';
// import 'package:shop/app/card_management/presentation/logic/utils/methods.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_core/td_flutter_core.dart';

// const CARD_VIEW = 'card_details.dart';
// const VISA = 'visa';
// const VERVE = 'verve';
// const MASTER = 'master';

// class CardDetail extends StatelessWidget {
//   final PaymentCard? card;
//   final bool hideAction;

//   CardDetail({this.card, this.hideAction = false});

//   final ValueNotifier<String> message = ValueNotifier<String>('');

//   void hideLoader() {
//     TdLoader.hide();
//   }

//   void _setCardAsMain(BuildContext context, outlet) async {
//     final res = await locator.get<VerifyCard>().call(
//           card,
//         );
//     res.maybeWhen(
//       success: (val) {
//         BlocProvider.of<CardCubit>(context, listen: false).fetchCard(outlet);
//         Toast.success('Card was changed successfully', context, duration: 5);
//         hideLoader();
//       },
//       apiFailure: (e, _) {
//         Toast.error('${ApiExceptions.getErrorMessage(e)}', context,
//             duration: 5);
//         hideLoader();
//       },
//       orElse: () {
//         Toast.error(
//             'An unexpected error occurred. Please try again later', context,
//             duration: 5);
//         hideLoader();
//       },
//     );
//   }

//   void _deleteCard(BuildContext context, outlet) async {
//     TdLoader.show(context);
//     final res = await locator.get<DeleteCard>().call(
//           card,
//         );
//     res.maybeWhen(
//       success: (val) {
//         BlocProvider.of<CardCubit>(context, listen: false).fetchCard(outlet);
//         Toast.success('Card was deleted successfully', context, duration: 5);
//         hideLoader();
//       },
//       apiFailure: (e, _) {
//         Toast.error('${ApiExceptions.getErrorMessage(e)}', context,
//             duration: 5);
//         hideLoader();
//       },
//       orElse: () {
//         Toast.error(
//             'An unexpected error occurred. Please try again later', context,
//             duration: 5);
//         hideLoader();
//       },
//     );
//   }

//   void handleSelectedItem(int value, BuildContext context, user) {
//     switch (value) {
//       case 1:
//         return _setCardAsMain(context, user);

//       case 2:
//         return _deleteCard(context, user);
//     }
//   }

//   Widget _buildTrailing(BuildContext context, user) {
//     return card?.active == true
//         ? Image.asset(
//             kActive,
//           )
//         : PopupMenuButton<int>(
//             icon: SvgPicture.asset(
//               kSvgOption,
//             ),
//             onSelected: (int value) => handleSelectedItem(value, context, user),
//             itemBuilder: (context) => [
//               PopupMenuItem(
//                 value: 1,
//                 child: Text(
//                   'Set as Active',
//                   style: KTextStyle.bodyText2,
//                 ),
//               ),
//               PopupMenuItem(
//                 value: 2,
//                 child: Text(
//                   'Delete',
//                   style: KTextStyle.bodyText2,
//                 ),
//               ),
//             ],
//           );
//   }

//   @override
//   Widget build(BuildContext context) {
//     final user = Provider.of<UserCubit>(context, listen: false);
//     String expired = card!.isExpired()
//         ? 'expired'
//         : "${card!.expMonth ?? ''}/${card!.expYear ?? ''}";

//     return Padding(
//       padding: const EdgeInsets.only(
//         left: 20,
//         right: 20,
//         bottom: 10,
//       ),
//       child: Container(
//         padding: EdgeInsets.all(10),
//         decoration: BoxDecoration(
//             color: kColorWhite,
//             borderRadius: BorderRadius.circular(4),
//             boxShadow: [
//               BoxShadow(
//                   color: Color.fromARGB(25, 0, 95, 255),
//                   blurRadius: 5,
//                   spreadRadius: 1,
//                   offset: Offset(0, 1)),
//             ]),
//         child: ListTile(
//           title: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 '**** ${card!.lastFour} ',
//                 style: KTextStyle.cartPartialQtyText.copyWith(
//                   fontSize: 16,
//                 ),
//               ),
//               Text(
//                 expired,
//                 style: KTextStyle.cartPartialQtyText.copyWith(
//                   fontSize: 16,
//                 ),
//               )
//             ],
//           ),
//           leading: SizedBox(
//             width: 30,
//             height: 30,
//             child: getCardImage(card!.cardType!),
//           ),
//           trailing: _buildTrailing(context, user.currentOutlet!.id),
//         ),
//       ),
//     );
//   }
// }
