// import 'dart:core';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:flutter_paystack_client/flutter_paystack_client.dart';
// import 'package:go_router/go_router.dart';
// import 'package:provider/provider.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/card_management/data/model/payment_card.dart';
// import 'package:shop/app/card_management/domain/params/params.dart';
// import 'package:shop/app/card_management/domain/use_case/authorize_payment.dart';
// import 'package:shop/app/card_management/domain/use_case/veify_payment.dart';
// import 'package:shop/app/card_management/presentation/logic/card/bloc.dart';
// import 'package:shop/app/card_management/presentation/ui/widget/card_details.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_core/td_flutter_core.dart';
// import 'package:td_flutter_src/scaler/scaler.dart';
// import 'package:td_flutter_src/td_flutter_src.dart';

// const CARD_CHARGE_AMOUNT = 5000;

// class MyCardList extends StatefulWidget {
//   final List<PaymentCard> cards;
//   final bool navigateHome;

//   MyCardList(this.cards, this.navigateHome);

//   @override
//   _MyCardListState createState() => _MyCardListState();
// }

// class _MyCardListState extends State<MyCardList> {
//   String? _message;

//   _doPayment() async {
//     TdLoader.show(context);
//     UserCubit user = Provider.of<UserCubit>(context, listen: false);
//     String? transactionRef;
//     final charge = Charge();
//     final res = await locator.get<Authorize>().call(
//           AuthorizeParams(
//             amount: CARD_CHARGE_AMOUNT,
//             userId: user.currentUser?.userId,
//             authToken: user.currentUser?.authToken,
//             retailOutletId: user.currentUser?.currentRetailOutlet?.id,
//             paymentMethod: 'LOAN',
//             domain: 'RETAIL',
//           ),
//         );

//     res.maybeWhen(
//       success: (val) {
//         transactionRef = val['data']['transactionRef'];
//       },
//       apiFailure: (e, _) {
//         TdLoader.hide();
//         _message = ApiExceptions.getErrorMessage(e);
//         Toast.error(_message, context, duration: 5);
//       },
//       orElse: () {
//         TdLoader.hide();
//         _message = 'An unexpected error occurred. Please try again later';
//         Toast.error(_message, context, duration: 5);
//       },
//     );
//     TdLoader.hide();
//     charge.amount = CARD_CHARGE_AMOUNT;
//     charge.reference = transactionRef;
//     charge.email = user.currentUser!.email ?? '';

//     final response = await PaystackClient.checkout(
//       context,
//       hideEmail: true,
//       charge: charge,
//     );
//     if (response.status) {
//       final res2 = await locator.get<Verify>().call(
//             VerifyParams(
//               userId: user.currentUser!.userId,
//               authToken: user.currentUser!.authToken,
//               transactionRef: response.reference,
//               domain: 'RETAIL',
//             ),
//           );

//       res2.maybeWhen(
//         success: (val) {
//           _message = 'Card was added successfully';
//           Toast.success(_message, context, duration: 5);
//         },
//         apiFailure: (e, _) {
//           _message = ApiExceptions.getErrorMessage(e);
//           Toast.error(_message, context, duration: 5);
//         },
//         orElse: () {
//           _message = 'An unexpected error occurred. Please try again later';
//           Toast.error(_message, context, duration: 5);
//         },
//       );
//     }
//     BlocProvider.of<CardCubit>(context, listen: false)
//         .fetchCard(user.currentOutlet!.id);
//   }

//   _navigateHome() {
//     context.goNamed(HomePath);
//   }

//   Future<bool> _handlePop() async {
//     if (widget.navigateHome) {
//       _navigateHome();
//       return false;
//     } else {
//       return true;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: _handlePop,
//       child: Scaffold(
//         body: SafeArea(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   KBackButton(
//                     handleTap: () {
//                       if (widget.navigateHome) {
//                         _navigateHome();
//                       } else {
//                         Navigator.of(context).pop();
//                       }
//                     },
//                   ),
//                 ],
//               ),
//               buildIntro(context),
//               SizedBox(
//                 height: 10,
//               ),
//               Expanded(
//                 child: ListView.builder(
//                   physics: ScrollPhysics(),
//                   shrinkWrap: true,
//                   itemCount: widget.cards.length,
//                   itemBuilder: (BuildContext context, int index) {
//                     final card = widget.cards[index];
//                     return CardDetail(
//                       card: card,
//                     );
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget buildIntro(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             AppLocalizations.of(context)!.card_management,
//             style: KTextStyle.headerTitleText,
//           ),
//           YMarginScale(0.02),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 AppLocalizations.of(context)!.manage_your_cards,
//                 style: KTextStyle.subtitleTitleText,
//               ),
//               ElevatedButton(
//                 onPressed: () {
//                   _doPayment();
//                 },
//                 child: Text(
//                   AppLocalizations.of(context)!.new_card,
//                   style: textStyleRegular(
//                     context,
//                     fontSize: 14.0,
//                     fontColor: kColorWhite,
//                   ),
//                 ),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: kColorBlue,
//                   disabledForegroundColor:
//                       kColorBlue.withValues(alpha: .5).withValues(alpha: 0.38),
//                   disabledBackgroundColor:
//                       kColorBlue.withValues(alpha: .5).withValues(alpha: 0.12),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(6.0),
//                   ),
//                   padding:
//                       context.insetsSymetric(horizontal: 26.0, vertical: 14.0),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
