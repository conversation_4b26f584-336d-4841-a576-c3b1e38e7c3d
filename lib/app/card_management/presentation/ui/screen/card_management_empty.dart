// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:flutter_paystack_client/flutter_paystack_client.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:provider/provider.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/card_management/domain/params/params.dart';
// import 'package:shop/app/card_management/domain/use_case/authorize_payment.dart';
// import 'package:shop/app/card_management/domain/use_case/veify_payment.dart';
// import 'package:shop/app/card_management/presentation/logic/card/bloc.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/buttons.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/assets/svgs/svgs.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_core/td_flutter_core.dart';
// import 'package:td_flutter_src/scaler/scaler.dart';
// import 'package:td_flutter_src/td_flutter_src.dart';

// const CARD_CHARGE_AMOUNT = 5000;

// class CardManagementEmpty extends StatefulWidget {
//   final bool doPayment;
//   final bool navigateHome;

//   CardManagementEmpty({
//     Key? key,
//     this.doPayment = false,
//     this.navigateHome = false,
//   }) : super(key: key);

//   @override
//   _CardManagementEmptyState createState() => _CardManagementEmptyState();
// }

// class _CardManagementEmptyState extends State<CardManagementEmpty> {
//   String? _message;

//   @override
//   void initState() {
//     if (widget.doPayment) {
//       _doPayment();
//     }
//     super.initState();
//   }

//   _disclaimerBottomSheet() async {
//     FocusScope.of(context).unfocus();
//     await showModalBottomSheet(
//       isScrollControlled: true,
//       useRootNavigator: true,
//       context: context,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(10.0),
//           topRight: Radius.circular(10.0),
//         ),
//       ),
//       builder: (BuildContext context) {
//         return FractionallySizedBox(
//           heightFactor: 0.4,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   'Add a new card',
//                   style: KTextStyle.headerTitleText,
//                 ),
//                 YMargin(5),
//                 Text(
//                   "Please note you would be charged ₦50 to add a new card which would then be refunded back to you. This is to validate & confirm if card added is active",
//                   style: KTextStyle.bodyText2.copyWith(
//                       fontFamily: 'roboto', height: 1.8, fontSize: 15),
//                   textAlign: TextAlign.center,
//                 ),
//                 YMargin(20),
//                 KButton(
//                     text: 'Proceed',
//                     onPressed: () {
//                       Navigator.maybePop(context);
//                       _doPayment();
//                     }),
//                 YMarginScale(0.02),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Future<void> _doPayment() async {
//     TdLoader.show(context);

//     UserCubit user = Provider.of<UserCubit>(context, listen: false);

//     String? transactionRef;
//     final charge = Charge();
//     final res = await locator.get<Authorize>().call(
//           AuthorizeParams(
//             amount: CARD_CHARGE_AMOUNT,
//             userId: user.currentUser!.userId,
//             authToken: user.currentUser!.authToken,
//             retailOutletId: user.currentUser!.currentRetailOutlet!.id,
//             paymentMethod: 'LOAN',
//             domain: 'RETAIL',
//           ),
//         );

//     res.maybeWhen(
//       success: (val) {
//         transactionRef = val['data']['transactionRef'];
//       },
//       apiFailure: (e, _) {
//         TdLoader.hide();
//         _message = ApiExceptions.getErrorMessage(e);
//         Toast.error(_message, context, duration: 5);
//       },
//       orElse: () {
//         TdLoader.hide();
//         _message = 'An unexpected error occurred. Please try again later';
//         Toast.error(_message, context, duration: 5);
//       },
//     );
//     TdLoader.hide();
//     charge.amount = CARD_CHARGE_AMOUNT;
//     charge.reference = transactionRef;
//     charge.email = user.currentUser!.email ?? '';
//     final response = await PaystackClient.checkout(
//       context,
//       hideEmail: true,
//       charge: charge,
//     );
//     if (response.status) {
//       final res2 = await locator.get<Verify>().call(
//             VerifyParams(
//               userId: user.currentUser!.userId,
//               authToken: user.currentUser!.authToken,
//               transactionRef: response.reference,
//               domain: 'RETAIL',
//             ),
//           );
//       res2.maybeWhen(
//         success: (val) {
//           _message = 'Card was added successfully';
//           Toast.success(_message, context, duration: 5);
//         },
//         apiFailure: (e, _) {
//           _message = ApiExceptions.getErrorMessage(e);
//           Toast.error(_message, context, duration: 5);
//         },
//         orElse: () {
//           _message = 'An unexpected error occurred. Please try again later';
//           Toast.error(_message, context, duration: 5);
//         },
//       );
//     }
//     BlocProvider.of<CardCubit>(context, listen: false)
//         .fetchCard(user.currentOutlet!.id);
//   }

//   _navigateHome() {
//     context.goNamed(HomePath);
//   }

//   Future<bool> _handlePop() async {
//     if (widget.navigateHome) {
//       _navigateHome();
//       return false;
//     } else {
//       return true;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: _handlePop,
//       child: Scaffold(
//         body: SafeArea(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               KBackButton(
//                 handleTap: () {
//                   if (widget.navigateHome) {
//                     _navigateHome();
//                   } else {
//                     Navigator.of(context).pop();
//                   }
//                 },
//               ),
//               buildIntro(context),
//               Expanded(
//                 child: Padding(
//                   padding: const EdgeInsets.only(left: 20, right: 20),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Center(
//                         child: Column(
//                           children: [
//                             YMarginScale(0.2),
//                             SvgPicture.asset(kSvgEmptyCard),
//                             YMarginScale(0.07),
//                             SizedBox(
//                               width: screenWidth(context, percent: 0.7),
//                               child: Text(
//                                 'You don’t have any card added to your account. Why don’t you add a card as one of your payment methods.',
//                                 style: KTextStyle.subtitleTitleText,
//                                 textAlign: TextAlign.center,
//                               ),
//                             ),
//                             YMarginScale(0.12),
//                           ],
//                         ),
//                       ),
//                       KButton(
//                           onPressed: () {
//                             _disclaimerBottomSheet();
//                           },
//                           text: 'Add Card'),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// Widget buildIntro(BuildContext context) {
//   return Padding(
//     padding: const EdgeInsets.only(left: 20, right: 20),
//     child: Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           AppLocalizations.of(context)!.card_management,
//           style: KTextStyle.headerTitleText,
//         ),
//       ],
//     ),
//   );
// }
