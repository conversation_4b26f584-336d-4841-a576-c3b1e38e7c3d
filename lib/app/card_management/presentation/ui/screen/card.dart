// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:shop/app/card_management/presentation/logic/card/bloc.dart';
// import 'package:shop/app/card_management/presentation/ui/screen/card_list.dart';

// import 'card_management_empty.dart';

// class MyCardManagement extends StatelessWidget {
//   final bool doPayment;
//   final bool navigateHome;

//   MyCardManagement({
//     Key? key,
//     this.doPayment = false,
//     this.navigateHome = false,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     Widget page;
//     return BlocBuilder<CardCubit, CardState>(
//       builder: (context, state) {
//         if (state.cards == null) {
//           return CardManagementEmpty(
//               doPayment: doPayment, navigateHome: navigateHome);
//         }
//         if (state.cards!.isNotEmpty) {
//           page = MyCardList(state.cards!, navigateHome);
//         } else {
//           page = CardManagementEmpty(
//               doPayment: doPayment, navigateHome: navigateHome);
//         }
//         return page;
//       },
//     );
//   }
// }
