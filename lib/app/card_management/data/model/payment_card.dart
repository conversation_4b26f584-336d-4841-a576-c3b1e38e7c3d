class PaymentCard {
  String? id;
  String? status;
  String? bin;
  String? lastFour;
  String? expMonth;
  String? expYear;
  String? authorizationCode;
  String? cardType;
  String? bank;
  String? countryCode;
  String? retailOutletId;

  PaymentCard({
    this.id,
    this.status,
    this.bin,
    this.lastFour,
    this.expMonth,
    this.expYear,
    this.authorizationCode,
    this.cardType,
    this.bank,
    this.countryCode,
    this.retailOutletId,
  });

  bool get active => status == 'active';

  bool isExpired() {
    int year = DateTime.now().toLocal().year;
    int month = DateTime.now().toLocal().month;
    int cardExpYear = int.tryParse(expYear!)!;
    int? cardExpMonth = int.tryParse(expMonth!);

    if (year <= cardExpYear) {
      if (year < cardExpYear) {
        return false;
      } else {
        return month > cardExpMonth!;
      }
    } else {
      return true;
    }
  }

  PaymentCard.fromMap(Map<String, dynamic> map) {
    id = map['_id'];
    status = map['status'];
    bin = map['bin'];
    lastFour = map['lastFour'].toString();
    expMonth = map['expMonth'];
    expYear = map['expYear'];
    authorizationCode = map['authorizationCode'];
    cardType = map['cardType'];
    bank = map['bank'];
    countryCode = map['countryCode'];
    retailOutletId = map['retailOutletId'];
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'status': status,
      'bin': bin,
      'lastFour': lastFour,
      'expMonth': expMonth,
      'expYear': expYear,
      'authorizationCode': authorizationCode,
      'cardType': cardType,
      'bank': bank,
      'countryCode': countryCode,
      'retailOutletId': retailOutletId,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
