import 'package:shop/app/card_management/domain/params/params.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

/// Base class for [CardManagementDataSourceImpl].
abstract class CardManagementDataSource {
  Future authorizePayment(AuthorizeParams params);
  Future<dynamic> verifyPayment(VerifyParams params);
  Future<List<PaymentCard>> fetchCards(String outletId);
  Future<dynamic> verifyCard(PaymentCard? card);
  Future<dynamic> deleteCard(PaymentCard? card);
}
