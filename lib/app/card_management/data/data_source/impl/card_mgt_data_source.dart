import 'package:shop/app/card_management/data/data_source/card_mgt_data_source.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:shop/app/card_management/domain/params/params.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

/// Implements [CardManagementDataSource] abstract class.
///
/// Makes network call.
class CardManagementDataSourceImpl implements CardManagementDataSource {
  static const String _authorizePaymentPath = 'shop/v1/authorize';
  static const String _verifyPaymentPath = 'shop/v1/verify-reference';
  static const String _getCardsPath = 'shop/v4/getDebitCards';
  static const String _verifyCardPath = 'shop/v4/verifyDebitCard';
  static const String _deleteCardPath = 'shop/v4/deleteDebitCard';

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request
  final TdApiClient? _apiClient;

  /// API base url
  final String? _firebaseServiceUrl;

  CardManagementDataSourceImpl(this._firebaseServiceUrl, this._apiClient);

  /// Creates a payment authorization request.
  @override
  Future authorizePayment(AuthorizeParams params) async {
    String url = '$_firebaseServiceUrl/$_authorizePaymentPath';

    final response = await _apiClient!.post(
      url,
      data: params.toMap(),
    );
    Map<String, dynamic> data = Map<String, dynamic>.from(response.data);
    return data;
  }

  /// Validates payment made from [authorizePayment] request.
  @override
  Future<dynamic> verifyPayment(VerifyParams params) async {
    String url = '$_firebaseServiceUrl/$_verifyPaymentPath';

    final response = await _apiClient!.post(
      url,
      data: params.toMap(),
    );

    Map<String, dynamic> data = Map<String, dynamic>.from(response.data);
    return data;
  }

  /// Returns a list of [PaymentCard].
  @override
  Future<List<PaymentCard>> fetchCards(String outletId) async {
    String url = '$_firebaseServiceUrl/$_getCardsPath?outletId=$outletId';
    List<PaymentCard> cardList = [];

    final response = await _apiClient!.get(
      url,
    );
    Map<String, dynamic> data = Map<String, dynamic>.from(response.data);
    List<dynamic> cards = data['res']['data'];
    if (cards.isNotEmpty) {
      cardList = cards.map((plan) {
        return PaymentCard.fromMap(plan);
      }).toList();
    }
    return cardList;
  }

  /// Validates a [PaymentCard] details.
  @override
  Future<dynamic> verifyCard(PaymentCard? card) async {
    String url =
        '$_firebaseServiceUrl/$_verifyCardPath?outletId=${card!.retailOutletId}&cardId=${card.id}';

    final response = await _apiClient!.get(
      url,
    );

    Map<String, dynamic> data = Map<String, dynamic>.from(response.data);
    return data;
  }

  /// Deletes a [PaymentCard].
  @override
  Future<dynamic> deleteCard(PaymentCard? card) async {
    String url =
        '$_firebaseServiceUrl/$_deleteCardPath?outletId=${card!.retailOutletId}&cardId=${card.id}';
    final response = await _apiClient!.get(url);
    Map<String, dynamic> data = Map<String, dynamic>.from(response.data);
    return data;
  }
}
