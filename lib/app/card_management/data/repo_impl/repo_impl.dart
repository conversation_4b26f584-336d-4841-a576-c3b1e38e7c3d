import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_flutter_core/connectivity/src/network_connection.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:shop/app/card_management/data/data_source/card_mgt_data_source.dart';
import 'package:shop/app/card_management/domain/params/params.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

/// Implements the [CardManagementRepo] abstract class.
///
/// [CardManagementRepo] make calls to [CardManagementDataSource].
class CardManagementRepoImplementation implements CardManagementRepo {
  /// Instance of [CardManagementDataSource].
  final CardManagementDataSource? cardManagementDataSource;

  /// Returns network connectivity state.
  final NetworkConnection? networkConnection;

  CardManagementRepoImplementation(
      this.cardManagementDataSource, this.networkConnection);

  /// Call [CardManagementDataSource] to create a payment authorization request.
  @override
  Future<ApiResult<dynamic>> authroizePayment(AuthorizeParams params) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        final result = await cardManagementDataSource!.authorizePayment(params);
        return ApiResult.success(data: result);
      } catch (exception) {
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [CardManagementDataSource] to validate payment from [authroizePayment] request.
  @override
  Future<ApiResult<dynamic>> verifyPayment(VerifyParams params) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        final result = await cardManagementDataSource!.verifyPayment(params);
        return ApiResult.success(data: result);
      } catch (e, s) {
        ErrorHandler.report(e, s);
        return ApiResult.apiFailure(error: ApiExceptions.getDioException(e));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [CardManagementDataSource] to fetch a list of [PaymentCard] for a retail outlet.
  @override
  Future<ApiResult<List<PaymentCard>>> fetchCard(String outletId) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        final result = await cardManagementDataSource!.fetchCards(outletId);
        return ApiResult.success(data: result);
      } catch (e, s) {
        ErrorHandler.report(e, s);
        return ApiResult.apiFailure(error: ApiExceptions.getDioException(e));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [CardManagementDataSource] to validates a [PaymentCard] details.
  @override
  Future<ApiResult<dynamic>> verifyCard(PaymentCard? card) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        final result = await cardManagementDataSource!.verifyCard(card);
        return ApiResult.success(data: result);
      } catch (e, s) {
        ErrorHandler.report(e, s);
        return ApiResult.apiFailure(error: ApiExceptions.getDioException(e));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [CardManagementDataSource] to delete a [PaymentCard] details.
  @override
  Future<ApiResult<dynamic>> deleteCard(PaymentCard? card) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        final result = await cardManagementDataSource!.deleteCard(card);
        return ApiResult.success(data: result);
      } catch (e, s) {
        ErrorHandler.report(e, s);
        return ApiResult.apiFailure(error: ApiExceptions.getDioException(e));
      }
    } else {
      return ApiResult.apiFailure(error: ApiExceptions.noInternetConnection());
    }
  }
}
