class AuthorizeParams {
  final int? amount ;
  final String? userId;
  final String? authToken;
  final String? retailOutletId;
  final String? paymentMethod;
  final String? domain;

  const AuthorizeParams({
    this.amount,
    this.authToken,
    this.domain,
    this.paymentMethod,
    this.retailOutletId,
    this.userId
  });

  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'paymentMethod': paymentMethod,
      'userId': userId,
      'authToken': authToken,
      'retailOutletId': retailOutletId,
      'domain': domain,
    };
  }
}

class VerifyParams {
  final String? userId;
  final String? authToken;
  final String? transactionRef;
  final String? domain;

  const VerifyParams({
    this.authToken,
    this.domain,
    this.transactionRef,
    this.userId
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'authToken': authToken,
      'transactionRef': transactionRef,
      'domain': domain,
    };
  }
}