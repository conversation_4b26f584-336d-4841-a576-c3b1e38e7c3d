import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

class VerifyCard with UseCases<ApiResult<dynamic>, PaymentCard> {
  const VerifyCard(this.repo);

  /// Instance of [CardManagementRepo].
  final CardManagementRepo? repo;

  /// Validates a [PaymentCard] details.
  @override
  Future<ApiResult<dynamic>> call(PaymentCard? card) {
    return repo!.verifyCard(card);
  }
}
