import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

class DeleteCard with UseCases<ApiResult<dynamic>, PaymentCard> {
  const DeleteCard(this.repo);

  /// Instance of [CardManagementRepo].
  final CardManagementRepo? repo;

  /// Deletes a users [PaymentCard].
  @override
  Future<ApiResult<dynamic>> call(PaymentCard? card) {
    return repo!.deleteCard(card);
  }
}
