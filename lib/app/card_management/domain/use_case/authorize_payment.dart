import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/domain/params/params.dart';

class Authorize with UseCases<ApiResult<dynamic>, AuthorizeParams> {
  const Authorize(this.repo);

  /// Instance of [CardManagementRepo].
  final CardManagementRepo? repo;

  /// Create a payment authorization request.
  @override
  Future<ApiResult<dynamic>> call(AuthorizeParams params) {
    return repo!.authroizePayment(params);
  }
}
