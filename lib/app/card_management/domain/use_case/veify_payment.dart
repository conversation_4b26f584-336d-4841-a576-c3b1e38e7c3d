import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/domain/params/params.dart';

class Verify with UseCases<ApiResult<dynamic>, VerifyParams> {
  const Verify(this.repo);

  /// Instance of [CardManagementRepo].
  final CardManagementRepo? repo;

  /// Validate payment made from [AuthorizePayment] use case.
  @override
  Future<ApiResult<dynamic>> call(VerifyParams params) {
    return repo!.verifyPayment(params);
  }
}
