import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

class FetchCard with UseCases<ApiResult<dynamic>, String> {
  const FetchCard(this.repo);

  /// Instance of [CardManagementRepo].
  final CardManagementRepo? repo;

  /// Returns a list of [PaymentCard] for a given [outletId].
  @override
  Future<ApiResult<List<PaymentCard>>> call(String outletId) {
    return repo!.fetchCard(outletId);
  }
}
