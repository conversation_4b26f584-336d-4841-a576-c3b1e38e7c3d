import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:shop/app/card_management/domain/params/params.dart';
import 'package:shop/app/card_management/data/model/payment_card.dart';

/// Base class for [CardManagementRepoImplementation].
abstract class CardManagementRepo {
  Future<ApiResult<dynamic>> authroizePayment(AuthorizeParams params);
  Future<ApiResult<dynamic>> verifyPayment(VerifyParams params);
  Future<ApiResult<List<PaymentCard>>> fetchCard(String outletId);
  Future<ApiResult<dynamic>> verifyCard(PaymentCard? card);
  Future<ApiResult<dynamic>> deleteCard(PaymentCard? card);
}
