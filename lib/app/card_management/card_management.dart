import 'package:shop/app/card_management/data/data_source/card_mgt_data_source.dart';
import 'package:shop/app/card_management/data/data_source/impl/card_mgt_data_source.dart';
import 'package:shop/app/card_management/data/repo_impl/repo_impl.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:shop/app/card_management/domain/repo/repo.dart';
import 'package:shop/app/card_management/domain/use_case/authorize_payment.dart';
import 'package:shop/app/card_management/domain/use_case/veify_payment.dart';
import 'package:shop/app/card_management/domain/use_case/verify_card.dart';
import 'package:shop/app/card_management/domain/use_case/fetch_cards.dart';
import 'package:shop/app/card_management/domain/use_case/delete_card.dart';

void registerCardManagementDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<CardManagementDataSource>(
    () => CardManagementDataSourceImpl(
      config.firebaseServiceUrl,
      locator(),
    ),
  );

  // Repositories
  locator.registerLazySingleton<CardManagementRepo>(
      () => CardManagementRepoImplementation(locator(), locator()));

  // Use cases
  locator.registerLazySingleton(() => Authorize(locator()));
  locator.registerLazySingleton(() => Verify(locator()));
  locator.registerLazySingleton(() => VerifyCard(locator()));
  locator.registerLazySingleton(() => DeleteCard(locator()));
  locator.registerLazySingleton(() => FetchCard(locator()));
}
