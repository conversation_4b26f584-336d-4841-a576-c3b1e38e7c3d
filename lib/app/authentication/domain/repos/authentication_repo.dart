import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/authentication/data/models/account_type.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/data/repos_impl/authentication_repo_impl.dart';
import 'package:shop/app/authentication/domain/params/address_params.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/homepage/data/models/referee.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/service_result/service_result.dart';

import '../../data/models/preference.dart';

/// Base class for [AuthenticationRepoImpl].
abstract class AuthenticationRepo {
  Future<ApiResult<User>> signUp(SignUpParams params);

  Future<ApiResult<User>> loginWithOtp(LoginTokenParams params);

  Future<ApiResult<LoginResponse>> loginWithPin(LoginPinParams params);

  Future<ApiResult<Map<String, dynamic>?>> createPin(CreatePinParams params);

  Future<ApiResult<Map<String, dynamic>?>> createAccount(AccountType params);

  Future<ApiResult<Map<String, dynamic>?>> resetPin(ResetPinParams params);

  Future<ApiResult<UserStatus>> checkPhone(CheckPhoneParams params);

  Future<User> doFirebaseLogin(User user, {User? currentUser});

  Future<ApiResult<WalletAccount?>> setUpWallet([User? user]);

  Future<void> saveUser(User user);

  Future<ApiResult<RetailOutlet?>> fetchOutlet(FetchOutletParams params);

  Stream<RetailOutlet?> streamOutlet(String? outletId);

  Future<ApiResult<Map<String, dynamic>?>> verifyPin(VerifyPinParams params);

  Future<ApiResult<num>> getAppliedInterest(String outletId);

  Future<ApiResult<InitiateJobResponse>> initiateJob(
      InitiateJobRequest request);

  Future<ApiResult<InitiateStripeResponse>> initiateStripeVerification(
      InitiateStripeRequest request);

  Future<ApiResult<CompanySearchData>> companySearch(String query);

  Future<ApiResult<String>> verifyCompany(String companyId);

  Future<ApiResult<StripeStatusResponse>> stripeStatus();

  Future<ApiResult<dynamic>> validateJob(String jobId);

  Future<ApiResult<CurrentJob>> getCurrentJob();

  Future<ApiResult<VerifyNinResponse>> verifyNin(NinParams params);

  Future<ApiResult<Map<PreferenceType, List<Preference>>>> getPreferences(
      GetPreferencesParams params);

  Future<ApiResult<Map<String, dynamic>?>> savePreferences(
      SavePreferencesParams params);

  Future<ApiResult<Map<String, dynamic>?>> updateEmail(
      UpdateEmailParams params);

  Future<ApiResult<String>> addRefereeCode(String code);

  Future<ApiResult<Referee?>> getReferee();

  Future<ApiResult<TdAddress>> validateAddress(AddressParams params);

  Future<ApiResult<Object>> getAddresses(AddressParams params);

  Future<ApiResult<bool>> deleteAccount();

  Future<ApiResult<Map<String, dynamic>?>> sendOTP(SendOTParams params);

  Future<ApiResult<VerifiedOTP>> verifyOTP(VerifyOTParams params);

  Future<ApiResult<Map<String, dynamic>>> addPhone(String phoneNumber);
}
