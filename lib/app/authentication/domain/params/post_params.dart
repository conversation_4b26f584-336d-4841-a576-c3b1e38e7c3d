import 'package:td_commons_flutter/models/index.dart';

import '../../data/models/preference.dart';

class SignUpParams {
  final String phoneNumber;
  final String accessToken;
  final Map<String, dynamic> data;
  // final String visitorId;

  const SignUpParams({
    required this.data,
    required this.phoneNumber,
    required this.accessToken,
    // required this.visitorId,
  });

  Map<String, dynamic> toMap() {
    final items = Map<String, dynamic>.from(data);

    final user = Map<String, dynamic>.from(items['user']);
    user['phoneNumber'] = phoneNumber;
    items['user'] = user;
    items['phone'] = phoneNumber;
    // items['visitorId'] = visitorId;

    return items;
  }
}

class LoginTokenParams {
  final String? phone;
  final String? email;
  final String token;

  LoginTokenParams({required this.token, this.phone, this.email}) {
    assert(
        email != null || phone != null, 'Both email and phone cannot be null.');
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {
      'accessToken': token,
    };

    if (email != null) {
      data['email'] = email;
    }

    if (phone != null) {
      data['phoneNumber'] = phone;
    }

    return data;
  }
}

class LoginPinParams {
  final String phone;
  final String pin;
  final String visitorId;
  final bool? useDevice;

  LoginPinParams({
    required this.phone,
    required this.pin,
    required this.visitorId,
    this.useDevice = false,
  });

  Map<String, dynamic> toMap() {
    if (useDevice!) {
      return {
        'phoneNumber': phone,
        'pin': pin,
        'visitorId': visitorId,
        'useDevice': useDevice,
      };
    } else {
      return {
        'phoneNumber': phone,
        'pin': pin,
        'visitorId': visitorId,
      };
    }
  }

  LoginPinParams copyWith({
    String? phone,
    String? pin,
    String? visitorId,
    bool? useDevice,
  }) {
    return LoginPinParams(
      phone: phone ?? this.phone,
      pin: pin ?? this.pin,
      visitorId: visitorId ?? this.visitorId,
      useDevice: useDevice ?? this.useDevice,
    );
  }
}

class CheckPhoneParams {
  final String? phone;
  final String? email;

  const CheckPhoneParams({this.phone, this.email});

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {};

    if (phone != null && phone!.isNotEmpty) {
      data['phoneNumber'] = phone;
    }
    if (email != null && email!.isNotEmpty) {
      data['email'] = email;
    }

    return data;
  }
}

class FirebaseLoginParams {
  final User user;
  final User? currentUser;

  const FirebaseLoginParams(this.user, {this.currentUser});
}

class CreatePinParams {
  final String? pin;

  const CreatePinParams(this.pin);

  Map<String, dynamic> toMap() {
    return {
      'pin': pin,
    };
  }
}

class ResetPinParams {
  final String? pin;
  final String phoneNumber;
  final String token;

  const ResetPinParams(
    this.pin,
    this.phoneNumber,
    this.token,
  );

  ResetPinParams addPin(String? newPin) {
    return ResetPinParams(newPin, phoneNumber, token);
  }

  Map<String, dynamic> toMap() {
    return {
      'newPin': pin,
      'phoneNumber': phoneNumber,
      'accessToken': token,
    };
  }
}

class ReserveAccountParams {
  final String? outletId;

  const ReserveAccountParams({
    required this.outletId,
  });

  Map<String, dynamic> toMap() {
    return {
      'outletId': outletId,
    };
  }
}

// class VerifyPinParams {
//   final String pin;

//   VerifyPinParams({
//     required this.pin,
//   });

//   Map<String, dynamic> toMap() {
//     return {
//       'pin': pin,
//     };
//   }

//   @override
//   String toString() {
//     return '${toMap()}';
//   }
// }

class FetchOutletParams {
  final String? outletId;
  final String? method;
  final String? phoneNumber;

  const FetchOutletParams({
    this.outletId,
    this.method,
    this.phoneNumber,
  });

  Map<String, dynamic> toMap() {
    return {
      'outletId': outletId,
      'method': method,
      'phoneNumber': phoneNumber,
    };
  }

  @override
  String toString() => '''FetchOutletParams(
        outletId: $outletId, 
        method: $method, 
        phoneNumber: $phoneNumber,
      )''';
}

class NinParams {
  final String nin;
  final bool? isValidated;
  final String? phoneNumber;

  NinParams({
    required this.nin,
    this.isValidated,
    this.phoneNumber,
  });

  Map<String, dynamic> toMap() {
    return {
      'nin': nin,
      'isValidated': isValidated,
      'phoneNumber': phoneNumber,
    };
  }

  factory NinParams.fromMap(Map<String, dynamic> map) {
    return NinParams(
        nin: map['nin'],
        isValidated: map['isValidated'],
        phoneNumber: map['phoneNumber']);
  }
}

class PayParams {
  final String supplier;
  final String agent;
  final String phoneNumber;
  final num amount;

  PayParams(
      {required this.agent,
      required this.phoneNumber,
      required this.supplier,
      required this.amount});

  factory PayParams.fromMap(Map<String, dynamic> agentDetails) {
    return PayParams(
        supplier: agentDetails["Supplier"],
        agent: agentDetails["Agent"],
        amount: agentDetails["amount"],
        phoneNumber: agentDetails["phoneNumber"]);
  }

  Map<String, dynamic> toMap() {
    return {
      "Agent": agent,
      "Supplier": supplier,
      "phoneNumber": phoneNumber,
      "amount": amount
    };
  }
}

class UpdateEmailParams {
  final String email;
  final bool? validateToken;

  UpdateEmailParams({required this.email, this.validateToken = false});

  Map<String, dynamic> toMap() {
    if (validateToken == true) {
      return {
        'email': email,
        'validateToken': true,
      };
    } else {
      return {
        'email': email,
      };
    }
  }
}

class VerifyNinResponse {
  final String message;
  final String? ninPhone;
  final bool isVerified;

  VerifyNinResponse({
    required this.message,
    this.ninPhone,
    required this.isVerified,
  });

  factory VerifyNinResponse.fromMap(Map<String, dynamic> map) {
    return VerifyNinResponse(
      message: map['message'] ?? 'NIN Verification Successful',
      ninPhone: map['ninPhone'],
      isVerified: map['isVerified'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'ninPhone': ninPhone,
      'isVerified': isVerified,
    };
  }

  @override
  String toString() => '${toMap()}';
}

class SavePreferencesParams {
  final List<Preference> shopPreferences;

  SavePreferencesParams(
    this.shopPreferences,
  );

  Map<String, dynamic> toMap() {
    return {
      'shopPreferences': shopPreferences.map((e) => e.toMap()).toList(),
    };
  }
}

class GetPreferencesParams {
  final PreferenceType type;
  final String? hexCode;

  const GetPreferencesParams({required this.type, this.hexCode})
      : assert(type != PreferenceType.collection || hexCode != null);
}

enum PreferenceType { user, collection }

enum PhoneAuthMode { SMS, WhatsApp, Email, All }

class SendOTParams {
  const SendOTParams({
    required this.mode,
    this.phoneNumber,
    required this.url,
    this.email,
    this.domain = SendOtpDomain.tradedepot,
  }) : assert((mode == PhoneAuthMode.Email && email != null) ||
            (mode != PhoneAuthMode.Email && phoneNumber != null));

  final PhoneAuthMode mode;
  final String? phoneNumber;
  final String url;
  final String? email;
  final SendOtpDomain? domain;

  Map<String, String?> toMap() {
    if (mode == PhoneAuthMode.Email) {
      assert(email != null, 'Email cannot be null when PhoneAuthMode is email');
      return {
        'mode': mode.name,
        'email': email,
        if (domain != null) 'domain': domain?.name,
        'url': url
      };
    }

    assert(phoneNumber != null,
        'PhoneNumber cannot be null for SMS or WhatsApp mode');

    return {
      'mode': mode.name,
      'phoneNumber': phoneNumber,
      if (domain != null) 'domain': domain?.name,
      if (email != null) 'email': email,
      'url': url
    };
  }

  factory SendOTParams.fromMap(Map<String, dynamic> map) {
    return SendOTParams(
        mode: PhoneAuthMode.values.byName(map['mode']),
        email: map['email'],
        phoneNumber: map['phoneNumber'],
        domain: map['domain'] != null
            ? SendOtpDomain.values.byName(map['domain'])
            : null,
        url: map['url']);
  }
}

enum SendOtpDomain { shoptopup, tradedepot }

class VerifyOTParams {
  VerifyOTParams({
    this.phoneNumber,
    this.email,
    required this.token,
    required this.countryCode,
    required this.url,
  }) : assert(
            (phoneNumber != null && email == null) ||
                (phoneNumber == null && email != null),
            'Either phoneNumber or email must be provided, but not both.');

  final String? phoneNumber;
  final String? email;
  final String token;
  final String countryCode;
  final String url;

  Map<String, String> toMap() {
    if (phoneNumber != null) {
      return {
        'phoneNumber': phoneNumber!,
        'token': token,
        'countryCode': countryCode,
      };
    }

    if (email != null) {
      return {
        'email': email!,
        'token': token,
        'countryCode': countryCode,
      };
    }

    return {
      'token': token,
      'countryCode': countryCode,
    };
  }
}
