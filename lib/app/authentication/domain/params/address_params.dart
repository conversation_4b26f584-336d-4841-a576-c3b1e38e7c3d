import 'package:shop/app/authentication/data/models/address_find.dart';

class AddressParams {
  String? _region;
  String? _postalCode;
  String? _address1;
  String? _address2;
  String? _locality;
  String? _id;
  AddressParamsType? _type;

  set address1(String value) {
    _address1 = value;
  }

  set address2(String? value) {
    _address2 = value;
  }

  String? get address2 => _address2;

  set region(String value) {
    _region = value;
  }

  set locality(String value) {
    _locality = value;
  }

  set id(String value) {
    _id = value;
  }

  set type(AddressParamsType type) {
    _type = type;
  }

  factory AddressParams.init() =>
      AddressParams(null, null, null, null, null, null, null);

  factory AddressParams.find(String region, String postal, String? id, AddressParamsType? type) =>
      AddressParams(region, postal, null, null, null, id, type);

  AddressParams(this._region, this._postalCode, this._address1, this._address2,
      this._locality, this._id, this._type);

  Map<String, dynamic> toMap() {
    return {
      "address": {
        "regionCode": _region,
        "postalCode": _postalCode,
        "addressLines": _address2?.isEmpty ?? true ? [_address1] : [_address1, _address2],
        "locality": _locality
      },
    };
  }

  Map<String, dynamic> toAutoSearchMap() {
    return {
      "country": _region,
      "postCode": _postalCode,
      "id": _id,
      "type": _type?.name ?? AddressParamsType.containerFind.name
    };
  }

  set postalCode(String value) {
    _postalCode = value;
  }
}

enum AddressParamsType {containerFind, addressFind, addressRetrieve}

extension ParamsType on AddressFindType{
 AddressParamsType get paramsType {
    switch(this){
      case AddressFindType.Container:
        return AddressParamsType.addressFind;
      case AddressFindType.Address:
        return AddressParamsType.addressRetrieve;
      case AddressFindType.other:
        return AddressParamsType.containerFind;
    }
  }
}