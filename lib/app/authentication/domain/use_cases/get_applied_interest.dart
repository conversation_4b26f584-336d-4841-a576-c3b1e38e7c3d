import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetAppliedInterest with UseCases<ApiResult<num>, String> {
  const GetAppliedInterest(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Validate user inputted pin
  @override
  Future<ApiResult<num>> call(String outletId) {
    return repo!.getAppliedInterest(outletId);
  }
}
