import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/models/wallet_account.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class SetupWallet with UseCases<ApiResult<WalletAccount?>, User> {
  const SetupWallet(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Creates [WalletAccount] for a retail oulet.
  ///
  /// Only if [WalletAccount] is null or [WalletAccount.secAccountNumber] is null.
  @override
  Future<ApiResult<WalletAccount?>> call(User? user) {
    return repo!.setUpWallet(user);
  }
}
