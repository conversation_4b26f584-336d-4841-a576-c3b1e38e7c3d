import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ValidateJob with UseCases<ApiResult<dynamic>, String> {
  const ValidateJob(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<dynamic>> call(String params) {
    return repo!.validateJob(params);
  }
}
