import 'package:shop/app/authentication/data/models/account_type.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CreateAccount
    with UseCases<ApiResult<Map<String, dynamic>?>, AccountType> {
  const CreateAccount(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Creates a secondary [Account] for a retail outlet.
  ///
  /// Either [Wallet] or [Collections] account is created based on the [AccountType] provided.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(AccountType params) {
    return repo!.createAccount(params);
  }
}
