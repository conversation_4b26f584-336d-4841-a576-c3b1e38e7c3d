import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CompanyRegSearch with UseCases<ApiResult<CompanySearchData>, String> {
  const CompanyRegSearch(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<CompanySearchData>> call(String query) {
    return repo!.companySearch(query);
  }
}
