import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import '../params/post_params.dart';

class SavePreferences
    with UseCases<ApiResult<Map<String, dynamic>?>, SavePreferencesParams> {
  SavePreferences(this._repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? _repo;

  /// Save a list of user selected shopping preferences.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(SavePreferencesParams params) =>
      _repo!.savePreferences(params);
}

// path: save-preferences
// type: claimsUrl
// method: POST
// data: {shopPreferences: [{_id: collectionID, name: collectionName}]}
// requires x-api-key

// path: save-preferences
// type: claimsUrl
// method: POST
// data: {shopPreferences: [{_id: collectionID, name: collectionName}]}
// requires x-api-key
