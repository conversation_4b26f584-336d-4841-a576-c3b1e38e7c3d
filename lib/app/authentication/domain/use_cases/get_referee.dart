import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:shop/app/homepage/data/models/referee.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class GetReferee with UseCases<ApiResult<Referee?>, NoParams> {
  const GetReferee(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Create a new user pin.
  ///
  /// Retrieve a referee object
  @override
  Future<ApiResult<Referee?>> call(NoParams params) {
    return repo!.getReferee();
  }
}
