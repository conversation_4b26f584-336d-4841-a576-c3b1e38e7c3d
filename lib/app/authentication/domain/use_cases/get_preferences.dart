import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import '../../data/models/preference.dart';
import '../params/post_params.dart';

class GetPreferences
    with
        UseCases<ApiResult<Map<PreferenceType, List<Preference>>>,
            GetPreferencesParams> {
  GetPreferences(this._repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? _repo;

  /// Get a list of shopping preferences for a user or from collections.
  @override
  Future<ApiResult<Map<PreferenceType, List<Preference>>>> call(
          GetPreferencesParams params) =>
      _repo!.getPreferences(params);
}
