import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class LoginWithPin with UseCases<ApiResult<LoginResponse>, LoginPinParams> {
  const LoginWithPin(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Login a user with pin.
  @override
  Future<ApiResult<LoginResponse>> call(LoginPinParams params) {
    return repo!.loginWithPin(params);
  }
}
