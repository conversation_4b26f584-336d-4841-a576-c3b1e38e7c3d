import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CheckPhone with UseCases<ApiResult<UserStatus>, CheckPhoneParams> {
  const CheckPhone(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Checks if [params.phoneNumber] has a valid account. Returns [UserStatus].
  @override
  Future<ApiResult<UserStatus>> call(CheckPhoneParams params) {
    return repo!.checkPhone(params);
  }
}
