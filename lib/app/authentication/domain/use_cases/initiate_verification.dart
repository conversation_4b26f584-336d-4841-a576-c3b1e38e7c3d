import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class InitiateStripeVerification
    with UseCases<ApiResult<InitiateStripeResponse>, InitiateStripeRequest> {
  const InitiateStripeVerification(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<InitiateStripeResponse>> call(InitiateStripeRequest params) {
    return repo!.initiateStripeVerification(params);
  }
}
