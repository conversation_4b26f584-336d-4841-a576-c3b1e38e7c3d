import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class SendOTP with UseCases<ApiResult<Map?>, SendOTParams> {
  const SendOTP(this.repo);
  final AuthenticationRepo repo;

  @override
  Future<ApiResult<Map?>> call(SendOTParams params) {
    return repo.sendOTP(params);
  }
}
