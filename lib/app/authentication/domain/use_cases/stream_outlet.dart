import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_commons_flutter/models/retailer.dart';
// import 'package:td_flutter_core/td_flutter_core.dart';

class StreamOutlet {
  StreamOutlet(this._repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? _repo;

  /// Returns a stream of a retail outlet from firestore.
  Stream<RetailOutlet?> call(String? outletId) => _repo!.streamOutlet(outletId);
}
