import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class AddPhone with UseCases<ApiResult<Map<String, dynamic>>, String> {
  const AddPhone(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Add [phoneNumber] to a retail outlet account.
  @override
  Future<ApiResult<Map<String, dynamic>>> call(String phoneNumber) {
    return repo!.addPhone(phoneNumber);
  }
}
