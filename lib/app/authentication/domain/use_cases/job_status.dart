import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class JobStatus with UseCases<ApiResult<CurrentJob>, NoParams> {
  const JobStatus(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<CurrentJob>> call(NoParams params) {
    return repo!.getCurrentJob();
  }
}
