import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class SignUp with UseCases<ApiResult<User>, SignUpParams> {
  const SignUp(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Signup a new user.
  @override
  Future<ApiResult<User>> call(SignUpParams params) {
    return repo!.signUp(params);
  }
}
