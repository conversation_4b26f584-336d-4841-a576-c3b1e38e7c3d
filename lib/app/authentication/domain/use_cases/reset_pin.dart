import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class ResetPin with UseCases<ApiResult<Map<String, dynamic>?>, ResetPinParams> {
  const ResetPin(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Resets a user pin.
  ///
  /// Pin is used for login and other authorisation and validation requests.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(ResetPinParams params) {
    return repo!.resetPin(params);
  }
}
