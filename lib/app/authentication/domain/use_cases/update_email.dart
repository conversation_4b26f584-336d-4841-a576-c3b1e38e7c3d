import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class UpdateEmail
    with UseCases<ApiResult<Map<String, dynamic>?>, UpdateEmailParams> {
  const UpdateEmail(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<Map<String, dynamic>?>> call(UpdateEmailParams params) {
    return repo!.updateEmail(params);
  }
}
