import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';
import 'package:td_flutter_core/use_cases/use_cases.dart';

class VerifyOTP implements UseCases<ApiResult<VerifiedOTP>, VerifyOTParams> {
  const VerifyOTP(this.repo);

  final AuthenticationRepo repo;

  @override
  Future<ApiResult<VerifiedOTP>> call(VerifyOTParams params) {
    return repo.verifyOTP(params);
  }
}
