import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class VerificationStatus
    with UseCases<ApiResult<StripeStatusResponse>, NoParams> {
  const VerificationStatus(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<StripeStatusResponse>> call(NoParams params) {
    return repo!.stripeStatus();
  }
}
