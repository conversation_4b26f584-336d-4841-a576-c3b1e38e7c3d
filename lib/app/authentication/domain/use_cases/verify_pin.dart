// import 'package:shop/app/authentication/domain/params/post_params.dart';
// import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
// import 'package:td_flutter_core/td_flutter_core.dart';

// class VerifyPin
//     with UseCases<ApiResult<Map<String, dynamic>?>, VerifyPinParams> {
//   const VerifyPin(this.repo);

//   /// Instance of [AuthenticationRepo].
//   final AuthenticationRepo? repo;

//   /// Validate user inputted pin
//   @override
//   Future<ApiResult<Map<String, dynamic>?>> call(VerifyPinParams params) {
//     return repo!.verifyPin(params);
//   }
// }
