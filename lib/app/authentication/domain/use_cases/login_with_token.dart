import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class LoginWithToken with UseCases<ApiResult<User>, LoginTokenParams> {
  const LoginWithToken(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Login a user with OTP.
  ///
  /// OTP is sent to [params.phone].
  @override
  Future<ApiResult<User>> call(LoginTokenParams params) {
    return repo!.loginWithOtp(params);
  }
}
