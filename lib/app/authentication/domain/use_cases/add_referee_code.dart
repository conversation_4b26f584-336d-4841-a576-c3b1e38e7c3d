import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class AddRefereeCode with UseCases<ApiResult<String>, String> {
  const AddRefereeCode(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Create a new user pin.
  ///
  /// Add a referee code
  @override
  Future<ApiResult<String>> call(String code) {
    return repo!.addRefereeCode(code);
  }
}
