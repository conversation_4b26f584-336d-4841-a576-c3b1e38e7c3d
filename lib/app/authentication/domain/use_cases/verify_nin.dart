import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import '../params/post_params.dart';

class VerifyNin with UseCases<ApiResult<VerifyNinResponse>, NinParams> {
  const VerifyNin(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Validate user inputted pin
  @override
  Future<ApiResult<VerifyNinResponse>> call(NinParams params) {
    return repo!.verifyNin(params);
  }
}
