import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class InitiateJob
    with UseCases<ApiResult<InitiateJobResponse>, InitiateJobRequest> {
  const InitiateJob(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<InitiateJobResponse>> call(InitiateJobRequest params) {
    return repo!.initiateJob(params);
  }
}
