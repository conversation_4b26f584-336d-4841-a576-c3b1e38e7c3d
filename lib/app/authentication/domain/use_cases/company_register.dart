import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CompanyRegister with UseCases<ApiResult<String>, String> {
  const CompanyRegister(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  @override
  Future<ApiResult<String>> call(String companyId) {
    return repo!.verifyCompany(companyId);
  }
}
