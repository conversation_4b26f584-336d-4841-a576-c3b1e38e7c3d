import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchOutlet with UseCases<ApiResult<RetailOutlet?>, FetchOutletParams> {
  FetchOutlet(this._repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? _repo;

  /// Returns a retail outlet's latest value.
  @override
  Future<ApiResult<RetailOutlet?>> call(FetchOutletParams params) =>
      _repo!.fetchOutlet(params);
}
