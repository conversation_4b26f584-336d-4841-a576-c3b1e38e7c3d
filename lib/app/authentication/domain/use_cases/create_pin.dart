import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CreatePin
    with UseCases<ApiResult<Map<String, dynamic>?>, CreatePinParams> {
  const CreatePin(this.repo);

  /// Instance of [AuthenticationRepo].
  final AuthenticationRepo? repo;

  /// Create a new user pin.
  ///
  /// Pin is used for login and other authorisation and validation requests.
  @override
  Future<ApiResult<Map<String, dynamic>?>> call(CreatePinParams params) {
    return repo!.createPin(params);
  }
}
