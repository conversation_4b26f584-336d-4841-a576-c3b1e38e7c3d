import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:sentry_flutter/sentry_flutter.dart' as sentry;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/authentication/data/data_source/authentication_remote_data_source.dart';
import 'package:shop/app/authentication/data/models/account_type.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/preference.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/data/models/visitor.dart';
import 'package:shop/app/authentication/domain/params/address_params.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/homepage/data/models/referee.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/services/td_exception.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/models/wallet_account.dart';
import 'package:td_flutter_core/connectivity/src/network_connection.dart';
import 'package:td_flutter_core/service_exceptions/service_exception.dart';
import 'package:td_flutter_core/service_result/src/api_result.dart';

/// Implements the [AuthenticationRepo] abstract class.
///
/// [AuthenticationRepo] make calls to [AuthenticationRemoteDataSource].
class AuthenticationRepoImpl implements AuthenticationRepo {
  /// Instance of [AuthenticationRemoteDataSource].
  final AuthenticationRemoteDataSource _authenticationRemoteDataSource;

  /// Returns network connectivity state.
  final NetworkConnection networkConnection;

  AuthenticationRepoImpl(
    this._authenticationRemoteDataSource,
    this.networkConnection,
  );

  /// Call [AuthenticationRemoteDataSource] to login user with OTP.
  @override
  Future<ApiResult<User>> loginWithOtp(LoginTokenParams params) async {
    final res = await dioInterceptor(
      () => _authenticationRemoteDataSource.loginWithOtp(params),
    );

    return res.maybeWhen(
      success: (user) async {
        if (user.currentRetailOutlet?.phoneNumber == null) {
          try {
            final sp = await SharedPreferences.getInstance();
            sp.setBool(Keys.updatePhone, true);
          } catch (_) {}
        }

        try {
          await doFirebaseLogin(user);
        } catch (e, s) {
          ErrorHandler.report(
              '$e, outletId: ${user.currentRetailOutlet?.id}, email: ${user.currentRetailOutlet?.email}, toast-error: Failed to login. Please try again later, function: loginWithOtp - in auth repo',
              s);
          return const ApiResult.apiFailure(
            error: ApiExceptions.defaultError(
              'Failed to login. Please try again later',
            ),
          );
        }
        return res;
      },
      orElse: () => res,
    );
  }

  /// Call [AuthenticationRemoteDataSource] to login a user with pin.
  @override
  Future<ApiResult<LoginResponse>> loginWithPin(LoginPinParams params) async {
    // final res = await dioInterceptor(
    //   () => _authenticationRemoteDataSource.loginWithPin(params),
    // );

    try {
      if (await networkConnection.isDeviceConnected) {
        final res = await _authenticationRemoteDataSource.loginWithPin(params);

        return await handleUserResponse(res);

        // return ApiResult.success(data: res);
      } else {
        return const ApiResult.apiFailure(
            error: ApiExceptions.noInternetConnection());
      }
    } on DioError catch (error) {
      if (error.response?.statusCode != null &&
          error.response!.statusCode == 409) {
        final visitor = Visitor.fromMap(error.response!.data['data']);

        if (visitor.visit == null) {
          return const ApiResult.apiFailure(
            error: ApiExceptions.defaultError(UNEXPECTED_ERROR),
          );
        }

        return ApiResult.success(
            data: LoginResponse(
                type: LoginResponseType.visitor, visitor: visitor));
      }

      return ApiResult.apiFailure(
        error: ApiExceptions.getDioException(error),
      );
    } catch (exception) {
      return ApiResult.apiFailure(
        error: ApiExceptions.getDioException(exception),
      );
    }
  }

  Future<ApiResult<LoginResponse>> handleUserResponse(LoginResponse res) async {
    try {
      await doFirebaseLogin(res.user!);
      return ApiResult.success(data: res);
    } catch (e, s) {
      ErrorHandler.report(
          '$e, outletId: ${res.user?.currentRetailOutlet?.id}, email: ${res.user?.currentRetailOutlet?.email}, toast-error: Failed to login. Please try again later, function: handleUserResponse - in auth repo',
          s);
      return const ApiResult.apiFailure(
        error: ApiExceptions.defaultError(
          'Failed to login. Please try again later',
        ),
      );
    }
  }

  /// Call [AuthenticationRemoteDataSource] to signup a new user.
  @override
  Future<ApiResult<User>> signUp(SignUpParams params) async {
    final res = await dioInterceptor(
      () => _authenticationRemoteDataSource.signUp(params),
    );

    return res.maybeWhen(
      success: (user) async {
        try {
          await doFirebaseLogin(user);
        } catch (e, s) {
          ErrorHandler.report(
              '$e, outletId: ${user.currentRetailOutlet?.id}, email: ${user.currentRetailOutlet?.email}, toast-error: Failed to login. Please try again later, function: signUp - in auth repo',
              s);
          return const ApiResult.apiFailure(
            error: ApiExceptions.defaultError(
              'Failed to login. Please try again later',
            ),
          );
        }
        return res;
      },
      orElse: () => res,
    );
  }

  /// Call [AuthenticationRemoteDataSource] to create a new user pin.
  @override
  Future<ApiResult<Map<String, dynamic>?>> createPin(
    CreatePinParams params,
  ) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.createPin(params),
      doNotReport: [403, 422],
    );
  }

  /// Call [AuthenticationRemoteDataSource] to create secondary wallet account for a user.
  @override
  Future<ApiResult<Map<String, dynamic>?>> createAccount(
      AccountType params) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.createAccount(params),
      doNotReport: [406, 400],
    );
  }

  /// Call [AuthenticationRemoteDataSource] to reset a user pin.
  @override
  Future<ApiResult<Map<String, dynamic>?>> resetPin(
    ResetPinParams params,
  ) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.resetPin(params),
    );
  }

  /// Call [AuthenticationRemoteDataSource] to check if [params.phoneNumber] has a valid account.
  @override
  Future<ApiResult<UserStatus>> checkPhone(
    CheckPhoneParams params,
  ) {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.checkPhone(params),
    );
  }

  /// Call [AuthenticationRemoteDataSource] to authenticate user with firebase.
  ///
  /// Only if [loginWithPin], [loginWithOtp] or [signUp] is successful.
  @override
  Future<User> doFirebaseLogin(User user, {User? currentUser}) async {
    try {
      await _validateUserToken(user);
      await _signInWithCustomToken(user.token!);
      _updateUserDisplayName(user);
      _updateUserInUserCubit(user, currentUser);
      _handleAnonymousUser(currentUser);
      _trackUserLoginEvent(user);
      _identifyUserForSentry(user);
      saveUser(user);
      _setupWalletIfShopTopUp(user);

      return user;
    } catch (_) {
      rethrow;
    }

    // if (user.token == null) {
    //   throw TdException('User token is null during API login', hint: {
    //     'userId': user.userId,
    //     'phoneNumber': user.phoneNumber,
    //   });
    // }

    // await auth.FirebaseAuth.instance.signInWithCustomToken(user.token!);
    // final userData = auth.FirebaseAuth.instance.currentUser!;
    // userData.updateDisplayName(user.displayName);
    // UserCubit.instance?.updatingUser(user);

    // if (currentUser != null && currentUser.isAnonymous!) {
    //   FirebaseFirestore.instance
    //       .collection('users')
    //       .doc(currentUser.userId)
    //       .set(
    //     {'anonymousUserId': currentUser.userId},
    //     SetOptions(merge: true),
    //   );
    // }

    // Segment.track(
    //   eventName: SegmentEvents.loggedIn,
    //   properties: {
    //     'user_login': user.userId,
    //   },
    // );

    // // Identify user for sentry
    // sentry.Sentry.configureScope(
    //   (scope) {
    //     scope.setUser(
    //       sentry.SentryUser(
    //         id: user.userId,
    //         username: user.phoneNumber,
    //         email: user.email,
    //         data: {
    //           'name': user.displayName,
    //           'retailOutlet': user.currentRetailOutlet?.toMap(),
    //         },
    //       ),
    //     );
    //   },
    // );

    // // save the current user data
    // saveUser(user);

    // // setup wallet/collection accounts and update the saved user
    // if (user.currentRetailOutlet?.domain == AppHost.shopTopUp) {
    //   setUpWallet(user);
    // }

    // return user;
  }

  Future<void> _validateUserToken(User user) async {
    if (user.token == null) {
      throw TdException(
        'User token is null during API login, userId: ${user.userId}, phoneNumber: ${user.phoneNumber}',
        hint: {
          'userId': user.userId,
          'phoneNumber': user.phoneNumber,
        },
      );
    }

    return;
  }

  Future<void> _signInWithCustomToken(String token) async {
    try {
      await auth.FirebaseAuth.instance.signInWithCustomToken(token);
    } on auth.FirebaseAuthException catch (error) {
      throw TdException(
        'Firebase signInWithCustomToken failed, token: $token, errorCode: ${error.code}',
        hint: {
          'token': token,
          'errorCode': error.code,
        },
      );
    }
  }

  void _updateUserDisplayName(User user) {
    try {
      final userData = auth.FirebaseAuth.instance.currentUser!;
      userData.updateDisplayName(user.displayName);
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  void _updateUserInUserCubit(User user, User? currentUser) {
    try {
      UserCubit.instance?.updatingUser(user);
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  void _handleAnonymousUser(User? currentUser) {
    try {
      if (currentUser != null && currentUser.isAnonymous!) {
        FirebaseFirestore.instance
            .collection('users')
            .doc(currentUser.userId)
            .set(
          {'anonymousUserId': currentUser.userId},
          SetOptions(merge: true),
        );
      }
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  void _trackUserLoginEvent(User user) {
    try {
      Segment.track(
        eventName: SegmentEvents.loggedIn,
        properties: {'user_login': user.userId},
      );
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  void _identifyUserForSentry(User user) {
    try {
      final retailOutlet = user.currentRetailOutlet;
      sentry.Sentry.configureScope((scope) {
        scope.setUser(
          sentry.SentryUser(
            id: user.userId,
            username: user.phoneNumber,
            email: user.email,
            data: {
              'name': user.displayName,
              'first_name': user.firstName,
              'last_name': user.lastName,
              'outlet_id': retailOutlet?.id,
              'outlet_name': retailOutlet?.outletBusinessName,
              'outlet_type': retailOutlet?.outletType,
              'domain': retailOutlet?.domain.name,
              'country': retailOutlet?.country,
              'state': retailOutlet?.state,
              'lga': retailOutlet?.lga,
              'coordinates': retailOutlet?.coordinates != null
                  ? {
                      'latitude': retailOutlet?.coordinates?.latitude,
                      'longitude': retailOutlet?.coordinates?.longitude,
                      'plusCode': retailOutlet?.coordinates?.plusCode,
                    }
                  : null,
              'sales_channel': retailOutlet?.salesChannel,
              'assignee': retailOutlet?.assignee.assigneeName,
              'is_verified': retailOutlet?.isVerified,
              'retailOutlet': user.currentRetailOutlet?.toMap(),
            },
          ),
        );

        // Add tags for better filtering in Sentry
        scope.setTag('domain', retailOutlet?.domain.name ?? 'unknown');
        scope.setTag('country', retailOutlet?.country ?? 'unknown');
        scope.setTag('outlet_type', retailOutlet?.outletType ?? 'unknown');
        scope.setTag(
            'is_verified', (retailOutlet?.isVerified ?? false).toString());
      });
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  void _setupWalletIfShopTopUp(User user) {
    try {
      if (user.currentRetailOutlet?.domain == AppHost.shopTopUp) {
        setUpWallet(user);
      }
    } catch (error) {
      ErrorHandler.report(error, StackTrace.current);
    }
  }

  /// Call [AuthenticationRemoteDataSource] to create [WalletAccount] for a retail outlet.
  @override
  Future<ApiResult<WalletAccount?>> setUpWallet([User? user]) async {
    user ??= UserCubit.instance?.currentUser;

    if (user == null) {
      return const ApiResult.apiFailure(
        error: ApiExceptions.defaultError('No user found'),
      );
    }

    final walletAccount = user.currentRetailOutlet?.walletAccount;
    final secAccountNumber = walletAccount?.secAccountNumber;
    final colAccountNumber = walletAccount?.colAccountNumber;

    if (colAccountNumber == null || colAccountNumber.isEmpty) {
      try {
        await _authenticationRemoteDataSource
            .createAccount(AccountType.collections);
      } catch (e) {
        // Silently ignore errors when creating collections account
        // This is a best-effort operation and should not block the main flow
      }
    }

    if (secAccountNumber != null) {
      // already has a wallet account
      return const ApiResult.success(data: null);
    }

    final res = await dioInterceptor(
      () =>
          _authenticationRemoteDataSource.createAccount(AccountType.shoptopup),
    );

    late ApiResult<WalletAccount?> response;

    res.when(
      success: (data) {
        // fetch outlet with updated account data
        UserCubit.instance?.updateOutlet();

        if (data != null) {
          final account = walletAccount?.copyWith(
              secBankName: data['bank']['name'],
              secAccountNumber: data['account_number']);

          response = ApiResult.success(data: account);
        }
        response = const ApiResult.success(data: null);
      },
      apiFailure: (error, _) {
        response = ApiResult.apiFailure(error: error);
      },
    );
    return response;
  }

  /// Save logged in [user] locally.
  @override
  Future<void> saveUser(User user) async {
    final preferences = await SharedPreferences.getInstance();
    preferences.setString(Keys.user, json.encode(user.toMap()));
  }

  /// Call [AuthenticationRemoteDataSource] to fetch a retail outlet data.
  @override
  Future<ApiResult<RetailOutlet?>> fetchOutlet(FetchOutletParams params) async {
    try {
      RetailOutlet? outlet =
          await _authenticationRemoteDataSource.fetchOutlet(params);
      return ApiResult.success(data: outlet);
    } on DioError catch (error) {
      if (error.response?.statusCode != null &&
          error.response!.statusCode == 404) {
        return ApiResult.success(data: RetailOutlet());
      }
      return ApiResult.apiFailure(
        error: ApiExceptions.getDioException(error),
      );
    } catch (exception) {
      return ApiResult.apiFailure(
        error: ApiExceptions.getDioException(exception),
      );
    }
  }

  /// Gets a stream of a retail outlet from firestore
  /// and transform to [RetailOutlet].
  @override
  Stream<RetailOutlet?> streamOutlet(String? outletId) {
    return FirebaseFirestore.instance
        .collection("retailoutlets")
        .doc(outletId)
        .snapshots()
        .map((snapshot) {
      if (!snapshot.exists) return null;
      return RetailOutlet.fromMap(snapshot.data()!);
    });
  }

  /// Call [AuthenticationRemoteDataSource] to validate user inputted pin.
  @override
  Future<ApiResult<Map<String, dynamic>?>> verifyPin(
      VerifyPinParams params) async {
    if (await networkConnection.isDeviceConnected) {
      try {
        final result = await _authenticationRemoteDataSource.verifyPin(params);
        return ApiResult.success(data: result);
      } on DioError catch (error) {
        if (error.response?.statusCode != null &&
            error.response!.statusCode == 422) {
          return ApiResult.apiFailure(
              error: ApiExceptions.defaultError(
                  error.response!.data['data']['message']));
        }
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(error));
      } catch (exception, s) {
        ErrorHandler.report(exception, s);
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      }
    } else {
      return const ApiResult.apiFailure(
          error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [AuthenticationRemoteDataSource] to get appliedInterest
  @override
  Future<ApiResult<num>> getAppliedInterest(String outletId) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.getAppliedInterest(outletId),
    );
  }

  @override
  Future<ApiResult<VerifyNinResponse>> verifyNin(NinParams params) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.verifyNin(params),
    );
  }

  @override
  Future<ApiResult<Map<PreferenceType, List<Preference>>>> getPreferences(
      GetPreferencesParams params) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.getPreferences(params),
    );
  }

  @override
  Future<ApiResult<Map<String, dynamic>?>> savePreferences(
      SavePreferencesParams params) async {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.savePreferences(params),
    );
  }

  @override
  Future<ApiResult<InitiateJobResponse>> initiateJob(
      InitiateJobRequest request) {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.initiateJob(request));
  }

  @override
  Future<ApiResult<InitiateStripeResponse>> initiateStripeVerification(
      InitiateStripeRequest request) {
    final res = dioInterceptor(() =>
        _authenticationRemoteDataSource.initiateStripeVerification(request));

    return res;
  }

  @override
  Future<ApiResult<CompanySearchData>> companySearch(String query) async {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.companySearch(query));
  }

  @override
  Future<ApiResult<String>> verifyCompany(String companyId) async {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.verifyCompany(companyId));
  }

  @override
  Future<ApiResult<StripeStatusResponse>> stripeStatus() {
    return dioInterceptor(() => _authenticationRemoteDataSource.stripeStatus());
  }

  @override
  Future<ApiResult> validateJob(String jobId) {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.validateJob(jobId),
    );
  }

  @override
  Future<ApiResult<CurrentJob>> getCurrentJob() {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.getCurrentJob());
  }

  @override
  Future<ApiResult<Map<String, dynamic>?>> updateEmail(
      UpdateEmailParams params) {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.updateEmail(params));
  }

  @override
  Future<ApiResult<String>> addRefereeCode(String code) async {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.addRefereeCode(code));
  }

  @override
  Future<ApiResult<Referee?>> getReferee() async {
    if (await networkConnection.isDeviceConnected) {
      try {
        final result = await _authenticationRemoteDataSource.getReferee();
        return ApiResult.success(data: result);
      } on DioError catch (error) {
        if (error.response?.statusCode != null &&
            error.response!.statusCode == 422) {
          return const ApiResult.success(data: null);
        }
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(error));
      } catch (exception, s) {
        ErrorHandler.report(exception, s);
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      }
    } else {
      return const ApiResult.apiFailure(
          error: ApiExceptions.noInternetConnection());
    }
    // return dioInterceptor(() => _authenticationRemoteDataSource.getReferee());
  }

  @override
  Future<ApiResult<TdAddress>> validateAddress(AddressParams params) {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.validateAddress(params));
  }

  @override
  Future<ApiResult<Object>> getAddresses(AddressParams params) {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.getAddresses(params));
  }

  @override
  Future<ApiResult<bool>> deleteAccount() {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.deleteAccount());
  }

  @override
  Future<ApiResult<Map<String, dynamic>?>> sendOTP(SendOTParams params) async {
    return dioInterceptor(
        () => _authenticationRemoteDataSource.sendOTP(params));
  }

  @override
  Future<ApiResult<VerifiedOTP>> verifyOTP(VerifyOTParams params) async {
    if (await networkConnection.isDeviceConnected) {
      try {
        final data = await _authenticationRemoteDataSource.verifyOTP(params);
        return ApiResult.success(data: data);
      } on DioError catch (e, s) {
        ErrorHandler.report(e, s);
        if (e.response?.statusCode != null && e.response!.statusCode == 401) {
          final String? msg = e.response?.data['res'];
          return ApiResult.apiFailure(
            error:
                ApiExceptions.defaultError(msg ?? 'INVALID OR EXPIRED TOKEN'),
          );
        }
        return ApiResult.apiFailure(
          error: ApiExceptions.getDioException(e),
        );
      } catch (e, s) {
        ErrorHandler.report(e, s);
        return ApiResult.apiFailure(
          error: ApiExceptions.getDioException(e),
        );
      }
    } else {
      return const ApiResult.apiFailure(
          error: ApiExceptions.noInternetConnection());
    }
  }

  @override
  Future<ApiResult<Map<String, dynamic>>> addPhone(String phoneNumber) {
    return dioInterceptor(
      () => _authenticationRemoteDataSource.addPhone(phoneNumber),
      doNotReport: [401],
      logoutOn401: false,
      customErrorHandler: (DioError error) {
        if (error.response?.statusCode != null &&
            error.response!.statusCode == 401) {
          return ApiExceptions.defaultError(
              error.response!.data['data']['message']);
        }

        return ApiExceptions.getDioException(error);
      },
    );
  }
}
