import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/authentication/data/models/account_type.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/domain/params/address_params.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/homepage/data/models/referee.dart';
import 'package:td_commons_flutter/models/index.dart';

import '../models/preference.dart';
import 'impl/authentication_remote_data_source_impl.dart';

/// Base class for [AuthenticationRemoteDataSourceImpl].
abstract class AuthenticationRemoteDataSource {
  Future<User> signUp(SignUpParams params);

  Future<User> loginWithOtp(LoginTokenParams params);

  Future<LoginResponse> loginWithPin(LoginPinParams params);

  Future<Map<String, dynamic>?> createPin(CreatePinParams params);

  Future<Map<String, dynamic>?> resetPin(ResetPinParams params);

  Future<WalletAccount> reserveAccount(ReserveAccountParams params);

  Future<Map<String, dynamic>?> createAccount(AccountType params);

  Future<UserStatus> checkPhone(CheckPhoneParams params);

  Future<RetailOutlet?> fetchOutlet(FetchOutletParams params);

  Future<Map<String, dynamic>?> verifyPin(VerifyPinParams params);

  Future<num> getAppliedInterest(String outletId);

  Future<VerifyNinResponse> verifyNin(NinParams params);

  Future<InitiateJobResponse> initiateJob(InitiateJobRequest request);

  Future<InitiateStripeResponse> initiateStripeVerification(
      InitiateStripeRequest request);

  Future<StripeStatusResponse> stripeStatus();

  Future<CompanySearchData> companySearch(String query);

  Future<String> verifyCompany(String companyId);

  Future<dynamic> validateJob(String jobId);

  Future<CurrentJob> getCurrentJob();

  Future<Map<PreferenceType, List<Preference>>> getPreferences(
      GetPreferencesParams params);

  Future<Map<String, dynamic>?> savePreferences(SavePreferencesParams params);

  Future<Map<String, dynamic>?> updateEmail(UpdateEmailParams params);

  Future<String> addRefereeCode(String code);

  Future<Referee?> getReferee();
  Future<TdAddress> validateAddress(AddressParams params);
  Future<Object> getAddresses(AddressParams params);

  Future<bool> deleteAccount();

  Future<Map<String, dynamic>?> sendOTP(SendOTParams params);
  Future<VerifiedOTP> verifyOTP(VerifyOTParams params);

  Future<Map<String, dynamic>> addPhone(String phoneNumber);
}
