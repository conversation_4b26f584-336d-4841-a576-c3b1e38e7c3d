import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/foundation.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/authentication/data/data_source/authentication_remote_data_source.dart';
import 'package:shop/app/authentication/data/models/account_type.dart';
import 'package:shop/app/authentication/data/models/address_find.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/preference.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/domain/params/address_params.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/homepage/data/models/referee.dart';
import 'package:shop/app_config.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/models/wallet_account.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Implements [AuthenticationRemoteDataSource] abstract class.
///
/// Makes network call.
class AuthenticationRemoteDataSourceImpl
    implements AuthenticationRemoteDataSource {
  static const String _loginTokenPath = 'shop/v4/loginWithToken';
  static const String _loginPinPath = 'shop/v4/loginWithPin';
  static const String _checkPhonePath = 'shop/v4/getPhoneStatus';
  static const String _signUpPath = 'shop/v2/signup';
  static const String _savePinPath = 'shop/v4/savePin';
  static const String _resetPinPath = 'shop/v2/resetUserPin';
  static const String _reserveAccountPath = 'shop/v4/reserveAccount';
  // static const String _createAccountPath = 'shop/v4/createWalletAccount';
  static const String _createAccountPath = 'v3/retail/accounts';
  static const String _verifyPinPath = 'shop/v4/verifyPin';
  static const String _getOutletPath = 'shop/v4/getRetailOutlet';
  static const String _appliedInterestPath = 'shop/v4/getAppliedInterest';
  static const String _verifyNinPath = 'v3/retail/verify/nin';
  static const String _savePreferencesPath = 'save-preferences';
  static const String _userPreferencesPath = 'get-preferences';
  static const String _collectionPreferencesPath = 'shop/v5/getCategories';
  static const String _updateEmailPath = 'v3/retail/accounts/update-email';
  static const String _addRefereeCodePath = 'v3/retail/referral-code';
  static const String _getRefereePath = 'v3/retail/referee-details';
  static const String _getAddressesPath = "get-addresses";

  // static const String _collectionPreferencesPath = 'get-product-collections';

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request
  final TdApiClient _apiClient;

  /// API base url
  final String _firebaseServiceUrl;

  const AuthenticationRemoteDataSourceImpl(
    this._apiClient,
    this._firebaseServiceUrl,
  );

  /// Login user with OTP(One Time Password).
  @override
  Future<User> loginWithOtp(LoginTokenParams params) async {
    String url = '$_firebaseServiceUrl/$_loginTokenPath';
    final res = await _apiClient.post(
      url,
      data: params.toMap(),
    );

    return User.fromMap(res.data);
  }

  /// Login user with pin.
  @override
  Future<LoginResponse> loginWithPin(LoginPinParams params) async {
    String url = '$_firebaseServiceUrl/$_loginPinPath';
    final res = await _apiClient.post(
      url,
      data: params.toMap(),
    );
    final user = User.fromMap(res.data);
    return LoginResponse(type: LoginResponseType.user, user: user);
  }

  /// Checks if [phoneNumber || email] has a valid account.
  ///
  /// Returns account info as [UserStatus].
  @override
  Future<UserStatus> checkPhone(CheckPhoneParams params) async {
    try {
      final res = await _apiClient.get(
        '$_firebaseServiceUrl/$_checkPhonePath',
        queryParameters: params.toMap(),
      );
      return UserStatus.fromMap(res.data['data']['data']);
    } on DioError catch (e) {
      if (e.response?.statusCode == 404) {
        return UserStatus.from404(e.response!.data['error']['data']);
      }
      rethrow;
    }
  }

  /// SignUp a new user.
  @override
  Future<User> signUp(SignUpParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/$_signUpPath',
      data: params.toMap(),
    );

    return User.fromMap(res.data['data']['details']['account']);
  }

  /// Creates a new pin for a user.
  @override
  Future<Map<String, dynamic>?> createPin(CreatePinParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/$_savePinPath',
      data: params.toMap(),
    );

    return res.data;
  }

  /// Resets a user pin
  @override
  Future<Map<String, dynamic>?> resetPin(ResetPinParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/$_resetPinPath',
      data: params.toMap(),
    );

    return res.data;
  }

  /// Creates [WalletAccount] for a retail oulet.
  ///
  /// Only if [WalletAccount] is null or [WalletAccount.secAccountNumber] is null.
  @override
  Future<WalletAccount> reserveAccount(ReserveAccountParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/$_reserveAccountPath',
      data: params.toMap(),
    );
    return WalletAccount.fromMap(res.data);
  }

  /// Creates a secondary [Account] for a retail outlet.
  ///
  /// Either [Wallet] or [Collections] account is created based on the [AccountType] provided.
  @override
  Future<Map<String, dynamic>?> createAccount(AccountType params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'path': _createAccountPath,
        'data': params.toMap(),
      },
    );

    return res.data?['data']?['data'];
  }

  /// Returns a retail outlet latest value.
  @override
  Future<RetailOutlet?> fetchOutlet(FetchOutletParams params) async {
    if (params.outletId == null && params.phoneNumber == null) return null;

    try {
      final res = await _apiClient.get('$_firebaseServiceUrl/$_getOutletPath',
          queryParameters: params.toMap());
      final outletMap = res.data;

      return RetailOutlet.fromMap((outletMap));
    } catch (e, s) {
      ErrorHandler.report(e, s);
      return null;
    }
  }

  /// Validate user inputted pin.
  @override
  Future<Map<String, dynamic>?> verifyPin(VerifyPinParams params) async {
    final res = await _apiClient.post('$_firebaseServiceUrl/$_verifyPinPath',
        data: params.toMap());
    return res.data;
  }

  @override
  Future<num> getAppliedInterest(String outletId) async {
    final res = await _apiClient
        .get('$_firebaseServiceUrl/$_appliedInterestPath?outletId=$outletId');
    return res.data['appliedInterest'];
  }

  @override
  Future<VerifyNinResponse> verifyNin(NinParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'type': 'app',
        'method': 'post',
        'path': _verifyNinPath,
        'data': params.toMap(),
      },
    );
    final response = res.data['data'];
    return VerifyNinResponse.fromMap(response);
  }

  @override
  Future<Map<PreferenceType, List<Preference>>> getPreferences(
      GetPreferencesParams params) async {
    final Map<PreferenceType, List<Preference>> result = {};

    if (params.type == PreferenceType.user) {
      late Response<dynamic> userPreferences;
      late Response<dynamic> collectionPreferences;

      await Future.wait<void>(
        [
          (() async {
            return userPreferences = await _apiClient.post(
              '$_firebaseServiceUrl/shop/v3/proxy',
              data: {
                'method': 'get',
                'type': 'claimsUrl',
                'path': _userPreferencesPath,
              },
            );
          })(),
          (() async {
            return collectionPreferences = await _apiClient.get(
              '$_firebaseServiceUrl/$_collectionPreferencesPath?plusCode6Hex=${Uri.encodeComponent('${params.hexCode}')}',
            );
          })(),
        ],
        eagerError: true,
      );

      final userResponse = userPreferences.data['data'];

      final List<dynamic> userData =
          userResponse != null ? userResponse['shopPreferences'] : [];
      final List<dynamic> collectionData =
          collectionPreferences.data['collectionData'];

      result[PreferenceType.user] =
          userData.map((e) => Preference.fromMap(e)).toList();
      result[PreferenceType.collection] =
          collectionData.map((e) => Preference.fromMap(e)).toList();
    } else if (params.type == PreferenceType.collection) {
      final response = await _apiClient.get(
        '$_firebaseServiceUrl/$_collectionPreferencesPath?plusCode6Hex=${Uri.encodeComponent('${params.hexCode}')}',
      );

      final List<dynamic> data =
          response.data['collection'] ?? response.data['collectionData'] ?? [];
      result[PreferenceType.collection] =
          data.map((e) => Preference.fromMap(e)).toList();
      result[PreferenceType.user] = [];
    } else {
      throw Exception('Unrecognized PreferencesType: ${params.type}');
    }

    return result;
  }

  @override
  Future<Map<String, dynamic>?> savePreferences(
      SavePreferencesParams params) async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'type': 'claimsUrl',
        'path': _savePreferencesPath,
        'data': params.toMap(),
      },
    );

    return response.data;
  }

  @override
  Future<InitiateJobResponse> initiateJob(InitiateJobRequest request) async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "/initiate-job",
        "method": "POST",
        "data": request.toJson(),
        "type": "serviceUrl"
      },
    );
    return InitiateJobResponse.fromJson(response.data);
  }

  @override
  Future<InitiateStripeResponse> initiateStripeVerification(
      InitiateStripeRequest request) async {
    final response = await _apiClient.post(
      '${config.businessFilesUploadUrl}/create-verification-session',
      data: request.toJson(),
    );

    return InitiateStripeResponse.fromJson(response.data['data']);
  }

  @override
  Future<CompanySearchData> companySearch(String query) async {
    final response = await _apiClient.get(
      '${config.businessFilesUploadUrl}/gb-company-search?q=$query',
    );
    return CompanySearchData.fromJson(response.data);
  }

  @override
  Future<String> verifyCompany(String companyId) async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'POST',
        'path': "verify-business",
        "type": "serviceUrl",
        "data": {'companyNo': companyId}
      },
    );
    return response.data['data']['message'];
  }

  @override
  Future<StripeStatusResponse> stripeStatus() async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'GET',
        'path': "gb-company-status",
        "type": "serviceUrl"
      },
    );
    return StripeStatusResponse.fromJson(response.data['data']);
  }

  @override
  Future validateJob(String jobId) async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": "/validate-job",
        "method": "POST",
        "data": {"job_id": jobId},
        "type": "serviceUrl"
      },
    );
    return response.data;
  }

  @override
  Future<CurrentJob> getCurrentJob() async {
    final response = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {"path": "current-job", "method": "GET", "type": "serviceUrl"},
    );
    return CurrentJob.fromJson(response.data['data']);
  }

  @override
  Future<Map<String, dynamic>?> updateEmail(UpdateEmailParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": _updateEmailPath,
        "method": "POST",
        'data': params.toMap(),
      },
    );

    return res.data;
  }

  @override
  Future<String> addRefereeCode(String code) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": _addRefereeCodePath,
        'type': 'app',
        "method": "POST",
        'data': {
          'referralCode': code,
        },
      },
    );
    return res.data['data']['message'];
  }

  @override
  Future<Referee?> getReferee() async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": _getRefereePath,
        'type': 'app',
        "method": "GET",
      },
    );

    return Referee.fromMap(res.data['data']);
  }

  @override
  Future<Object> getAddresses(AddressParams params) async {
    final res = auth.FirebaseAuth.instance.currentUser == null
        ? await _apiClient.get('$_firebaseServiceUrl/shop/v5/getAddress',
            queryParameters: params.toAutoSearchMap())
        : await _apiClient.post(
            '$_firebaseServiceUrl/shop/v3/proxy',
            data: {
              "path": _getAddressesPath,
              "params": params.toAutoSearchMap(),
              'type': 'serviceUrl',
              "method": "GET",
            },
          );
    if (res.data['data'] is! List) {
      return TdAddress.addressRetrieve(res.data['data']);
    }
    String? type = ((res.data['data'] as List).firstOrNull)?["Type"];
    switch (type) {
      case "Container" || "Address":
        return (res.data['data'] as List)
            .map((e) => AddressFind.fromMap(e))
            .toList();
      default:
        return [];
    }
  }

  @override
  Future<TdAddress> validateAddress(AddressParams params) async {
    final dio = Dio();
    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        responseBody: true,
        error: true,
        requestHeader: true,
        responseHeader: true,
        request: true,
        requestBody: true,
      ));
    }
    final res = await dio.post(
        "https://addressvalidation.googleapis.com/v1:validateAddress",
        queryParameters: {"key": config.googleMapsApiKey},
        data: params.toMap());
    return TdAddress.fromValidationResult(res.data["result"]);
  }

  @override
  Future<bool> deleteAccount() async {
    await _apiClient.post('$_firebaseServiceUrl/shop/v4/deleteAccount');
    return true;
  }

  @override
  Future<Map<String, dynamic>?> sendOTP(SendOTParams params) async {
    final res = await _apiClient.post(
      params.url,
      data: params.toMap(),
    );
    return res.data;
  }

  @override
  Future<VerifiedOTP> verifyOTP(VerifyOTParams params) async {
    final res = await _apiClient.post(
      params.url,
      data: params.toMap(),
    );
    return VerifiedOTP.fromMap(res.data['res']);
  }

  @override
  Future<Map<String, dynamic>> addPhone(String phoneNumber) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        "path": 'v4/procurement/update-outlet-phone',
        'type': 'app',
        "method": "POST",
        "data": {
          "phoneNumber": phoneNumber,
        }
      },
    );

    return res.data;
  }
}

String encodeMap(Map data) {
  return data.keys
      .map((key) =>
          "${Uri.encodeComponent(key)}=${Uri.encodeComponent(data[key]?.toString() ?? '')}")
      .join("&");
}
