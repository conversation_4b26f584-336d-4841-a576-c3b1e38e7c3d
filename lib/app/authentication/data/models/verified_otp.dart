class VerifiedOTP {
  VerifiedOTP._({
    required this.expiresAt,
    required this.phoneNumber,
    required this.token,
    required this.countryCode,
    required this.accessToken,
  });

  factory VerifiedOTP.fromMap(Map<String, dynamic> data) {
    return VerifiedOTP._(
      expiresAt: DateTime.fromMillisecondsSinceEpoch(data['expiresAt']),
      phoneNumber: data['msisdn'] ?? '',
      token: data['token'] ?? '',
      countryCode: data['countryCode'] ?? '',
      accessToken: data['accessToken'] ?? '',
    );
  }

  final DateTime expiresAt;
  final String phoneNumber;
  final String token;
  final String countryCode;
  final String accessToken;
}
