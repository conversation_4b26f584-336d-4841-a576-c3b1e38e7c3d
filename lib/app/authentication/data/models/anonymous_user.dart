import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:td_commons_flutter/models/currency.dart';

class AnonymousUser extends Equatable {
  final String id;
  final String? hexCode;
  final TdAddress address;
  final Currency? currency;
  const AnonymousUser({
    required this.id,
    this.hexCode,
    required this.address,
    this.currency,
  });

  AnonymousUser copyWith({
    String? id,
    String? hexCode,
    TdAddress? address,
    Currency? currency,
  }) {
    return AnonymousUser(
      id: id ?? this.id,
      hexCode: hexCode ?? this.hexCode,
      address: address ?? this.address,
      currency: currency ?? this.currency,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'hexCode': hexCode,
      'address': address.toMap(),
      'currency': currency?.toMap(),
    };
  }

  factory AnonymousUser.fromMap(Map<String, dynamic> map) {
    return AnonymousUser(
      id: map['id'] ?? '',
      hexCode: map['hexCode'],
      address: TdAddress.fromMap(map['address']),
      currency:
          map['currency'] != null ? Currency.fromMap(map['currency']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory AnonymousUser.fromJson(String source) =>
      AnonymousUser.fromMap(json.decode(source));

  double get longitude => address.longitude!;
  double get latitude => address.latitude!;

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [id, hexCode, address];
}
