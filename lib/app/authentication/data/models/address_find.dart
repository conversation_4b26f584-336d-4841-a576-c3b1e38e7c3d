class AddressFind {
  final String id;
  final String text;
  final String? description;
  final AddressFindType type;

  AddressFind(this.id, this.text, this.description, this.type);

  factory AddressFind.fromMap(Map<String, dynamic> data) {
    return AddressFind(
      data['Id'],
       data['Text'],
       data['Description'],
       AddressFindType.values.byName(data['Type'])
    );
  }

  @override
  toString(){
    return description?.isEmpty ?? true ? text : "$text\n${description?.substring(3,description?.length)}";
  }
}

enum AddressFindType {Container, Address, other}