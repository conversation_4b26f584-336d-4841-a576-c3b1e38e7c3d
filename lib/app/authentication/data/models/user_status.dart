import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/app_host.dart';

class UserStatus extends Equatable {
  final String? businessName;
  final String? userId;
  final bool? pinEnabled;
  final bool hasRetailStore;
  final bool hasUser;
  final bool migrated;
  final String? retailOutletId;
  final AppHost domain;
  final bool emailMerged;
  final String? redactedEmail;

  const UserStatus({
    required this.businessName,
    required this.pinEnabled,
    required this.hasRetailStore,
    required this.hasUser,
    required this.userId,
    required this.migrated,
    required this.retailOutletId,
    required this.domain,
    required this.emailMerged,
    this.redactedEmail,
  });

  factory UserStatus.fromMap(Map<String, dynamic> data) {
    final retailOutlet = data['retailOutlet'];
    return UserStatus(
      businessName: retailOutlet['outletBusinessName'],
      retailOutletId: retailOutlet['_id'],
      pinEnabled: data['pinEnabled'],
      userId: data['userId'],
      hasRetailStore: data['hasRetailStore'] ?? false,
      hasUser: data['hasUser'] ?? false,
      migrated: data['migrated'] ?? false,
      domain: AppHost.fromString(retailOutlet?['domain'] ?? ''),
      emailMerged: data['emailMerged'] ?? false,
      redactedEmail: data['redactedEmail'],
    );
  }

  factory UserStatus.from404(Map<String, dynamic> data) {
    return UserStatus(
      businessName: '',
      pinEnabled: false,
      hasRetailStore: data['hasRetailStore'] ?? false,
      hasUser: data['hasUser'] ?? false,
      userId: null,
      migrated: data['migrated'] ?? false,
      retailOutletId: '',
      domain: AppHost.fromString(data['domain'] ?? ''),
      emailMerged: data['emailMerged'] ?? false,
      redactedEmail: data['redactedEmail'],
    );
  }

  @override
  List<Object?> get props => [
        businessName,
        pinEnabled,
        hasRetailStore,
        migrated,
        userId,
        domain,
        emailMerged,
        redactedEmail
      ];
}
