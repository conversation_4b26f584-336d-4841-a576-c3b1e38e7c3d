class InitiateJobResponse {
  InitiateJobResponse({
    required this.status,
    required this.errorCode,
    required this.data,
  });
  late final String? status;
  late final int? errorCode;
  late final Data? data;

  InitiateJobResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    errorCode = json['errorCode'];

    data = Data.fromJson(json['data'] ?? {});
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['status'] = status;
    jsonMap['errorCode'] = errorCode;
    jsonMap['data'] = data?.toJson();
    return jsonMap;
  }
}

class InitiateJobRequest {
  late final Field? field;
  late final String? jobtype;
  late final String? idNumber;

  InitiateJobRequest({this.field, this.idNumber, this.jobtype});

  InitiateJobRequest.fromJson(Map<String, dynamic> json) {
    field = json['field'];
    jobtype = json['jobType'];
    idNumber = json['idNumber'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    if (field != null) {
      jsonMap['fields'] = field!.toJson();
    }

    if (idNumber != null) {
      jsonMap['idNumber'] = idNumber;
    }
    jsonMap['job_type'] = jobtype;

    return jsonMap;
  }
}

class Data {
  Data({
    this.message,
  });
  late final String? message;
  late final dynamic jobId;
  late final String? jobType;
  late final bool? enhancedKyc;
  late final String? token;

  Data.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    jobId = json['job_id'];
    jobType = json['job_type'];
    enhancedKyc = json['enhancedKyc'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['message'] = message;
    jsonMap['job_id'] = jobId;
    jsonMap['job_type'] = jobType;
    jsonMap['enhancedKyc'] = enhancedKyc;
    jsonMap['token'] = token;

    return jsonMap;
  }
}

class Field {
  late final String? firstName;
  late final String? lastName;
  late final String? dob;

  Field({this.firstName, this.lastName, this.dob});

  Field.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'];
    lastName = json['lastName'];
    dob = json['dob'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['firstName'] = firstName;
    jsonMap['lastName'] = lastName;
    jsonMap['dob'] = dob;

    return jsonMap;
  }
}

class InitiateStripeRequest {
  late final String? redirectUrl;
  late final Director? director;

  InitiateStripeRequest({this.redirectUrl, this.director});

  InitiateStripeRequest.fromJson(Map<String, dynamic> json) {
    redirectUrl = json['redirectUrl'];
    director = Director.fromJson(json['director']);
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['redirectUrl'] = redirectUrl;
    if (director != null) {
      jsonMap['director'] = director?.toJson();
    }

    return jsonMap;
  }
}

class InitiateStripeResponse {
  late final String? verificationUrl;
  late final String? redirectUrl;

  InitiateStripeResponse({this.verificationUrl});

  InitiateStripeResponse.fromJson(Map<String, dynamic> json) {
    verificationUrl = json['verificationUrl'];
    redirectUrl = json['redirectUrl'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};

    jsonMap['verificationUrl'] = verificationUrl;

    return jsonMap;
  }
}

class StripeStatusResponse {
  late final String? companyVerification;
  late final String? identityVerification;
  late final int? identityErrorCode;
  late final String? identityRejectionReason;
  late final int? companyErrorCode;
  late final String? companyRejectionReason;
  late final Company? company;
  late final LastVerificationSession? lastVerificationSession;
  late final VerificationSession? verificationSession;

  StripeStatusResponse(
      {this.identityVerification,
      this.companyVerification,
      this.lastVerificationSession,
      this.companyErrorCode,
      this.identityErrorCode,
      this.companyRejectionReason,
      this.identityRejectionReason,
      this.verificationSession,
      this.company});

  StripeStatusResponse.fromJson(Map<String, dynamic> json) {
    companyVerification = json['companyVerification'];
    identityVerification = json['identityVerification'];
    companyErrorCode = json['companyErrorCode'];
    identityErrorCode = json['identityErrorCode'];
    companyRejectionReason = json['companyRejectionReason'];
    identityRejectionReason = json['identityRejectionReason'];
    verificationSession =
        VerificationSession.fromJson(json['verificationSession'] ?? {});
    lastVerificationSession = json['lastVerificationSession'] != null
        ? LastVerificationSession.fromJson(json['lastVerificationSession'])
        : null;
    company =
        json['company'] != null ? Company.fromJson(json['company']) : null;
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    // if (redirectUrl != null) {
    jsonMap['companyVerification;'] = companyVerification;
    jsonMap['identityVerification'] = identityVerification;
    jsonMap['lastVerificationSession'] = lastVerificationSession;
    jsonMap['verificationSession'] = verificationSession;
    jsonMap['companyErrorCode'] = companyErrorCode;
    jsonMap['identityErrorCode'] = identityErrorCode;
    jsonMap['companyRejectionReason'] = companyRejectionReason;
    jsonMap['identityRejectionReason'] = identityRejectionReason;
    jsonMap['company'] = company?.toJson();
    //  }

    return jsonMap;
  }
}

class LastVerificationSession {
  late final String? status;
  late final Director? director;
  LastVerificationSession({this.status, this.director});

  LastVerificationSession.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    director =
        json['director'] != null ? Director.fromJson(json['director']) : null;
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};

    jsonMap['status'] = status;
    jsonMap['director'] = director?.toJson();

    return jsonMap;
  }
}

class Director {
  Director({
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
  });
  late final String firstName;
  late final String lastName;
  late final DateOfBirth dateOfBirth;

  Director.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'];
    lastName = json['lastName'];
    dateOfBirth = DateOfBirth.fromJson(json['dateOfBirth']);
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['firstName'] = firstName;
    jsonMap['lastName'] = lastName;
    jsonMap['dateOfBirth'] = dateOfBirth.toJson();
    return jsonMap;
  }
}

class Company {
  late final String? companyName;
  late final String? companyNumber;

  Company({this.companyName, this.companyNumber});

  Company.fromJson(Map<String, dynamic> json) {
    companyName = json['companyName'];
    companyNumber = json['companyNumber'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};

    jsonMap['companyName'] = companyName;
    jsonMap['CompanyNumber'] = companyNumber;

    return jsonMap;
  }
}

class DateOfBirth {
  DateOfBirth({
    required this.day,
    required this.month,
    required this.year,
  });
  late final int day;
  late final int month;
  late final int year;

  DateOfBirth.fromJson(Map<String, dynamic> json) {
    day = json['day'];
    month = json['month'];
    year = json['year'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['day'] = day;
    jsonMap['month'] = month;
    jsonMap['year'] = year;
    return jsonMap;
  }
}

class VerificationSession {
  VerificationSession({
    required this.kyb,
    required this.kyc,
  });
  late final Kyb kyb;
  late final Kyc kyc;

  VerificationSession.fromJson(Map<String, dynamic> json) {
    kyb = Kyb.fromJson(json['kyb'] ?? {});
    kyc = Kyc.fromJson(json['kyc'] ?? {});
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['kyb'] = kyb.toJson();
    jsonMap['kyc'] = kyc.toJson();

    return jsonMap;
  }
}

class Kyb {
  Kyb({required this.url, this.stripeStatus});
  late final String? url;
  late final String? stripeStatus;

  Kyb.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    stripeStatus = json['stripeStatus'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['url'] = url;
    jsonMap['stripeStatus'] = stripeStatus;
    return jsonMap;
  }
}

class Kyc {
  Kyc({required this.url, this.stripeStatus});
  late final String? url;
  late final String? stripeStatus;

  Kyc.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    stripeStatus = json['stripeStatus'];
  }

  Map<String, dynamic> toJson() {
    final jsonMap = <String, dynamic>{};
    jsonMap['url'] = url;
    jsonMap['stripeStatus'] = stripeStatus;
    return jsonMap;
  }
}
