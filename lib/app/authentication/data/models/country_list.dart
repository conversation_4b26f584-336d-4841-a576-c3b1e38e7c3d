const countryList = [
  {
    "num_code": "566",
    "alpha_2_code": "NG",
    "alpha_3_code": "NGA",
    "en_short_name": "Nigeria",
    "nationality": "Nigerian",
    "dial_code": "+234",
    "hint": "8031235678",
  },
  {
    "num_code": "710",
    "alpha_2_code": "ZA",
    "alpha_3_code": "ZAF",
    "en_short_name": "South Africa",
    "nationality": "South African",
    "dial_code": "+27",
    "hint": "832223333",
  },
  {
    "num_code": "288",
    "alpha_2_code": "GH",
    "alpha_3_code": "GHA",
    "en_short_name": "Ghana",
    "nationality": "Ghanaian",
    "dial_code": "+233",
    "hint": "244345678",
  },
];

const countriesWithPhoneCode = [
  {
    'countryName': 'United Kingdom',
    'countryCode': 'GB',
    'phoneCode': '44',
    'exampleNumberMobileNational': '07400 123456',
    'exampleNumberFixedLineNational': '0************',
    'phoneMaskMobileNational': '00000 000000',
    'phoneMaskFixedLineNational': '0************',
    'exampleNumberMobileInternational': '+44 7400 123456',
    'exampleNumberFixedLineInternational': '+44 ************',
    'phoneMaskMobileInternational': '+00 0000 000000',
    'phoneMaskFixedLineInternational': '+00 ************',
  },
  {
    'countryName': 'Nigeria',
    'countryCode': 'NG',
    'phoneCode': '234',
    'exampleNumberMobileNational': '0************',
    'exampleNumberFixedLineNational': '01 804 0123',
    'phoneMaskMobileNational': '0************',
    'phoneMaskFixedLineNational': '00 000 0000',
    'exampleNumberMobileInternational': '+234 ************',
    'exampleNumberFixedLineInternational': '+234 1 804 0123',
    'phoneMaskMobileInternational': '+000 ************',
    'phoneMaskFixedLineInternational': '+000 0 000 0000',
  },
  {
    'countryName': 'South Africa',
    'countryCode': 'ZA',
    'phoneCode': '27',
    'exampleNumberMobileNational': '************',
    'exampleNumberFixedLineNational': '************',
    'phoneMaskMobileNational': '************',
    'phoneMaskFixedLineNational': '************',
    'exampleNumberMobileInternational': '+27 71 123 4567',
    'exampleNumberFixedLineInternational': '+27 10 123 4567',
    'phoneMaskMobileInternational': '+00 00 000 0000',
    'phoneMaskFixedLineInternational': '+00 00 000 0000',
  },
  {
    'countryName': 'Ghana',
    'countryCode': 'GH',
    'phoneCode': '233',
    'exampleNumberMobileNational': '************',
    'exampleNumberFixedLineNational': '************',
    'phoneMaskMobileNational': '************',
    'phoneMaskFixedLineNational': '************',
    'exampleNumberMobileInternational': '+233 23 123 4567',
    'exampleNumberFixedLineInternational': '+233 30 234 5678',
    'phoneMaskMobileInternational': '+000 00 000 0000',
    'phoneMaskFixedLineInternational': '+000 00 000 0000',
  },
];
