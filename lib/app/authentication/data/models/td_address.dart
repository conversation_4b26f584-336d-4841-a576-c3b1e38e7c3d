import 'package:google_maps_webservice/geocoding.dart';

class TdAddress {
  final String? address;
  final String? city;
  final String? lga;
  final String? state;
  final String? countryCode;
  final double? latitude;
  final double? longitude;
  final String? postal;

  TdAddress({
    this.address,
    this.city,
    this.lga,
    this.state,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.postal,
  });

  factory TdAddress.fromGeocode(GeocodingResult result) {
    final data = <String, String>{};

    for (var element in result.addressComponents) {
      if (element.types.contains('neighborhood')) {
        data['city'] = element.longName;
        continue;
      }
      if (element.types.contains('locality')) {
        data['state'] = element.longName;
        continue;
      }

      if (element.types.contains('administrative_area_level_2')) {
        data['lga'] = element.longName;
        continue;
      }

      if (element.types.contains('country')) {
        data['countryCode'] = element.shortName;
        continue;
      }

      if (element.types.contains('postal_code')) {
        data['postal'] = element.longName;
        continue;
      }
    }

    final extractedState = data['state'] ?? data['countryCode'];

    return TdAddress(
      address: result.formattedAddress,
      city: data['city'] ?? extractedState,
      state: data['state'] ?? extractedState,
      lga: data['lga'] ?? extractedState,
      countryCode: data['countryCode'],
      postal: data['postal'],
      latitude: result.geometry.location.lat,
      longitude: result.geometry.location.lng,
    );
  }

  // factory TdAddress.fromPickResult(PickResult result) {
  //   final data = <String, String>{};

  //   result.addressComponents?.forEach((element) {
  //     if (element.types.contains('neighborhood')) {
  //       data['city'] = element.longName;
  //       return;
  //     }
  //     if (element.types.contains('locality')) {
  //       data['state'] = element.longName;
  //       return;
  //     }

  //     if (element.types.contains('administrative_area_level_2')) {
  //       data['lga'] = element.longName;
  //       return;
  //     }

  //     if (element.types.contains('country')) {
  //       data['countryCode'] = element.shortName;
  //       return;
  //     }

  //     if (element.types.contains('postal_code')) {
  //       data['postal'] = element.longName;
  //       return;
  //     }
  //   });

  //   final extractedState = data['state'] ?? data['countryCode'];

  //   return TdAddress(
  //     address: result.formattedAddress,
  //     city: data['city'] ?? extractedState,
  //     state: data['state'] ?? extractedState,
  //     lga: data['lga'] ?? extractedState,
  //     countryCode: data['countryCode'],
  //     postal: data['postal'],
  //     latitude: result.geometry?.location.lat,
  //     longitude: result.geometry?.location.lng,
  //   );
  // }

  // factory TdAddress.fromPlace(Place result) {
  //   final data = <String, String>{};

  //   result.addressComponents?.forEach((element) {
  //     if (element.types.contains('neighborhood')) {
  //       data['city'] = element.name;
  //       return;
  //     }
  //     if (element.types.contains('locality')) {
  //       data['state'] = element.name;
  //       return;
  //     }

  //     if (element.types.contains('administrative_area_level_2')) {
  //       data['lga'] = element.name;
  //       return;
  //     }

  //     if (element.types.contains('country')) {
  //       data['countryCode'] = element.name;
  //       return;
  //     }

  //     if (element.types.contains('postal_code')) {
  //       data['postal'] = element.name;
  //       return;
  //     }
  //   });

  //   final extractedState = data['state'] ?? data['countryCode'];

  //   return TdAddress(
  //     address: result.address,
  //     city: data['city'] ?? extractedState,
  //     state: data['state'] ?? extractedState,
  //     lga: data['lga'] ?? extractedState,
  //     countryCode: data['countryCode'],
  //     postal: data['postal'],
  //     latitude: result.latLng?.lat,
  //     longitude: result.latLng?.lng,
  //   );
  // }

  factory TdAddress.fromValidationResult(Map<String, dynamic> map) {
    final addressLine = map['address']?["postalAddress"]?['addressLines'];
    final unConfirmedComponents =
        map["address"]?["unconfirmedComponentTypes"] as List?;
    final missingComponents = map["address"]?["missingComponentTypes"] as List?;
    return TdAddress(
        address: ((unConfirmedComponents?.contains("street_address") ??
                    false) ||
                (missingComponents?.contains("street_address") ?? false) ||
                /*(unConfirmedComponents?.contains("subpremise") ?? false) ||
                (missingComponents?.contains("subpremise") ?? false) ||*/
                (unConfirmedComponents?.contains("street_number") ?? false) ||
                (missingComponents?.contains("street_number") ?? false) ||
                (unConfirmedComponents?.contains("premise") ?? false) ||
                (missingComponents?.contains("premise") ?? false))
            ? null
            : map['address']["formattedAddress"] ??
                ((addressLine is List)
                    ? addressLine.first
                    : addressLine.toString()),
        city: ((unConfirmedComponents?.contains("locality") ?? false) ||
                (unConfirmedComponents?.contains("postal_town") ?? false))
            ? null
            : map['address']?["postalAddress"]?["locality"] ??
                map['address']?["postalAddress"]?["administrativeArea"],
        lga: map['address']?["postalAddress"]?["locality"],
        state: map['address']?["postalAddress"]?["administrativeArea"] ??
            map['address']?["postalAddress"]?["locality"],
        countryCode: map['address']["postalAddress"]['regionCode'],
        postal: unConfirmedComponents?.contains("postal_code") ?? false
            ? null
            : map['address']?["postalAddress"]?['postalCode'],
        latitude: map['geocode']?["location"]?['latitude'],
        longitude: map['geocode']?["location"]?['longitude']);
  }

  factory TdAddress.fromMap(Map<String, dynamic> map) {
    return TdAddress(
      address: map['address'],
      city: map['city'],
      lga: map['lga'],
      state: map['state'],
      countryCode: map['countryCode'],
      postal: map['postal'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
    );
  }

  factory TdAddress.addressRetrieve(Map<String, dynamic> map) {
    final String company = map['Company'];
    final String number = map["BuildingNumber"];
    final String line1 = map['Line1'];
    final String line2 = map['Line2'];
    final String street = map['Street'];
    final String province = map['Province'];
    final String city = map['City'];
    final String adminArea = map['AdminAreaName'];
    final String district = map['District'];
    final String neighbourhood = map['Neighbourhood'];
    final String country = map["CountryName"];
    final String town = city.isNotEmpty ? city : neighbourhood;
    final String lga = district.isNotEmpty ? district : adminArea;
    final String state = province.isNotEmpty ? province : city;
    return TdAddress(
      address:
          "${company.isNotEmpty ? "$company, " : ""}${line1.isNotEmpty ? line1 : street.isNotEmpty ? number.isNotEmpty ? ", $number $street" : ", $street" : map['Company']}${line2.isNotEmpty ? ", $line2" : ""}${lga.isNotEmpty ? ", $lga" : ""}${town.isNotEmpty && lga != town ? ", $town" : ""}${state.isNotEmpty && state != town ? ", $state" : ""}, ${map['PostalCode']}${country.isNotEmpty ? ", $country" : ""}",
      city: town,
      lga: lga,
      state: state,
      countryCode: map['CountryIso2'],
      postal: map['PostalCode'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'address': address,
      'city': city,
      'lga': lga,
      'state': state,
      'countryCode': countryCode,
      'postal': postal,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  Map<String, dynamic> toData() {
    return {
      'address': address,
      'state': state,
      'country': countryCode,
    };
  }

  bool get hasNullValues => [
        address,
        city,
        lga,
        state,
        countryCode,
        postal,
        latitude,
        longitude,
      ].any((field) => field == null);
}

typedef SetAddress = void Function(TdAddress address);
