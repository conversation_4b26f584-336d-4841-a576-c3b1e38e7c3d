import 'package:equatable/equatable.dart';

class RetailOutletType extends Equatable {
  final String? id;
  final String? name;
  final String? domain;

  const RetailOutletType({
    this.id,
    this.name,
    this.domain,
  }) : super();

  @override
  List<Object?> get props => [id];

  factory RetailOutletType.fromMap(Map<String, dynamic> map) {
    return RetailOutletType(
      id: map['_id'],
      name: map['name'],
      domain: map['domain'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'domain': domain,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
