class CurrentJob {
  CurrentJob({
    required this.bvnJob,
    required this.ninJob,
  });
  late final List<CurrentJobData> bvnJob;
  late final List<CurrentJobData> ninJob;
  late final bool? runEnhanced;

  CurrentJob.fromJson(Map<String, dynamic> json) {
    bvnJob = List.from(json['bvnJob'])
        .map((e) => CurrentJobData.fromJson(e))
        .toList();
    ninJob = List.from(json['ninJob'])
        .map((e) => CurrentJobData.fromJson(e))
        .toList();
    runEnhanced = json['runEnhanced'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['bvnJob'] = bvnJob.map((e) => e.toJson()).toList();
    data['ninJob'] = ninJob.map((e) => e.toJson()).toList();
    data['runEnhanced'] = runEnhanced;
    return data;
  }
}

class CurrentJobData {
  CurrentJobData(
      {required this.id,
      required this.retailOutletId,
      required this.jobId,
      required this.jobType,
      required this.status,
      required this.createdAt,
      required this.resultCode,
      required this.resultText,
      required this.support});
  late final String? id;
  late final String? retailOutletId;
  late final String? jobId;
  late final String? jobType;
  late final String? status;
  late final String? createdAt;
  late final String? resultCode;
  late final String? resultText;
  late final bool? support;

  CurrentJobData.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    retailOutletId = json['retailOutletId'];
    jobId = json['job_id'];
    jobType = json['job_type'];
    status = json['status'];
    createdAt = json['createdAt'];
    resultCode = json['resultCode'];
    resultText = json['resultText'];
    support = json['support'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = id;
    data['retailOutletId'] = retailOutletId;
    data['job_id'] = jobId;
    data['job_type'] = jobType;
    data['status'] = status;
    data['createdAt'] = createdAt;
    data['resultCode'] = resultCode;
    data['resultText'] = resultText;
    data['support'] = support;
    return data;
  }
}
