class CompanySearchData {
  final String? status;
  final Data? data;

  CompanySearchData({
    required this.status,
    required this.data,
  });

  factory CompanySearchData.fromJson(Map<String, dynamic> json) =>
      CompanySearchData(
        status: json["status"],
        data: Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() {
    try {
      return {
        "status": status,
        "data": data?.toJson(),
      };
    } catch (e) {
      return {};
    }
  }
}

class Data {
  final int? pageNumber;
  final List<Item>? items;
  final int? itemsPerPage;
  final int? totalResults;
  final int? startIndex;
  final String? kind;

  Data({
    required this.pageNumber,
    required this.items,
    required this.itemsPerPage,
    required this.totalResults,
    required this.startIndex,
    required this.kind,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      pageNumber: json["page_number"],
      items: List<Item>.from(json["items"].map((x) => Item.fromJson(x))),
      itemsPerPage: json["items_per_page"],
      totalResults: json["total_results"],
      startIndex: json["start_index"],
      kind: json["kind"],
    );
  }

  Map<String, dynamic> toJson() {
    try {
      return {
        "page_number": pageNumber,
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "items_per_page": itemsPerPage,
        "total_results": totalResults,
        "start_index": startIndex,
        "kind": kind,
      };
    } catch (e) {
      return {};
    }
  }
}

class Item {
  final String? addressSnippet;
  final List<String>? descriptionIdentifier;
  final String? companyStatus;
  final String? companyType;
  final String? kind;
  final String? companyNumber;
  final String? description;
  final String? title;
  final Address? address;
  final DateTime? dateOfCessation;

  Item({
    this.addressSnippet,
    this.descriptionIdentifier,
    this.companyStatus,
    this.companyType,
    this.kind,
    this.companyNumber,
    this.description,
    this.title,
    this.address,
    this.dateOfCessation,
  });

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        addressSnippet: json["address_snippet"],
        descriptionIdentifier: [],
        /*json["description_identifier"] == null
            ? []
            : List<String>.from(json["description_identifier"].map((x) => x)),*/
        companyStatus: json["company_status"],
        companyType: json["company_type"],
        kind: json["kind"],
        companyNumber: json["company_number"],
        description: json["description"],
        title: json["title"],
        address: Address.fromJson(json["address"] ?? {}),
        dateOfCessation: json["date_of_cessation"] != null
            ? DateTime.parse(json["date_of_cessation"])
            : DateTime.now(),
      );

  Map<String, dynamic> toJson() {
    try {
      return {
        "address_snippet": addressSnippet,
        "description_identifier":
            List<dynamic>.from(descriptionIdentifier!.map((x) => x)),
        "company_status": companyStatus,
        "company_type": companyType,
        "kind": kind,
        "company_number": companyNumber,
        "description": description,
        "title": title,
        "address": address?.toJson(),
        "date_of_cessation":
            "${dateOfCessation?.year.toString().padLeft(4, '0')}-${dateOfCessation?.month.toString().padLeft(2, '0')}-${dateOfCessation?.day.toString().padLeft(2, '0')}",
      };
    } catch (e) {
      return {};
    }
  }
}

class Address {
  final String? addressLine1;
  final String? country;
  final String? locality;
  final String? postalCode;
  final String? premises;
  final String? region;
  final String? addressLine2;

  Address({
    required this.addressLine1,
    required this.country,
    required this.locality,
    required this.postalCode,
    required this.premises,
    required this.region,
    required this.addressLine2,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        addressLine1: json["address_line_1"],
        country: json["country"],
        locality: json["locality"],
        postalCode: json["postal_code"],
        premises: json["premises"],
        region: json["region"],
        addressLine2: json["address_line_2"],
      );

  Map<String, dynamic> toJson() {
    try {
      return {
        "address_line_1": addressLine1,
        "country": country,
        "locality": locality,
        "postal_code": postalCode,
        "premises": premises,
        "region": region,
        "address_line_2": addressLine2,
      };
    } catch (e) {
      return {};
    }
  }
}

class Links {
  final String? self;

  Links({
    required this.self,
  });

  factory Links.fromJson(Map<String, dynamic> json) => Links(
        self: json["self"],
      );

  Map<String, dynamic> toJson() {
    try {
      return {"self": self};
    } catch (e) {
      return {};
    }
  }
}
