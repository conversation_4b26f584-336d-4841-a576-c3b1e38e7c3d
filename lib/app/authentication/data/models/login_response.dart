import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/utils/methods.dart';

import 'package:shop/app/authentication/data/models/visitor.dart';

class LoginResponse extends Equatable {
  final LoginResponseType type;
  final User? user;
  final Visitor? visitor;
  LoginResponse({
    required this.type,
    this.user,
    this.visitor,
  }) : assert(
          type == LoginResponseType.user
              ? user != null
              : type == LoginResponseType.visitor
                  ? visitor != null
                  : false,
          '${capitalize(type.name)} cannot be null based on the LoginResponseType',
        );

  // LoginResponse copyWith({
  //   LoginResponseType? type,
  //   User? user,
  //   Visitor? visitor,
  // }) {
  //   return LoginResponse(
  //     type: type ?? this.type,
  //     user: user ?? this.user,
  //     visitor: visitor ?? this.visitor,
  //   );
  // }

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'user': user?.toMap(),
      'visitor': visitor?.toMap(),
    };
  }

  @override
  List<Object?> get props => [type, user, visitor];
}

enum LoginResponseType { user, visitor }
