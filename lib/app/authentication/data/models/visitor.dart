import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/utils/index.dart';

class Visitor extends Equatable {
  final String visitorId;
  final Visit? visit;
  final bool isLoggedIn;
  final DateTime? lastTimesStamp;
  final String paginationKey;
  const Visitor({
    required this.visitorId,
    required this.visit,
    required this.isLoggedIn,
    required this.lastTimesStamp,
    required this.paginationKey,
  });

  String get city =>
      visit?.ipLocation.city != null && visit!.ipLocation.city.isNotEmpty
          ? '${visit!.ipLocation.city}, '
          : '';

  Visitor copyWith({
    String? visitorId,
    Visit? visit,
    bool? isLoggedIn,
    DateTime? lastTimesStamp,
    String? paginationKey,
  }) {
    return Visitor(
      visitorId: visitorId ?? this.visitorId,
      visit: visit ?? this.visit,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      lastTimesStamp: lastTimesStamp ?? this.lastTimesStamp,
      paginationKey: paginationKey ?? this.paginationKey,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'visitorId': visitorId,
      'visit': visit?.toMap(),
      'isLoggedIn': isLoggedIn,
      'lastTimesStamp': lastTimesStamp?.millisecondsSinceEpoch,
      'paginationKey': paginationKey,
    };
  }

  factory Visitor.fromMap(Map<String, dynamic> map) {
    return Visitor(
      visitorId: map['visitorId'] ?? '',
      visit: map['visits'] != null ? Visit.fromMap(map['visits'][0]) : null,
      isLoggedIn: map['isLoggedIn'] ?? false,
      lastTimesStamp: parseDate(map['lastTimesStamp']),
      paginationKey: map['paginationKey'] ?? '',
    );
  }

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props =>
      [visitorId, visit, isLoggedIn, lastTimesStamp, paginationKey];
}

class Visit {
  final String requestId;
  final BrowserDetails browserDetails;
  final bool incognito;
  final String ip;
  final IpLocation ipLocation;
  final bool visitorFound;
  final num confidenceScore;
  final DateTime time;
  final DateTime timestamp;
  final String url;
  Visit({
    required this.requestId,
    required this.browserDetails,
    required this.incognito,
    required this.ip,
    required this.ipLocation,
    required this.visitorFound,
    required this.confidenceScore,
    required this.time,
    required this.timestamp,
    required this.url,
  });

  Visit copyWith({
    String? requestId,
    BrowserDetails? browserDetails,
    bool? incognito,
    String? ip,
    IpLocation? ipLocation,
    bool? visitorFound,
    num? confidenceScore,
    DateTime? time,
    DateTime? timestamp,
    String? url,
  }) {
    return Visit(
      requestId: requestId ?? this.requestId,
      browserDetails: browserDetails ?? this.browserDetails,
      incognito: incognito ?? this.incognito,
      ip: ip ?? this.ip,
      ipLocation: ipLocation ?? this.ipLocation,
      visitorFound: visitorFound ?? this.visitorFound,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      time: time ?? this.time,
      timestamp: timestamp ?? this.timestamp,
      url: url ?? this.url,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'requestId': requestId,
      'browserDetails': browserDetails.toMap(),
      'incognito': incognito,
      'ip': ip,
      'ipLocation': ipLocation.toMap(),
      'visitorFound': visitorFound,
      'confidenceScore': confidenceScore,
      'time': time.millisecondsSinceEpoch,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'url': url,
    };
  }

  factory Visit.fromMap(Map<String, dynamic> map) {
    return Visit(
      requestId: map['requestId'] ?? '',
      browserDetails: BrowserDetails.fromMap(map['browserDetails']),
      incognito: map['incognito'] ?? false,
      ip: map['ip'] ?? '',
      ipLocation: IpLocation.fromMap(map['ipLocation']),
      visitorFound: map['visitorFound'] ?? false,
      confidenceScore: map['confidence']['score'] ?? 0,
      time: parseDate(map['time'])!,
      timestamp: parseDate(map['timestamp'])!,
      url: map['url'] ?? '',
    );
  }

  @override
  String toString() => '${toMap()}';
}

class BrowserDetails {
  final String browserName;
  final String browserMajorVersion;
  final String browserFullVersion;
  final String os;
  final String osVersion;
  final String device;
  final String userAgent;
  BrowserDetails({
    required this.browserName,
    required this.browserMajorVersion,
    required this.browserFullVersion,
    required this.os,
    required this.osVersion,
    required this.device,
    required this.userAgent,
  });

  String get deviceName =>
      ['other'].contains(device.toLowerCase()) ? browserName : device;

  BrowserDetails copyWith({
    String? browserName,
    String? browserMajorVersion,
    String? browserFullVersion,
    String? os,
    String? osVersion,
    String? device,
    String? userAgent,
  }) {
    return BrowserDetails(
      browserName: browserName ?? this.browserName,
      browserMajorVersion: browserMajorVersion ?? this.browserMajorVersion,
      browserFullVersion: browserFullVersion ?? this.browserFullVersion,
      os: os ?? this.os,
      osVersion: osVersion ?? this.osVersion,
      device: device ?? this.device,
      userAgent: userAgent ?? this.userAgent,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'browserName': browserName,
      'browserMajorVersion': browserMajorVersion,
      'browserFullVersion': browserFullVersion,
      'os': os,
      'osVersion': osVersion,
      'device': device,
      'userAgent': userAgent,
    };
  }

  factory BrowserDetails.fromMap(Map<String, dynamic> map) {
    return BrowserDetails(
      browserName: map['browserName'] ?? '',
      browserMajorVersion: map['browserMajorVersion'] ?? '',
      browserFullVersion: map['browserFullVersion'] ?? '',
      os: map['os'] ?? '',
      osVersion: map['osVersion'] ?? '',
      device: map['device'] ?? '',
      userAgent: map['userAgent'] ?? '',
    );
  }

  @override
  String toString() => '${toMap()}';
}

class IpLocation {
  final num accuracyRadius;
  final num latitude;
  final num longitude;
  final String timezone;
  final String city;
  final String country;
  final String continent;
  IpLocation({
    required this.accuracyRadius,
    required this.latitude,
    required this.longitude,
    required this.timezone,
    required this.city,
    required this.country,
    required this.continent,
  });

  IpLocation copyWith({
    num? accuracyRadius,
    num? latitude,
    num? longitude,
    String? timezone,
    String? city,
    String? country,
    String? continent,
  }) {
    return IpLocation(
      accuracyRadius: accuracyRadius ?? this.accuracyRadius,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      city: city ?? this.city,
      country: country ?? this.country,
      continent: continent ?? this.continent,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accuracyRadius': accuracyRadius,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'city': city,
      'country': country,
      'continent': continent,
    };
  }

  factory IpLocation.fromMap(Map<String, dynamic> map) {
    return IpLocation(
      accuracyRadius: map['accuracyRadius'] ?? 0,
      latitude: map['latitude'] ?? 0,
      longitude: map['longitude'] ?? 0,
      timezone: map['timezone'] ?? '',
      city: map['city'] != null ? map['city']['name'] ?? '' : '',
      country: map['country']['name'] ?? '',
      continent: map['continent']['name'] ?? '',
    );
  }

  @override
  String toString() => '${toMap()}';
}
