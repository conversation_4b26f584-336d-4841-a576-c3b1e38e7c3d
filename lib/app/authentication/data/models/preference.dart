import 'package:equatable/equatable.dart';

class Preference extends Equatable {
  final String id;
  final String name;
  const Preference({
    required this.id,
    required this.name,
  });

  Preference copyWith({
    String? id,
    String? name,
  }) {
    return Preference(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'name': name,
    };
  }

  factory Preference.fromMap(Map<String, dynamic> map) {
    return Preference(
      id: map['_id'] ?? map['id'] ?? '',
      name: map['name'] ?? '',
    );
  }

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [id];
}
