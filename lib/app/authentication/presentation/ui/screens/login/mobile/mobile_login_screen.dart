import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safe_insets/index.dart';
import 'package:shop/route_constants.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class MobileLoginScreen extends StatelessWidget {
  final String? forgotPhoneNumber;
  final String? name;

  const MobileLoginScreen({
    super.key,
    this.forgotPhoneNumber,
    this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('MobileLoginScreen'),
        _buildFooter(context),
      ],
    );
    // return VerifyPhoneScreen(
    //   pageType: AuthPageType.Login,
    //   title: "Hello,\n${name ?? 'Shopper'}",
    //   subtitle: forgotPhoneNumber != null
    //       ? 'Reset your pin by choosing a validation option below.'
    //       : 'Put in your phone number and gain access into '
    //           'your ShopTopUp account.',
    //   forgotPhoneNumber: forgotPhoneNumber,
    //   footer: _buildFooter(context),
    // );
  }

  Widget _buildFooter(BuildContext context) {
    if (forgotPhoneNumber != null) return YMargin(25);

    return Column(
      children: [
        YMargin(25),
        Text("I don’t have a TradeDepot Account."),
        TextButton(
          onPressed: () {
            context.pushReplacementNamed(SignUpPath);
          },
          child: SafeAreaWrap(
            Text(
              'SignUp',
              style: TextStyle(
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
