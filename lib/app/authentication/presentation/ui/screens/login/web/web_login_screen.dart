import 'package:flutter/material.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_phone/verify_phone_screen.dart';
import 'package:shop/src/components/src/widgets/margin.dart';

class WebLoginScreen extends StatelessWidget {
  final String? forgotPhoneNumber;
  final String? name;

  const WebLoginScreen({
    super.key,
    this.forgotPhoneNumber,
    this.name,
  });

  @override
  Widget build(BuildContext context) {
    return VerifyPhoneScreen(
      args: VerifyPhoneArgs(
        pageType: AuthPageType.login,
        otpParams: SendOTParams(
          mode: PhoneAuthMode.WhatsApp,
          phoneNumber: '',
          url: '',
        ), // Just for web build, so as not to disrupt the mobile flow
        webParams: WebParams(
          title: "Welcome Back",
          subtitle: forgotPhoneNumber != null
              ? 'Reset your pin by choosing a validation option below.'
              : 'Login to your TradeDepot account',
          forgotPhoneNumber: forgotPhoneNumber,
          footer: _buildFooter(context),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context) => YSpacing(25);
}
