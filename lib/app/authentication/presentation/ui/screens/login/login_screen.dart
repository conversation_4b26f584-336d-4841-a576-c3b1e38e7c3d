import 'package:flutter/cupertino.dart';
import 'package:shop/app/authentication/presentation/ui/screens/login/web/web_login_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class LoginScreen extends StatelessWidget {
  final String? forgotPhoneNumber;
  final String? name;

  const LoginScreen({
    super.key,
    this.forgotPhoneNumber,
    this.name,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebLoginScreen(
        forgotPhoneNumber: forgotPhoneNumber,
        name: name,
      ),
      smallScreen: SizedBox.expand(),
      mediumScreen: SizedBox.expand(),
      // smallScreen: MobileLoginScreen(
      //   forgotPhoneNumber: forgotPhoneNumber,
      //   name: name,
      // ),
      // mediumScreen: MobileLoginScreen(
      //   forgotPhoneNumber: forgotPhoneNumber,
      //   name: name,
      // ),
    );
  }
}

class LoginScreenArgs {
  final String? forgotPhoneNumber;
  final String? name;

  LoginScreenArgs(this.forgotPhoneNumber, this.name);
}
