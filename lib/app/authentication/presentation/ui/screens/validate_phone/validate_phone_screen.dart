import 'package:flutter/cupertino.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/web/web_validate_phone_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import '../../../../../loan/domain/params/loan_params.dart';
import 'mobile/mobile_validate_phone_screen.dart';

class ValidatePhoneScreen extends StatelessWidget {
  static const VERIFY_OTP_PATH = "phoneauth/verify-totp";

  final SendOTParams params;
  final AuthPageType? pageType;
  final bool forgotPin;
  final String countryCode;
  final String? name;
  final NinParams? ninParams;
  final PayParams? payParams;
  final BvnParams? bvnParams;
  final String? pin;

  const ValidatePhoneScreen({
    super.key,
    required this.params,
    required this.pageType,
    this.payParams,
    this.forgotPin = false,
    required this.countryCode,
    this.name,
    this.ninParams,
    this.bvnParams,
    this.pin,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebValidatePhoneScreen(
          params: params,
          pageType: pageType,
          countryCode: countryCode,
          forgotPin: forgotPin,
          name: name,
          ninParams: ninParams,
          pin: pin,),
      smallScreen: MobileValidatePhoneScreen(
        params: params,
        pageType: pageType,
        payParams: payParams,
        countryCode: countryCode,
        forgotPin: forgotPin,
        name: name,
        ninParams: ninParams,
        bvnParams: bvnParams,
      ),
      mediumScreen: MobileValidatePhoneScreen(
        params: params,
        pageType: pageType,
        payParams: payParams,
        countryCode: countryCode,
        forgotPin: forgotPin,
        name: name,
        ninParams: ninParams,
        bvnParams: bvnParams,
      ),
    );
  }
}

class ValidatePhoneArgs {
  static const VERIFY_OTP_PATH = "phoneauth/verify-totp";

  final SendOTParams params;
  final AuthPageType? pageType;
  final bool forgotPin;
  final String countryCode;
  final String? name;
  final NinParams? ninParams;
  final BvnParams? bvnParams;
  final String? pin;

  const ValidatePhoneArgs({
    Key? key,
    required this.params,
    required this.pageType,
    this.forgotPin = false,
    required this.countryCode,
    this.name = '',
    this.ninParams,
    this.bvnParams,
    this.pin,
  });
}
