import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:safe_insets/safe_area_wrap.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_nin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_state.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/dial_ussd_button.dart';
import 'package:shop/app_config.dart';
import 'package:shop/app/core/cubit_state/cubit_state.dart';
import 'package:shop/app/loan/presentation/logic/bloc/base_loan_cubit.dart';
import 'package:shop/app/pay/domain/params/pay_charge_params.dart';
import 'package:shop/app/pay/presentation/logic/charge_cubit.dart';
import 'package:shop/app/pay/presentation/logic/charge_otp_cubit.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../sign_up_user/sign_up_user_screen.dart';
import '../../../../../../../src/res/values/analytics/segment_events.dart';
import '../../../../../../loan/domain/params/loan_params.dart';
import '../../../../../../loan/domain/use-cases/verify_bvn.dart';

class MobileValidatePhoneScreen extends StatefulWidget {
  static const VERIFY_OTP_PATH = "phoneauth/verify-totp";

  final SendOTParams params;
  final AuthPageType? pageType;
  final bool forgotPin;
  final String countryCode;
  final String? name;
  final NinParams? ninParams;
  final PayParams? payParams;
  final BvnParams? bvnParams;

  const MobileValidatePhoneScreen({
    super.key,
    required this.params,
    required this.pageType,
    this.payParams,
    this.forgotPin = false,
    required this.countryCode,
    this.name,
    this.ninParams,
    this.bvnParams,
  });

  @override
  _ValidatePhoneScreenState createState() => _ValidatePhoneScreenState();
}

class _ValidatePhoneScreenState extends State<MobileValidatePhoneScreen> {
  final _bloc = PhoneValidateCubit(locator(), locator());
  final _signUpBloc = VerifyPhoneCubit(locator(), locator());
  final TextEditingController _controller = TextEditingController();
  final _countDownController = CountdownController(autoStart: true);
  late final ChargeOtpCubit _chargeOtpBloc = ChargeOtpCubit(locator());
  bool isResendingOtp = false;
  FocusNode inputNode = FocusNode();

  String code = '';

  void _verify() async {
    // validate
    if (_controller.text.isEmpty) {
      return;
    }
    await _bloc.verifyOTP(
      VerifyOTParams(
        phoneNumber: widget.params.phoneNumber!,
        token: _controller.text,
        countryCode: '',
        url: '${config.firebaseServiceUrl!}/${MobileValidatePhoneScreen.VERIFY_OTP_PATH}',
      ),
      widget.pageType,
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(inputNode);
    });
  }

  void _proceed() async {
    context.read<ChargeCubit>().payAgent(
          PayChargeParams(
              amount: widget.payParams!.amount,
              phoneNumber: widget.payParams!.phoneNumber,
              token: _controller.text,
              skipDuplicate: false),
        );
  }

  bool get verifyingNin =>
      widget.pageType == AuthPageType.verifySignUpNin ||
      widget.pageType == AuthPageType.verifyKycNin ||
      widget.pageType == AuthPageType.verifyHomeNin ||
      widget.pageType == AuthPageType.verifyPaymentNin;

  String get maskedPhone {
    final phone = widget.params.phoneNumber;
    final prefix = phone!.substring(0, 4);
    final postfix = phone.substring(phone.length - 4);
    final mask = '******';
    return '$prefix$mask$postfix';
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PhoneValidateCubit, PhoneValidateState>(
      bloc: _bloc,
      builder: (context, state) => _build(context, state),
      listener: (_, state) async {
        if (state is ValidatedAfterLoginState) {
          // try {
          //   final info = await deviceInfo();
          //   await saveDeviceInfo(info);
          // } catch (e, s) {
          //   ErrorHandler.report(e, s);
          // }

          // Remove the login OTP prompt on app load.
          SharedPreferences.getInstance().then((sp) {
            sp.remove(Keys.loginOtp);
          });

          TdLoader.hide();
          context.goNamed(HomePath);
          return;
        }

        if (state is ValidatedNINState) {
          final res = await locator.get<VerifyNin>().call(widget.ninParams!);
          res.when(
            success: (response) {
              if (widget.pageType == AuthPageType.verifyKycNin) {
                TdLoader.hide();
                context.goNamed(NINSuccessPath);
                return;
              }

              if (widget.pageType == AuthPageType.verifySignUpNin) {
                TdLoader.hide();
                goHomeMobile(context, true);
                return;
              }

              if (widget.pageType == AuthPageType.verifyPaymentNin) {
                UserCubit userCubit = context.read();
                BaseLoanCubit baseLoanCubit = context.read();
                baseLoanCubit.reloadState(userCubit.currentOutlet);
                context.pushReplacementNamed(PaymentPath);
              } else {
                TdLoader.hide();
                context.goNamed(NINSuccessPath);
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
        }

        if (state is ValidatedBVNState) {
          final res = await locator.get<VerifyBvn>().call(VerifyBvnParams(
              outletId: widget.bvnParams!.outletId,
              bvn: widget.bvnParams!.bvn,
              isValidated: widget.bvnParams!.isValidated,
              phoneNumber: widget.bvnParams!.phoneNumber));

          res.when(
            success: (data) {
              if (widget.bvnParams?.fromLoan == true) {
                UserCubit.instance?.updateOutlet();

                context
                    .read<BaseLoanCubit>()
                    .reloadState(UserCubit.instance?.currentOutlet);
                Segment.track(
                  eventName: SegmentEvents.kycValidationCompleted,
                  properties: {
                    'Status': 1,
                  },
                );

                TdLoader.hide();

                Navigator.pop(context);
              } else {
                UserCubit.instance?.updateOutlet();

                TdLoader.hide();

                Navigator.pop(context);
                Navigator.pop(context);
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
        }

        if (state is ValidatingOTPState) {
          TdLoader.show(context);
        } else {
          TdLoader.hide();
        }

        if (state is FailedToValidateOTP) {
          Toast.error(state.errorMessage, _);
        }

        if (state is ValidatedOTPState) {
          // next page
          context.pushNamed(
            SignUpUserPath,
            extra: SignUpUserArgs(
              accessToken: state.accessToken,
              phoneNumber: widget.params.phoneNumber!,
              countryCode: state.countryCode,
            ),
          );
        }

        if (state is PhoneValidateUserLoggedIn) {
          if (widget.pageType == AuthPageType.login) {
            SharedPreferences.getInstance().then((sp) {
              sp.setBool(Keys.resetPin, true);
            });

            // user want to reset pin
            context.goNamed(
              EnterPinPath,
              extra: EnterPinScreenArgs(
                name: state.firstName,
                resetPinParams: ResetPinParams(
                  null,
                  widget.params.phoneNumber!,
                  state.accessToken,
                ),
              ),
            );
          } else {
            context.goNamed(HomePath);
          }
        }
      },
    );
  }

  Widget _build(BuildContext context, PhoneValidateState state) {
    return AppScreen(
      child: Scaffold(
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildTop(state)),
              const SizedBox(height: 10),

              // hide footer when keyboard is up
              if (MediaQuery.of(context).viewInsets.bottom < 10)
                _buildFooter(state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(PhoneValidateState state) {
    bool showUssdOption = widget.countryCode != 'ZA';
    return SafeAreaWrap(
      Padding(
        padding: context.insetsOnly(
          left: defaultHorizontalContentPadding,
          right: defaultHorizontalContentPadding,
          bottom: defaultVerticalContentPaddingLarge,
        ),
        child: code.length > 1
            ? _submitButton()
            : showUssdOption
                ? DialUssdButton(phoneNumber: widget.params.phoneNumber!)
                : SizedBox.shrink(),
      ),
    );
  }

  Widget _submitButton() {
    return KButton(
      onPressed: () {
        if (widget.pageType == AuthPageType.verifyAgent) {
          _proceed();
        } else {
          _verify();
        }
      },
      text: 'Submit',
    );
  }

  Widget _buildTop(PhoneValidateState state) {
    Size screenSize = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 55,
          child: KBackButton(
            logout: widget.pageType == AuthPageType.proceedLogin,
            close: (widget.pageType == AuthPageType.proceedLogin ||
                widget.pageType == AuthPageType.verifySignUpNin ||
                widget.pageType == AuthPageType.verifyKycNin ||
                widget.pageType == AuthPageType.verifyHomeNin),
          ),
        ),
        YMargin(20),
        Padding(
          padding: context.insetsSymetric(
            horizontal: defaultHorizontalContentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Verification",
                style: textStyleBold(
                  context,
                  fontSize: 35,
                ),
              ),
              YMargin(20),
              Text("Enter the code sent to the number"),
              YMargin(15),
              Text(
                // widget.params.phoneNumber!,
                maskedPhone,
                // verifyingNin ? maskedPhone : widget.params.phoneNumber,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                ),
              ),
              YMargin(25),
              // Text('Enter OTP Code'),
              const SizedBox(height: 20),

              Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints.tightFor(
                      width: screenSize.width * 0.7,
                      height: screenSize.width * 0.1),
                  child: Pinput(
                    focusNode: inputNode,
                    // autofocus: true,
                    length: 6,
                    controller: _controller,
                    onCompleted: (value) {
                      if (widget.pageType == AuthPageType.verifyAgent) {
                        _proceed();
                      } else {
                        _verify();
                      }
                    },
                  ),
                ),
              ),
              YMargin(30),

              Countdown(
                seconds: 60,
                controller: _countDownController,
                build: (_, sec) {
                  final seconds = sec.toInt();
                  // time elapsed
                  bool canResend = seconds < 1;
                  return BlocListener<ChargeOtpCubit,
                      CubitState<ChargeOtpResponse>>(
                    bloc: _chargeOtpBloc,
                    listener: (context, state) {
                      state.maybeWhen(
                        orElse: () {},
                        loading: (loading) {
                          setState(() => isResendingOtp = true);
                        },
                        error: (errMsg) {
                          setState(() => isResendingOtp = false);
                          Toast.error(errMsg, context);
                        },
                        completed: ((model) {
                          setState(() {
                            isResendingOtp = false;
                            _controller.clear();
                            _countDownController.restart();

                            Toast.success('OTP code has been resent', context);
                          });
                        }),
                      );
                    },
                    child: BlocConsumer<VerifyPhoneCubit, VerifyPhoneState>(
                      listener: ((context, state) {
                        if (state is SentOTPState) {
                          Toast.success('OTP code has been resent', context);
                          _controller.clear();
                          _countDownController.restart();
                        }
                      }),
                      bloc: _signUpBloc,
                      builder: (context, state) {
                        final sending = state is VerifyPhoneLoading;
                        return isResendingOtp || sending
                            ? ConstrainedBox(
                                constraints: BoxConstraints.tightFor(
                                    width: 30, height: 30),
                                child: CircularProgressIndicator())
                            : canResend
                                ? InkWell(
                                    onTap: () {
                                      if (isResendingOtp || sending) {
                                        return;
                                      }
                                      FocusManager.instance.primaryFocus
                                          ?.unfocus();
                                      widget.pageType ==
                                              AuthPageType.verifyAgent
                                          ? _chargeOtpBloc.sendChargeOtp(
                                              ChargeOtpParams(
                                                  amount:
                                                      widget.payParams!.amount),
                                            )
                                          : _signUpBloc.sendOTP(widget.params);
                                    },
                                    child: codeResendWidget())
                                : Text(
                                    canResend
                                        ? 'Resend'
                                        : "Resend in ${_extractTime(seconds)}",
                                    style: TextStyle(),
                                  );
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget codeResendWidget() {
    return Center(
      child: Column(
        children: [
          Text(
            "Didn't receive code?",
            style: TextStyle(),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Resend",
                style: TextStyle(decoration: TextDecoration.underline),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _signUpBloc.close();
    _bloc.close();
    super.dispose();
  }

  String _extractTime(int seconds) {
    if (seconds >= 60) {
      return '01:00';
    }
    if (seconds < 10) {
      return '00:0$seconds';
    }
    return '00:$seconds';
  }
}
