import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:safe_insets/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/verify_nin_screen.dart';
import 'package:shop/app/collections/presentation/logic/bloc/index.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/values/config/keys.dart';

import '../../../../../../route_constants.dart';
import '../../../../../../src/components/components.dart';
import '../../../../../../src/components/src/error/error_widget.dart';
import '../../../../../../src/components/src/loader/loader.dart';
import '../../../../data/models/preference.dart';
import '../../../../domain/params/post_params.dart';
import '../../../../domain/use_cases/save_preferences.dart';
import '../../../logic/bloc/preferences_cubit.dart';
import '../../../logic/bloc/user_cubit.dart';
import '../../../logic/utils/methods.dart';

class ShopPreferencesScreen extends StatefulWidget {
  final PreferenceType type;
  const ShopPreferencesScreen({super.key, required this.type});

  @override
  _EnterPinScreenState createState() => _EnterPinScreenState();
}

class _EnterPinScreenState extends State<ShopPreferencesScreen> {
  late UserCubit userCubit;

  final List<Preference> _userPreferences = [];
  final List<Preference> _cachedPreferences = [];
  bool dirty = false;

  @override
  void initState() {
    context.read<PreferencesCubit>().getPreferences(widget.type);
    userCubit = context.read<UserCubit>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final isCollection = widget.type == PreferenceType.collection;

    return SafeArea(
      child: SafeAreaWrap(
        AppScreen(
          child: isCollection
              ? Scaffold(
                  body: _build(isCollection, textTheme, context),
                )
              : Scaffold(
                  appBar: ShopAppBar.shopAppBar(context,
                      title: 'Shopping Preferences'),
                  body: _build(isCollection, textTheme, context),
                ),
        ),
      ),
    );
  }

  Widget _build(
    bool isCollection,
    TextTheme textTheme,
    BuildContext context,
  ) {
    return CustomScrollView(
      slivers: [
        if (isCollection) ...[
          SliverAppBar(
            automaticallyImplyLeading: false,
            actions: [
              KButtonPrimary(
                text: 'Skip',
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(38.0),
                ),
                constraints: BoxConstraints.tight(
                  Size(85, 40),
                ),
                onTap: nextAction,
              ),
              const XMargin(18),
            ],
          ),
        ],
        SliverFillRemaining(
          hasScrollBody: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YMargin(20),
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: defaultHorizontalContentPadding),
                child: Text(
                  '${isCollection ? 'Customise' : 'Update'} your shopping experience',
                  style: textTheme.headlineLarge
                      ?.copyWith(fontWeight: FontWeight.w500),
                ),
              ),
              const YMargin(10),
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: defaultHorizontalContentPadding),
                child: Text(
                    'Get better item recommendations by setting up your shopping preferences',
                    style: textTheme.bodyLarge),
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                        horizontal: defaultHorizontalContentPadding)
                    .copyWith(
                        top: defaultVerticalContentPaddingLarge,
                        bottom: defaultVerticalContentPaddingLarge),
                child: Text(
                  'I’m interested in...',
                  style: textTheme.bodyMedium,
                ),
              ),
              BlocConsumer<PreferencesCubit, PreferencesState>(
                listener: (context, state) {
                  if (state is PreferencesLoaded) {
                    if (state.update!) {
                      _userPreferences.addAll(state.userPreferences);
                      _cachedPreferences.addAll(state.userPreferences);
                    }
                  }
                },
                builder: (context, state) {
                  Widget child = SizedBox.shrink();

                  if (state is PreferencesLoading) {
                    child = Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          KLoader(),
                        ],
                      ),
                    );
                  }

                  if (state is PreferencesError) {
                    child = Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          KErrorScreen(
                            state.message,
                            () {
                              context
                                  .read<PreferencesCubit>()
                                  .getPreferences(widget.type, true);
                            },
                          ),
                        ],
                      ),
                    );
                  }

                  if (state is PreferencesLoaded) {
                    final items = state.collectionPreferences;
                    if (items.isEmpty) {
                      child = Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 40),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(kSvgShopPreferencesIcon),
                              const YMargin(40),
                              Text(
                                'No Shopping preference',
                                style: textTheme.headlineSmall
                                    ?.copyWith(fontSize: 18),
                                textAlign: TextAlign.center,
                              ),
                              const YMargin(20),
                              Text(
                                "You don't have any shopping preferences at the moment, please check back soon",
                                style: textTheme.bodyLarge,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    } else {
                      child = Wrap(
                        spacing: 8.0, // horizontal spacing between items
                        runSpacing: 8.0, // vertical spacing between lines
                        children: items.map(
                          (item) {
                            final isSelected = _userPreferences.contains(item);

                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  if (isSelected) {
                                    _userPreferences.remove(item);
                                  } else {
                                    _userPreferences.add(item);
                                  }
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: isSelected
                                        ? Theme.of(context).colorScheme.primary
                                        : Theme.of(context)
                                            .colorScheme
                                            .outlineVariant,
                                    width: 1,
                                  ),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: defaultVerticalContentPadding),
                                child: Text(
                                  item.name,
                                  style: textTheme.bodyMedium,
                                ),
                              ),
                            );
                          },
                        ).toList(),
                      );
                    }
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: defaultHorizontalContentPadding),
                    child: child,
                  );
                },
              ),
              Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(
                        horizontal: defaultHorizontalContentPadding)
                    .copyWith(bottom: defaultVerticalContentPadding),
                child: KButtonPrimary(
                  disabled:
                      !areListsDifferent(_userPreferences, _cachedPreferences),
                  text: isCollection ? 'Continue' : 'Update & Save',
                  onTap: () async {
                    TdLoader.show(context);

                    final res = await locator
                        .get<SavePreferences>()
                        .call(SavePreferencesParams(_userPreferences));

                    res.when(
                      success: (_) {
                        Toast.show('Shopping Preferences Saved', context);
                        TdLoader.hide();

                        final outlet = userCubit.currentOutlet;

                        if (outlet == null) return;

                        _cachedPreferences.clear();
                        _cachedPreferences.addAll(_userPreferences);

                        context
                            .read<PreferencesCubit>()
                            .updateUserPreferences(_userPreferences);

                        // Fetch home collections to reflect the updated user preferences
                        context.read<CollectionCubit>().fetchOutletCollection(
                            outlet.coordinates?.plusCode6Hex, true);

                        // context
                        //     .read<BrowseCollectionCubit>()
                        //     .fetchBrowseCollection(outlet);

                        if (isCollection) {
                          nextAction();
                          return;
                        }

                        setState(() {
                          //rebuild
                        });
                      },
                      apiFailure: (error, _) {
                        final msg = ApiExceptions.getErrorMessage(error);
                        TdLoader.hide();
                        Toast.error(msg, context);
                      },
                    );
                  },
                ),
              )
            ],
          ),
        ),
        // SliverToBoxAdapter(
        //   child: ConstrainedBox(
        //     constraints: BoxConstraints(
        //       minHeight: MediaQuery.of(context).size.height * 0.1,
        //       maxHeight: MediaQuery.of(context).size.height * 0.4,
        //     ),
        //   ),
        // ),
      ],
    );
  }

  bool areListsDifferent(List<Preference> listOne, List<Preference> listTwo) {
    if (listOne.length != listTwo.length) {
      return true;
    }

    listOne.sort((a, b) => a.id.compareTo(b.id));
    listTwo.sort((a, b) => a.id.compareTo(b.id));

    for (int i = 0; i < listOne.length; i++) {
      if (listOne[i] != listTwo[i]) {
        return true;
      }
    }

    return false;
  }

  void nextAction() {
    final userCubit = context.read<UserCubit>();
    SharedPreferences.getInstance().then(
      (sp) {
        sp.remove(Keys.shopPreferences);
      },
    );
    (switch (userCubit.domain) {
      AppHost.tradeDepot => () => goHomeMobile(context, true),
      _ => () {
          if (userCubit.isNgUser == true) {
            context.goNamed(
              VerifyNINPath,
              extra: VerifyNinScreenArgs(AuthPageType.verifySignUpNin),
            );
          } else {
            goHomeMobile(context, true);
          }
        },
    })();
  }
}
