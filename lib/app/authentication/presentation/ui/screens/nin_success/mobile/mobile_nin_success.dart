import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class MobileNinSuccess extends StatelessWidget {
  const MobileNinSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(kPaySuccess),
                  YSpacing(30),
                  Text("Success", style: KTextStyle.semiBold24),
                  Text(
                    "You have successfully validated your NIN",
                    style: KTextStyle.regular14.copyWith(fontSize: 16),
                  ),
                  YSpacing(15),
                ],
              ),
            ),
            Padding(
                padding: EdgeInsets.only(
                    top: defaultVerticalContentPaddingLarge,
                    right: defaultHorizontalContentPaddingLarge,
                    left: defaultHorizontalContentPaddingLarge,
                    bottom: defaultVerticalContentPaddingLarge),
                child: KButton(
                  text: 'Continue Shopping',
                  onPressed: () => context.goNamed(HomePath),
                )),
            YSpacing(15)
          ],
        ),
      ),
    );
  }
}
