import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:safe_insets/index.dart';
import 'package:shop/app/authentication/domain/repos/authentication_repo.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/screens/delete_account/delete_account.dart';
import 'package:shop/src/components/src/buttons/src/k_back_icon_button.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/flexible_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class MobileDeleteAccount extends StatelessWidget {
  const MobileDeleteAccount({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: AppBar(
        leading: KBackIconButton(),
        title: Text("Delete Account"),
        elevation: 0.5,
      ),
      body: FlexibleConstrainedBox(
        minHeight: 600,
        padding: screenPaddingLarge,
        child: Column(
          children: [
            Text(
                "Once your account is deleted, TradeDepot Wholesale won’t remember the info you might have shared including:",
                style: textTheme.titleMedium),
            YMargin(25),
            ...info
                .map(
                  (e) => DeleteTile(text: e),
                )
                ,
            Spacer(),
            KButtonPrimary(text: "Delete", onTap: () => deleteModal(context)),
            YSpacing(4),
            SafeArea(
              child: SafeAreaWrap(
                Text.rich(
                  TextSpan(
                      text:
                          "For more information about how we handle your data, see our ",
                      style: textTheme.bodySmall?.copyWith(),
                      children: [
                        WidgetSpan(
                            child: InkWell(
                              onTap: openPrivacyUri,
                              child: Text(
                                'Privacy Statement',
                                style: textTheme.bodyMedium
                                    ?.copyWith(color: colorScheme.primary),
                              ),
                            ),
                            baseline: TextBaseline.alphabetic,
                            alignment: ui.PlaceholderAlignment.baseline),
                      ]),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> deleteModal(BuildContext context) async {
    final theme = Theme.of(context).textTheme;
    final key = GlobalKey<FormState>();
    var mode = AutovalidateMode.disabled;
    showModalBottomSheet<void>(
      isScrollControlled: true,
      context: context,
      builder: (contxt) => Padding(
        padding: EdgeInsets.only(
            top: defaultVerticalContentPaddingLarge,
            left: defaultHorizontalContentPaddingLarge,
            right: defaultHorizontalContentPaddingLarge,
            bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(kSvgAlert),
            YSpacing(30),
            Text(
              "Delete your account",
              style: theme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            Text(
              "Your data and account information will be deleted permanently and this cannot be undone.",
              style: theme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            YMargin(30),
            Align(
              alignment: Alignment.centerLeft,
              child: Text.rich(
                TextSpan(
                    text: "To confirm please type ",
                    style: theme.bodyMedium,
                    children: [
                      TextSpan(
                        text: '"DELETE"',
                        style: theme.labelLarge
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ]),
              ),
            ),
            YSpacing(8),
            Form(
              key: key,
              child: TextFormField(
                textCapitalization: TextCapitalization.characters,
                decoration: InputDecoration(
                  hintText: "input",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        BorderSide(color: Theme.of(context).shadowColor),
                  ),
                ),
                validator: (input) => input?.trim() == "DELETE"
                    ? null
                    : "Input is required to match keyword \"DELETE\" ",
                autovalidateMode: mode,
              ),
            ),
            YMargin(30),
            KButtonPrimary(
              onTap: () {
                if (key.currentState!.validate()) {
                  Navigator.pop(contxt);
                  delete(context);
                } else {
                  mode = AutovalidateMode.always;
                }
              },
              text: 'Delete',
            ),
            YSpacing(10),
            KButtonPrimary.inverted(
              contxt,
              onTap: () => Navigator.pop(contxt),
              text: 'Cancel',
            ),
          ],
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  void delete(BuildContext context) async {
    TdLoader.show(context);
    final response = await locator<AuthenticationRepo>().deleteAccount();
    TdLoader.hide();
    response.when(
        success: (_) {
          Toast.success("Account has been deleted successfully", context);
          context.read<UserCubit>().logout();
        },
        apiFailure: (err, _) => Toast.apiError(err, context));
  }
}

class DeleteTile extends StatelessWidget {
  const DeleteTile({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsets.only(bottom: defaultVerticalContentPaddingLarge),
      child: Row(
        children: [
          CircleAvatar(
            radius: 10,
            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            child: SvgPicture.asset(
              kSvgCheck,
              fit: BoxFit.fitWidth,
              color: Theme.of(context).primaryColor,
              width: 15,
              height: 8,
            ),
          ),
          XSpacing(15),
          Flexible(
            child: Text(text, style: textTheme.bodyLarge),
          )
        ],
      ),
    );
  }
}
