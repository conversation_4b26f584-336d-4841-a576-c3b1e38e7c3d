import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:safe_insets/safe_area_wrap.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/change_pin.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/visitor_widget.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../../../route_constants.dart';
import '../../../../../../../src/components/src/buttons/src/k_button_primary.dart';
import '../../../../../../../src/components/src/form/form.dart';
import '../../../../../../../src/components/src/widgets/k_keyboard.dart';
import '../../../../../../../src/res/values/config/keys.dart';
import '../../../../../../../src/res/values/styles/text_style.dart';
import '../../../../../../../app_config.dart';
import '../../../../../domain/params/post_params.dart';
import '../../../../../domain/use_cases/login_with_pin.dart';
import '../../../../logic/bloc/user_cubit.dart';
import '../../../../logic/bloc/verify_phone_cubit.dart';
import '../../../../logic/bloc/verify_phone_state.dart';
import '../../../../logic/utils/methods.dart';
import '../../../widgets/otp_methods.dart';
import '../../verify_phone/verify_phone_screen.dart';
import '../login_with_pin.dart';

class MobileLoginWithPinScreen extends StatefulWidget {
  const MobileLoginWithPinScreen({
    super.key,
    required this.args,
  });

  final LoginWithPinArgs args;

  @override
  State<MobileLoginWithPinScreen> createState() =>
      _MobileLoginWithPinScreenState();
}

class _MobileLoginWithPinScreenState extends State<MobileLoginWithPinScreen> {
  final _passcodeCtrl = TextEditingController();
  bool canContinue = false;

  late final bool migrated = widget.args.migrated!;
  late final int length = widget.args.migrated! ? 6 : 4;

  final _verifyPhoneCubit = VerifyPhoneCubit(locator(), locator());

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      child: BlocListener<VerifyPhoneCubit, VerifyPhoneState>(
        bloc: _verifyPhoneCubit,
        listener: (context, state) {
          if (state is VerifyPhoneLoading) {
            TdLoader.show(context);
          } else {
            TdLoader.hide();
          }

          if (state is FailedToSendOTPState) {
            Toast.error(state.errorMessage, context);
          }

          if (state is SentOTPState) {
            context.pushNamed(
              ValidateOTPPath,
              extra: VerifyPhoneArgs(
                otpParams: state.params,
                pageType: AuthPageType.resetPinBeforeLogin,
              ),
            );
          }
        },
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            elevation: 0,
            leading: widget.args.fromPinReset! ? null : BackButton(),
          ),
          body: Column(
            children: [
              Expanded(
                child: DecoratedBox(
                  decoration: BoxDecoration(),
                  child: CustomScrollView(
                    shrinkWrap: true,
                    slivers: [
                      SliverToBoxAdapter(
                        child: Column(
                          children: [
                            Text(
                              'Hi ${widget.args.businessName ?? ''}',
                              style: KTextStyle.bodyText2.copyWith(
                                fontSize: 28,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              'Enter your PIN to login',
                              style: KTextStyle.bodyText2.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(context).hintColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: defaultHorizontalContentPadding),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const YMargin(40),
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            bottom:
                                                defaultVerticalContentPaddingLarge),
                                        child: IgnorePointer(
                                          ignoring: true,
                                          child: KCodeInput(
                                            controller: _passcodeCtrl,
                                            length: length,
                                            builder:
                                                CodeInputBuilders.darkRectangle(
                                              context: context,
                                              emptySize: Size(40.0, 50.0),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: KKeyPad(
                                        activatePeriod: false,
                                        onKeyTap: _addToText,
                                        onRemoveTap: _removeLastDigit,
                                        disabled: false,
                                      ),
                                    ),
                                    const YMargin(20),
                                    Center(
                                      child: Builder(
                                        builder: (ctx) {
                                          return InkWell(
                                            onTap: () async {
                                              await OtpMethods.show(
                                                ctx,
                                                title: 'Reset your PIN',
                                                subtitle:
                                                    'Select an option to initiate a PIN reset',
                                                phoneNumber:
                                                    widget.args.phoneNumber,
                                                handleTap: (mode) {
                                                  Navigator.pop(context);
                                                  if (mode ==
                                                          PhoneAuthMode.SMS ||
                                                      mode ==
                                                          PhoneAuthMode
                                                              .WhatsApp) {
                                                    _verifyPhoneCubit.sendOTP(
                                                      SendOTParams(
                                                        mode: mode!,
                                                        phoneNumber: widget
                                                            .args.phoneNumber,
                                                        url: '${config
                                                                .firebaseServiceUrl!}/$SEND_OTP_PATH',
                                                      ),
                                                    );

                                                    //Used when routing to pin-login after pin-reset
                                                    SharedPreferences
                                                            .getInstance()
                                                        .then((sp) {
                                                      sp.setString(
                                                          Keys.outletBusinessName,
                                                          '${widget.args.businessName}');
                                                    });
                                                  }
                                                },
                                              );
                                            },
                                            child: Text(
                                              'Forgot your PIN?',
                                              style:
                                                  KTextStyle.bodyText2.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const YMargin(40),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: SafeAreaWrap(
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: defaultHorizontalContentPadding,
                        vertical: defaultVerticalContentPadding),
                    child: KButtonPrimary(
                      text: 'Continue',
                      onTap: canContinue ? handleLogin : null,
                      disabled: !canContinue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }

    if (_passcodeCtrl.text.length == length) {
      setState(() {
        canContinue = true;
      });
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }

    if (canContinue) {
      setState(() {
        canContinue = false;
      });
    }
  }

  Future<void> handleLogin() async {
    final pin = _passcodeCtrl.text;

    // to prevent splash screen listener from immediately routing to homePage on PWA when userStream has data
    SharedPreferences.getInstance().then(
      (sp) {
        sp.setBool(Keys.skipLoginWebListener, true);
        // Remove saved businessName during pin-reset
        sp.remove(Keys.outletBusinessName);
      },
    );

    TdLoader.show(context);

    final visitorId = await getVisitorId();

    final res = await locator.get<LoginWithPin>().call(
          LoginPinParams(
            phone: widget.args.phoneNumber,
            pin: pin,
            visitorId: visitorId,
          ),
        );

    res.when(
      success: (res) async {
        if (res.type == LoginResponseType.user) {
          UserCubit.instance?.updatingUser(res.user!);

          final sp = await SharedPreferences.getInstance();
          sp.remove(Keys.resetPin);
          sp.remove(Keys.createPin);

          if (!migrated) {
            sp.setBool(Keys.migratePin, true);
            sp.setString(Keys.oldPin, pin);
            context.pushNamed(ChangePinPath,
                extra: ChangePinArgs(
                  ChangePinType.migrate,
                  oldPin: pin,
                  migrated: true,
                ));
          } else {
            await goHomeMobile(context);
          }
        } else {
          VisitorWidget.show(
              context, res.visitor!, widget.args.phoneNumber, pin, false);
        }

        TdLoader.hide();
      },
      apiFailure: (e, code) {
        setState(() {
          // clear pin inputted
          _passcodeCtrl.text = '';
          canContinue = false;
        });

        TdLoader.hide();
        final msg = ApiExceptions.getErrorMessage(e);
        Toast.error(msg, context,
            customAction: code == 1013
                ? CustomAction("Contact Support",
                    (BuildContext context) => _intercom(context))
                : null);
      },
    );
  }

  Future<void> _intercom(BuildContext context) async {
    await Intercom.instance.initialize(
      config.intercomAppId!,
      androidApiKey: config.intercomAndroidApiKey,
      iosApiKey: config.intercomIosApiKey,
    );
    await Intercom.instance.loginIdentifiedUser(userId: widget.args.userId);
    await Intercom.instance.updateUser(
      name: widget.args.businessName,
      phone: widget.args.phoneNumber,
      company: widget.args.businessName,
      customAttributes: {
        "retailOutletId": widget.args.retailOutletId,
        "phoneNumber": widget.args.phoneNumber,
      },
    );
    _displayIntercom();
  }

  void _displayIntercom() async {
    try {
      await Intercom.instance.displayMessenger();
    } catch (e) {
      ErrorHandler.report(
        e,
        StackTrace.current,
        hint: {
          'info': 'in app intercom error (displayMessenger)',
        },
      );
    }
  }
}
