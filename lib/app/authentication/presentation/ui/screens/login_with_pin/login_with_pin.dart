import 'package:flutter/material.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import 'mobile/mobile_login_with_pin.dart';

class LoginWithPinScreen extends StatelessWidget {
  const LoginWithPinScreen({
    super.key,
    required this.args,
  });

  final LoginWithPinArgs args;

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      mediumScreen: MobileLoginWithPinScreen(
        args: args,
      ),
      smallScreen: MobileLoginWithPinScreen(
        args: args,
      ),
    );
  }
}

class LoginWithPinArgs {
  const LoginWithPinArgs(
      {required this.phoneNumber,
      this.businessName,
      this.fromPinReset = false,
      this.migrated = false,
      this.userId,
      this.retailOutletId});

  final String phoneNumber;
  final String? businessName;
  final bool? fromPinReset;
  final bool? migrated;
  final String? userId;
  final String? retailOutletId;
}
