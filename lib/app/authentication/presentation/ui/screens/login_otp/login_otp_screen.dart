import 'package:flutter/cupertino.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/presentation/ui/screens/login_otp/mobile/mobile_login_otp-screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/login_otp/web/web_login_otp_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class LoginOtpScreen extends StatelessWidget {
  final AuthPageType? pageType;

  const LoginOtpScreen({super.key, this.pageType});
  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebLoginOtpScreen(),
      smallScreen: MobileLoginOtpScreen(
        pageType: pageType,
      ),
      mediumScreen: MobileLoginOtpScreen(
        pageType: pageType,
      ),
    );
  }
}
