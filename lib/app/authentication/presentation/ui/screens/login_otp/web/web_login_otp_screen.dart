import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/validate_phone_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:td_flutter_core/config/DI/di.dart';

import '../../../../../../../src/components/src/utils/values.dart';

class WebLoginOtpScreen extends StatefulWidget {
  const WebLoginOtpScreen({super.key});

  @override
  _LoginOtpScreenState createState() => _LoginOtpScreenState();
}

class _LoginOtpScreenState extends State<WebLoginOtpScreen> {
  late final SendOTParams params;
  late final VerifyPhoneCubit _bloc;

  bool _init = false;

  @override
  void initState() {
    super.initState();

    _bloc = VerifyPhoneCubit(locator(), locator());
    params = SendOTParams(
      mode: PhoneAuthMode.WhatsApp,
      phoneNumber: context.read<UserCubit>().currentUser?.phoneNumber ?? '',
      url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_init) return;

    _init = true;

    if (params.phoneNumber!.isEmpty) {
      // Return to onBoarding screen if the user is not logged in or phone number is empty.
      Future.microtask(() {
        context.goNamed(OnBoardPath);
      });
      return;
    }

    _bloc.sendOTP(params);
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VerifyPhoneCubit, VerifyPhoneState>(
      bloc: _bloc,
      listenWhen: (prev, current) =>
          current is FailedToSendOTPState ? true : false,
      listener: (context, state) {
        if (state is FailedToSendOTPState) {
          Toast.error(state.errorMessage, context);
        }
      },
      child: ValidatePhoneScreen(
        params: params,
        pageType: AuthPageType.proceedLogin,
        countryCode: context.read<UserCubit>().currentOutlet?.country ?? '',
      ),
    );
  }
}
