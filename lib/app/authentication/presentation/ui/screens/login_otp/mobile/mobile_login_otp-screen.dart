import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:td_flutter_core/config/DI/di.dart';

import '../../verify_phone/verify_phone_screen.dart';

class MobileLoginOtpScreen extends StatefulWidget {
  final AuthPageType? pageType;
  const MobileLoginOtpScreen({super.key, this.pageType});

  @override
  _LoginOtpScreenState createState() => _LoginOtpScreenState();
}

class _LoginOtpScreenState extends State<MobileLoginOtpScreen> {
  late final SendOTParams params;
  late final VerifyPhoneCubit _bloc;

  bool _init = false;

  @override
  void initState() {
    super.initState();

    _bloc = VerifyPhoneCubit(locator(), locator());
    params = SendOTParams(
      mode: PhoneAuthMode.WhatsApp,
      phoneNumber: context.read<UserCubit>().currentUser?.phoneNumber ?? '',
      url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_init) return;

    _init = true;

    if (params.phoneNumber!.isEmpty) {
      // Return to onBoarding screen if the user is not logged in or phone number is empty.
      Future.microtask(() {
        context.goNamed(OnBoardPath);
      });
      return;
    }

    _bloc.sendOTP(params);
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VerifyPhoneCubit, VerifyPhoneState>(
      bloc: _bloc,
      listenWhen: (prev, current) =>
          current is FailedToSendOTPState ? true : false,
      listener: (context, state) => state is FailedToSendOTPState
          ? Toast.error(state.errorMessage, context)
          : () {},
      child: VerifyPhoneScreen(
        args: VerifyPhoneArgs(
          otpParams: params,
          pageType: widget.pageType ?? AuthPageType.proceedLogin,
        ),
        // countryCode: context.read<UserCubit>().currentOutlet?.country ?? '',
      ),
    );
  }
}
