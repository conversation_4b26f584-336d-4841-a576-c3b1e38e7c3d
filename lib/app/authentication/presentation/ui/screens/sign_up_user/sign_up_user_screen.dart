import 'package:flutter/material.dart';
import 'package:shop/app/authentication/presentation/ui/screens/sign_up_user/mobile/mobile_sign_up_user.dart';
import 'package:shop/app/authentication/presentation/ui/screens/sign_up_user/web/web_sign_up_user.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class SignUpUserScreen extends StatelessWidget {
  final SignUpUserArgs args;

  const SignUpUserScreen({
    super.key,
    required this.args,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebSignUpUserScreen(
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
      ),
      mediumScreen: MobileSignUpUserScreen(
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
      ),
      smallScreen: MobileSignUpUserScreen(
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
      ),
    );
  }
}

class SignUpUserArgs {
  final String accessToken;

  /// value can either be a `phoneNumber` or an `email`
  /// depending on the [AppHost].
  final String phoneNumber;
  final String countryCode;

  SignUpUserArgs({
    required this.accessToken,
    required this.phoneNumber,
    required this.countryCode,
  });
}
