import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/ui/screens/sign_up_store/sign_up_store_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_user/sign_up_user_form.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/progress_bar.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/side_bar.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/src/services/branch_service.dart';

class WebSignUpUserScreen extends StatefulWidget {
  final String accessToken;
  final String phoneNumber;
  final String countryCode;

  const WebSignUpUserScreen({
    super.key,
    required this.accessToken,
    required this.phoneNumber,
    required this.countryCode,
  });

  @override
  _SignUpUserScreenState createState() => _SignUpUserScreenState();
}

class _SignUpUserScreenState extends State<WebSignUpUserScreen> {
  final _storeControllers = <String, TdTextController>{};
  final _userControllers = <String, TdTextController>{};
  TdAddress? address;
  bool _loadingLocation = false;
  // bool _loading = false;

  void _registerTextControllers() {
    _storeControllers['name'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _storeControllers['type'] = TdTextController(
      validators: [
        Validators.required(),
      ],
    );

    _storeControllers['address'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    // Optional fields
    _storeControllers['lga'] = TdTextController();
    _storeControllers['landmark'] = TdTextController();

    _userControllers['gender'] = TdTextController(
      validators: [
        Validators.required(),
      ],
    );

    _userControllers['contactAddress'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _userControllers['firstName'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _userControllers['lastName'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _userControllers['referralCode'] = TdTextController();
  }

  @override
  void initState() {
    super.initState();
    _registerTextControllers();
    _checkReferralCode();
  }

  Future<void> _checkReferralCode() async {
    final ref = await BranchService.it.extractReferralCode();
    _userControllers['referralCode']!.controller!.text = ref;
  }

  Widget _submitButton() {
    if (_loadingLocation) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return KButton(
      onPressed: () async {
        if (!_userControllers.validate()) {
          return;
        }

        // extract referrals
        // setState(() {
        //   _loading = true;
        // });

        // final ref = await BranchService.it.extractReferralCode();
        // _userControllers['referralCode']!.controller!.text = ref;
        /*    Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SignUpUserScreen(
                previous: _storeControllers.data(),
                userControllers: _userControllers,
                phoneNumber: widget.phoneNumber,
                accessToken: widget.accessToken,
                address: address,
                countryCode: widget.countryCode,
              ),
            ));*/
        context.pushNamed(
          SignUpStorePath,
          extra: SignUpStoreArgs(
            previous: _userControllers.data(),
            storeControllers: _storeControllers,
            phoneNumber: widget.phoneNumber,
            accessToken: widget.accessToken,
            address: address,
            countryCode: widget.countryCode,
          ),
        );
        // setState(() {
        //   _loading = false;
        // });
      },
      text: 'Continue',
    );
  }

  @override
  Widget build(BuildContext context) => AppScreen(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: DesktopConstrainedBox(
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.zero,
                      bottomRight: Radius.circular(5),
                      bottomLeft: Radius.zero,
                      topRight: Radius.circular(5))),
              alignment: Alignment.center,
              padding: EdgeInsets.zero,
              height: 800,
              width: 1200, //SizeConfig.scaleX(0.57),
              child: Row(
                children: [
                  SideBar(),
                  Expanded(
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 30, horizontal: 50),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ProgressBar(0),
                          Padding(
                            padding: EdgeInsets.only(top: 30),
                            child: SvgPicture.asset(kSvgLogo,
                                width: 100.0,
                                height: 100,
                                alignment: Alignment.centerLeft),
                          ),
                          YSpacing(16),
                          Text("Create your TradeDepot Account",
                              style: KTextStyle.semiBold24),
                          YSpacing(5),
                          Text(
                              "Add your shop name and address to sign up your business and for delivery of your orders.",
                              style: KTextStyle.regular14),
                          YSpacing(30),
                          SignUpUserForm(
                            controllers: _userControllers,
                            countryCode: widget.countryCode,
                            showReferralCode:
                                widget.countryCode.toLowerCase() != 'za',
                            onSetLocation: (data) => address = data,
                            isLoading: _loadingLocation,
                            loading: (value) {
                              setState(() {
                                _loadingLocation = value;
                              });
                            },
                          ),
                          YSpacing(30),
                          Row(
                            children: [
                              Expanded(child: Offstage()),
                              XSpacing(30),
                              Container(),
                              _submitButton()
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}
