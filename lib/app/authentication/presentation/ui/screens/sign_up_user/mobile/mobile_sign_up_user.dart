import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_user/sign_up_user_form.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:safe_insets/index.dart';

import '../../../../../../../route_constants.dart';
import '../../../../../../../src/components/src/buttons/src/k_button_primary.dart';
import '../../../../../../../src/components/src/form/form.dart';
import '../../../../../../../src/services/branch_service.dart';
import '../../sign_up_store/sign_up_store_screen.dart';

class MobileSignUpUserScreen extends StatefulWidget {
  final String accessToken;

  /// value can either be a `phoneNumber` or an `email`
  /// depending on the [AppHost].
  final String phoneNumber;
  final String countryCode;

  const MobileSignUpUserScreen({
    super.key,
    required this.accessToken,
    required this.phoneNumber,
    required this.countryCode,
  });

  @override
  _SignUpUserScreenState createState() => _SignUpUserScreenState();
}

class _SignUpUserScreenState extends State<MobileSignUpUserScreen> {
  final _userControllers = <String, TdTextController>{};
  final _storeControllers = <String, TdTextController>{};
  TdAddress? address;
  bool _loadingLocation = false;
  // bool _loading = false;

  @override
  void initState() {
    _registerTextControllers();
    _checkReferralCode();
    super.initState();
  }

  Future<void> _checkReferralCode() async {
    final ref = await BranchService.it.extractReferralCode();
    _userControllers['referralCode']!.controller!.text = ref;
  }

  @override
  Widget build(BuildContext context) {
    return _build(context);
  }

  Widget _build(BuildContext context) {
    return AppScreen(
      child: Scaffold(
        appBar: ShopAppBar.shopAppBar(
          context,
          leading: SizedBox.shrink(),
          actions: [
            IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.clear_rounded),
              iconSize: 30,
              color: const Color.fromRGBO(18, 34, 66, 1),
            )
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTop(),
                YMargin(20),
                Padding(
                  padding: context.insetsSymetric(
                    horizontal: defaultHorizontalContentPadding,
                  ),
                  child: Center(
                    child: SignUpUserForm(
                      controllers: _userControllers,
                      countryCode: widget.countryCode,
                      showReferralCode:
                          widget.countryCode.toLowerCase() != 'za',
                      onSetLocation: (data) => address = data,
                      isLoading: _loadingLocation,
                      loading: (value) {
                        setState(() {
                          _loadingLocation = value;
                        });
                      },
                    ),
                  ),
                ),
                YMarginScale(0.02),
                Padding(
                  padding: context.insetsOnly(
                    left: defaultHorizontalContentPadding,
                    right: defaultHorizontalContentPadding,
                    bottom: defaultVerticalContentPaddingLarge,
                    top: defaultVerticalContentPaddingLarge,
                  ),
                  child: SafeAreaWrap(
                    Center(
                      child: KButtonPrimary(
                        text: 'Continue',
                        onTap: handleSubmit,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> handleSubmit() async {
    if (!_userControllers.validate()) {
      return;
    }

    // extract referrals
    // setState(() {
    //   _loading = true;
    // });

    context.pushNamed(
      SignUpStorePath,
      extra: SignUpStoreArgs(
        previous: _userControllers.data(),
        storeControllers: _storeControllers,
        phoneNumber: widget.phoneNumber,
        accessToken: widget.accessToken,
        address: address,
        countryCode: widget.countryCode,
      ),
    );

    // setState(() {
    //   _loading = false;
    // });
  }

  Widget _buildTop() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Align(
        //   alignment: Alignment.topRight,
        //   child: IconButton(
        //     onPressed: () => context.pop(),
        //     icon: const Icon(Icons.clear_rounded),
        //     iconSize: 30,
        //     color: const Color.fromRGBO(18, 34, 66, 1),
        //   ),
        // ),
        // YMargin(20),
        Padding(
          padding: context.insetsSymetric(
            horizontal: defaultHorizontalContentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Personal Details",
                style: KTextStyle.bodyText2.copyWith(
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                ),
              ),
              YMargin(20),
              Text(
                'Enter your personal details as in your official documents',
                style: KTextStyle.bodyText2.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).hintColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _registerTextControllers() async {
    final userCubit = context.read<UserCubit>();
    final shopTopUpApp = userCubit.domain == AppHost.shopTopUp;

    _userControllers['firstName'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _userControllers['lastName'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _userControllers['gender'] = TdTextController(
      validators: [
        Validators.required(),
      ],
    );

    if (shopTopUpApp) {
      _userControllers['contactAddress'] = TdTextController(
        validators: [
          if (shopTopUpApp) Validators.required(),
          Validators.min(3),
        ],
      );
    }

    _userControllers['referralCode'] = TdTextController();

    _storeControllers['name'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    _storeControllers['type'] = TdTextController(
      validators: [
        Validators.required(),
      ],
    );

    _storeControllers['address'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(3),
      ],
    );

    // Optional fields
    _storeControllers['lga'] = TdTextController();
    _storeControllers['landmark'] = TdTextController();
  }
}
