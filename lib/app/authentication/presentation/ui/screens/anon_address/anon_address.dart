// import 'package:flutter/material.dart';
// import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:shop/app/account_settings/presentation/ui/widget/permission_dialog.dart';
// import 'package:shop/app/authentication/data/models/anonymous_user.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
// import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/src/form/src/td_form_field.dart';
// import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
// import 'package:shop/src/res/assets/svgs/svgs.dart';
// import 'package:shop/src/services/location_service.dart';
// import 'package:shop/app/authentication/data/models/td_address.dart';
// import 'package:shop/app_config.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/autocomplete/td_autocomplete.dart'
//     as Td;
// import 'package:geolocator/geolocator.dart';
// import 'package:rxdart/rxdart.dart';
// import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
// import 'package:shop/src/components/src/widgets/margin.dart';
// import 'package:shop/src/res/values/colors/colors.dart';

// class AnonAddress extends StatefulWidget {
//   const AnonAddress();

//   @override
//   State<StatefulWidget> createState() {
//     return _AnonAddress();
//   }
// }

// class _AnonAddress extends State<AnonAddress> {
//   final ValueNotifier<(List<AutocompletePrediction>, bool)> addressState =
//       ValueNotifier(([], false));
//   TdAddress? selectedAddress;

//   // AutocompletePrediction? selectedPrediction;
//   final _queryBehavior = BehaviorSubject<String>.seeded("");
//   final FlutterGooglePlacesSdk _places =
//       FlutterGooglePlacesSdk(config.googleMapsApiKey!, locale: Locale('en'))
//         ..isInitialized().then((value) {
//           debugPrint('Places Initialized: $value');
//         });
//   List<PlaceTypeFilter> _placeTypesFilter = [PlaceTypeFilter.ADDRESS];
//   List<PlaceField> _placeFields = [
//     PlaceField.Address,
//     PlaceField.AddressComponents,
//     PlaceField.BusinessStatus,
//     PlaceField.Id,
//     PlaceField.Location,
//   ];
//   Position? position;

//   @override
//   void initState() {
//     _queryBehavior.stream
//         .debounce((_) => TimerStream(true, const Duration(milliseconds: 300)))
//         .listen(handleSearch);
//     super.initState();
//   }

//   void _pop() {
//     if (context.canPop()) {
//       context.pop();
//     } else {
//       context.goNamed(OnBoardPath);
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return PopScope(
//       onPopInvoked: (value) {
//         _pop();
//       },
//       child: Scaffold(
//         appBar: ShopAppBar.shopAppBar(
//           context,
//           title: 'Location',
//           leading: BackButton(
//             onPressed: _pop,
//           ),
//         ),
//         body: Padding(
//           child: ValueListenableBuilder(
//             valueListenable: addressState,
//             builder: (context, state, child) {
//               return Column(
//                 children: [
//                   Expanded(
//                     child: Column(children: [
//                       TdFormField(
//                           widget: Td.Autocomplete(
//                             displayStringForOption: (prediction) =>
//                                 prediction.fullText,
//                             optionsBuilder:
//                                 (TextEditingValue textEditingValue) {
//                               if (textEditingValue.text == '') {
//                                 return const Iterable<
//                                     AutocompletePrediction>.empty();
//                               } else {
//                                 return state.$1;
//                               }
//                             },
//                             onSelected: (AutocompletePrediction selection) {
//                               fetchAddresses(selection.placeId);
//                             },
//                             fieldViewBuilder:
//                                 (context, controller, node, func) {
//                               return TextField(
//                                 autofocus: true,
//                                 style: textTheme.bodyLarge
//                                     ?.copyWith(fontWeight: FontWeight.w400),
//                                 decoration: InputDecoration(
//                                   hintText: 'Enter your store address',
//                                   hintStyle: textTheme.bodyLarge?.copyWith(
//                                       color: k717A8E,
//                                       fontWeight: FontWeight.w400),
//                                   suffixIcon: state.$2
//                                       ? CircularProgressIndicator(
//                                           strokeWidth: 2)
//                                       : InkWell(
//                                           child: SvgPicture.asset(
//                                             kSvgGps,
//                                             color: Theme.of(context)
//                                                 .colorScheme
//                                                 .primary,
//                                           ),
//                                           onTap: () async {
//                                             addressState.value = ([], true);
//                                             position = await LocationService
//                                                 .getCurrentLocation();
//                                             if (position != null) {
//                                               selectedAddress =
//                                                   await LocationService
//                                                       .addressFromLocation(
//                                                           position!);
//                                               controller.text =
//                                                   selectedAddress!.address!;
//                                             }
//                                             addressState.value = ([], false);
//                                             final permission = await Geolocator
//                                                 .checkPermission();
//                                             if (position == null &&
//                                                 (permission ==
//                                                         LocationPermission
//                                                             .deniedForever ||
//                                                     permission ==
//                                                         LocationPermission
//                                                             .denied)) {
//                                               PermissionDialog.display(
//                                                   context, "Location");
//                                             }
//                                           },
//                                         ),
//                                   suffixIconConstraints: BoxConstraints.tight(
//                                     Size(18, 18),
//                                   ),
//                                   errorMaxLines: 2,
//                                 ),
//                                 controller: controller,
//                                 focusNode: node,
//                                 onChanged: (input) {
//                                   if (input.isEmpty) {
//                                     addressState.value = ([], false);
//                                     selectedAddress = null;
//                                   } else {
//                                     _queryBehavior.add(input);
//                                   }
//                                   /*  if (input.length == 1) {
//                                 //   autoCompleteState.value = (state.$1, false, false); todo error
//                               }*/
//                                 },
//                               );
//                             },
//                             optionsMaxWidth:
//                                 MediaQuery.of(context).size.width - 40,
//                             optionsMaxHeight: 200,
//                           ),
//                           label: "Store address"),
//                       child!,
//                       YSpacing(60),
//                     ]),
//                   ),
//                   KButtonPrimary(
//                       text: "Continue",
//                       onTap: complete,
//                       disabled: selectedAddress == null),
//                 ],
//               );
//             },
//             child: Spacer(),
//           ),
//           padding: EdgeInsets.all(20),
//         ),
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     _queryBehavior.close();
//     addressState.dispose();
//     super.dispose();
//   }

//   Future<void> complete() async {
//     try {
//       TdLoader.show(context);
//       final user = await loginAnonymously();
//       await UserCubit.instance?.updatingAnonymousUser(
//           AnonymousUser(id: user.uid, address: selectedAddress!));
//       goHomeMobile(context);
//       TdLoader.hide();
//     } catch (e) {
//       TdLoader.hide();
//       Toast.error('$e', context);
//     }
//   }

//   void handleSearch(String query) async {
//     if (query.isNotEmpty) {
//       try {
//         addressState.value = (addressState.value.$1, true);
//         selectedAddress = null;
//         final result = await _places.findAutocompletePredictions(
//           query,
//           countries: null,
//           placeTypesFilter: _placeTypesFilter,
//           newSessionToken: false,
//           origin: position == null
//               ? null
//               : LatLng(lat: position!.latitude, lng: position!.longitude),
//         );

//         addressState.value = (result.predictions, false);
//       } catch (error) {
//         addressState.value = (addressState.value.$1, false);
//         Toast.error(error.toString(), context);
//       }
//     }
//   }

//   void fetchAddresses(String placeId) async {
//     try {
//       addressState.value = (addressState.value.$1, true);
//       selectedAddress = null;
//       final result = await _places.fetchPlace(placeId, fields: _placeFields);
//       selectedAddress = TdAddress.fromPlace(result.place!);
//       if (selectedAddress!.hasNullValues) {
//         selectedAddress = await LocationService.addressFromPlaceId(placeId);
//       }
//       addressState.value = (addressState.value.$1, false);
//     } catch (error) {
//       addressState.value = (addressState.value.$1, false);
//       Toast.error(error.toString(), context);
//     }
//   }
// }
