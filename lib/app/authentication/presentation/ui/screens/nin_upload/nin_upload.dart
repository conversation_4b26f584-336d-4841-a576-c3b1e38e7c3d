// import 'dart:convert';
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'package:shop/src/components/src/widgets/app_screen.dart';
// import 'package:td_flutter_src/scaler/src/margin.dart';

// import '../../../../../../src/components/src/buttons/src/k_button_primary.dart';
// import '../../../../../../src/res/assets/svgs/svgs.dart';
// import '../../../../../../src/res/values/colors/colors.dart';
// import '../../../../../../src/res/values/config/keys.dart';
// import '../../../logic/utils/methods.dart';

// class NinUploadArgs {
//   final AuthPageType type;
//   NinUploadArgs(this.type);
// }

// class NinUploadScreen extends StatefulWidget {
//   const NinUploadScreen(this.type, {Key? key}) : super(key: key);
//   final AuthPageType type;
//   @override
//   State<NinUploadScreen> createState() => _NinUploadScreenState();
// }

// class _NinUploadScreenState extends State<NinUploadScreen> {
//   File? _imageFile;
//   String? _base64Image;
//   String? _imageName;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return AppScreen(
//       child: Scaffold(
//         appBar: AppBar(
//           elevation: 1,
//           backgroundColor: kColorWhite,
//           centerTitle: true,
//           title: Text(
//             _imageName ?? '',
//             style: textTheme.headlineSmall?.copyWith(fontSize: 18),
//           ),
//           leading: InkWell(
//             onTap: () {
//               context.pop();
//             },
//             child: Icon(
//               Icons.arrow_back,
//               color: kColorBlack,
//             ),
//           ),
//         ),
//         body: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 18),
//           child: CustomScrollView(
//             slivers: [
//               SliverToBoxAdapter(
//                 child: const YMargin(60),
//               ),
//               SliverToBoxAdapter(
//                   child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 40),
//                 child: InkWell(
//                   onTap: _openImagePicker,
//                   child: Container(
//                     width: 40,
//                     height: 240,
//                     decoration: BoxDecoration(
//                       border: Border.all(color: kE7E7E7),
//                       borderRadius: BorderRadius.all(Radius.circular(8)),
//                     ),
//                     alignment: Alignment.center,
//                     child: _imageFile != null
//                         ? Image.file(
//                             _imageFile!,
//                             fit: BoxFit.cover,
//                           )
//                         : Column(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               SvgPicture.asset(kSvgUploadIcon),
//                               const YMargin(10),
//                               Text(
//                                 'Upload the front of your NIN \ndocument',
//                                 textAlign: TextAlign.center,
//                                 style: textTheme.bodyLarge
//                                     ?.copyWith(color: kColorBlue),
//                               ),
//                             ],
//                           ),
//                   ),
//                 ),
//               )),
//               SliverFillRemaining(
//                 hasScrollBody: false,
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.end,
//                   children: [
//                     Text(
//                       'Ensure that [number of] image(s) are clear and the text on the document is clearly visible',
//                       textAlign: TextAlign.center,
//                       style: textTheme.bodyLarge?.copyWith(color: k717A8E),
//                     ),
//                     const YMargin(80),
//                     KButtonPrimary(
//                       text: 'Submit',
//                       onTap: _uploadNin,
//                     ),
//                     const YMargin(10),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   void _openImagePicker() async {
//     final pickedFile =
//         await ImagePicker().pickImage(source: ImageSource.gallery);

//     if (pickedFile != null) {
//       setState(() {
//         _imageFile = File(pickedFile.path);
//         _imageName = pickedFile.path.split('/').last;
//         _base64Image = 'data:image/jpeg;base64,' +
//             base64Encode(_imageFile!.readAsBytesSync());
//       });

//       _uploadNin();
//     }
//   }

//   void nextAction() {
//     SharedPreferences.getInstance().then(
//       (sp) => sp.remove(Keys.ninValidation),
//     );

//     if (widget.type == AuthPageType.verifyKycNin) {
//       Navigator.of(context)
//         ..pop()
//         ..pop();
//     }
//     if (widget.type == AuthPageType.verifySignUpNin) {
//       goHomeMobile(context, true);
//     }
//   }

//   void _uploadNin() {
//     print('Base64 Image: $_base64Image');
//   }
// }
