import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_phone/verify_phone_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/td_phone_number_field.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/config.dart';

import '../../login_with_pin/login_with_pin.dart';

class WebGetStartedScreen extends StatefulWidget {
  // final AuthPageType pageType;
  // final Widget? footer;
  // final String title;
  // final String subtitle;
  // final String? info;
  // final String? forgotPhoneNumber;

  const WebGetStartedScreen({
    super.key,
    // required this.pageType,
    // required this.title,
    // required this.subtitle,
    // this.info,
    // this.forgotPhoneNumber,
    // this.footer,
  });

  @override
  _VerifyPhoneScreenState createState() => _VerifyPhoneScreenState();
}

class _VerifyPhoneScreenState extends State<WebGetStartedScreen> {
  final _authPageType = ValueNotifier<AuthPageType>(AuthPageType.signUp);
  final _verifyPhoneCubit = VerifyPhoneCubit(locator(), locator());
  bool? fullButtons;
  bool hasDefaultPhone = false;

  final Map<String, TdTextController> controller = {
    'phone': TdTextController(
      validators: [Validators.required()],
    ),
  };

  // @override
  // void initState() {
  //   super.initState();
  //   _loadCountries();
  //   _setPageType();
  //   _controller.addListener(() {
  //     // clear error message on edit
  //     if (_controller.text.isNotEmpty && errorMessage != null) {
  //       setState(() {
  //         errorMessage = null;
  //       });
  //     }
  //   });
  // }

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();

  //   if (widget.forgotPhoneNumber != null && fullButtons == null) {
  //     final phone = widget.forgotPhoneNumber;

  //     final countries = _countries.where(
  //       (country) => phone!.contains(country.phoneCode),
  //     );

  //     if (countries.length > 0) {
  //       _selectedCountry = countries.first;
  //       fullButtons = true;
  //       hasDefaultPhone = true;
  //       // extract normal phone
  //       _controller.text = phone!
  //           .replaceFirst('+${_selectedCountry!.phoneCode}', '')
  //           .replaceAll(' ', '');
  //     }
  //   }
  // }

  // void _loadCountries([String? phoneNumber]) {
  //   _countries =
  //       countriesWithPhoneCode.map((e) => mapCountryWithPhoneCode(e)).toList();

  //   if (phoneNumber != null) {
  //     final picked =
  //         _countries.where((e) => phoneNumber.contains(e.phoneCode)).first;
  //     _selectedCountry = picked;
  //     if (mounted) {
  //       setState(() {
  //         _selectedCountry = picked;
  //       });
  //     }
  //   } else {
  //     _selectedCountry = _countries.first;
  //     if (mounted) {
  //       setState(() {
  //         _selectedCountry = _countries.first;
  //       });
  //     }
  //   }

  //   _loading.value = false;
  // }

  // void verifyPhone(PhoneAuthMode mode) async {
  //   final phone = await _validatePhone();

  //   if (phone == null) return;

  //   if (_pageType == AuthPageType.signUp) {
  //     final res = await _verifyPhoneCubit.checkPhone(CheckPhoneParams(phone));
  //     if (res == null) return;

  //     if (res.hasRetailStore) {
  //       // switch to login flow
  //       _pageType = AuthPageType.login;

  //       if (res.pinEnabled!) {
  //         //TODO:rev Implement web loginWithPin and test
  //         context.pushNamed(
  //           LoginWithPinPath,
  //           extra: LoginWithPinArgs(
  //             phoneNumber: phone,
  //             businessName: res.businessName,
  //           ),
  //         );
  //         // context.pushNamed(
  //         //   LoginEnterPinPath,
  //         //   extra: EnterPinScreenArgs(
  //         //     phone: phone,
  //         //     name: res.businessName,
  //         //   ),
  //         // );
  //         _setPageType();
  //         return;
  //       }
  //       // else: Proceed to send otp, then login
  //     }
  //   }

  //   _verifyPhoneCubit.sendOTP(
  //     SendOTParams(
  //       mode: mode,
  //       phoneNumber: phone,
  //       url: config.firebaseServiceUrl! + '/' + SEND_OTP_PATH,
  //     ),
  //   );
  // }

  Future<void> verifyPhone(BuildContext context) async {
    if (!controller.validate()) return;

    final phone = controller.data()['phone'];

    final res =
        await _verifyPhoneCubit.checkPhone(CheckPhoneParams(phone: phone));
    if (res == null) return;

    // reset to default to prevent using previously set value
    _authPageType.value = AuthPageType.signUp;

    if (res.hasRetailStore) {
      _authPageType.value = AuthPageType.login;

      if (res.pinEnabled == true) {
        context.pushNamed(
          LoginWithPinPath,
          extra: LoginWithPinArgs(
            phoneNumber: phone,
            businessName: res.businessName,
            migrated: res.migrated,
          ),
        );
        return;
      }
    }

    _verifyPhoneCubit.sendOTP(
      SendOTParams(
        mode: PhoneAuthMode.WhatsApp,
        phoneNumber: phone,
        url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
      ),
    );
  }

  // Future<String?> _validatePhone() async {
  //   // validate
  //   if (_controller.text.isEmpty) {
  //     setState(() {
  //       errorMessage = 'Enter your phone number';
  //     });
  //     return null;
  //   }

  //   String text = _controller.text;
  //   if (text.startsWith('0')) {
  //     text = text.substring(1);
  //   }
  //   final phone = '+' + _selectedCountry!.phoneCode + text;
  //   bool isValid = true;
  //   try {
  //     await FlutterLibphonenumber().parse(phone);
  //   } catch (e) {
  //     isValid = false;
  //   }

  //   // Additional validation for SA numbers
  //   if (isValid && _selectedCountry!.countryCode == 'ZA') {
  //     // check length
  //     if (text.replaceAll(' ', '').length != 9) {
  //       isValid = false;
  //     }
  //   }

  //   if (!isValid) {
  //     setState(() {
  //       errorMessage = 'Invalid phone number';
  //     });
  //     return null;
  //   }

  //   return phone.replaceAll(' ', '');
  // }

  // List<DropdownMenuItem<CountryWithPhoneCode>> _mapCountryToDropdownItem(
  //   List<CountryWithPhoneCode> countries,
  // ) {
  //   return countries
  //       .map(
  //         (country) => DropdownMenuItem<CountryWithPhoneCode>(
  //           value: country,
  //           child: Image.asset(
  //             country.flagUri(country.countryCode),
  //             width: 20,
  //           ),
  //         ),
  //       )
  //       .toList();
  // }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<VerifyPhoneCubit, VerifyPhoneState>(
      bloc: _verifyPhoneCubit,
      builder: (context, state) => _build(context, state),
      listener: (_, state) {
        if (state is VerifyPhoneLoading) {
          TdLoader.show(context);
        } else {
          TdLoader.hide();
        }

        if (state is FailedToSendOTPState) {
          Toast.error(state.errorMessage, context);
        }

        if (state is SentOTPState) {
          context.pushNamed(
            WebValidateOTPPath,
            extra: VerifyPhoneArgs(
              otpParams: state.params,
              pageType: _authPageType.value,
            ),
          );

          // track signUp-initiated event
          Segment.track(
            eventName: SegmentEvents.accountCreationInitiated,
            properties: {
              'phone_number': state.params.phoneNumber,
              'otp_mode': state.params.mode.name,
            },
          );

          // reset value to signUp
          _authPageType.value = AuthPageType.signUp;
        }
      },
    );
  }

  Widget _build(BuildContext context, VerifyPhoneState state) {
    return AppScreen(
      child: Scaffold(
        body: DesktopConstrainedBox(
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(5))),
            alignment: Alignment.center,
            padding: EdgeInsets.only(
                top: 30, bottom: 10.0, left: 120.0, right: 120.0),
            height: 800,
            width: 892, //SizeConfig.scaleX(0.57),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                YSpacing(50),
                _buildTop(state),
                YSpacing(30),
                // hide footer when keyboard is up
                // if (MediaQuery.of(context).viewInsets.bottom < 10)
                _buildCheckButton(state),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Widget _buildFooter(VerifyPhoneState state) {
  //   ///RESET PASSWORD
  //   // if ((widget.forgotPhoneNumber?.isNotEmpty ?? false) &&
  //   //     _pageType == AuthPageType.Login) return _buildResetButton(state);

  //   ///LOGIN
  //   if (fullButtons == null && _authPageType.value == AuthPageType.login) {
  //     return _buildCheckButton(state);
  //   }

  //   ///FOR SIGN UP
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(vertical: 50),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.center,
  //       children: [
  //         KButton(
  //           text: 'Continue',
  //           width: 400,
  //           isLoading: state is VerifyPhoneLoading,
  //           onPressed: () {
  //             verifyPhone(context);
  //           },
  //         ),
  //         YSpacing(50),
  //         // Info(widget.pageType),
  //         YSpacing(30),
  //         // if (widget.footer != null) widget.footer!,
  //       ],
  //     ),
  //   );
  // }

  ///FOR LOGIN
  Widget _buildCheckButton(VerifyPhoneState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 50),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          KButton(
            text: 'Continue',
            width: 400,
            isLoading: state is VerifyPhoneLoading,
            onPressed: () => verifyPhone(context),
          ),
          YSpacing(50),
          // Info(widget.pageType),
          // if (widget.footer != null) widget.footer!,
        ],
      ),
    );
  }

  // Widget _buildResetButton(VerifyPhoneState state) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 80),
  //     child: Column(
  //       children: [
  //         KButton(
  //           text: 'Send by SMS',
  //           width: 400,
  //           isLoading: state is VerifyPhoneLoading,
  //           onPressed: () {
  //             verifyPhone(context);
  //           },
  //         ),
  //         YSpacing(10),
  //         KButton(
  //           text: 'Send by Whatsapp',
  //           width: 400,
  //           isLoading: state is VerifyPhoneLoading,
  //           onPressed: () {
  //             verifyPhone(context);
  //           },
  //         ),
  //         // if (widget.footer != null) widget.footer!,
  //       ],
  //     ),
  //   );
  // }

/*  Widget _buildNavButton() {
    return widget.pageType == AuthPageType.SignUp
        ? Column(
            children: [
              Text(AppLocalizations.of(context)!.have_an_account,
                  style: KTextStyle.regular14.copyWith(color: kColorGreyAlt)),
              TdTextButton(
                  label: AppLocalizations.of(context)!.sign_in,
                  onPressed: () =>
                      context.navigator.pushNamedAndReplace(loginPageConfig)),
              SizedBox(height: 40),
            ],
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
          )
        : Column(
            children: [
              Text("New here?",
                  style: KTextStyle.regular14.copyWith(color: kColorGreyAlt)),
              TdTextButton(
                  label: AppLocalizations.of(context)!.sign_up,
                  onPressed: () =>
                      context.navigator.pushNamedAndReplace(signUpPageConfig)),
              SizedBox(height: 40),
            ],
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
          );
  }*/

  Widget _buildTop(VerifyPhoneState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(kSvgShopTopUp,
            width: 100.0, height: 50, alignment: Alignment.centerLeft),
        YSpacing(16),
        Text('Get started', style: KTextStyle.semiBold24),
        YSpacing(15),
        Text('Enter your phone number to continue',
            style: KTextStyle.book14.copyWith(fontSize: 16)),
        YSpacing(30),
        SizedBox(
          width: 400,
          child: TdPhoneNumberField(
            autoValidate: false,
            title: 'Phone Number',
            textController: controller['phone'],
            inputFormatters: [validInput()],
          ),
        ),
        // if (widget.info != null)
        //   Column(
        //     children: [
        //       Text(
        //         widget.info!,
        //         style: KTextStyle.book14.copyWith(color: kColorGreyAlt),
        //         textAlign: TextAlign.center,
        //       ),
        //       YMargin(30),
        //     ],
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //   ),
        // SizedBox(
        //   width: 400,
        //   child: ValueListenableBuilder(
        //     valueListenable: _loading,
        //     builder: (BuildContext context, bool loading, _) {
        //       Widget child = SizedBox.shrink();
        //       if (!loading) {
        //         child = TdFormField(
        //           widget: TextField(
        //             controller: _controller,
        //             readOnly: hasDefaultPhone || state is VerifyPhoneLoading,
        //             keyboardType: TextInputType.number,
        //             style: TextStyle(
        //               fontSize: 14,
        //             ),
        //             inputFormatters: [
        //               LibPhonenumberTextFormatter(
        //                 country: _selectedCountry!,
        //                 phoneNumberType: PhoneNumberType.mobile,
        //                 additionalDigits: 1,
        //                 phoneNumberFormat: PhoneNumberFormat.international,
        //               ),
        //             ],
        //             decoration: InputDecoration(
        //               hintText: _selectedCountry!.phoneMaskMobileNational,
        //               contentPadding: const EdgeInsets.symmetric(
        //                 horizontal: 16,
        //                 vertical: 8,
        //               ),
        //               prefixIcon: Padding(
        //                 padding: const EdgeInsets.only(
        //                   left: 16,
        //                   right: 8,
        //                   bottom: 1.2,
        //                 ),
        //                 child: Row(
        //                   mainAxisSize: MainAxisSize.min,
        //                   children: [
        //                     DropdownButtonHideUnderline(
        //                       child: DropdownButton<CountryWithPhoneCode>(
        //                         value: _selectedCountry,
        //                         items: _mapCountryToDropdownItem(_countries),
        //                         onChanged: (value) {
        //                           if (hasDefaultPhone ||
        //                               state is VerifyPhoneLoading) {
        //                             return;
        //                           } else {
        //                             setState(() {
        //                               _selectedCountry = value;
        //                               errorMessage = null;
        //                             });
        //                           }
        //                         },
        //                       ),
        //                     ),
        //                     const SizedBox(width: 5),
        //                     Container(
        //                       height: 30,
        //                       width: 1,
        //                       decoration: BoxDecoration(
        //                         color: kColorGrey2,
        //                       ),
        //                     ),
        //                     const SizedBox(width: 10),
        //                     Text(
        //                       '+${_selectedCountry!.phoneCode}',
        //                     ),
        //                   ],
        //                 ),
        //               ),
        //               enabledBorder: KTextStyle.outlineInputBorder,
        //               focusedBorder: KTextStyle.outlineInputBorder,
        //               border: KTextStyle.outlineInputBorder,
        //             ),
        //           ),
        //           label: 'Phone number',
        //         );
        //       }

        //       return AnimatedSwitcher(
        //           duration: kThemeAnimationDuration, child: child);
        //     },
        //   ),
        // ),
        // if (errorMessage != null)
        //   Padding(
        //     padding: const EdgeInsets.only(top: 4),
        //     child: Text(
        //       errorMessage!,
        //       style: TextStyle(
        //         color: Colors.red,
        //         fontSize: 12,
        //       ),
        //     ),
        //   ),
      ],
    );
  }

  @override
  void dispose() {
    controller['phone']?.close();
    _verifyPhoneCubit.close();
    super.dispose();
  }
}
