import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/check_phone.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_phone/verify_phone_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/td_phone_number_field.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class MobileGetStartedScreen extends StatefulWidget {
  const MobileGetStartedScreen({super.key, this.getStartedType});

  final GetStartedType? getStartedType;

  @override
  State<MobileGetStartedScreen> createState() => _MobileGetStartedScreenState();
}

class _MobileGetStartedScreenState extends State<MobileGetStartedScreen> {
  final _authPageType = ValueNotifier<AuthPageType>(AuthPageType.signUp);
  late final displayPhoneField =
      (widget.getStartedType == GetStartedType.phone ||
          widget.getStartedType == GetStartedType.updatePhone);
  EmailType? emailType;
  String email = '';
  String phone = '';
  String? storeName;
  bool emailValid = false;

  final Map<String, TdTextController> phoneController = {
    'phone': TdTextController(
      validators: [Validators.required()],
    ),
  };

  final Map<String, TdTextController> emailController = {
    'email': TdTextController(
      validators: [Validators.required(), Validators.email()],
    ),
  };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final subText = 'email address';
    final msg =
        'TradeDepot Wholesale is not available for your region. Please use the ShopTopUp app.';

    return AppScreen(
      child: Scaffold(
        backgroundColor: theme.colorScheme.surface,
        appBar: ShopAppBar.shopAppBar(
          context,
          leading: widget.getStartedType == GetStartedType.updatePhone
              ? SizedBox.shrink()
              : BackButton(
                  onPressed: () {
                    if (GoRouter.of(context).canPop()) {
                      context.pop();
                    } else {
                      context.goNamed(OnBoardPath);
                    }
                  },
                ),
        ),
        body: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 0,
                              horizontal: defaultHorizontalContentPadding),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (displayPhoneField)
                                Text(
                                  'Phone Number',
                                  style: textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                )
                              else
                                Text(
                                  'Get started',
                                  style: textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              const YMargin(10),
                              if (displayPhoneField)
                                Text(
                                  'We will send a verification code to this number',
                                  style: textTheme.bodyLarge?.copyWith(
                                    color: theme.hintColor,
                                  ),
                                )
                              else
                                Text(
                                  'Enter your $subText to continue',
                                  style: textTheme.bodyLarge?.copyWith(
                                    color: theme.hintColor,
                                  ),
                                ),
                              const YMargin(20),
                              if (displayPhoneField)
                                TdPhoneNumberField(
                                  autoValidate: false,
                                  title: 'Phone Number',
                                  keyboardType: TextInputType.phone,
                                  textController: phoneController['phone'],
                                  inputFormatters: [validInput()],
                                  onSubmitted: () {
                                    verifyPhone(context, msg);
                                  },
                                )
                              else
                                TdTextField(
                                  autoFocus: true,
                                  title: 'Email Address',
                                  hint: '<EMAIL>',
                                  keyboardType: TextInputType.emailAddress,
                                  textController: emailController['email'],
                                  onSubmitted: () {
                                    verifyEmail(context, msg);
                                  },
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            SafeArea(
              child: Column(
                children: [
                  Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                                horizontal: defaultHorizontalContentPadding)
                            .copyWith(
                                bottom: defaultVerticalContentPaddingLarge),
                        child: KButtonPrimary(
                            text: 'Continue',
                            onTap: () {
                              if (displayPhoneField) {
                                verifyPhone(context, msg);
                              } else {
                                verifyEmail(context, msg);
                              }
                            }),
                      ),
                      // if (emailValid) const YMarginScale(0.15)
                    ],
                  ),
                  // SafeAreaWrap(
                  //   Container(),
                  // ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> verifyPhone(BuildContext context, String msg) async {
    if (!phoneController.validate()) return;

    final phone = phoneController.data()['phone'];

    FocusScope.of(context).unfocus();

    if (widget.getStartedType == GetStartedType.updatePhone) {
      sendOtpToPhone(phone);
      return;
    }

    final sp = await SharedPreferences.getInstance();
    final email = sp.getString(Keys.tdAppEmail) ?? '';

    TdLoader.show(context);

    final res = await locator
        .get<CheckPhone>()
        .call(CheckPhoneParams(phone: phone, email: email));

    res.when(
      success: (data) {
        TdLoader.hide();

        _authPageType.value = AuthPageType.signUp;

        if (data.hasRetailStore) {
          if (data.emailMerged) {
            emailType = EmailType.merged;
            this.email = email;
            storeName = data.businessName;
          } else if (data.redactedEmail != null) {
            emailType = EmailType.redacted;
            this.email = data.redactedEmail!;
            this.phone = phone;
          } else {
            // for console users trying to access the app
            Toast.error(
              'The phone number is already associated with an account. Please use a different number to sign up',
              title: 'The number already exists',
              context,
              duration: 10,
              customAction:
                  CustomAction("Contact Support", (BuildContext context) async {
                await setupIntercomWithEmail(context, email);
                _displayIntercom(phone, email);
              }),
            );
            return;
          }
        }

        sendOtpToPhone(phone);
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  Future<void> sendOtpToPhone(String phone) async {
    TdLoader.show(context);

    final params = SendOTParams(
      mode: PhoneAuthMode.WhatsApp,
      phoneNumber: phone,
      url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
    );

    final res = await locator.get<SendOTP>().call(params);

    res.when(
      success: (_) {
        try {
          final args0 = VerifyPhoneArgs(
            otpParams: params,
            pageType: AuthPageType.tradeDepot,
            getStartedType: widget.getStartedType,
          );

          final args = switch (emailType) {
            EmailType.merged => args0.copyWith(
                hasRetailStore: true,
                emailFromMergedAccount: email,
                storeName: storeName,
              ),
            EmailType.redacted => args0.copyWith(
                otpParams: SendOTParams(
                  mode: params.mode,
                  phoneNumber: phone,
                  email: email,
                  url: params.url,
                ),
                pageType: AuthPageType.emailRedacted,
              ),
            _ => args0.copyWith(
                pageType: (widget.getStartedType == GetStartedType.phone ||
                        widget.getStartedType == GetStartedType.updatePhone)
                    ? AuthPageType.tradeDepot
                    : _authPageType.value,
              )
          };

          final path = widget.getStartedType == GetStartedType.updatePhone
              ? AuthValidateOTPPath
              : ValidateOTPPath;

          context.pushNamed(path, extra: args.toMap());

          TdLoader.hide();

          _authPageType.value = AuthPageType.signUp;

          try {
            // track signUp-initiated event
            Segment.track(
              eventName: SegmentEvents.accountCreationInitiated,
              properties: {
                'email': params.email,
                'phone_number': params.phoneNumber,
                'otp_mode': params.mode.name,
              },
            );
          } catch (e, s) {
            ErrorHandler.report(e, s);
          }
        } catch (e, s) {
          Toast.error('$e', context);
          ErrorHandler.report(e, s);
        }
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  Future<void> verifyEmail(BuildContext context, String msg) async {
    if (!emailController.validate()) return;

    final String enteredEmail =
        emailController.data()['email'].trim().toString().toLowerCase();

    email = enteredEmail;

    FocusScope.of(context).unfocus();

    TdLoader.show(context);

    final res = await locator
        .get<CheckPhone>()
        .call(CheckPhoneParams(phone: phone, email: email));

    res.when(
      success: (data) {
        TdLoader.hide();

        if (data.retailOutletId != null) {
          context.read<UserCubit>().setDomain(data.domain);
        }

        sendOtpToEmail(email, data.hasRetailStore);
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  Future<void> sendOtpToEmail(String email, bool hasRetailStore) async {
    TdLoader.show(context);

    final params = SendOTParams(
      mode: PhoneAuthMode.Email,
      email: email,
      url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
    );

    final res = await locator.get<SendOTP>().call(params);

    res.when(
      success: (_) {
        emailValid = true;
        context.pushNamed(
          ValidateOTPPath,
          extra: VerifyPhoneArgs(
            otpParams: params,
            pageType: AuthPageType.tradeDepot,
            getStartedType: GetStartedType.email,
            hasRetailStore: hasRetailStore,
          ).toMap(),
        );
        TdLoader.hide();
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  Future<void> _displayIntercom(String phone, String email) async {
    final message =
        'Hello, I am experiencing issues signing up. My phone number, $phone, is reportedly associated with another account. Could you please assist me?';
    try {
      await Intercom.instance.displayMessageComposer(
        message,
      );
    } catch (e) {
      ErrorHandler.report(
        e,
        StackTrace.current,
        hint: {
          'info': 'tradeDepot app sign up',
          'email': email,
          'message': message,
        },
      );
    }
  }

  @override
  void dispose() {
    phoneController['phone']?.close();
    emailController['email']?.close();
    super.dispose();
  }
}

enum EmailType { merged, redacted }
