import 'package:flutter/material.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import 'mobile/mobile_get_started.dart';

class GetStarted extends StatelessWidget {
  final GetStartedType? getStartedType;
  const GetStarted({
    super.key,
    this.getStartedType,
  });
  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: SizedBox.expand(),
      smallScreen: MobileGetStartedScreen(getStartedType: getStartedType),
      mediumScreen: MobileGetStartedScreen(getStartedType: getStartedType),
    );
  }
}
