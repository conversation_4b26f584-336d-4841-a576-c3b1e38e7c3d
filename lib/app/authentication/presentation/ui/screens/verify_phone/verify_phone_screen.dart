import 'package:flutter/material.dart';

import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import '../../../../../loan/domain/params/loan_params.dart';
import '../../../../domain/params/post_params.dart';
import 'mobile/mobile_verify_phone.dart';
import 'web/web_verify_phone_screen.dart';

class VerifyPhoneScreen extends StatelessWidget {
  final VerifyPhoneArgs args;

  const VerifyPhoneScreen({
    super.key,
    required this.args,
  });
  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebVerifyPhoneScreen(
        pageType: args.pageType,
        title: args.webParams?.title ??
            '', // This shouldn't be null on web, was getting an error here on mobile hence I added a null check.
        subtitle: args.webParams?.subtitle ?? '', // same as the above
        footer: args.webParams?.footer,
        forgotPhoneNumber: args.webParams?.forgotPhoneNumber,
        info: args.webParams?.info,
      ),
      smallScreen: MobileVerifyPhoneScreen(
        pageType: args.pageType,
        otpParams: args.otpParams,
        forgotPin: args.forgotPin!,
        payParams: args.payParams,
        ninParams: args.ninParams,
        bvnParams: args.bvnParams,
        hasRetailStore: args.hasRetailStore,
        getStartedType: args.getStartedType,
        emailFromMergedAccount: args.emailFromMergedAccount,
        storeName: args.storeName,
      ),
      mediumScreen: MobileVerifyPhoneScreen(
        pageType: args.pageType,
        otpParams: args.otpParams,
        forgotPin: args.forgotPin!,
        payParams: args.payParams,
        ninParams: args.ninParams,
        bvnParams: args.bvnParams,
        hasRetailStore: args.hasRetailStore,
        getStartedType: args.getStartedType,
        emailFromMergedAccount: args.emailFromMergedAccount,
        storeName: args.storeName,
      ),
    );
  }
}

class VerifyPhoneArgs {
  final SendOTParams otpParams;
  final AuthPageType pageType;
  final bool? forgotPin;
  final String? businessBvn;
  final PhoneValidateCubit? bloc;
  final NinParams? ninParams;
  final PayParams? payParams;
  final BvnParams? bvnParams;
  final WebParams? webParams;
  final String? pin;
  final bool? hasRetailStore;
  final GetStartedType? getStartedType;
  final String? emailFromMergedAccount;
  final String? storeName;

  const VerifyPhoneArgs({
    Key? key,
    required this.otpParams,
    required this.pageType,
    this.forgotPin = false,
    this.payParams,
    this.businessBvn,
    this.bloc,
    this.ninParams,
    this.bvnParams,
    this.webParams,
    this.pin,
    this.hasRetailStore,
    this.getStartedType,
    this.emailFromMergedAccount,
    this.storeName,
  }) : assert(!(pageType == AuthPageType.tradeDepot && getStartedType == null),
            'If pageType is AuthPageType.tradedepot, getStartedType cannot be null');

  VerifyPhoneArgs copyWith({
    SendOTParams? otpParams,
    AuthPageType? pageType,
    bool? forgotPin,
    String? businessBvn,
    PhoneValidateCubit? bloc,
    NinParams? ninParams,
    PayParams? payParams,
    BvnParams? bvnParams,
    WebParams? webParams,
    String? pin,
    bool? hasRetailStore,
    GetStartedType? getStartedType,
    String? emailFromMergedAccount,
    String? storeName,
  }) {
    return VerifyPhoneArgs(
      otpParams: otpParams ?? this.otpParams,
      pageType: pageType ?? this.pageType,
      forgotPin: forgotPin ?? this.forgotPin,
      businessBvn: businessBvn ?? this.businessBvn,
      bloc: bloc ?? this.bloc,
      ninParams: ninParams ?? this.ninParams,
      payParams: payParams ?? this.payParams,
      bvnParams: bvnParams ?? this.bvnParams,
      webParams: webParams ?? this.webParams,
      pin: pin ?? this.pin,
      hasRetailStore: hasRetailStore ?? this.hasRetailStore,
      getStartedType: getStartedType ?? this.getStartedType,
      emailFromMergedAccount:
          emailFromMergedAccount ?? this.emailFromMergedAccount,
      storeName: storeName ?? this.storeName,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'otpParams': otpParams.toMap(),
      'pageType': pageType.name,
      'forgotPin': forgotPin,
      'businessBvn': businessBvn,
      'bloc': null,
      'ninParams': ninParams?.toMap(),
      'payParams': payParams?.toMap(),
      'bvnParams': bvnParams?.toMap(),
      'webParams': webParams?.toMap(),
      'pin': pin,
      'hasRetailStore': hasRetailStore,
      "getStartedType": getStartedType?.name,
      "emailFromMergedAccount": emailFromMergedAccount,
      "storeName": storeName
    };
  }

  factory VerifyPhoneArgs.fromMap(Map<String, dynamic> data) {
    return VerifyPhoneArgs(
        otpParams: SendOTParams.fromMap(data['otpParams']),
        pageType: AuthPageType.values.byName(data['pageType']),
        forgotPin: data['forgotPin'],
        businessBvn: data['businessBvn'],
        bloc: null,
        ninParams: data['ninParams'] != null
            ? NinParams.fromMap(data['ninParams'])
            : null,
        payParams: data['payParams'] != null
            ? PayParams.fromMap(data['payParams'])
            : null,
        bvnParams: data['bvnParams'] != null
            ? BvnParams.fromMap(data['bvnParams'])
            : null,
        webParams: data['webParams'] != null
            ? WebParams.fromMap(data['webParams'])
            : null,
        pin: data['pin'],
        hasRetailStore: data['hasRetailStore'],
        getStartedType: data['getStartedType'] != null
            ? GetStartedType.values.byName(data['getStartedType'])
            : null,
        emailFromMergedAccount: data['emailFromMergedAccount'],
        storeName: data['storeName']);
  }
}

class WebParams {
  final Widget? footer;
  final String title;
  final String? info;
  final String subtitle;
  final String? forgotPhoneNumber;

  WebParams({
    this.footer,
    required this.title,
    this.info,
    required this.subtitle,
    this.forgotPhoneNumber,
  });

  factory WebParams.fromMap(Map<String, dynamic> map) {
    return WebParams(
        footer: null,
        title: map['title'],
        subtitle: map['subtitle'],
        info: map['info'],
        forgotPhoneNumber: map['forgotPhoneNumber']);
  }

  Map<String, dynamic> toMap() {
    return {
      'footer': null,
      'title': title,
      'subtitle': subtitle,
      "info": info,
      'forgotPhoneNumber': forgotPhoneNumber,
    };
  }
}
