// import 'dart:convert';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'package:shop/app/authentication/domain/params/post_params.dart';
// import 'package:shop/app/authentication/domain/use_cases/verify_nin.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_state.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
// import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
// import 'package:shop/app/authentication/presentation/ui/widgets/info.dart';
// import 'package:shop/app/authentication/presentation/ui/widgets/otp_counter.dart';
// import 'package:shop/app/core/config/app_config.dart';
// import 'package:shop/app/homepage/presentation/logic/utils/method.dart';
// import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/src/k_button.dart';
// import 'package:shop/src/components/src/buttons/src/td_text_button.dart';
// import 'package:shop/src/components/src/form/src/td_form_field.dart';
// import 'package:shop/src/components/src/utils/error_handler.dart';
// import 'package:shop/src/components/src/widgets/app_screen.dart';
// import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
// import 'package:shop/src/components/src/widgets/margin.dart';
// import 'package:shop/src/res/assets/assets.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/config/keys.dart';
// import 'package:shop/src/res/values/styles/button_style.dart';
// import 'package:shop/src/res/values/styles/form_style.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:shop/src/services/remote_config_service.dart';
// import 'package:td_flutter_core/config/config.dart';
// import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
// import 'package:td_phone_auth/td_phone_auth.dart';
// import 'package:url_launcher/url_launcher.dart';

// import '../../sign_up_user/sign_up_user_screen.dart';

// class WebVerifyPhoneScreen extends StatefulWidget {
//   static const VERIFY_OTP_PATH = "phoneauth/verify-totp";

//   final SendOTParams otpParams;
//   final AuthPageType pageType;
//   final bool forgotPin;
//   // final String countryCode;
//   final String? businessBvn;
//   final NinParams? ninParams;

//   const WebVerifyPhoneScreen({
//     Key? key,
//     required this.otpParams,
//     required this.pageType,
//     this.forgotPin = false,
//     // required this.countryCode,
//     this.businessBvn,
//     this.ninParams,
//   }) : super(key: key);

//   @override
//   _ValidatePhoneScreenState createState() => _ValidatePhoneScreenState();
// }

// class _ValidatePhoneScreenState extends State<WebVerifyPhoneScreen> {
//   final _bloc = PhoneValidateCubit(locator(), locator());
//   final _signUpBloc = VerifyPhoneCubit(locator(), locator());
//   TextEditingController _controller = TextEditingController();

//   String code = '';
//   String ussdCode = "";

//   bool get isNinPageType => (widget.pageType == AuthPageType.verifySignUpNin ||
//       widget.pageType == AuthPageType.verifyKycNin ||
//       widget.pageType == AuthPageType.verifyHomeNin);

//   @override
//   void initState() {
//     super.initState();
//     fetchUssdCode();
//     _controller.addListener(() {
//       if (_controller.text == code) return;

//       // auto submit otp
//       if (_controller.text.length == 6) {
//         // close keyboard
//         closeKeyboard(context);
//         _verify();
//       }

//       // rebuild for footer button
//       setState(() {
//         code = _controller.text;
//       });
//     });
//   }

//   void fetchUssdCode() async {
//     final config = await RemoteConfigService.fetchString(
//         SHOP_V3_OTP_USSD, jsonEncode(DEFAULT_OTP_USSD));

//     final code = formatConfigString(config, DEFAULT_OTP_USSD)[
//         countryCodeFromPhone(widget.otpParams.phoneNumber)];

//     ussdCode = code;

//     if (mounted) {
//       setState(() {
//         ussdCode = code;
//       });
//     }
//   }

//   void _verify() async {
//     // validate
//     if (_controller.text.isEmpty) {
//       return;
//     }

//     await _bloc.verifyOTP(
//       VerifyOTParams(
//         phoneNumber: widget.otpParams.phoneNumber,
//         token: _controller.text,
//         countryCode: '',
//         url: config.firebaseServiceUrl! +
//             '/' +
//             WebVerifyPhoneScreen.VERIFY_OTP_PATH,
//       ),
//       widget.pageType,
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocConsumer<PhoneValidateCubit, PhoneValidateState>(
//       bloc: _bloc,
//       builder: (context, state) => _build(context, state),
//       listener: (_, state) async {
//         if (state is ValidatedAfterLoginState) {
//           try {
//             final info = await deviceInfo();
//             await saveDeviceInfo(info);
//           } catch (e, s) {
//             ErrorHandler.report(e, s);
//           }
//           // Remove the login OTP prompt on app load.
//           SharedPreferences.getInstance().then((sp) {
//             sp.remove(Keys.loginOtp);
//           });

//           TdLoader.hide();
//           context.goNamed(HomePath);
//           return;
//         }

//         /*  if (state is ValidatingOTPState) {
//           TdLoader.show(context);
//         } else {
//           TdLoader.hide();
//         }*/

//         if (state is FailedToValidateOTP) {
//           Toast.error(state.errorMessage, _);
//         }

//         if (state is ValidatedNINState) {
//           TdLoader.show(context);
//           final res = await locator.get<VerifyNin>().call(widget.ninParams!);
//           res.when(
//             success: (response) {
//               // if (widget.pageType == AuthPageType.verifyKycNin) {
//               //   TdLoader.hide();
//               //   context.goNamed(ninSuccessPageConfig);
//               //   return;
//               // }

//               TdLoader.hide();
//               goHomeWeb(context, true);

//               // if (widget.pageType == AuthPageType.verifySignUpNin) {
//               //   SharedPreferences.getInstance().then((sp) {
//               //     sp.setBool(Keys.createPin, true);
//               //   });
//               //   context.navigator.pushNamed(enterPinPageConfig,
//               //       arguments: EnterPinScreenArgs(name: widget.name));
//               // } else {
//               //   context.goNamed(ninSuccessPageConfig);
//               // }
//             },
//             apiFailure: (error, _) {
//               TdLoader.hide();
//               final msg = ApiExceptions.getErrorMessage(error);
//               Toast.error(msg, context);
//             },
//           );
//         }

//         if (state is ValidatedOTPState) {
//           // next page
//           context.pushNamed(
//             SignUpUserPath,
//             extra: SignUpUserArgs(
//               accessToken: state.accessToken,
//               phoneNumber: widget.otpParams.phoneNumber,
//               countryCode: state.countryCode,
//             ),
//           );
//         }

//         if (state is PhoneValidateUserLoggedIn) {
//           if (widget.pageType == AuthPageType.login) {
//             SharedPreferences.getInstance().then((sp) {
//               sp.setBool(Keys.resetPin, true);
//             });

//             // user want to reset pin
//             context.goNamed(
//               EnterPinPath,
//               extra: EnterPinScreenArgs(
//                 name: state.firstName,
//                 resetPinParams: ResetPinParams(
//                   null,
//                   widget.otpParams.phoneNumber,
//                   state.accessToken,
//                 ),
//               ),
//             );
//           } else {
//             context.goNamed(HomePath); //homeIntro
//           }
//         }
//       },
//     );
//   }

//   Widget _build(BuildContext context, PhoneValidateState state) {
//     return AppScreen(
//       child: Scaffold(
//         body: DesktopConstrainedBox(
//           child: BlocListener<VerifyPhoneCubit, VerifyPhoneState>(
//             bloc: _signUpBloc,
//             listener: (context, state) {
//               if (state is SentOTPState) {
//                 _controller.clear();
//               }
//             },
//             child: Container(
//               decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.all(Radius.circular(5))),
//               alignment: Alignment.center,
//               padding: EdgeInsets.symmetric(horizontal: 80, vertical: 30),
//               height: 804,
//               width: 892, //SizeConfig.scaleX(0.57),
//               child: _buildScreen(state),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

// /*  Widget _buildFooter(PhoneValidateState state) {
//     bool showUssdOption = widget.countryCode != 'ZA';
//     return Padding(
//       padding: context.insetsOnly(
//         left: 20,
//         right: 20,
//         bottom: 20,
//       ),
//       child: code.length > 1
//           ? _submitButton()
//           : showUssdOption
//               ? DialUssdButton()
//               : SizedBox.shrink(),
//     );
//   }*/

//   Widget _submitButton(PhoneValidateState state) {
//     return KButton(
//       onPressed: _verify,
//       width: 400,
//       text: 'Verify',
//       isLoading:
//           state is ValidatingOTPState || state is ValidatedAfterLoginState,
//     );
//   }

//   Widget _skipButton() {
//     return KButton(
//       onPressed: () {
//         if (widget.pageType == AuthPageType.verifySignUpNin) {
//           // SharedPreferences.getInstance().then((sp) {
//           //   sp.setBool(Keys.createPin, true);
//           // });
//           // context.navigator.pushNamed(enterPinPageConfig,
//           //     arguments: EnterPinScreenArgs(name: widget.name));
//           goHomeWeb(context, true);
//         } else {
//           // context.goNamed(homePageConfig);
//           goHomeWeb(context, false);
//         }
//       },
//       width: 400,
//       text: 'Skip',
//       isOutline: true,
//       outlineColor: kColorBlue,
//       textColor: kColorBlue,
//     );
//   }

//   Widget _buildScreen(PhoneValidateState state) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       mainAxisAlignment:
//           isNinPageType ? MainAxisAlignment.center : MainAxisAlignment.start,
//       children: [
//         SvgPicture.asset(kSvgShopTopUp,
//             width: 100.0, height: 100, alignment: Alignment.centerLeft),
//         YSpacing(30),
//         Text(AppLocalizations.of(context)!.enter_otp,
//             style: KTextStyle.semiBold24.copyWith(color: kColorBlack)),
//         YSpacing(10),
//         // Text(AppLocalizations.of(context)!.enter_otp_info,
//         //     style: KTextStyle.book14.copyWith(fontSize: 16)),
//         Text('Enter the code that was sent to ${widget.otpParams.phoneNumber}',
//             style: KTextStyle.book14.copyWith(fontSize: 16)),
//         YSpacing(40),
//         SizedBox(
//           width: 400,
//           child: TdFormField(
//             label: AppLocalizations.of(context)!.enter_otp,
//             widget: TextField(
//               controller: _controller,
//               readOnly: state is ValidatingOTPState,
//               style: TextStyle(
//                 fontSize: 14,
//               ),
//               decoration: FormStyle.formDecoration(
//                 AppLocalizations.of(context)!.enter_otp_hint,
//               ).copyWith(
//                 suffixIcon: OTPCounter(
//                   params: widget.otpParams,
//                   bloc: _signUpBloc,
//                   isDesktop: true,
//                 ),
//               ),
//               maxLength: 6,
//               maxLengthEnforcement: MaxLengthEnforcement.enforced,
//               keyboardType: TextInputType.number,
//               inputFormatters: [
//                 FilteringTextInputFormatter.allow(RegExp(r"\d+")),
//                 // maximum of 6 numbers for OTP
//                 LengthLimitingTextInputFormatter(6),
//               ],
//             ),
//           ),
//         ),
//         YSpacing(50),
//         _submitButton(state),
//         if (isNinPageType) YSpacing(30),
//         if (isNinPageType) _skipButton(),
//         YSpacing(30),
//         if (!isNinPageType) Info(widget.pageType),
//         if (!isNinPageType) YSpacing(30),
//         if (!isNinPageType)
//           TdTextButton(
//               label: AppLocalizations.of(context)!.go_back,
//               onPressed: () => Navigator.canPop(context)
//                   ? context.pop()
//                   : context.pushReplacementNamed(
//                       LoginPath,
//                     ),
//               style: ButtonStyles.kPlainTextButtonStyle),
//         YSpacing(30),
//         ussdCode.isEmpty
//             ? SizedBox.shrink()
//             : Row(
//                 children: [
//                   Text("Not getting OTP? ",
//                       style: KTextStyle.book14.copyWith(color: kColorGreyAlt)),
//                   XSpacing(5),
//                   TdTextButton(
//                     label: "Dial $ussdCode",
//                     onPressed: () => _makeUssdCall(ussdCode),
//                     style: ButtonStyles.kPaleButtonStyle.copyWith(
//                       textStyle: MaterialStateProperty.all(
//                         KTextStyle.regular14.copyWith(fontSize: 12),
//                       ),
//                     ),
//                   ),
//                 ],
//                 mainAxisAlignment: MainAxisAlignment.center,
//               )
//       ],
//     );
//   }

//   Future<void> _makeUssdCall(String code) async {
//     String no = Uri.encodeComponent(code);
//     final url = Uri.parse('tel:$no');
//     try {
//       if (await canLaunchUrl(url)) {
//         await launchUrl(url);
//       } else {
//         throw 'Could not launch $url';
//       }
//     } catch (e) {
//       Toast.error("Could not launch $code", context);
//     }
//   }

//   @override
//   void dispose() {
//     _signUpBloc.close();
//     _bloc.close();
//     _controller.dispose();
//     super.dispose();
//   }
// }
