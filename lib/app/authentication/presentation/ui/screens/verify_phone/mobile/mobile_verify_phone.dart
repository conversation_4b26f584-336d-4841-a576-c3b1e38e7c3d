import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safe_insets/safe_area_wrap.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_verification_modal.dart';
import 'package:shop/app/authentication/data/models/verified_otp.dart';
import 'package:shop/app/authentication/domain/use_cases/add_phone.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_token.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_otp.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/phone_exists_modal.dart';
import 'package:shop/app/credit/presentation/logic/states/credit_docs_state.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/services/url_service.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../../../route_constants.dart';
import '../../../../../../../src/components/src/buttons/src/k_button_primary.dart';
import '../../../../../../../src/components/src/form/form.dart';
import '../../../../../../../src/res/values/analytics/segment_events.dart';
import '../../../../../../../src/res/values/config/keys.dart';
import '../../../../../../../src/res/values/styles/text_style.dart';
import '../../../../../../../app_config.dart';
import '../../../../../../loan/domain/params/loan_params.dart';
import '../../../../../../loan/domain/use-cases/verify_bvn.dart';
import '../../../../../../loan/presentation/logic/bloc/base_loan_cubit.dart';
import '../../../../../../loan/presentation/logic/utils/methods.dart';
import '../../../../../../pay/domain/params/pay_charge_params.dart';
import '../../../../../../pay/presentation/logic/charge_cubit.dart';
import '../../../../../data/models/page_type.dart';
import '../../../../../domain/params/post_params.dart';
import '../../../../../domain/use_cases/verify_nin.dart';
import '../../../../logic/bloc/user_cubit.dart';
import '../../../../logic/utils/methods.dart';
import '../../enter_pin/enter_pin_screen.dart';
import '../../sign_up_user/sign_up_user_screen.dart';

class MobileVerifyPhoneScreen extends StatefulWidget {
  final SendOTParams otpParams;
  final AuthPageType pageType;
  final bool forgotPin;
  final NinParams? ninParams;
  final PayParams? payParams;
  final BvnParams? bvnParams;
  final String?
      pin; //TODO: merge/unify remove pin and handle device management without pin
  final bool? hasRetailStore;
  final GetStartedType? getStartedType;
  final String? emailFromMergedAccount;
  final String? storeName;

  MobileVerifyPhoneScreen({
    super.key,
    required this.otpParams,
    required this.pageType,
    required this.forgotPin,
    this.payParams,
    this.ninParams,
    this.bvnParams,
    this.pin,
    this.hasRetailStore,
    this.getStartedType,
    this.emailFromMergedAccount,
    this.storeName,
  }) {
    assert(!(pageType == AuthPageType.tradeDepot && getStartedType == null),
        'If pageType is AuthPageType.tradedepot, getStartedType cannot be null.');
  }

  @override
  State<MobileVerifyPhoneScreen> createState() =>
      _MobileVerifyPhoneScreenState();
}

class _MobileVerifyPhoneScreenState extends State<MobileVerifyPhoneScreen> {
  final _otpMode = ValueNotifier<PhoneAuthMode>(PhoneAuthMode.WhatsApp);
  late String ussdCode = context.read<UserCubit>().ussdCode;
  late final verifyingPhone = widget.getStartedType == GetStartedType.phone ||
      widget.getStartedType == GetStartedType.updatePhone ||
      widget.pageType == AuthPageType.orderPayment;

  @override
  void initState() {
    if (widget.pageType == AuthPageType.verifyBusinessBvn) {
      _otpMode.value = PhoneAuthMode.All;
    }
    super.initState();
  }

  final Map<String, TdTextController> controller = {
    'otp': TdTextController(
      validators: [Validators.required()],
    ),
  };

  @override
  Widget build(BuildContext context) {
    final style = Theme.of(context)
        .textTheme
        .bodyLarge
        ?.copyWith(color: Color.fromRGBO(113, 122, 142, 1));

    final title = widget.getStartedType == GetStartedType.phone
        ? 'Verify your phone number'
        : widget.getStartedType == GetStartedType.email
            ? 'Verify your email address'
            : widget.pageType == AuthPageType.deviceManagement
                ? 'Register your device'
                : 'Verify your ${widget.pageType == AuthPageType.verifyBusinessBvn ? 'BVN' : 'phone number'}';

    final subTitle = widget.getStartedType == GetStartedType.phone
        ? "We've sent a verification code, via WhatsApp, to the phone number $maskedPhone. Please enter the code below."
        : widget.getStartedType == GetStartedType.email
            ? "We've sent a verification code to the email you provided, Please enter the code below."
            : widget.pageType == AuthPageType.verifyBusinessBvn
                ? "We have sent a code, via WhatsApp, to the phone number (****${getStringLastFour(widget.otpParams.phoneNumber!)})${widget.otpParams.email != null ? ' and email ${convertEmailToAsterisk(widget.otpParams.email!)}' : ''} associated with the BVN provided."
                : widget.pageType == AuthPageType.deviceManagement
                    ? 'We have sent a verification code, via WhatsApp, to the phone number on your account $maskedPhone. Please enter the code to register your device.'
                    : "We've sent a verification code, via WhatsApp, to the phone number $maskedPhone. Please enter the code below.";

    return AppScreen(
      child: Scaffold(
        appBar: ShopAppBar.shopAppBar(
          context,
          leading: BackButton(
            onPressed: () {
              if (widget.pageType == AuthPageType.proceedLogin) {
                context.read<UserCubit>().logout();
              } else {
                context.pop();
              }
            },
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: KTextStyle.bodyText2.copyWith(
                              fontSize: 28,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const YMargin(10),
                          Text(
                            subTitle,
                            style: KTextStyle.bodyText2.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).hintColor,
                            ),
                          ),
                          const YMargin(20),
                          Align(
                            alignment: Alignment.center,
                            child: TdTextField(
                              autoFocus:
                                  widget.pageType == AuthPageType.orderPayment
                                      ? false
                                      : true,
                              title: 'Enter Code',
                              hint: '000000',
                              textController: controller['otp'],
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                validInput(),
                                FilteringTextInputFormatter.allow(
                                    RegExp(r"\d+")),
                                // maximum of 6 numbers for OTP
                                LengthLimitingTextInputFormatter(6),
                              ],
                              onSubmitted: _verifyPhone,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Align(
                        alignment: Alignment.bottomLeft,
                        child: Text.rich(
                          TextSpan(
                            children: [
                              WidgetSpan(
                                child: Text(
                                  "Not getting a code?",
                                  style: style,
                                ),
                              ),
                              WidgetSpan(
                                child: InkWell(
                                  onTap: () => _resendOtp(context),
                                  child: Text(
                                    " Resend ",
                                    style: style?.copyWith(
                                      //  color: Color.fromRGBO(38, 91, 246, 1),
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          style: style,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),
                  if (verifyingPhone && ussdCode.isNotEmpty)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Column(
                          children: [
                            Text(
                              "or ",
                              style: style,
                            ),
                            ussdButton(context),
                            const YMargin(5),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
            SafeArea(
              child: SafeAreaWrap(
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 8.0),
                  child: KButtonPrimary(
                    text: 'Verify',
                    onTap: _verifyPhone,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  void _proceed() async {
    if (widget.payParams?.phoneNumber == null) return;
    final otp = controller.data()['otp'];
    context.read<ChargeCubit>().payAgent(
          PayChargeParams(
            amount: widget.payParams!.amount,
            phoneNumber: widget.payParams!.phoneNumber,
            token: otp,
            skipDuplicate: false,
          ),
        );
  }

  Widget ussdButton(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints.tight(Size(300, 36)),
        child: TextButton(
          onPressed: () {
            UrlService.it.call(Uri.encodeComponent(ussdCode));
          },
          style: TextButton.styleFrom(
            foregroundColor: colorScheme.onPrimary,
            backgroundColor: colorScheme.onPrimary,
            elevation: 0,
            side: BorderSide.none,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(42.0),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Get OTP by dialling',
                style: KTextStyle.bodyText2.copyWith(
                  color: Color(0xFF717A8E),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                ' $ussdCode',
                style: KTextStyle.bodyText2.copyWith(
                  color: colorScheme.primary,
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void _verify() async {
    final otp = controller.data()['otp'];
    final params = verifyingPhone
        ? VerifyOTParams(
            phoneNumber: widget.otpParams.phoneNumber!,
            token: otp,
            countryCode: countryCodeFromPhone(widget.otpParams.phoneNumber!),
            url: '${config.firebaseServiceUrl!}/$VERIFY_OTP_PATH',
          )
        : VerifyOTParams(
            email: widget.otpParams.email!,
            token: otp,
            countryCode: '',
            url: '${config.firebaseServiceUrl!}/$VERIFY_OTP_PATH',
          );

    TdLoader.show(context);

    final sp = await SharedPreferences.getInstance();
    final res = await locator.get<VerifyOTP>().call(params);

    res.when(
      success: (data) async {
        TdLoader.hide();

        final pageType = widget.pageType;

        if (pageType == AuthPageType.login) {
          _loginUser(
            LoginTokenParams(
              phone: params.phoneNumber,
              token: data.accessToken,
            ),
          );
          return;
        }

        if (pageType == AuthPageType.proceedLogin) {
          // Remove the login OTP prompt on app load.
          sp.remove(Keys.loginOtp);

          TdLoader.hide();
          context.goNamed(HomePath);
          return;
        }

        if (pageType == AuthPageType.verifySignUpNin ||
            pageType == AuthPageType.verifyKycNin ||
            pageType == AuthPageType.verifyHomeNin ||
            pageType == AuthPageType.verifyPaymentNin) {
          final res = await locator.get<VerifyNin>().call(widget.ninParams!);

          res.when(
            success: (response) {
              if (pageType == AuthPageType.verifyKycNin) {
                TdLoader.hide();
                Navigator.of(context)
                  ..pop()
                  ..pop();
                showModalBottomSheet(
                  isScrollControlled: true,
                  context: context,
                  builder: (_) => KYCVerificationModal(),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(30),
                    ),
                  ),
                  clipBehavior: Clip.antiAlias,
                );
                //  context.goNamed(NINSuccessPath);
                return;
              }

              if (pageType == AuthPageType.verifySignUpNin) {
                sp.remove(Keys.ninValidation);
                TdLoader.hide();
                goHomeMobile(context, true);
                return;
              }

              if (pageType == AuthPageType.verifyPaymentNin) {
                UserCubit userCubit = context.read();
                BaseLoanCubit baseLoanCubit = context.read();
                baseLoanCubit.reloadState(userCubit.currentOutlet);
                context.pushReplacementNamed(PaymentPath);
              } else {
                TdLoader.hide();
                context.goNamed(NINSuccessPath);
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
          return;
        }

        if (pageType == AuthPageType.verifyKycBvn) {
          final res = await locator.get<VerifyBvn>().call(VerifyBvnParams(
              outletId: widget.bvnParams!.outletId,
              bvn: widget.bvnParams!.bvn,
              isValidated: widget.bvnParams?.isValidated,
              phoneNumber: widget.bvnParams?.phoneNumber));

          res.when(
            success: (data) {
              if (widget.bvnParams?.fromLoan == true) {
                UserCubit.instance?.updateOutlet();

                context
                    .read<BaseLoanCubit>()
                    .reloadState(UserCubit.instance?.currentOutlet);
                Segment.track(
                  eventName: SegmentEvents.kycValidationCompleted,
                  properties: {
                    'Status': 1,
                  },
                );

                TdLoader.hide();

                Navigator.pop(context);
              } else {
                UserCubit.instance?.updateOutlet();

                TdLoader.hide();

                Navigator.pop(context);
                Navigator.pop(context);
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
          return;
        }

        if (pageType == AuthPageType.verifyBusinessBvn) {
          validateBusinessBVN(context);
          return;
        }

        if (pageType == AuthPageType.orderPayment) {
          Navigator.pop(context, data.accessToken);
          return;
        }

        if (widget.pageType == AuthPageType.resetPinBeforeLogin) {
          context.pushNamed(
            ResetPinPath,
            extra: EnterPinScreenArgs(
              pageType: widget.pageType,
              resetPinParams: ResetPinParams(
                null,
                widget.otpParams.phoneNumber!,
                data.accessToken,
              ),
            ),
          );
          return;
        }

        if (widget.pageType == AuthPageType.deviceManagement) {
          handleLogin(
              context, false, widget.otpParams.phoneNumber!, widget.pin!);
          return;
        }

        if (widget.pageType == AuthPageType.tradeDepot) {
          if (widget.getStartedType == GetStartedType.updatePhone) {
            TdLoader.show(context);

            final res = await locator
                .get<AddPhone>()
                .call(widget.otpParams.phoneNumber!);

            res.when(
              success: (_) {
                Toast.success('PhoneNumber added successfully', context);
                sp.remove(Keys.updatePhone);
                context.goNamed(HomePath);
                TdLoader.hide();
              },
              apiFailure: (error, _) {
                Toast.apiError(error, context);
                TdLoader.hide();
              },
            );
            return;
          } else if (widget.hasRetailStore == true) {
            loginWithToken(data);
          } else {
            if (widget.getStartedType == GetStartedType.phone) {
              // set domain in userCubit
              UserCubit.instance
                  ?.setDomainFromPhone(widget.otpParams.phoneNumber!);
              // set phone value after OTP verification
              sp.setString(Keys.tdAppPhone, widget.otpParams.phoneNumber!);
              signUpUser(data);
            } else {
              // set phone value after OTP verification
              sp.setString(Keys.tdAppEmail, widget.otpParams.email!);
              context.pushNamed(GetStartedPath,
                  extra: GetStartedType.phone.name);
            }
          }
          return;
        }

        if (widget.pageType == AuthPageType.emailRedacted) {
          final sp = await SharedPreferences.getInstance();
          final email = sp.getString(Keys.tdAppEmail) ?? '';
          PhoneExistsModal.show(
            context,
            phoneNumber: widget.otpParams.phoneNumber!,
            redactedEmail: widget.otpParams.email!,
            email: email,
          );
          return;
        }

        // next page
        signUpUser(data);
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  /// Attempt to login a user
  Future<void> _loginUser(LoginTokenParams params) async {
    final sp = await SharedPreferences.getInstance();
    final res = await locator.get<LoginWithToken>().call(params);

    res.when(
      success: (user) {
        if (widget.pageType == AuthPageType.login) {
          sp.setBool(Keys.createPin, true);

          // user wants to create pin
          context.pushNamed(
            EnterPinPath,
            extra: EnterPinScreenArgs(
              name: user.firstName,
              pageType: widget.pageType,
            ),
          );
        } else {
          context.goNamed(HomePath);
        }
      },
      apiFailure: (error, _) {
        Toast.apiError(error, context);
        // Toast.error(capitalize(state.errorMessage), context);
      },
    );
  }

  bool get verifyingNin =>
      widget.pageType == AuthPageType.verifySignUpNin ||
      widget.pageType == AuthPageType.verifyKycNin ||
      widget.pageType == AuthPageType.verifyHomeNin ||
      widget.pageType == AuthPageType.verifyPaymentNin;

  String get maskedPhone {
    final phone = widget.otpParams.phoneNumber;
    final prefix = phone?.substring(0, 4);
    final postfix = phone?.substring(phone.length - 3);
    final mask = '*******';
    return '$prefix$mask$postfix';
  }

  void signUpUser(VerifiedOTP data) {
    context.pushNamed(
      SignUpUserPath,
      extra: SignUpUserArgs(
        accessToken: data.accessToken,
        phoneNumber: verifyingPhone
            ? widget.otpParams.phoneNumber!
            : widget.otpParams.email!,
        countryCode: data.countryCode,
      ),
    );
  }

  Future<void> loginWithToken(VerifiedOTP data) async {
    TdLoader.show(context);

    if (widget.storeName != null &&
        widget.emailFromMergedAccount != null &&
        widget.hasRetailStore == true) {
      Toast.show(
          "Welcome ${widget.storeName}! We have saved your email address ${widget.emailFromMergedAccount} and you can now log in using this email address and receive order updates and information from us via email.",
          context,
          duration: 10);
    }

    final sp = await SharedPreferences.getInstance();
    final res = await locator.get<LoginWithToken>().call(LoginTokenParams(
        email: widget.emailFromMergedAccount ?? widget.otpParams.email!,
        token: data.accessToken));

    res.when(
      success: (user) {
        if (sp.getBool(Keys.updatePhone) == true) {
          context.goNamed(AuthGetStartedPath,
              extra: GetStartedType.updatePhone.name);
        } else {
          context.goNamed(HomePath);
        }
        TdLoader.hide();
      },
      apiFailure: (error, _) {
        Toast.apiError(error, context);
        TdLoader.hide();
      },
    );
  }

  Future<void> _verifyPhone() async {
    if (!controller.validate()) return;

    FocusScope.of(context).unfocus();

    if (widget.pageType == AuthPageType.verifyAgent) {
      _proceed();
    } else {
      _verify();
    }
  }

  Future<void> _resendOtp(BuildContext context) async {
    TdLoader.show(context);
    final params = verifyingPhone
        ? SendOTParams(
            mode: _otpMode.value,
            phoneNumber: widget.otpParams.phoneNumber,
            email: widget.otpParams.email,
            url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
          )
        : SendOTParams(
            mode: PhoneAuthMode.Email,
            email: widget.otpParams.email,
            url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
          );

    controller['otp']?.controller?.text = '';

    final res = await locator.get<SendOTP>().call(params);

    res.when(
      success: (data) {
        TdLoader.hide();
        controller['otp']?.controller?.text = '';
        Toast.show('OTP code has been sent', context);
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        Toast.apiError(error, context);
      },
    );
  }

  void validateBusinessBVN(BuildContext context) async {
    TdLoader.show(context);
    final result = await context.read<CreditDocsState>().verifyBusinessBvn(
      params: (isVerified: true, phoneNumber: widget.otpParams.phoneNumber),
    );
    TdLoader.hide();
    result.when(
      success: (response) {
        if (response.$1) {
          Toast.show("Bvn process submitted successfully", context);
          context.pop();
        } else {
          Toast.error("Invalid OTP", context);
        }
      },
      apiFailure: (err, _) => Toast.apiError(err, context),
    );
  }

  String getStringLastFour(String str) {
    if (str.length <= 4) {
      return str;
    } else {
      return str.substring(str.length - 4);
    }
  }

  String convertEmailToAsterisk(String email) {
    if (email.isEmpty) {
      return '';
    }

    // Split the email into username and domain parts
    List<String> parts = email.split('@');
    if (parts.length != 2) {
      return email; // Invalid email format, return original email
    }

    String username = parts[0];
    String domain = parts[1];

    // Calculate the number of characters to mask
    int numAsterisksUsername = (username.length * 0.6).round();

    // Create masked username string
    String maskedUsername =
        '${'*' * numAsterisksUsername}${username.substring(numAsterisksUsername)}';

    // Combine the masked username and domain to form the masked email
    String maskedEmail = '$maskedUsername@$domain';

    return maskedEmail;
  }
}
