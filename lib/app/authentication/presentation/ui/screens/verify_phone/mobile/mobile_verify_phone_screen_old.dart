// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
// import 'package:go_router/go_router.dart';
// import 'package:shop/app/authentication/data/models/country_list.dart';
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'package:shop/app/authentication/domain/params/post_params.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/validate_phone_screen.dart';
// import 'package:shop/app/core/config/app_config.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/components/components.dart';
// import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
// import 'package:shop/src/components/src/buttons/src/k_button.dart';
// import 'package:shop/src/components/src/form/src/extensions.dart';
// import 'package:shop/src/components/src/widgets/app_screen.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:shop/src/res/values/styles/text_style.dart';
// import 'package:td_flutter_core/config/config.dart';
// import 'package:td_flutter_src/scaler/scaler.dart';
// import 'package:td_phone_auth/td_phone_auth.dart';

// class MobileVerifyPhoneScreen extends StatefulWidget {
//   static const SEND_OTP_PATH = "phoneauth/send-totp";
//   final AuthPageType pageType;
//   final Widget? footer;
//   final String title;
//   final String subtitle;
//   final String? forgotPhoneNumber;

//   const MobileVerifyPhoneScreen({
//     Key? key,
//     required this.pageType,
//     required this.title,
//     required this.subtitle,
//     this.forgotPhoneNumber,
//     this.footer,
//   }) : super(key: key);

//   @override
//   _VerifyPhoneScreenState createState() => _VerifyPhoneScreenState();
// }

// class _VerifyPhoneScreenState extends State<MobileVerifyPhoneScreen> {
//   AuthPageType? _pageType;
//   TextEditingController _controller = TextEditingController();
//   final _bloc = VerifyPhoneCubit(locator(), locator());
//   ValueNotifier<bool> _loading = ValueNotifier(true);
//   CountryWithPhoneCode? _selectedCountry;
//   List<CountryWithPhoneCode> _countries = [];
//   String? errorMessage;
//   bool? fullButtons;
//   bool hasDefaultPhone = false;

//   @override
//   void initState() {
//     super.initState();
//     _loadCountries(widget.forgotPhoneNumber);
//     _setPageType();
//     _controller.addListener(() {
//       // clear error message on edit
//       if (_controller.text.isNotEmpty && errorMessage != null) {
//         setState(() {
//           errorMessage = null;
//         });
//       }
//     });
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     if (widget.forgotPhoneNumber != null && fullButtons == null) {
//       final phone = widget.forgotPhoneNumber;
//       final countries = _countries.where(
//         (country) => phone!.contains(country.phoneCode),
//       );

//       if (countries.length > 0) {
//         _selectedCountry = countries.first;
//         fullButtons = true;
//         hasDefaultPhone = true;
//         // extract normal phone
//         _controller.text = phone!
//             .replaceFirst('+${_selectedCountry!.phoneCode}', '')
//             .replaceAll(' ', '');
//       }
//     }
//   }

//   void _loadCountries([String? phoneNumber]) {
//     _countries =
//         countriesWithPhoneCode.map((e) => mapCountryWithPhoneCode(e)).toList();

//     if (phoneNumber != null) {
//       final picked =
//           _countries.where((e) => phoneNumber.contains(e.phoneCode)).first;
//       _selectedCountry = picked;
//       if (mounted) {
//         setState(() {
//           _selectedCountry = picked;
//         });
//       }
//     } else {
//       _selectedCountry = _countries.first;
//       if (mounted) {
//         setState(() {
//           _selectedCountry = _countries.first;
//         });
//       }
//     }

//     _loading.value = false;
//   }

//   void _send(PhoneAuthMode mode) async {
//     final phone = await _validatePhone();

//     if (phone == null) return;

//     if (_pageType == AuthPageType.SignUp) {
//       final res = await _bloc.checkPhone(CheckPhoneParams(phone));
//       if (res == null) return;

//       if (res.hasRetailStore) {
//         // switch to login flow
//         _pageType = AuthPageType.Login;

//         if (res.pinEnabled!) {
//           context.pushNamed(
//             LoginEnterPinPath,
//             extra: EnterPinScreenArgs(
//               phone: phone,
//               name: res.businessName,
//             ),
//           );
//           _setPageType();
//           return;
//         }
//         // else: Proceed to send otp, then login
//       }
//     }

//     _bloc.sendOTP(
//       SendOTParams(
//         mode: mode,
//         phoneNumber: phone,
//         url: config.firebaseServiceUrl! +
//             '/' +
//             MobileVerifyPhoneScreen.SEND_OTP_PATH,
//       ),
//     );
//   }

//   Future<String?> _validatePhone() async {
//     // validate
//     if (_controller.text.isEmpty) {
//       setState(() {
//         errorMessage = 'Enter your phone number';
//       });
//       return null;
//     }

//     String text = _controller.text;
//     if (text.startsWith('0')) {
//       text = text.substring(1);
//     }
//     final phone =
//         ('+' + _selectedCountry!.phoneCode + text).replaceAll(' ', '');
//     bool isValid = true;
//     try {
//       await FlutterLibphonenumber().parse(phone);
//     } catch (e) {
//       isValid = false;
//     }

//     // Additional validation for SA numbers
//     if (isValid && _selectedCountry!.countryCode == 'ZA') {
//       // check length
//       if (text.replaceAll(' ', '').length != 9) {
//         isValid = false;
//       }
//     }

//     if (!isValid) {
//       setState(() {
//         errorMessage = 'Invalid phone number';
//       });
//       return null;
//     }

//     return phone.replaceAll(' ', '');
//   }

//   List<DropdownMenuItem<CountryWithPhoneCode>> _mapCountryToDropdownItem(
//     List<CountryWithPhoneCode> countries,
//   ) {
//     return countries
//         .map(
//           (country) => DropdownMenuItem<CountryWithPhoneCode>(
//             value: country,
//             child: Image.asset(
//               country.flagUri(country.countryCode),
//               width: 20,
//             ),
//           ),
//         )
//         .toList();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocConsumer<VerifyPhoneCubit, VerifyPhoneState>(
//       bloc: _bloc,
//       builder: (context, state) => _build(context, state),
//       listener: (_, state) {
//         if (state is VerifyPhoneLoading) {
//           TdLoader.show(_);
//         } else {
//           TdLoader.hide();
//         }

//         if (state is FailedToSendOTPState) {
//           Toast.error(state.errorMessage, _);
//         }

//         if (state is SentOTPState) {
//           // next page
//           context.pushNamed(
//             ValidateOTPPath,
//             extra: ValidatePhoneArgs(
//               params: state.params,
//               pageType: _pageType,
//               forgotPin: widget.forgotPhoneNumber != null,
//               countryCode: _selectedCountry!.countryCode,
//             ),
//           );
//         }
//       },
//     );
//   }

//   Widget _build(BuildContext context, VerifyPhoneState state) {
//     return AppScreen(
//       child: Scaffold(
//         backgroundColor: Colors.white,
//         body: SafeArea(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Expanded(child: _buildTop(state)),
//               const SizedBox(height: 10),
//               // hide footer when keyboard is up
//               if (MediaQuery.of(context).viewInsets.bottom < 10)
//                 _buildFooter(state),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildFooter(VerifyPhoneState state) {
//     if (fullButtons == null && _pageType == AuthPageType.Login) {
//       return _buildCheckButton();
//     }

//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       child: Column(
//         children: [
//           KButton(
//             text: 'Send by SMS',
//             onPressed: () {
//               _send(PhoneAuthMode.SMS);
//             },
//           ),
//           const SizedBox(height: 10),
//           KButton(
//             text: 'Send by Whatsapp',
//             onPressed: () {
//               _send(PhoneAuthMode.WhatsApp);
//             },
//             color: kColorWhatsapp,
//           ),
//           if (widget.footer != null) widget.footer!,
//         ],
//       ),
//     );
//   }

//   Widget _buildCheckButton() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       child: Column(
//         children: [
//           KButton(
//             text: 'Continue',
//             onPressed: () async {
//               final phone = await _validatePhone();
//               if (phone == null) return;

//               final res = await _bloc.checkPhone(CheckPhoneParams(phone));
//               if (res == null) return;

//               if (!res.hasRetailStore) {
//                 return _bloc.emitError(
//                   'Phone number does not exists. Please sign up.',
//                 );
//               }

//               if (res.pinEnabled!) {
//                 context.pushNamed(
//                   LoginEnterPinPath,
//                   extra: EnterPinScreenArgs(
//                     phone: phone,
//                     name: res.businessName,
//                   ),
//                 );
//                 _setPageType();
//                 return;
//               }

//               // send OTP
//               _send(PhoneAuthMode.SMS);
//             },
//           ),
//           if (widget.footer != null) widget.footer!,
//         ],
//       ),
//     );
//   }

//   Widget _buildTop(VerifyPhoneState state) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         KBackButton(),
//         YMargin(25),
//         Padding(
//           padding: context.insetsSymetric(
//             horizontal: 20,
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 widget.title,
//                 style: textStyleBold(
//                   context,
//                   fontSize: 30,
//                 ),
//               ),
//               YMargin(20),
//               Text(widget.subtitle),
//               YMargin(20),
//               Text('Phone number'),
//               const SizedBox(height: 2),
//               ValueListenableBuilder(
//                 valueListenable: _loading,
//                 builder: (BuildContext context, bool loading, _) {
//                   Widget child = CircularProgressIndicator();
//                   if (!loading) {
//                     // final country = getPhoneCountry();

//                     child = TextField(
//                       controller: _controller,
//                       readOnly: hasDefaultPhone || state is VerifyPhoneLoading,
//                       keyboardType: TextInputType.number,
//                       textInputAction: TextInputAction.done,
//                       style: TextStyle(
//                         fontSize: 14,
//                       ),
//                       inputFormatters: [
//                         LibPhonenumberTextFormatter(
//                           country: _selectedCountry!,
//                           phoneNumberType: PhoneNumberType.mobile,
//                           additionalDigits: 1,
//                           phoneNumberFormat: PhoneNumberFormat.international,
//                           // additionalDigits: 1,
//                         ),
//                       ],
//                       decoration: InputDecoration(
//                         hintText: _selectedCountry!.phoneMaskMobileNational,
//                         contentPadding: const EdgeInsets.symmetric(
//                           horizontal: 16,
//                           vertical: 8,
//                         ),
//                         prefixIcon: Padding(
//                           padding: const EdgeInsets.only(
//                             left: 16,
//                             right: 8,
//                             bottom: 1.2,
//                           ),
//                           child: Row(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               DropdownButtonHideUnderline(
//                                 child: DropdownButton<CountryWithPhoneCode>(
//                                     value: _selectedCountry,
//                                     items:
//                                         _mapCountryToDropdownItem(_countries),
//                                     onChanged: (value) {
//                                       if (hasDefaultPhone ||
//                                           state is VerifyPhoneLoading) {
//                                         return;
//                                       } else {
//                                         setState(() {
//                                           _selectedCountry = value;
//                                           errorMessage = null;
//                                         });
//                                       }
//                                     }),
//                               ),
//                               const SizedBox(width: 5),
//                               Container(
//                                 height: 30,
//                                 width: 1,
//                                 decoration: BoxDecoration(
//                                   color: kColorGrey2,
//                                 ),
//                               ),
//                               const SizedBox(width: 10),
//                               Text(
//                                 '+${_selectedCountry!.phoneCode}',
//                               ),
//                             ],
//                           ),
//                         ),
//                         enabledBorder: KTextStyle.outlineInputBorder,
//                         focusedBorder: KTextStyle.outlineInputBorder,
//                         border: KTextStyle.outlineInputBorder,
//                       ),
//                     );
//                   }
//                   return AnimatedSwitcher(
//                       duration: kThemeAnimationDuration, child: child);
//                 },
//               ),
//               if (errorMessage != null)
//                 Padding(
//                   padding: const EdgeInsets.only(top: 4),
//                   child: Text(
//                     errorMessage!,
//                     style: TextStyle(
//                       color: Colors.red,
//                       fontSize: 12,
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   void _setPageType([AuthPageType? type]) {
//     // revert page type to original if not specified
//     _pageType = type ?? widget.pageType;
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     _bloc.close();
//     super.dispose();
//   }
// }
