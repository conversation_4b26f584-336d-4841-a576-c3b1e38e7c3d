import 'package:flutter/material.dart';
import 'package:shop/app/authentication/presentation/ui/screens/sign_up_store/web/web_sign_up_store_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';
import '../../../../../../src/components/src/form/form.dart';
import '../../../../data/models/td_address.dart';
import 'mobile/mobile_sign_up_store_screen.dart';

class SignUpStoreScreen extends StatelessWidget {
  final SignUpStoreArgs args;

  const SignUpStoreScreen({
    super.key,
    required this.args,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebSignUpStoreScreen(
        address: args.address,
        userInfo: args.previous,
        storeControllers: args.storeControllers,
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
        key: key,
      ),
      smallScreen: MobileSignUpStoreScreen(
        address: args.address,
        userInfo: args.previous,
        storeControllers: args.storeControllers,
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
        key: key,
      ),
      mediumScreen: MobileSignUpStoreScreen(
        address: args.address,
        userInfo: args.previous,
        storeControllers: args.storeControllers,
        accessToken: args.accessToken,
        phoneNumber: args.phoneNumber,
        countryCode: args.countryCode,
        key: key,
      ),
    );
  }
}

class SignUpStoreArgs {
  final TdAddress? address;
  final Map<String, dynamic> previous;
  final Map<String, TdTextController> storeControllers;
  final String accessToken;
  final String phoneNumber;
  final String countryCode;

  SignUpStoreArgs({
    required this.previous,
    required this.storeControllers,
    required this.accessToken,
    required this.phoneNumber,
    this.address,
    required this.countryCode,
  });
}
