import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safe_insets/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_store/sign_up_store_form.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

import '../../../../../../../src/components/src/loader/td_loader.dart';
import '../../../../../../../src/components/src/toast/toast.dart';
import '../../../../../../../src/components/src/utils/utils.dart';
import '../../../../../../../src/components/src/widgets/app_screen.dart';
import '../../../../../../../src/res/values/config/keys.dart';
import '../../../../../domain/params/post_params.dart';
import '../../../../logic/bloc/signup_cubit.dart';

class MobileSignUpStoreScreen extends StatefulWidget {
  final TdAddress? address;
  final Map<String, dynamic> userInfo;
  final Map<String, TdTextController> storeControllers;
  final String accessToken;

  /// value can either be a `phoneNumber` or an `email`
  /// depending on the [AppHost].
  final String phoneNumber;
  final String countryCode;

  const MobileSignUpStoreScreen({
    super.key,
    required this.userInfo,
    required this.storeControllers,
    required this.accessToken,
    required this.phoneNumber,
    this.address,
    required this.countryCode,
  });

  @override
  _SignUpStoreScreenState createState() => _SignUpStoreScreenState();
}

class _SignUpStoreScreenState extends State<MobileSignUpStoreScreen> {
  final _bloc = SignUpCubit(locator());
  // TdAddress? storeAddress;
  bool _loadingLocation = false;

  void setStoreAddress(TdAddress? address) {
    SharedPreferences.getInstance().then((sp) {
      sp.setString(Keys.storeAddress, json.encode(address?.toMap()));
    });
  }

  Future<TdAddress?> getStoreAddress() async {
    final sp = await SharedPreferences.getInstance();
    final address = sp.getString(Keys.storeAddress);
    return address != null ? TdAddress.fromMap(json.decode(address)) : null;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      child: BlocConsumer<SignUpCubit, SignUpState>(
        bloc: _bloc,
        builder: (context, state) => _build(context, state),
        listener: (context, state) {
          if (state is SignUpCompleted) {
            SharedPreferences.getInstance().then(
              (sp) {
                if (kIsWeb) {
                  sp.setBool(Keys.shopPreferences, true);
                  sp.remove(Keys.storeAddress);
                } else {
                  sp.setBool(Keys.enableNotification, true);
                  sp.remove(Keys.storeAddress);
                }
              },
            );

            if (kIsWeb) {
              context.goNamed(
                ShopPreferencesPath,
                extra: PreferenceType.collection,
              );
            } else {
              context.goNamed(NotificationSetupPath);
            }

            TdLoader.hide();

            // track completed signUp event
            Segment.track(
              eventName: SegmentEvents.accountCreationCompleted,
              properties: {
                'user': state.user.toMap(),
              },
            );
          }

          if (state is SignUpFailed) {
            Toast.error('${state.errorMessage}', context);
            TdLoader.hide();
          }

          if (state is SignUpLoading) {
            TdLoader.show(context);
          }
        },
      ),
    );
  }

  Widget _build(BuildContext context, SignUpState state) {
    final userCubit = context.read<UserCubit>();
    final shopTopUpApp = userCubit.domain == AppHost.shopTopUp;
    return Scaffold(
      appBar: ShopAppBar.shopAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTop(),
              YMargin(20),
              Padding(
                padding: context.insetsSymetric(
                  horizontal: defaultHorizontalContentPadding,
                ),
                child: Align(
                  alignment: Alignment.center,
                  child: SignUpStoreForm(
                    controllers: widget.storeControllers,
                    countryCode: widget.countryCode,
                    onSetLocation: (data) {
                      setStoreAddress(data);
                    },
                    isLoading: _loadingLocation,
                    loading: (value) {
                      setState(() {
                        _loadingLocation = value;
                      });
                    },
                  ),
                ),
              ),
              YMargin(shopTopUpApp ? 80 : 30),
              Padding(
                padding: context.insetsSymetric(
                  horizontal: defaultHorizontalContentPadding,
                ),
                child: Center(
                  child: KButtonPrimary(
                    text: 'Create Profile',
                    onTap: () => createProfile(shopTopUpApp),
                  ),
                ),
              ),
              Center(child: _buildFooter()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    final appName = 'TradeDepot';

    return Padding(
      padding: context.insetsOnly(
        left: defaultHorizontalContentPadding,
        right: defaultHorizontalContentPadding,
        bottom: defaultVerticalContentPaddingLarge,
      ),
      child: Column(
        children: [
          YMargin(10),
          Text(
            'By signing up, you agree to $appName',
            style: KTextStyle.bodyText2,
          ),
          SafeAreaWrap(
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: openTermsUri,
                  child: Text(
                    'Terms of Service',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        // decoration: TextDecoration.underline,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary),
                  ),
                ),
                Text(' & '),
                InkWell(
                  onTap: openPrivacyUri,
                  child: Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        // decoration: TextDecoration.underline,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> createProfile(bool shopTopUpApp) async {
    if (!widget.storeControllers.validate()) {
      return;
    }

    final user = widget.userInfo;
    final store = widget.storeControllers.data();

    final contactAddress = widget.address;

    if (shopTopUpApp && contactAddress == null) {
      // should never occur
      _bloc.setError('House address not found');
      return;
    }

    final storeAddress = await getStoreAddress();
    // final visitorId = await getVisitorId();

    if (storeAddress == null) {
      // should never occur
      _bloc.setError('Store address not found ');
      return;
    }

    final data = <String, dynamic>{
      'name': store['name'],
      'description': store['name'],
      'country': storeAddress.countryCode,
      'lga': store['lga'],
      'landmark': store['landmark'],
      'gender': user['gender'],
      'user': {
        'firstName': user['firstName'],
        'lastName': user['lastName'],
      },
      'address': {
        'company': store['name'],
        'fullName': user['firstName'] + ' ' + user['lastName'],
        'address': storeAddress.address,
        'city': storeAddress.city,
        'lga': storeAddress.lga,
        'state': storeAddress.state,
        'country': storeAddress.countryCode,
        'postal': storeAddress.postal,
        if (storeAddress.latitude != null && storeAddress.longitude != null)
          'coordinates': {
            'latitude': storeAddress.latitude,
            'longitude': storeAddress.longitude,
          },
      },
      "outletTypeId": store['type'],
      "extChannel": getExtChannel(),
      'referralCode': user['referralCode'],
    };

    final sp = await SharedPreferences.getInstance();
    final phone = sp.getString(Keys.tdAppPhone);
    final email = sp.getString(Keys.tdAppEmail);

    final userCubit = context.read<UserCubit>();

    data['email'] = email;
    data['phoneNumber'] = phone!;
    data['domain'] = userCubit.domain?.name;

    _bloc.signUp(
      SignUpParams(
        data: data,
        phoneNumber: phone,
        accessToken: widget.accessToken,
      ),
    );
  }

  Widget _buildTop() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: context.insetsSymetric(
            horizontal: defaultHorizontalContentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Store Details",
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              YMargin(20),
              Text(
                "Add your store's information for delivery and contact.",
                style: KTextStyle.bodyText2.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).hintColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
