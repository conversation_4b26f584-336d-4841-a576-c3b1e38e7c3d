import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/signup_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_store/sign_up_store_form.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/progress_bar.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/side_bar.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/DI/di.dart';

class WebSignUpStoreScreen extends StatefulWidget {
  final TdAddress? address;
  final Map<String, dynamic> userInfo;
  final Map<String, TdTextController> storeControllers;
  final String accessToken;
  final String phoneNumber;
  final String countryCode;

  const WebSignUpStoreScreen({
    super.key,
    required this.userInfo,
    required this.storeControllers,
    required this.accessToken,
    required this.phoneNumber,
    this.address,
    required this.countryCode,
  });

  @override
  State<StatefulWidget> createState() {
    return _SignUpStore();
  }
}

class _SignUpStore extends State<WebSignUpStoreScreen> {
  final _bloc = SignUpCubit(locator());
  // bool _hasReferralCode = false;
  TdAddress? contactAddress;
  bool _loadingLocation = false;

  void setStoreAddress(TdAddress? address) {
    SharedPreferences.getInstance().then((sp) {
      sp.setString(Keys.storeAddress, json.encode(address?.toMap()));
    });
  }

  Future<TdAddress?> getStoreAddress() async {
    final sp = await SharedPreferences.getInstance();
    final address = sp.getString(Keys.storeAddress);
    return address != null ? TdAddress.fromMap(json.decode(address)) : null;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignUpCubit, SignUpState>(
      bloc: _bloc,
      builder: (context, state) => _build(context, state),
      listener: (context, state) {
        if (state is SignUpCompleted) {
          SharedPreferences.getInstance().then((sp) {
            sp.setBool(Keys.createPin, true);
          });
          context.pushNamed(
            EnterPinPath,
            extra: EnterPinScreenArgs(name: state.user.name),
          );
        }

        if (state is SignUpFailed) {
          Toast.error('${state.errorMessage}', context);
          TdLoader.hide();
        }
      },
    );
  }

  Widget _build(BuildContext context, SignUpState state) {
    return AppScreen(
      child: Scaffold(
        backgroundColor: Colors.white,
        body: DesktopConstrainedBox(
          child: Container(
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.zero,
                    bottomRight: Radius.circular(5),
                    bottomLeft: Radius.zero,
                    topRight: Radius.circular(5))),
            alignment: Alignment.center,
            padding: EdgeInsets.zero,
            height: 800,
            width: 1200, //SizeConfig.scaleX(0.57),
            child: Row(
              children: [
                SideBar(),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 30, horizontal: 50),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ProgressBar(1),
                        Padding(
                          padding: EdgeInsets.only(top: 30),
                          child: SvgPicture.asset(kSvgLogo,
                              width: 100.0,
                              height: 100,
                              alignment: Alignment.centerLeft),
                        ),
                        YSpacing(16),
                        Text("Create your TradeDepot Account",
                            style: KTextStyle.semiBold24),
                        YSpacing(5),
                        Text(
                            "Add your shop name and address to sign up your business and for delivery of your orders.",
                            style: KTextStyle.regular14),
                        YSpacing(30),
                        SignUpStoreForm(
                          controllers: widget.storeControllers,
                          countryCode: widget.countryCode,
                          onSetLocation: (data) {
                            setStoreAddress(data);
                          },
                          isLoading: _loadingLocation,
                          loading: (value) {
                            setState(() {
                              _loadingLocation = value;
                            });
                          },
                        ),
                        YSpacing(30),
                        Row(
                          children: [
                            Expanded(child: Offstage()),
                            XSpacing(30),
                            Container(),
                            _submitButton(state)
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

/*  Widget _buildFooter(SignUpState state) {
    return Padding(
      padding: context.insetsOnly(
        left: 20,
        right: 20,
        bottom: 5,
        top: 20,
      ),
      child: _submitButton(),
    );
  }*/

  Widget _submitButton(SignUpState state) {
    if (_loadingLocation) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return KButton(
      onPressed: () async {
        if (!widget.storeControllers.validate()) {
          return;
        }

        final user = widget.userInfo;
        final store = widget.storeControllers.data();

        final contactAddress = widget.address;

        if (contactAddress == null) {
          // should never occur
          _bloc.setError('House address not found');
          return;
        }

        final storeAddress = await getStoreAddress();
        //  final visitorId = await getVisitorId();

        if (storeAddress == null) {
          // should never occur
          _bloc.setError('Store address not found ');
          return;
        }

        final data = <String, dynamic>{
          'name': store['name'],
          'description': store['name'],
          'country': contactAddress.countryCode,
          'lga': store['lga'],
          'landmark': store['landmark'],
          'gender': user['gender'],
          'user': {
            'firstName': user['firstName'],
            'lastName': user['lastName'],
          },
          'contactAddress': {
            'address': contactAddress.address,
            'city': contactAddress.city,
            'lga': contactAddress.lga,
            'state': contactAddress.state,
            'country': contactAddress.countryCode,
            'coordinates': {
              'latitude': contactAddress.latitude,
              'longitude': contactAddress.longitude,
            },
          },
          'address': {
            'company': store['name'],
            'fullName': user['firstName'] + ' ' + user['lastName'],
            'address': storeAddress.address,
            'city': storeAddress.city,
            'lga': storeAddress.lga,
            'state': storeAddress.state,
            'country': storeAddress.countryCode,
            'coordinates': {
              'latitude': storeAddress.latitude,
              'longitude': storeAddress.longitude,
            },
          },
          "outletTypeId": store['type'],
          "extChannel": getExtChannel(),
          "phoneNumber": widget.phoneNumber,
          'referralCode': user['referralCode'],
        };

        _bloc.signUp(
          SignUpParams(
            data: data,
            phoneNumber: widget.phoneNumber,
            accessToken: widget.accessToken,
            // visitorId: visitorId,
          ),
        );
      },
      isLoading: state is SignUpLoading,
      text: 'Continue',
    );
  }
}
