// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:safe_insets/index.dart';
// import 'package:shop/route_constants.dart';
// import 'package:shop/src/res/values/colors/colors.dart';
// import 'package:td_flutter_src/td_flutter_src.dart';

// class MobileSignUpScreen extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Text('MobileSignUpScreen'),
//         _buildFooter(context),
//       ],
//     );
//     // return VerifyPhoneScreen(
//     //   pageType: AuthPageType.SignUp,
//     //   title: "Let’s get started, verify\nyour phone number",
//     //   subtitle: "We’ll send an SMS with a code to verify your phone "
//     //       "number and secure your ShopTopUp account.",
//     //   footer: _buildFooter(context),
//     // );
//   }

//   Widget _buildFooter(BuildContext context) {
//     return Column(
//       children: [
//         YMargin(25),
//         Text('I already have a ShopTopUp Account'),
//         SafeAreaWrap(
//           TextButton(
//             onPressed: () {
//               context.pushReplacementNamed(LoginPath);
//             },
//             child: Text(
//               'Login',
//               style: TextStyle(
//                 color: kColorBlue,
//                 decoration: TextDecoration.underline,
//                 fontWeight: FontWeight.normal,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
