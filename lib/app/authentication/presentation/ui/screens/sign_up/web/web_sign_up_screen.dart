import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_phone/verify_phone_screen.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:url_launcher/link.dart';

class WebSignUpScreen extends StatelessWidget {
  const WebSignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return VerifyPhoneScreen(
      args: VerifyPhoneArgs(
        pageType: AuthPageType.signUp,
        otpParams: const SendOTParams(
          mode: PhoneAuthMode.WhatsApp,
          phoneNumber: '',
          url: '',
        ), // Just for web build, so as not to disrupt the mobile flow
        webParams: WebParams(
          title: AppLocalizations.of(context)!.get_started,
          subtitle: AppLocalizations.of(context)!.get_started_info,
          info: "We’ll send an SMS with a code to verify your phone "
              "number \n and secure your TradeDepot account.",
          footer: _buildFooter(context),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppLocalizations.of(context)!.terms_n_conditions,
          style: KTextStyle.light14.copyWith(
            fontSize: 13,
          ),
        ),
        Link(
          uri: Uri.parse(context.read<AppConfig>().termsUrl!),
          target: LinkTarget.blank,
          builder: (context, onPressed) => InkWell(
              onTap: onPressed,
              child: Text(AppLocalizations.of(context)!.terms_of_use,
                  style: KTextStyle.light14.copyWith(
                      fontSize: 13, decoration: TextDecoration.underline))),
        ),
        Text(
          "&",
          style: KTextStyle.light14.copyWith(
            fontSize: 13,
          ),
        ),
        Link(
          uri: Uri.parse(context.read<AppConfig>().privacyUrl!),
          target: LinkTarget.blank,
          builder: (context, onPressed) => InkWell(
              onTap: onPressed,
              child: Text(
                AppLocalizations.of(context)!.privacy_policy,
                style: KTextStyle.light14.copyWith(
                    fontSize: 13, decoration: TextDecoration.underline),
              )),
        ),
      ],
    );
  }
}
