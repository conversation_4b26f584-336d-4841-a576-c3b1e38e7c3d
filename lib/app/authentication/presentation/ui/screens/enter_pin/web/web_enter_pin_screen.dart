import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/domain/use_cases/change_pin.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/create_pin.dart';
import 'package:shop/app/authentication/domain/use_cases/reset_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/verify_nin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/pin_field.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/progress_bar.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/side_bar.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/navigation/src/navigation.dart';

class WebEnterPinScreen extends StatefulWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final ResetPinParams? resetPinParams;
  final bool migrating; // wether user is migrating to six digit pin
  final String? oldPin;

  const WebEnterPinScreen({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.resetPinParams,
    required this.migrating,
    this.oldPin,
  });

  @override
  _EnterPinScreenState createState() => _EnterPinScreenState();
}

class _EnterPinScreenState extends State<WebEnterPinScreen> {
  String _errorMessage = '';
  bool _loading = false;

  bool get loggingIn => widget.phone != null;

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      canPop: !_loading,
      child: Scaffold(
    
        body: DesktopConstrainedBox(
          child: Container(
            decoration: BoxDecoration(
            
              borderRadius: BorderRadius.only(
                topLeft: Radius.zero,
                bottomRight: Radius.circular(5),
                bottomLeft: Radius.zero,
                topRight: Radius.circular(5),
              ),
            ),
            alignment: Alignment.center,
            padding: EdgeInsets.zero,
            height: 800,
            width: 1200, //SizeConfig.scaleX(0.57),
            child: Row(
              children: [
                SideBar(),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 30, horizontal: 50),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ProgressBar((widget.pin?.isEmpty ?? true) ? 2 : 3),
                        Padding(
                          padding: EdgeInsets.only(top: 30),
                          child: SvgPicture.asset(kSvgLogo,
                              width: 100.0,
                              height: 100,
                              alignment: Alignment.centerLeft),
                        ),
                        YSpacing(16),
                        Text(
                          widget.migrating
                              ? "Change your PIN"
                              : "Set Up your PIN",
                          style: KTextStyle.semiBold24,
                        ),
                        YSpacing(5),
                        _errorMessage.isEmpty
                            ? Text(
                                (widget.pin?.isEmpty ?? true)
                                    ? widget.migrating
                                        ? "You're required to upgrade to a 6 digit PIN, Please enter your new PIN."
                                        : "Create a 6 digit PIN that would be used to log into you account."
                                    : "Confirm your PIN",
                                style: KTextStyle.regular14)
                            : Text(_errorMessage, style: KTextStyle.regular14),
                        YSpacing(80),
                        PinField(_create, 6),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /* void _enterPin(String pin) async {
    if (!loggingIn) return _create(pin);

    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    final res = await locator.get<LoginWithPin>().call(
      LoginPinParams(widget.phone, pin),
    );

    res.maybeWhen(
      success: (_) {
        UserCubit _userCubit = context.read();
        _userCubit.updatingUser(_);

        SharedPreferences.getInstance().then((sp) {
          sp.remove(Keys.resetPin);
          sp.remove(Keys.createPin);
        });

        goHomeWeb();
      },
      apiFailure: (e, _) {
        setState(() {
          _loading = false;
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
      },
      orElse: () {
        setState(() {
          _loading = false;
          _errorMessage =
          'An unexpected error occurred. Please try again later.';
        });
      },
    );
  }*/

  void _createPin() async {
    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    ApiResult<Map<String, dynamic>?> res;

    final resettingPassword = widget.resetPinParams != null;

    if (resettingPassword) {
      res = await locator.get<ResetPin>().call(
            widget.resetPinParams!.addPin(widget.pin),
          );
    } else if (widget.migrating) {
      res = await locator
          .get<ChangePin>()
          .call(ChangePinParams(widget.oldPin, widget.pin));
    } else {
      res = await locator.get<CreatePin>().call(
            CreatePinParams(widget.pin),
          );
    }

    res.when(
      success: (_) {
        _proceed(resettingPassword);
      },
      apiFailure: (e, _) {
        final message = ApiExceptions.getErrorMessage(e);
        if (message.toLowerCase().contains('pin already created')) {
          _proceed(resettingPassword);
          return;
        }

        SharedPreferences.getInstance().then((sp) {
          sp.remove(Keys.migratePin);
          sp.remove(Keys.oldPin);
        });

        setState(() {
          _loading = false;
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
      },
    );
  }

  void _proceed(bool resettingPassword) {
    SharedPreferences.getInstance().then((sp) {
      sp.remove(Keys.resetPin);
      sp.remove(Keys.createPin);
      sp.remove(Keys.migratePin);
    });

    if (resettingPassword || widget.migrating) {
      goHomeWeb(context, false, resettingPassword);
    } else {
      final userCubit = UserCubit.instance;
      if (userCubit?.isNgUser == true) {
        context.goNamed(VerifyNINPath,
            extra: VerifyNinScreenArgs(AuthPageType.verifySignUpNin));
        return;
      }
      goHomeWeb(context, true);
    }
  }

  void _create(String pin) {
    if (!widget.confirm) {
      navigate(
        context,
        EnterPinScreen(
          confirm: true,
          pin: pin,
          resetPinParams: widget.resetPinParams,
          migrating: widget.migrating,
          oldPin: widget.oldPin,
        ),
      );

/*      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EnterPinScreen(
              confirm: true,
              pin: pin,
              resetPinParams: widget.resetPinParams,
            ),
            fullscreenDialog: false,
          ));*/
      return;
    }

    // validate pin
    if (pin != widget.pin) {
      setState(() {
        _errorMessage = 'Pin does not match';
      });
      return;
    }

    // submit
    _createPin();
  }

/*  Widget _buildCreateButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != 4 ? null : _enterPin,
      text: _loading
          ? 'Creating...'
          : widget.confirm
          ? 'Confirm Pin'
          : 'CREATE PIN',
    );
  }

  Widget _buildLoginButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != 4
          ? null
          : _enterPin, // submit
      text: _loading ? 'Loading...' : 'ENTER PIN',
    );
  }*/

  // Future<void> goHomeWeb([
  //   bool intro = false,
  //   bool resetPassword = false,
  // ]) async {
  //   if (intro) {
  //     context.goNamed(homePageConfig);
  //   } else if (resetPassword) {
  //     context.goNamed(loginPageConfig);
  //   } else {
  //     final info = await deviceInfo();
  //     try {
  //       final userId = UserCubit.instance?.currentUser?.userId;
  //       final res =
  //           await FirebaseFirestore.instance.doc('devices/$userId').get();

  //       if (res.exists && res.get('id') == info['id']) {
  //         // User device has not changed.
  //         // Take the user home and don't request for OTP
  //         context.goNamed(homePageConfig);
  //         return;
  //       }
  //     } catch (e) {}

  //     // Additional OTP verification on login
  //     SharedPreferences.getInstance().then(
  //       (sp) => sp.setBool(Keys.loginOtp, true),
  //     );
  //     context.goNamed(verifyOTPPageConfig);
  //   }
  // }
}

//PageConfiguration confirmEnterPinPageConfig = AuthPageConfiguration(
//  key: 'Auth_Confirm_Enter_Pin', path: EnterPinPath, uiPage: AuthPages.EnterPin);
