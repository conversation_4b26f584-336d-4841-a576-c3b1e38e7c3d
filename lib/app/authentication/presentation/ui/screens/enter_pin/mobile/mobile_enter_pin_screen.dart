import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safe_insets/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/create_pin.dart';
import 'package:shop/app/authentication/domain/use_cases/reset_pin.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/reset_pin_success/reset_pin_success.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/widgets/flexible_constrained_box.dart';
import 'package:shop/src/components/src/widgets/k_keyboard.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import '../../../../../../../src/components/src/loader/td_loader.dart';
import '../../../../../data/models/page_type.dart';

class MobileEnterPinScreen extends StatefulWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final ResetPinParams? resetPinParams;
  final AuthPageType? pageType;

  const MobileEnterPinScreen({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.resetPinParams,
    this.pageType,
  });

  @override
  _EnterPinScreenState createState() => _EnterPinScreenState();
}

class _EnterPinScreenState extends State<MobileEnterPinScreen> {
  final _passcodeCtrl = TextEditingController();
  String _errorMessage = '';
  // bool _loading = false;
  bool canContinue = false;

  final int length = 6;

  // bool get loggingIn => widget.phone != null;
  bool get resettingPin => widget.resetPinParams != null;

  Future<bool> handlePop() async {
    if (TdLoader.isLoading) return false;

    if (widget.confirm) {
      Navigator.pop(context);
    } else {
      context.pop();
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: handlePop,
      child: (widget.confirm || resettingPin)
          ? Scaffold(
              appBar: AppBar(
                elevation: 0,
                leading: InkWell(
                  onTap: handlePop,
                  child: Icon(
                    Icons.arrow_back,
                  ),
                ),
              ),
              body: _buildBody(),
            )
          : Scaffold(
              body: _buildBody(),
            ),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: FlexibleConstrainedBox(
        minHeight: MediaQuery.sizeOf(context).height * 0.5,
        child: Center(
          child: Column(
            children: [
              _buildTop(),
              YMargin(40),
              IgnorePointer(
                ignoring: true,
                child: KCodeInput(
                  controller: _passcodeCtrl,
                  length: length,
                  builder: CodeInputBuilders.darkRectangle(
                    context: context,
                    emptySize: Size(40.0, 50.0),
                  ),
                ),
              ),
              YMargin(20),
              Expanded(
                child: KKeyPad(
                  activatePeriod: false,
                  onKeyTap: _addToText,
                  onRemoveTap: _removeLastDigit,
                  // disabled: _loading,
                ),
              ),
              // const YMargin(60),
              // Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: defaultHorizontalContentPadding),
                child: KButtonPrimary(
                  text: widget.confirm ? 'Confirm' : 'Continue',
                  onTap: canContinue ? _create : null,
                  disabled: !canContinue,
                ),
              ),
              const SafeAreaWrap(
                SizedBox(height: 5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTop() {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // if (!widget.confirm &&
        //     widget.pageType != AuthPageType.resetPinBeforeLogin) ...[
        //   const YMargin(20),
        //   Padding(
        //     padding: const EdgeInsets.only(right: 18.0),
        //     child: Align(
        //       alignment: Alignment.bottomRight,
        //       child: KButtonPrimary(
        //         text: 'Skip',
        //         color: kColorBlue,
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(38.0),
        //         ),
        //         constraints: BoxConstraints.tight(
        //           Size(80, 40),
        //         ),
        //         onTap: () => _proceed(resettingPin),
        //       ),
        //     ),
        //   ),
        // ] else
        //   SizedBox(height: 20),
        YMargin(widget.confirm ? 20 : 48),
        Padding(
          padding: context.insetsSymetric(
            horizontal: defaultHorizontalContentPadding,
          ),
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  _title,
                  style: KTextStyle.bodyText2.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const YMargin(12),
                _errorMessage.isNotEmpty
                    ? Text(
                        textAlign: TextAlign.center,
                        _errorMessage,
                        style: textTheme.bodyLarge?.copyWith(
                          color: Colors.red,
                        ),
                      )
                    : Text(
                        _subtitle,
                        style: textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).hintColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _createPin() async {
    setState(() {
      _errorMessage = '';
      // _loading = true;
    });

    TdLoader.show(context);

    ApiResult<Map<String, dynamic>?> res;

    if (resettingPin) {
      res = await locator.get<ResetPin>().call(
            widget.resetPinParams!.addPin(widget.pin),
          );
    } else {
      res = await locator.get<CreatePin>().call(
            CreatePinParams(widget.pin),
          );
    }

    res.when(
      success: (_) {
        _proceed(resettingPin);
        // TdLoader.hide();
      },
      apiFailure: (e, _) {
        // TdLoader.hide();
        final message = ApiExceptions.getErrorMessage(e);
        if (message.toLowerCase().contains('pin already created')) {
          _proceed(resettingPin);
          return;
        }

        _passcodeCtrl.text = '';

        TdLoader.hide();

        setState(() {
          // _loading = false;
          _errorMessage = ApiExceptions.getErrorMessage(e);
          canContinue = false;
        });
      },
    );
  }

  void _proceed(bool resettingPin) async {
    final sp = await SharedPreferences.getInstance();

    sp.remove(Keys.resetPin);
    sp.remove(Keys.createPin);

    // TdLoader.hide();

    if (widget.pageType == AuthPageType.resetPinBeforeLogin) {
      final businessName = sp.getString(Keys.outletBusinessName);

      TdLoader.hide();

      context.goNamed(
        ResetPinSuccessPath,
        extra: ResetPinSuccessArgs(
          phoneNumber: widget.resetPinParams!.phoneNumber,
          businessName: '$businessName',
        ),
      );
    } else if (resettingPin) {
      TdLoader.hide();
      goHomeMobile(context, false, true);
    } else {
      if (kIsWeb) {
        sp.setBool(Keys.shopPreferences, true).then((value) {
          TdLoader.hide();
          Navigator.pop(context);
          context.goNamed(
            ShopPreferencesPath,
            extra: PreferenceType.collection,
          );
        });
      } else {
        sp.setBool(Keys.enableNotification, true).then((value) {
          TdLoader.hide();
          Navigator.pop(context);
          context.goNamed(
            NotificationSetupPath,
          );
        });
      }
    }
  }

  void _addToText(String character) {
    if (_passcodeCtrl.text.length < length) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}$character';
      });
    }
    // auto submit pin
    if (_passcodeCtrl.text.length == length) {
      setState(() {
        canContinue = true;
      });
    }
  }

  void _removeLastDigit() {
    if (_passcodeCtrl.text.isNotEmpty) {
      setState(() {
        _passcodeCtrl.text = '${_passcodeCtrl.text}0'
            .substring(0, _passcodeCtrl.text.length - 1);
      });
    }

    if (canContinue) {
      setState(() {
        canContinue = false;
      });
    }
  }

  String get _title {
    if (widget.confirm) {
      return "Confirm your PIN";
    }
    if (resettingPin) {
      String text = 'Reset PIN';
      try {
        final name =
            FirebaseAuth.instance.currentUser!.displayName!.split(' ').first;
        text = 'Hello $name';
      } catch (_) {}

      return text;
    }
    return "Faster login and payments";
  }

  String get _subtitle {
    if (widget.confirm) {
      return "";
    }
    if (resettingPin) {
      return 'Enter a new account PIN';
    }
    return 'Create an account PIN to enable faster login and payments';
  }

  void _create() {
    if (!widget.confirm) {
      navigate(
        context,
        EnterPinScreen(
          confirm: true,
          pin: _passcodeCtrl.text,
          resetPinParams: widget.resetPinParams,
          pageType: widget.pageType,
        ),
      );
      // context.pushNamed(
      //   VerifyPinPath,
      //   extra: EnterPinScreenArgs(
      //     confirm: true,
      //     pin: _passcodeCtrl.text,
      //     resetPinParams: widget.resetPinParams,
      //     pageType: widget.pageType,
      //   ),
      // );
      return;
    }

    // validate pin
    if (_passcodeCtrl.text != widget.pin) {
      setState(() {
        _errorMessage = 'Pin does not match';
      });
      return;
    }

    // submit
    _createPin();
  }
}
