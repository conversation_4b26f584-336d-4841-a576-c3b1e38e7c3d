import 'package:flutter/material.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/mobile/mobile_enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/web/web_enter_pin_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import '../../../../data/models/page_type.dart';

class EnterPinScreen extends StatelessWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final ResetPinParams? resetPinParams;
  final AuthPageType? pageType;
  final bool? migrating;
  final String? oldPin;

  const EnterPinScreen({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.resetPinParams,
    this.pageType,
    this.migrating,
    this.oldPin,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebEnterPinScreen(
        confirm: confirm,
        phone: phone,
        pin: pin,
        resetPinParams: resetPinParams,
        name: name,
        migrating: migrating ?? false,
        oldPin: oldPin,
      ),
      mediumScreen: MobileEnterPinScreen(
        confirm: confirm,
        phone: phone,
        pin: pin,
        resetPinParams: resetPinParams,
        name: name,
        pageType: pageType,
      ),
      smallScreen: MobileEnterPinScreen(
        confirm: confirm,
        phone: phone,
        pin: pin,
        resetPinParams: resetPinParams,
        name: name,
        pageType: pageType,
      ),
    );
  }
}

class EnterPinScreenArgs {
  final bool confirm;
  final Key? key;
  final String? pin;
  final String? name;
  final String? phone;
  final ResetPinParams? resetPinParams;
  final AuthPageType? pageType;
  final bool? migrated;
  final String? oldPin;

  EnterPinScreenArgs({
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.resetPinParams,
    this.pageType,
    this.key,
    this.migrated,
    this.oldPin,
  });
}
