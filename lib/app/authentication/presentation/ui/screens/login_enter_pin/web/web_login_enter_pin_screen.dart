import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/enter_pin/enter_pin_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/login/login_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/info.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/pin_field.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/visitor_widget.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/buttons/src/td_text_button.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:shop/src/res/values/styles/button_style.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class WebLoginEnterPinScreen extends StatefulWidget {
  final bool confirm;
  final String? pin;
  final String? name;
  final String? phone;
  final ResetPinParams? resetPinParams;
  final bool migrated;

  const WebLoginEnterPinScreen({
    super.key,
    this.confirm = false,
    this.pin,
    this.name,
    this.phone,
    this.resetPinParams,
    required this.migrated,
  });

  @override
  _EnterPinScreenState createState() => _EnterPinScreenState();
}

class _EnterPinScreenState extends State<WebLoginEnterPinScreen> {
  String _errorMessage = '';
  bool _loading = false;
  String pin = "";
  final GlobalKey<PinFieldState> _pinState = GlobalKey<PinFieldState>();
  bool get loggingIn => widget.phone != null;
  late final int length = widget.migrated ? 6 : 4;

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      canPop: !_loading,
      child: Scaffold(
        body: DesktopConstrainedBox(
          child: Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.zero,
                    bottomRight: Radius.circular(5),
                    bottomLeft: Radius.zero,
                    topRight: Radius.circular(5))),
            alignment: Alignment.center,
            padding: const EdgeInsets.all(50),
            height: 844,
            width: 892, //SizeConfig.scaleX(0.57),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(kSvgShopTopUp,
                    width: 100.0, height: 100, alignment: Alignment.center),
                const YSpacing(20),
                const Text("Welcome Back", style: KTextStyle.semiBold24),
                const YSpacing(10),
                Text("Login to your TradeDepot account",
                    style: KTextStyle.book14.copyWith(fontSize: 16)),
                const YSpacing(20),
                Align(
                  alignment: Alignment.center,
                  child: _errorMessage.isEmpty
                      ? RichText(
                          text: TextSpan(
                            text: "Enter your",
                            style: KTextStyle.book14,
                            children: [
                              TextSpan(
                                  text: " PIN ",
                                  style:
                                      KTextStyle.bold28.copyWith(fontSize: 14)),
                              const TextSpan(text: "to log into your account")
                            ],
                          ),
                        )
                      : Text(_errorMessage, style: KTextStyle.regular14),
                ),
                const YSpacing(30),
                PinField(_enterPin, length, key: _pinState),
                const YSpacing(80),
                KButton(
                  onPressed: () {},
                  width: 400,
                  text: 'Continue',
                  isLoading: _loading,
                ),
                const YSpacing(30),
                Info(AuthPageType.login),
                const YSpacing(30),
                TdTextButton(
                    label: AppLocalizations.of(context)!.go_back,
                    onPressed: () => context.pop(),
                    style: ButtonStyles.kPlainTextButtonStyle),
                const YSpacing(20),
                _buildFooter()
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _enterPin(String pin) async {
    // if (!loggingIn) return _create(pin);

    setState(() {
      _errorMessage = '';
      _loading = true;
    });
    // to prevent splash screen listener from immediately routing to homePage on web when userStream has data
    SharedPreferences.getInstance().then(
      (sp) => sp.setBool(Keys.skipLoginWebListener, true),
    );
    final visitorId = await getVisitorId();
    final res = await locator.get<LoginWithPin>().call(
          LoginPinParams(
            phone: widget.phone!,
            pin: pin,
            visitorId: visitorId,
          ),
        );

    res.when(
      success: (LoginResponse res) async {
        if (res.type == LoginResponseType.user) {
          UserCubit userCubit = context.read();
          userCubit.updatingUser(res.user!);

          final sp = await SharedPreferences.getInstance();
          sp.remove(Keys.resetPin);
          sp.remove(Keys.createPin);

          if (!widget.migrated) {
            sp.setBool(Keys.migratePin, true);
            sp.setBool(Keys.isDesktopWeb, true);
            sp.setString(Keys.oldPin, pin);
            context.pushNamed(
              EnterPinPath,
              extra: EnterPinScreenArgs(
                migrated: widget.migrated,
                oldPin: pin,
              ),
            );
          } else {
            if (mounted) goHomeWeb(context);
          }
        } else {
          VisitorWidget.show(context, res.visitor!, widget.phone!, pin, true);
          setState(() {
            _errorMessage = '';
            _loading = false;
            _pinState.currentState!.reset();
          });
        }
      },
      apiFailure: (e, _) {
        setState(() {
          _loading = false;
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
        _pinState.currentState!.reset();
      },
    );
  }

/*  Widget _submitButton() {
    return KButton(
      onPressed: () => pin.isEmpty ? () {} : _enterPin(pin),
      text: 'Continue',
      isLoading: _loading,
    );
  }*/

/*  void _createPin() async {
    setState(() {
      _errorMessage = '';
      _loading = true;
    });

    ApiResult<Map<String, dynamic>?> res;

    final resettingPassword = widget.resetPinParams != null;

    if (resettingPassword) {
      res = await locator.get<ResetPin>().call(
            widget.resetPinParams!.addPin(widget.pin),
          );
    } else {
      res = await locator.get<CreatePin>().call(
            CreatePinParams(widget.pin),
          );
    }

    res.maybeWhen(
      success: (_) {
        _proceed(resettingPassword);
      },
      apiFailure: (e, _) {
        final message = ApiExceptions.getErrorMessage(e);
        if (message.toLowerCase().contains('pin already created')) {
          _proceed(resettingPassword);
          return;
        }

        setState(() {
          _loading = false;
          _errorMessage = ApiExceptions.getErrorMessage(e);
        });
      },
      orElse: () {
        setState(() {
          _loading = false;
          _errorMessage =
              'An unexpected error occurred. Please try again later';
        });
      },
    );
  }*/

/*  void _proceed(bool resettingPassword) {
    SharedPreferences.getInstance().then((sp) {
      sp.remove(Keys.resetPin);
      sp.remove(Keys.createPin);
    });

    if (resettingPassword) {
      _goHome(false, true);
    } else {
      _goHome(true);
    }
  }*/

/*  void _create(String pin) {
    if (!widget.confirm) {
      */ /*     context.navigator.pushWidget(confirmEnterPinPageConfig, EnterPinScreen(
        confirm: true,
        pin: _passcodeCtrl.text,
        resetPinParams: widget.resetPinParams,
      ));*/ /*
      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EnterPinScreen(
              confirm: true,
              pin: pin,
              resetPinParams: widget.resetPinParams,
            ),
            fullscreenDialog: false,
          ));
      return;
    }

    // validate pin
    if (pin != widget.pin) {
      setState(() {
        _errorMessage = 'Pin does not match';
      });
      return null;
    }

    // submit
    _createPin();
  }*/

/*  Widget _buildCreateButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != 4 ? null : _enterPin,
      text: _loading
          ? 'Creating...'
          : widget.confirm
          ? 'Confirm Pin'
          : 'CREATE PIN',
    );
  }

  Widget _buildLoginButton() {
    return KSmallFlatButton(
      onPressed: _loading || _passcodeCtrl.text.length != 4
          ? null
          : _enterPin, // submit
      text: _loading ? 'Loading...' : 'ENTER PIN',
    );
  }*/

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text("I don’t remember my PIN ", style: KTextStyle.book14),
        TdTextButton(
          label: "Click Here",
          onPressed: () => context.goNamed(LoginPath,
              extra: LoginScreenArgs(widget.phone, widget.name)),
          style: ButtonStyles.paleTextButtonStyle.copyWith(
            minimumSize: WidgetStateProperty.all(const Size(89, 40)),
            textStyle: WidgetStateProperty.all(
                KTextStyle.regular14.copyWith(fontSize: 12)),
          ),
        ),
        /*      Link(
          uri: Uri.parse("/"),
          target: LinkTarget.blank,
          builder: (context, onPressed) => InkWell(
              child: Text("Click Here",
                  style: KTextStyle.bold28.copyWith(
                      fontSize: 12,
                      color: kColorBlue,
                      decoration: TextDecoration.underline,
                      decorationColor: kColorBlue)),
              onTap: onPressed),
        ),*/
      ],
    );
  }

/*  Future<void> _goHome([
    bool intro = false,
    bool resetPassword = false,
  ]) async {
    if (intro) {
      context.goNamed(HomePath);
    } else if (resetPassword) {
      context.goNamed(HomePath);
    } else {
      final info = await deviceInfo();
      try {
        final userId = UserCubit.instance?.currentUser?.userId;
        final res =
            await FirebaseFirestore.instance.doc('devices/$userId').get();

        if (res.exists && res.get('id') == info['id']) {
          // User device has not changed.
          // Take the user home and don't request for OTP
          context.goNamed(HomePath);
          return;
        }
      } catch (e) {}
      // Additional OTP verification on login
      SharedPreferences.getInstance().then(
        (sp) => sp.setBool(Keys.loginOtp, true),
      );
      context.pushNamed(VerifyOTPPath);
      setState(() {
        _loading = false;
        _errorMessage = '';
      });
      _pinState.currentState!.reset();
    }
  }*/
}

//PageConfiguration confirmEnterPinPageConfig = AuthPageConfiguration(
//  key: 'Auth_Confirm_Enter_Pin', path: EnterPinPath, uiPage: AuthPages.EnterPin);
