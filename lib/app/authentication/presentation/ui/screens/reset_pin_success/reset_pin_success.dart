import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../../route_constants.dart';
import '../login_with_pin/login_with_pin.dart';

class ResetPinSuccess extends StatelessWidget {
  final String phoneNumber;
  final String businessName;
  const ResetPinSuccess({
    super.key,
    required this.phoneNumber,
    required this.businessName,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: screenPadding,
          child: SingleChildScrollView(
            child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.9,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 75,
                    color: Theme.of(context).indicatorColor,
                  ),
                  const YMargin(30),
                  Text(
                    'PIN change successful',
                    style: KTextStyle.bodyText2.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YMargin(10),
                  Text(
                    'Please login with your New PIN',
                    style: KTextStyle.bodyText2.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: theme.hintColor,
                    ),
                  ),
                  const YMargin(60),
                  KButtonPrimary(
                    text: 'Login',
                    onTap: () {
                      context.goNamed(
                        LoginWithPinPath,
                        extra: LoginWithPinArgs(
                          phoneNumber: phoneNumber,
                          businessName: businessName,
                          migrated: true,
                          fromPinReset: true,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ResetPinSuccessArgs {
  final String phoneNumber;
  final String businessName;
  const ResetPinSuccessArgs({
    required this.phoneNumber,
    required this.businessName,
  });
}
