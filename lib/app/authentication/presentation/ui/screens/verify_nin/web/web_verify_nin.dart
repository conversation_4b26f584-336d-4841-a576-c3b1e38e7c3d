import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_nin.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/validate_phone_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/components/src/widgets/desktop_constrained_box.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import '../../../../../../../src/components/src/utils/values.dart';
import '../../../../logic/bloc/user_cubit.dart';

class WebVerifyNin extends StatefulWidget {
  const WebVerifyNin({
    super.key,
  });

  @override
  State<WebVerifyNin> createState() => _WebVerifyNinState();
}

class _WebVerifyNinState extends State<WebVerifyNin> {
  final _ninController = <String, TdTextController>{};

  @override
  void initState() {
    super.initState();
    _registerTextControllers();
  }

  void _registerTextControllers() {
    _ninController['nin'] = TdTextController(
      validators: [
        Validators.required(),
        Validators.min(11),
        Validators.max(11),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppScreen(
      child: Scaffold(
        backgroundColor: Colors.white,
        body: DesktopConstrainedBox(
          child: Container(
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(5))),
            alignment: Alignment.center,
            padding: EdgeInsets.only(
                top: 30, bottom: 10.0, left: 120.0, right: 120.0),
            height: 800,
            width: 892, //SizeConfig.scaleX(0.57),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Verify Your Identity',
                    style: KTextStyle.bodyText2
                        .copyWith(fontSize: 24, fontWeight: FontWeight.w600)),
                YSpacing(30),
                Text(
                  'Let’s get more identification credentials to complete your account creation\nand assist compliance.',
                  style: KTextStyle.bodyText2
                      .copyWith(fontSize: 16, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                ),
                YSpacing(70),
                SizedBox(
                  width: 400,
                  child: TdTextField(
                    title: 'NIN',
                    hint: 'Your National Identification Number',
                    textController: _ninController['nin'],
                    inputFormatters: [
                      validInput(),
                    ],
                    keyboardType: TextInputType.number,
                  ),
                ),
                YSpacing(50),
                KButton(
                  onPressed: () => handleNin(),
                  text: 'Confirm',
                ),
                YSpacing(30),
                KButton(
                  onPressed: () => goHomeWeb(context, true),
                  text: 'Skip to do so later ',
                  isOutline: true,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> handleNin() async {
    if (!_ninController.validate()) {
      return;
    }

    TdLoader.show(context);

    final nin = _ninController['nin']?.controller?.text;

    final res = await locator.get<VerifyNin>().call(NinParams(nin: nin!));

    res.when(
      success: (response) {
        if (response.isVerified) {
          TdLoader.hide();
          goHomeWeb(context, true);
        } else {
          final params = SendOTParams(
            mode: PhoneAuthMode.WhatsApp,
            phoneNumber: response.ninPhone!,
            url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
          );

          final nin = _ninController['nin']?.controller?.text;

          TdLoader.hide();

          context.pushNamed(
            WebValidateOTPPath,
            extra: ValidatePhoneArgs(
              params: params,
              pageType: AuthPageType.verifySignUpNin,
              countryCode: 'NG',
              name: UserCubit.instance?.currentUser?.name,
              ninParams: NinParams(
                  nin: nin!,
                  isValidated: true,
                  phoneNumber: params.phoneNumber),
            ),
          );
        }
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        final msg = ApiExceptions.getErrorMessage(error);
        Toast.error(msg, context);
      },
    );
  }
}
