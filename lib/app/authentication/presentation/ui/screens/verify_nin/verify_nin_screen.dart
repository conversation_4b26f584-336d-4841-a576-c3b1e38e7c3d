import 'package:flutter/material.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/mobile/mobile_verify_nin.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

import 'web/web_verify_nin.dart';

class VerifyNinScreen extends StatelessWidget {
  const VerifyNinScreen(
    this.type, {
    super.key,
  });

  final AuthPageType type;
  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebVerifyNin(),
      smallScreen: MobileVerifyNin(type),
      mediumScreen: MobileVerifyNin(type),
    );
  }
}

class VerifyNinScreenArgs {
  final AuthPageType type;
  VerifyNinScreenArgs(this.type);
}
