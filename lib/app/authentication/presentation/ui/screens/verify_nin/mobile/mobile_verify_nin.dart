import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_settings/domain/params/post_params.dart';
import 'package:shop/app/account_settings/presentation/ui/screens/smile_web.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/kyc_verification_modal.dart';
import 'package:shop/app/account_settings/presentation/ui/widget/permission_dialog.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_job.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app_config.dart';
import 'package:shop/app/credit/presentation/logic/methods/events_tracking.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/form/src/bloc/td_text_controller_cubit.dart';
import 'package:shop/src/components/src/form/src/extensions.dart';
import 'package:shop/src/components/src/form/src/td_text_field.dart';
import 'package:shop/src/components/src/form/src/validators.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/app_screen.dart';
import 'package:shop/src/js/helper.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

import '../../../../../../../src/components/src/buttons/src/k_button_primary.dart';
import '../../../../../../../src/res/assets/assets.dart';
//import 'dart:html' as webPermission;

class MobileVerifyNin extends StatefulWidget {
  const MobileVerifyNin(
    this.type, {
    super.key,
  });

  final AuthPageType type;
  @override
  State<MobileVerifyNin> createState() => _MobileVerifyNinState();
}

class _MobileVerifyNinState extends State<MobileVerifyNin> with EventsTracking {
  final _ninController = <String, TdTextController>{};
  final _nameController = <String, TdTextController>{};

  bool isLoading = false;
  late SmileJobCubit _smileJobCubit;
  bool ignoring = false;
  String? selectedDate;

  @override
  void initState() {
    super.initState();
    selectedDate =
        UserCubit.instance?.currentUser?.retailOutlets?.first.identity?.dob;
    _smileJobCubit = BlocProvider.of(context, listen: false);
    _registerTextControllers();
  }

  void _registerTextControllers() {
    _ninController['nin'] = TdTextController(
      validators: [Validators.required(), Validators.isNinOrBvnValid()],
    );
    _nameController['firstName'] = TdTextController(
      initialValue: UserCubit.instance?.currentUser?.firstName,
      validators: [Validators.required()],
    );
    _nameController['lastName'] = TdTextController(
      initialValue: UserCubit.instance?.currentUser?.lastName,
      validators: [Validators.required()],
    );
  }

  bool _canPop() {
    if (TdLoader.isLoading) return false;
    return !ignoring;
  }

  Future<void> handleSuccess(String jobId) async {
    _smileJobCubit.validateSmileJob(jobId);

    if (mounted) {
      setState(() {
        ignoring = true;
      });
    } else {
      ignoring = true;
    }

    if (widget.type == AuthPageType.verifySignUpNin) {
      context.pop();
      KYCVerificationModal.show(context);
      await Future.delayed(Duration(seconds: 2));
      nextAction();
    }

    if (widget.type == AuthPageType.verifyKycNin) {
      context.pop();
      context.pop();
      KYCVerificationModal.show(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final flavorIsDevelopment = config.environment == Environment.dev;
    return AppScreen(
      canPop: _canPop(),
      child: Scaffold(
        appBar: widget.type == AuthPageType.verifyKycNin ? AppBar() : null,
        bottomNavigationBar: Container(
          color: Theme.of(context).colorScheme.surface,
          padding: const EdgeInsets.all(16),
          child: SafeArea(
            child: KButtonPrimary(
              text: 'Continue',
              onTap: () {
                if (widget.type == AuthPageType.verifySignUpNin) {
                  initiateBiometrics("nin");
                } else {
                  handleNin();
                }
              },
            ),
          ),
        ),
        body: BlocListener<SmileJobCubit, SmileJobState>(
          listener: (context, state) async {
            if (state is SmileJobLoading) {
              TdLoader.show(context);
            } else {
              TdLoader.hide();
            }

            if (state is SmileJobFailed) {
              // TdLoader.hide();
              Toast.error(state.message, context);
            }

            if (state is SmileJobProcessInitiated) {
              ninUpdateInit();
              if (widget.type == AuthPageType.verifySignUpNin) {
                final helper = JSHelper();
                final res = await helper.requestCameraAndAudioPermissions(() {
                  /* if (state.enhancedKyc) {
                  context.pop();
                  KYCVerificationModal.show(context);
                } else {*/
                  final jobType = state.jobType == 'nin' ? "NIN_V2" : "BVN";
                  if (kIsWeb) {
                    final env = flavorIsDevelopment ? "sandbox" : "live";

                    return helper.configureSmileIdentityWebIntegration(
                      state.token!,
                      config.smileWebCallBackUrl!,
                      env,
                      jobType,
                      (String type) {
                        SmileResult result = SmileResult.values.byName(type);
                        switch (result) {
                          case SmileResult.success:
                            handleSuccess(state.jobId);
                            break;
                          case SmileResult.close:
                            // do nothing
                            break;
                          case SmileResult.error:
                            // TODO: render error message if necessary
                            break;
                        }
                      },
                    );
                  }
                  context.pushNamed(smileWebPath,
                      extra: SmileWebViewArgs(state.token!, jobType,
                          onSuccess: (value) async {
                        handleSuccess(state.jobId);
                      }, onClose: () {
                        context.pop();
                        Toast.error("Verification process exited", context);
                      }, onFailed: () {
                        context.pop();
                        Toast.error("Verification process Failed", context);
                      }));
                  // }
                }, state.enhancedKyc);

                if (!res) {
                  showDialog(
                      context: context,
                      builder: (ctx) {
                        return PermissionDialog();
                      });
                }
              } else {
                context.pop();
                KYCVerificationModal.show(context);
              }
            }

            if (state is SmileJobThreeTrialsWarning) {
              Toast.error(state.message, context);
            }

            if (state is SmileJobCompleted) {}
          },
          child: SafeArea(
            child: IgnorePointer(
              ignoring: ignoring,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: defaultHorizontalContentPadding),
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: widget.type == AuthPageType.verifySignUpNin
                          ? YMargin(20)
                          : SizedBox.shrink(),
                    ),
                    SliverToBoxAdapter(
                      child: Align(
                        alignment: Alignment.bottomRight,
                        child: widget.type == AuthPageType.verifySignUpNin
                            ? KButtonPrimary(
                                text: 'Skip',
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(38.0),
                                ),
                                constraints: BoxConstraints.tight(
                                  Size(85, 40),
                                ),
                                onTap: nextAction,
                              )
                            : SizedBox.shrink(),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: const YMargin(20),
                    ),
                    SliverToBoxAdapter(
                      child: widget.type == AuthPageType.verifySignUpNin
                          ? Text(
                              'One last thing...',
                              style: textTheme.headlineLarge,
                            )
                          : SizedBox.shrink(),
                    ),
                    SliverToBoxAdapter(
                      child: const YMargin(10),
                    ),
                    SliverToBoxAdapter(
                      child: Text(
                        'We need to verify your identity in order to meet the requirements of our regulators. To do this we require your NIN information.',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: const YMargin(24),
                    ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: Center(
                          child: TdTextField(
                            autoFocus: false,
                            title: 'First Name',
                            hint: 'Enter your first name',
                            textController: _nameController['firstName'],
                            inputFormatters: [
                              validInput(),
                            ],
                            keyboardType: TextInputType.name,
                          ),
                        ),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: const YMargin(5),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: Center(
                          child: TdTextField(
                            autoFocus: false,
                            title: 'Last Name',
                            hint: 'Enter your last name',
                            textController: _nameController['lastName'],
                            inputFormatters: [
                              validInput(),
                            ],
                            keyboardType: TextInputType.name,
                          ),
                        ),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: const YMargin(5),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: Center(
                          child: SizedBox(
                              width: 388, child: Text('Date of Birth')),
                        ),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: Center(
                          child: SizedBox(
                            width: 388,
                            child: InkWell(
                              onTap: () => _selectDate(context),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: defaultHorizontalContentPadding,
                                    vertical:
                                        defaultVerticalContentPaddingLarge),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color:
                                          Theme.of(context).colorScheme.outline,
                                    )),
                                child: selectedDate != null
                                    ? Text(selectedDate!)
                                    : Text(
                                        'Enter your D.O.B',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(
                                                color: Theme.of(context)
                                                    .hintColor),
                                      ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    if (flavorIsDevelopment)
                      SliverToBoxAdapter(
                        child: const YMargin(20),
                      ),
                    SliverToBoxAdapter(
                      child: Center(
                        child: TdTextField(
                          autoFocus: false,
                          title: 'Enter NIN',
                          hint: 'Your National Identification Number',
                          textController: _ninController['nin'],
                          inputFormatters: [
                            validInput(),
                          ],
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: const YMargin(20),
                    ),
                    SliverToBoxAdapter(
                      child: Text(
                          'Here is what we need to collect'.toUpperCase(),
                          style: textTheme.labelMedium),
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            top: defaultVerticalContentPadding),
                        child: Divider(),
                      ),
                    ),
                    // SliverToBoxAdapter(
                    //   child: const YMargin(20),
                    // ),
                    // SliverToBoxAdapter(
                    //   child: Text(
                    //     'We require this information to confirm your identity and meet the requirements of our regulators. Here’s what we collect:',
                    //     style: textTheme.bodyMedium?.copyWith(color: k717A8E),
                    //   ),
                    // ),
                    SliverToBoxAdapter(
                      child: const YMargin(14),
                    ),
                    _item(context, kSvgNinPhone, 'NIN Number'),
                    SliverToBoxAdapter(
                      child: const YMargin(14),
                    ),
                    _item(context, kSvgProfile, 'Full legal name'),
                    SliverToBoxAdapter(
                      child: const YMargin(14),
                    ),
                    _item(context, kSvgNinPhone, 'NIN linked phone number'),
                    SliverToBoxAdapter(
                      child: const YMargin(24),
                    ),
                    /* SliverToBoxAdapter(
                            child: Text.rich(
                              TextSpan(
                                text: 'Lost your NIN phone number? ',
                                style: textTheme.bodyLarge,
                                children: [
                                  WidgetSpan(
                                    child: InkWell(
                                      onTap: () => context.pushNamed(
                                          widget.type == AuthPageType.verifyKycNin
                                              ? DrawerNinUploadPath
                                              : NinUploadPath,
                                          extra: NinUploadArgs(widget.type)),
                                      child: Text(
                                        'Upload NIN slip',
                                        style: textTheme.bodyLarge?.copyWith(
                                          color: kColorBlue,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),*/
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> initiateBiometrics(String jobType) async {
    TdLoader.show(context);
    final res = await locator
        .get<InitiateJob>()
        .call(InitiateJobRequest(jobtype: jobType));

    res.maybeWhen(
      success: (response) {
        TdLoader.hide();
        context.pushNamed(smileWebPath,
            extra: SmileWebViewArgs(response.data!.token!, jobType,
                onSuccess: (value) async {
              _smileJobCubit.validateSmileJob(response.data!.jobId);
              context.pop();
              KYCVerificationModal.show(context);
            }, onClose: () {
              context.pop();
              Toast.error("Verification process exited", context);
            }, onFailed: () {
              context.pop();
              Toast.error("Verification process Failed", context);
            }));
      },
      apiFailure: (e, _) {
        TdLoader.hide();
        Toast.apiError(e, context);
      },
      orElse: () {
        TdLoader.hide();
        Toast.error("Error occured", context);
      },
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null) {
      setState(() {
        selectedDate = pickedDate.toString().substring(0, 10).trim();
      });
    }
  }

  // permissionCheck(VoidCallback action, bool isEnhancedKyc) async {
  //   if (isEnhancedKyc) {
  //     action();
  //     return;
  //   }

  //   if (kIsWeb) {
  //     /* webPermission.window.navigator
  //         .getUserMedia(audio: true, video: true)
  //         .then((v) {
  //       action();
  //     });*/
  //   } else {
  //     PermissionStatus status = await Permission.camera.request();
  //     RetailOutlet currentRetailOutlet =
  //         UserCubit.instance!.currentUser!.retailOutlets!.first;
  //     if (status.isGranted) {
  //       // isLoading = true;
  //       // TdLoader.show(context);
  //       PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //       Segment.track(
  //         eventName: SegmentEvents.ninValidationInitiated,
  //         properties: {
  //           'users.phoneNumber': UserCubit.instance?.currentUser?.phoneNumber,
  //           "latitude": currentRetailOutlet.coordinates?.latitude,
  //           "longitude": currentRetailOutlet.coordinates?.longitude,
  //           "city": currentRetailOutlet.contactAddress?.city,
  //           "country": currentRetailOutlet.contactAddress?.country,
  //           "app version": packageInfo.version,
  //           "status": "initiated"
  //         },
  //       );
  //       action();
  //       // isLoading = false;
  //       // TdLoader.hide();
  //     } else if (status.isDenied) {
  //       permissionCheck(() {
  //         action();
  //       }, isEnhancedKyc);
  //     } else if (status.isPermanentlyDenied) {
  //       showDialog(
  //           context: context,
  //           builder: (ctx) {
  //             return PermissionDialog();
  //           });

  //       return;
  //     }
  //   }
  // }

  Future<void> _initiateSmile() async {
    final nin = _ninController['nin']?.controller?.text;
    await _smileJobCubit.initiateJob(
      InitiateJobRequest(
          jobtype: 'nin',
          idNumber: nin,
          field: config.environment == Environment.dev
              ? Field(
                  dob: selectedDate,
                  firstName: _nameController['firstName']?.controller?.text,
                  lastName: _nameController['lastName']?.controller?.text,
                )
              : null),
    );
  }

  void nextAction() {
    SharedPreferences.getInstance().then(
      (sp) => sp.remove(Keys.ninValidation),
    );

    if (widget.type == AuthPageType.verifySignUpNin) {
      goHomeMobile(context, true);
      try {
        Segment.track(
          eventName: SegmentEvents.sigUpNinSkipped,
        );
      } catch (_) {}
    }
  }

  Future<void> handleNin() async {
    if (config.environment == Environment.dev) {
      if (selectedDate == null) {
        Toast.error('Enter Dob', context);
        return;
      }
      if (!_ninController.validate() || !_nameController.validate()) {
        return;
      }
    }
    // else {
    //   if (!_ninController.validate()) {
    //     return;
    //   }
    // }
    if (!_ninController.validate()) {
      return;
    }
    // permissionCheck();
    _initiateSmile();
  }

  Widget _item(BuildContext context, String icon, String text) {
    return SliverToBoxAdapter(
      child: Row(
        children: [
          SvgPicture.asset(
            icon,
            color: Theme.of(context).colorScheme.primary,
          ),
          const XMargin(5),
          Text(text, style: Theme.of(context).textTheme.bodyMedium)
        ],
      ),
    );
  }
}
