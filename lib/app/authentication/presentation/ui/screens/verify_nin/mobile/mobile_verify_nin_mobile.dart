// import 'package:flutter/material.dart';
// import 'package:package_info/package_info.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:shop/app/account_settings/presentation/ui/widget/permission_dialog.dart';
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';

// import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/mobile/mobile_verify_nin.dart';
// import 'package:shop/src/res/values/analytics/segment_events.dart';
// import 'package:td_commons_flutter/models/retailer.dart';

// class MobileVerifyNinMobile extends StatefulWidget {
//   const MobileVerifyNinMobile(this.type, {super.key});
//   final AuthPageType type;

//   @override
//   State<MobileVerifyNinMobile> createState() => _MobileVerifyNinMobileState();
// }

// class _MobileVerifyNinMobileState extends State<MobileVerifyNinMobile> {
//   @override
//   Widget build(BuildContext context) {
//     return MobileVerifyNin(widget.type);
//   }

//   permissionCheck(VoidCallback action, bool isEnhancedKyc) async {
//     if (isEnhancedKyc) {
//       action();
//       return;
//     }

//     PermissionStatus status = await Permission.camera.request();
//     RetailOutlet currentRetailOutlet =
//         UserCubit.instance!.currentUser!.retailOutlets!.first;
//     if (status.isGranted) {
//       // isLoading = true;
//       // TdLoader.show(context);
//       PackageInfo packageInfo = await PackageInfo.fromPlatform();
//       Segment.track(
//         eventName: SegmentEvents.ninValidationInitiated,
//         properties: {
//           'users.phoneNumber': UserCubit.instance?.currentUser?.phoneNumber,
//           "latitude": currentRetailOutlet.coordinates?.latitude,
//           "longitude": currentRetailOutlet.coordinates?.longitude,
//           "city": currentRetailOutlet.contactAddress?.city,
//           "country": currentRetailOutlet.contactAddress?.country,
//           "app version": packageInfo.version,
//           "status": "initiated"
//         },
//       );
//       action();
//       // isLoading = false;
//       // TdLoader.hide();
//     } else if (status.isDenied) {
//       permissionCheck(() {
//         action();
//       }, isEnhancedKyc);
//     } else if (status.isPermanentlyDenied) {
//       showDialog(
//           context: context,
//           builder: (ctx) {
//             return PermissionDialog();
//           });

//       return;
//     }
//   }
// }
