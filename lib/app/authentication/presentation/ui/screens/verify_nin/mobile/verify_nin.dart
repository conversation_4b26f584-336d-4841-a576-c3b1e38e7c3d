// import 'package:flutter/material.dart';
// import 'package:flutter/foundation.dart' show kIsWeb;
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/mobile/mobile_verify_nin_mobile.dart';
// import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/mobile/mobile_verify_nin_pwa.dart';

// /*export 'mobile_verify_nin_pwa.dart'
//     if (dart.library.js) 'mobile_verify_nin_pwa.dart'
//     if (dart.library.io) 'mobile_verify_nin_mobile.dart';*/

// //final isWeb = dart.library.js ? true : false;

// class VerifyNin extends StatelessWidget {
//   const VerifyNin(this.type, {super.key});
//   final AuthPageType type;

//   @override
//   Widget build(BuildContext context) {
//     late Widget child;
//     if (kIsWeb) {
//       child = MobileVerifyNinPwa(type);
//     } else {
//       MobileVerifyNinMobile(type);
//     }
//     return child;
//   }
// }
