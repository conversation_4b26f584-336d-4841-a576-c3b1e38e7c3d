// import 'package:flutter/material.dart';
// import 'package:shop/app/authentication/data/models/page_type.dart';
// import 'dart:html';

// import 'package:shop/app/authentication/presentation/ui/screens/verify_nin/mobile/mobile_verify_nin.dart';

// class MobileVerifyNinPwa extends StatelessWidget {
//   const MobileVerifyNinPwa(this.type, {super.key});
//   final AuthPageType type;

//   @override
//   Widget build(BuildContext context) {
//     return MobileVerifyNin(type);
//   }

//   permissionCheck(Function action, bool isEnhancedKyc) async {
//     if (isEnhancedKyc) {
//       action();
//       return;
//     }

//     //if app is running on web
//     // Navigator navigator = window.navigator;
//     window.navigator.getUserMedia(audio: true, video: true).then((v) {
//       action();
//     });
//   }
// }
