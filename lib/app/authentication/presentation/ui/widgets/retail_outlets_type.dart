import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/retail_outlet_type.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:go_router/go_router.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import '../../../../../src/components/src/widgets/margin.dart';
import '../../../../payments/presentation/ui/widgets/search_text_field.dart';

class RetailOutletsType extends StatefulWidget {
  final Function(RetailOutletType) onChanged;
  final String? title;

  const RetailOutletsType({super.key, 
    required this.onChanged,
    this.title,
  });

  @override
  _RetailOutletsTypeState createState() => _RetailOutletsTypeState();
}

class _RetailOutletsTypeState extends State<RetailOutletsType> {
  late Future<List<RetailOutletType>> _options;
  RetailOutletType? selectedType;

  @override
  void initState() {
    super.initState();
    _options = _fetchOptions();
  }

  Future<List<RetailOutletType>> _fetchOptions() async {
    final userCubit = context.read<UserCubit>();
    final collection = switch (userCubit.domain) {
      AppHost.tradeDepot => 'business_types',
      _ => 'retailoutlettypes',
    };

    QuerySnapshot<Map<String, dynamic>> snapshot =
        await FirebaseFirestore.instance.collection(collection).get();

    List<RetailOutletType> options = snapshot.docs
        .map((doc) => RetailOutletType.fromMap(doc.data()))
        .toList();

    // options
    //     .sort((a, b) => (a.name ?? a.id ?? '').compareTo(b.name ?? b.id ?? ''));

    return options;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<RetailOutletType>>(
      future: _options,
      builder: (BuildContext context,
          AsyncSnapshot<List<RetailOutletType>> snapshot) {
        if (snapshot.hasError) {
          String text = '${snapshot.error}';
          return TypeWrapper(
            text: text,
            title: widget.title,
            isError: true,
            onTap: () {
              final options = _fetchOptions();
              setState(() {
                _options = options;
              });
            },
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          return TypeWrapper(
            text: 'Loading...',
            title: widget.title,
          );
        } else {
          final outletTypes = snapshot.data!;

          if (outletTypes.isEmpty) {
            String text = 'Error fetching store types';
            return TypeWrapper(
              text: text,
              title: widget.title,
              isError: true,
              onTap: () {
                final options = _fetchOptions();
                setState(() {
                  _options = options;
                });
              },
            );
          }

          return TypeWrapper(
            text: selectedType?.name ?? selectedType?.id ?? 'Type of Store',
            title: widget.title,
            isValue: selectedType?.name != null || selectedType?.id != null,
            onTap: () async {
              final type =
                  await RetailOutletTypeSelectModal.show(context, outletTypes);
              if (type != null) {
                setState(() {
                  selectedType = type;
                });
                widget.onChanged(type);
              }
            },
          );
        }
      },
    );
  }
}

class TypeWrapper extends StatelessWidget {
  final String text;
  final String? title;
  final bool? isError;
  final VoidCallback? onTap;
  final bool? isValue;
  const TypeWrapper({
    super.key,
    required this.text,
    this.title,
    this.isError = false,
    this.onTap,
    this.isValue = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 4),
        ],
        InkWell(
          onTap: onTap,
          child: Container(
            constraints: BoxConstraints.tight(Size(388, 48)),
            alignment: Alignment.center,
            padding: EdgeInsets.only(left: 12, right: 10),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline),
              borderRadius: BorderRadius.all(Radius.circular(10)),
              color: theme.colorScheme.surface,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  text,
                  style: theme.textTheme.bodyLarge?.copyWith(
                      color: isError!
                          ? theme.colorScheme.error
                          : !isValue!
                              ? theme.hintColor
                              : null),
                ),
                Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class RetailOutletTypeSelectModal extends StatefulWidget {
  final List<RetailOutletType> outletTypes;
  // final Function(OutletType) onSelect;
  const RetailOutletTypeSelectModal({
    super.key,
    required this.outletTypes,
    // required this.onSelect,
  });

  static Future<RetailOutletType?> show(
    BuildContext context,
    List<RetailOutletType> types,
    // Function(OutletType) onSelect,
  ) async {
    return showModalBottomSheet<RetailOutletType?>(
      isScrollControlled: true,
      context: context,
      // useSafeArea: true,
      builder: (_) => FractionallySizedBox(
        heightFactor: 0.9,
        child: RetailOutletTypeSelectModal(
          outletTypes: types,
          // onSelect: onSelect,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  _OutletTypeSelectModalState createState() => _OutletTypeSelectModalState();
}

class _OutletTypeSelectModalState extends State<RetailOutletTypeSelectModal> {
  final TextEditingController _textEditingController = TextEditingController();
  late final ValueNotifier<List<RetailOutletType>> _typeNotifier =
      ValueNotifier(widget.outletTypes);
  late final types = widget.outletTypes;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _typeNotifier.dispose();
    super.dispose();
  }

  void _onSearch(String searchWord) {
    if (searchWord.isEmpty) {
      _typeNotifier.value = types;
    } else {
      _typeNotifier.value = types
          .where((element) =>
              element.name!.toLowerCase().contains(searchWord.toLowerCase()))
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Type of Store', style: textTheme.headlineSmall),
              InkWell(
                onTap: () => context.pop(),
                child: Icon(Icons.close, size: 28),
              )
            ],
          ),
          YSpacing(16),
          SearchTextField(
            textEditingController: _textEditingController,
            hintText: 'Enter Store Type',
            onSearch: _onSearch,
          ),
          YMargin(25),
          Expanded(
            child: ValueListenableBuilder<List<RetailOutletType>>(
              valueListenable: _typeNotifier,
              builder: (context, types, _) {
                return types.isNotEmpty
                    ? ListView.custom(
                        childrenDelegate: SliverChildBuilderDelegate(
                          (BuildContext context, int index) {
                            final type = types[index];
                            return Container(
                              // padding: EdgeInsets.only(bottom: 20),
                              height: 68,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(),
                                ),
                              ),
                              child: InkWell(
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      type.name ?? type.id ?? '',
                                      style: textTheme.bodyLarge,
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios_outlined,
                                      size: 14,
                                    )
                                  ],
                                ),
                                onTap: () => Navigator.pop(context, type),
                              ),
                            );
                          },
                          childCount: types.length,
                          findChildIndexCallback: (Key key) {
                            final ValueKey<RetailOutletType> valueKey =
                                key as ValueKey<RetailOutletType>;
                            final RetailOutletType data = valueKey.value;
                            return types.indexOf(data);
                          },
                        ),
                      )
                    : Text(
                        'No item found',
                        style: textTheme.bodyLarge,
                      );
              },
            ),
          ),
        ],
      ),
    );
  }
}
