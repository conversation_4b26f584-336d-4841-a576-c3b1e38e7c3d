import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:shop/src/components/components.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

class OTPCounter extends StatefulWidget {
  final SendOTParams params;
  final VerifyPhoneCubit bloc;
  final bool isDesktop;

  const OTPCounter({
    super.key,
    required this.params,
    required this.bloc,
    this.isDesktop = false,
  });

  @override
  _OTPCounterState createState() => _OTPCounterState();
}

class _OTPCounterState extends State<OTPCounter> {
  final _countDownController = CountdownController(autoStart: true);

  @override
  Widget build(BuildContext context) {
    return Countdown(
      seconds: 60,
      controller: _countDownController,
      build: (_, sec) {
        final seconds = sec.toInt();
        // time elapsed
        bool canResend = seconds < 1;

        return BlocConsumer<VerifyPhoneCubit, VerifyPhoneState>(
          bloc: widget.bloc,
          builder: (context, state) {
            final sending = state is VerifyPhoneLoading;
            if (sending) {
              canResend = false;
            }

            return Padding(
              padding: EdgeInsets.all(5),
              child: Material(
                clipBehavior: Clip.antiAlias,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  onTap: !canResend
                      ? null
                      : () {
                          widget.bloc.sendOTP(
                            widget.params,
                          );
                        },
                  child: Padding(
                    padding: widget.isDesktop
                        ? const EdgeInsets.symmetric(
                            vertical: 5,
                            horizontal: 10,
                          )
                        : const EdgeInsets.symmetric(
                            vertical: 20,
                            horizontal: 18,
                          ),
                    child: sending
                        ? Text('Sending...')
                        : Text(
                            canResend
                                ? 'Resend'
                                : "Resend in ${_extractTime(seconds)}",
                            style: TextStyle(),
                          ),
                  ),
                ),
              ),
            );
          },
          listener: (context, state) {
            if (state is SentOTPState) {
              _countDownController.restart();
              Toast.success('OTP code has been resent', context);
            }
          },
        );
      },
    );
  }

  String _extractTime(int seconds) {
    if (seconds >= 60) {
      return '01:00';
    }
    if (seconds < 10) {
      return '00:0$seconds';
    }
    return '00:$seconds';
  }
}
