import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_nin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_state.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/validate_phone_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/web/web_validate_phone_screen.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/otp_counter.dart';
import 'package:shop/app/loan/presentation/logic/bloc/base_loan_cubit.dart';
import 'package:shop/app_config.dart';
import 'package:shop/src/components/src/buttons/src/k_button.dart';
import 'package:shop/src/components/src/buttons/src/td_text_button.dart';
import 'package:shop/src/components/src/form/src/td_form_field.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/toast.dart';
import 'package:shop/src/components/src/utils/remote_config_manager.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/button_style.dart';
import 'package:shop/src/res/values/styles/form_style.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/scaler/scaler.dart';
import 'package:url_launcher/url_launcher.dart';

typedef NextAction = Future<void> Function(String accessToken);

class OtpModal extends StatefulWidget {
  final ValidatePhoneArgs argument;
  final NextAction? nextAction;
  final bool? isDesktop;
  const OtpModal(
      {super.key,
      required this.argument,
      this.nextAction,
      this.isDesktop = false});

  static Future<void> show(
    BuildContext context,
    ValidatePhoneArgs argument, {
    NextAction? nextAction,
    bool? isDesktop,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: OtpModal(argument: argument, nextAction: nextAction),
          titleTextStyle: KTextStyle.semiBold24.copyWith(fontSize: 20),
          contentTextStyle: KTextStyle.regular14.copyWith(fontSize: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          contentPadding: isDesktop == true
              ? const EdgeInsets.only(left: 30, bottom: 15, right: 30)
              : const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
          titlePadding: const EdgeInsets.only(left: 30, top: 15, right: 30),
          actionsPadding: EdgeInsets.zero,
        );
      },
    );
  }

  @override
  State<OtpModal> createState() => _OtpModalState();
}

class _OtpModalState extends State<OtpModal> {
  final _bloc = PhoneValidateCubit(locator(), locator());
  final _signUpBloc = VerifyPhoneCubit(locator(), locator());
  final TextEditingController _controller = TextEditingController();

  String code = '';
  // String ussdCode = "";

  @override
  void initState() {
    super.initState();
    // fetchUssdCode();
    _controller.addListener(() {
      if (_controller.text == code) return;

      // auto submit otp
      if (_controller.text.length == 6) {
        // close keyboard
        closeKeyboard(context);
        _verify();
      }

      // rebuild for footer button
      setState(() {
        code = _controller.text;
      });
    });
  }

  // void fetchUssdCode() async {
  //   final config = await RemoteConfigService.fetchString(
  //       SHOP_V3_OTP_USSD, jsonEncode(DEFAULT_OTP_USSD));

  //   final code = formatConfigString(config, DEFAULT_OTP_USSD)[
  //       countryCodeFromPhone(widget.argument.params.phoneNumber!)];

  //   ussdCode = code;

  //   if (mounted) {
  //     setState(() {
  //       ussdCode = code;
  //     });
  //   }
  // }

  void _verify() async {
    // validate
    if (_controller.text.isEmpty) {
      return;
    }

    await _bloc.verifyOTP(
      VerifyOTParams(
        phoneNumber: widget.argument.params.phoneNumber!,
        token: _controller.text,
        countryCode: '',
        url:
            '${config.firebaseServiceUrl!}/${WebValidatePhoneScreen.VERIFY_OTP_PATH}',
      ),
      widget.argument.pageType,
    );
  }

  @override
  Widget build(BuildContext context) {
    final configManager = AppConfigManager.instance;
    return BlocConsumer<PhoneValidateCubit, PhoneValidateState>(
      bloc: _bloc,
      builder: (context, state) =>
          _build(context, state, widget.isDesktop!, configManager),
      listener: (_, state) async {
        if (!widget.isDesktop!) {
          if (state is ValidatingOTPState ||
              state is ValidatedAfterLoginState) {
            TdLoader.show(context);
          } else {
            TdLoader.hide();
          }
        }

        if (state is FailedToValidateOTP) {
          Toast.error(state.errorMessage, _);
        }

        if (state is ValidatedNINState) {
          TdLoader.show(context);
          final res =
              await locator.get<VerifyNin>().call(widget.argument.ninParams!);
          res.when(
            success: (response) {
              if (widget.argument.pageType == AuthPageType.verifyKycNin) {
                UserCubit userCubit = context.read();
                BaseLoanCubit baseLoanCubit = context.read();
                userCubit.updateOutlet();
                baseLoanCubit.reloadState(userCubit.currentOutlet);
                TdLoader.hide();
                Navigator.pop(context);
              } else {
                Navigator.pop(context);
              }
            },
            apiFailure: (error, _) {
              TdLoader.hide();
              final msg = ApiExceptions.getErrorMessage(error);
              Toast.error(msg, context);
            },
          );
        }

        if (state is ValidatedOTPState) {
          if (widget.nextAction != null) {
            widget.nextAction!(state.accessToken);
            Navigator.pop(context);
          }
        }
      },
    );
  }

  Widget _build(BuildContext context, PhoneValidateState state, bool isDesktop,
      AppConfigManager configManager) {
    final textTheme = Theme.of(context).textTheme;
    final ussdCode = context.read<UserCubit>().ussdCode;
    return isDesktop
        ? Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(5))),
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 80, vertical: 30),
            height: 604,
            width: 692, //SizeConfig.scaleX(0.57),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: InkWell(
                    child: const Icon(Icons.close, size: 40),
                    onTap: () => Navigator.pop(context),
                  ),
                ),
                Text(AppLocalizations.of(context)!.enter_otp,
                    style: KTextStyle.semiBold24),
                const YSpacing(10),
                Text(
                    'Enter the code that was sent to ${widget.argument.params.phoneNumber}',
                    style: KTextStyle.book14.copyWith(fontSize: 16)),
                const YSpacing(40),
                SizedBox(
                  width: 400,
                  child: TdFormField(
                    label: AppLocalizations.of(context)!.enter_otp,
                    widget: TextField(
                      controller: _controller,
                      readOnly: state is ValidatingOTPState,
                      style: const TextStyle(
                        fontSize: 14,
                      ),
                      decoration: FormStyle.formDecoration(
                        AppLocalizations.of(context)!.enter_otp_hint,
                      ).copyWith(
                        suffixIcon: OTPCounter(
                          params: widget.argument.params,
                          bloc: _signUpBloc,
                          isDesktop: true,
                        ),
                      ),
                      maxLength: 6,
                      maxLengthEnforcement: MaxLengthEnforcement.enforced,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r"\d+")),
                        // maximum of 6 numbers for OTP
                        LengthLimitingTextInputFormatter(6),
                      ],
                    ),
                  ),
                ),
                const YSpacing(50),
                _submitButton(state),
                const YSpacing(30),
                if (ussdCode.isNotEmpty)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text("Not getting OTP? ", style: KTextStyle.book14),
                      const XSpacing(5),
                      TdTextButton(
                        label: "Dial $ussdCode",
                        onPressed: () => _makeUssdCall(ussdCode),
                        style: ButtonStyles.kPaleButtonStyle.copyWith(
                          textStyle: WidgetStateProperty.all(
                            KTextStyle.regular14.copyWith(fontSize: 12),
                          ),
                        ),
                      ),
                    ],
                  )
              ],
            ),
          )
        : SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: InkWell(
                    child: const Icon(Icons.close, size: 40),
                    onTap: () => Navigator.pop(context),
                  ),
                ),
                Text(AppLocalizations.of(context)!.enter_otp,
                    style: textTheme.titleLarge),
                const YMargin(15),
                Text(
                    "Enter the code that was sent to ${widget.argument.params.phoneNumber}",
                    style: textTheme.bodyLarge),
                const YMargin(15),
                // TdTextField(
                //   title: 'Enter Code',
                //   hint: '000000',
                //   autoFocus: true,
                //   textController:
                //       TdTextController(initialValue: _controller.text),
                //   keyboardType: TextInputType.number,
                //   inputFormatters: [
                //     FilteringTextInputFormatter.digitsOnly,
                //   ],
                // ),
                TdFormField(
                  label: AppLocalizations.of(context)!.enter_otp,
                  widget: TextField(
                    controller: _controller,
                    readOnly: state is ValidatingOTPState,
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                    decoration: FormStyle.formDecoration(
                      AppLocalizations.of(context)!.enter_otp_hint,
                    ).copyWith(
                      suffixIcon: OTPCounter(
                        params: widget.argument.params,
                        bloc: _signUpBloc,
                        isDesktop: true,
                      ),
                    ),
                    maxLength: 6,
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r"\d+")),
                      // maximum of 6 numbers for OTP
                      LengthLimitingTextInputFormatter(6),
                    ],
                  ),
                ),
                const YMargin(14),
                // YSpacing(50),
                // KButtonPrimary(
                //   text: 'Verify',
                //   isLoading: state is ValidatingOTPState ||
                //       state is ValidatedAfterLoginState,
                //   onTap: () {
                //     closeKeyboard(context);
                //     _verify();
                //   },
                // ),
                // YSpacing(30),
                if (ussdCode.isNotEmpty)
                  Text.rich(
                    TextSpan(
                      text: '',
                      children: [
                        WidgetSpan(
                          child: Text(
                            "Not getting OTP? Dial  ",
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        WidgetSpan(
                          child: GestureDetector(
                            onTap: () => _makeUssdCall(ussdCode),
                            child: Text(
                              ussdCode,
                              style: textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                // Align(
                //   alignment: Alignment.center,
                //   child: Text.rich(
                //     TextSpan(
                //       children: [
                //         WidgetSpan(
                //           child: Text(
                //             "Not getting a code? ",
                //             style: textTheme.bodyLarge,
                //           ),
                //         ),
                //         WidgetSpan(
                //           child: Countdown(
                //             seconds: 60,
                //             controller: _countDownController,
                //             build: (_, sec) {
                //               final seconds = sec.toInt();
                //               // time elapsed
                //               bool canResend = seconds < 1;

                //               return BlocConsumer<VerifyPhoneCubit,
                //                   VerifyPhoneState>(
                //                 bloc: _signUpBloc,
                //                 builder: (context, state) {
                //                   final sending = state is VerifyPhoneLoading;
                //                   if (sending) {
                //                     canResend = false;
                //                   }

                //                   return InkWell(
                //                     onTap: !canResend
                //                         ? null
                //                         : () {
                //                             _signUpBloc.sendOTP(
                //                               widget.argument.params,
                //                             );
                //                           },
                //                     child: sending
                //                         ? Text('Sending...')
                //                         : Text(
                //                             canResend
                //                                 ? 'Resend'
                //                                 : "Resend in ${_extractTime(seconds)}",
                //                             style: canResend
                //                                 ? textTheme.bodyLarge?.copyWith(
                //                                     color: Theme.of(context)
                //                                         .colorScheme
                //                                         .primary,
                //                                   )
                //                                 : textTheme.bodyLarge,
                //                           ),
                //                   );
                //                 },
                //                 listener: (context, state) {
                //                   if (state is SentOTPState) {
                //                     _countDownController.restart();
                //                     Toast.success(
                //                         'OTP code has been resent', context);
                //                   }
                //                 },
                //               );
                //             },
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
                // const YMargin(40),
                // KButtonPrimary(
                //   text: 'Verify',
                //   onTap: () {
                //     closeKeyboard(context);
                //     _verify();
                //   },
                // ),
              ],
            ),
          );
  }

  /* String _extractTime(int seconds) {
    if (seconds >= 60) {
      return '01:00';
    }
    if (seconds < 10) {
      return '00:0$seconds';
    }
    return '00:$seconds';
  }*/

  Widget _submitButton(PhoneValidateState state) {
    return KButton(
      onPressed: _verify,
      width: 400,
      text: 'Verify',
      isLoading:
          state is ValidatingOTPState || state is ValidatedAfterLoginState,
    );
  }

  Future<void> _makeUssdCall(String code) async {
    String no = Uri.encodeComponent(code);
    final url = Uri.parse('tel:$no');
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      Toast.error("Could not launch $code", context);
    }
  }

  @override
  void dispose() {
    _bloc.close();
    _controller.dispose();
    super.dispose();
  }
}
