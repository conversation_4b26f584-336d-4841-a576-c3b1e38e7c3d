import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class PinField extends StatefulWidget {
  final Function _action;
  final int length;

  const PinField(this._action, this.length, {super.key})
      : assert(length >= 4, 'Length must be at least 4');

  @override
  State<StatefulWidget> createState() {
    return PinFieldState();
  }
}

class PinFieldState extends State<PinField> {
  List<TextEditingController> pinControllers = [];
  List<FocusNode> focus = [];
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    for (var element in pinControllers) {
      element.dispose();
    }
    for (var element in focus) {
      element.dispose();
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < widget.length; i++) {
      pinControllers.add(TextEditingController());
      focus.add(FocusNode());
    }
  }

  void handleKey(RawKeyEvent key) {
    if (key.physicalKey == PhysicalKeyboardKey.backspace) {
      for (int i = widget.length - 1; i > 0; i--) {
        if (focus[i].hasFocus) {
          if (pinControllers[i].text.isEmpty) {
            FocusScope.of(context).requestFocus(focus[i - 1]);
          }
          break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> fields = [];

    for (int i = 0; i < widget.length; i++) {
      final isLastIndex = i == widget.length - 1;

      fields.add(
        SizedBox(
          width: 70,
          child: TextFormField(
            decoration: InputDecoration(
              isDense: true,
              filled: true,
              contentPadding: EdgeInsets.all(10),
              border: pinBorder,
              enabledBorder: pinBorder,
              focusedBorder: pinBorder,
            ),
            textAlign: TextAlign.center,
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.sentences,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly,
            ],
            obscureText: true,
            obscuringCharacter: "•",
            textAlignVertical: TextAlignVertical.center,
            focusNode: focus[i],
            controller: pinControllers[i],
            onChanged: (val) =>
                isLastIndex ? submit(val: val) : submit(id: (i + 1), val: val),
            style: KTextStyle.medium14.copyWith(fontSize: 40),
            keyboardType: TextInputType.number,
          ),
        ),
      );

      if (i < widget.length - 1) {
        fields.add(XSpacing(10));
      }
    }

    return RawKeyboardListener(
      focusNode: _focusNode,
      onKey: handleKey,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: fields,
      ),
    );
  }

  void submit({int? id, required String val}) {
    if (id != null && val.isNotEmpty) {
      FocusScope.of(context).requestFocus(focus[id]);
    }
    int index = pinControllers.indexWhere((element) => element.text.isEmpty);
    if (index == -1) {
      String text = "";
      for (var element in pinControllers) {
        text += element.text;
      }
      widget._action(text);
    }
  }

  void reset() {
    for (var element in pinControllers) {
      element.text = "";
    }
    FocusScope.of(context).requestFocus(focus[0]);
  }

  final OutlineInputBorder pinBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: BorderSide(color: Colors.transparent),
  );
}

// class PinField extends StatefulWidget {
//   final Function _action;
//   PinField(this._action, {Key? key}) : super(key: key);

//   @override
//   State<StatefulWidget> createState() {
//     return PinFieldState();
//   }
// }

// class PinFieldState extends State<PinField> {
//   List<TextEditingController> pinControllers = [
//     TextEditingController(),
//     TextEditingController(),
//     TextEditingController(),
//     TextEditingController(),
//     TextEditingController(),
//     TextEditingController()
//   ];
//   List<FocusNode> focus = [
//     FocusNode(),
//     FocusNode(),
//     FocusNode(),
//     FocusNode(),
//     FocusNode(),
//     FocusNode()
//   ];
//   FocusNode _focusNode = FocusNode();
//   int index = 0;

//   @override
//   void dispose() {
//     pinControllers.forEach((element) {
//       element.dispose();
//     });
//     focus.forEach((element) {
//       element.dispose();
//     });
//     super.dispose();
//   }

//   @override
//   void initState() {
//     super.initState();
//   }

//   handleKey(RawKeyEvent key) {
//     if (key.physicalKey == PhysicalKeyboardKey.backspace) {
//       index++;
//       if (index.isOdd) {
//         for (int i = 0; i < focus.length; i++) {
//           if (focus[i].hasFocus) {
//             if (pinControllers[i].text.isEmpty) {
//               if (i > 0) FocusScope.of(context).requestFocus(focus[i - 1]);
//             }
//             break;
//           }
//         }
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return RawKeyboardListener(
//       focusNode: _focusNode,
//       onKey: handleKey,
//       child: Row(
//         children: [
//           SizedBox(
//             width: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textAlign: TextAlign.center,
//                 textInputAction: TextInputAction.done,
//                 textCapitalization: TextCapitalization.sentences,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 textAlignVertical: TextAlignVertical.center,
//                 focusNode: focus[0],
//                 controller: pinControllers[0],
//                 onChanged: (val) => submit(id: 1, val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//           XSpacing(10),
//           SizedBox(
//             width: 70,
//             //height: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textAlign: TextAlign.center,
//                 textInputAction: TextInputAction.done,
//                 textCapitalization: TextCapitalization.sentences,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 textAlignVertical: TextAlignVertical.center,
//                 focusNode: focus[1],
//                 controller: pinControllers[1],
//                 onChanged: (val) => submit(id: 2, val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//           XSpacing(10),
//           SizedBox(
//             width: 70,
//             //height: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textInputAction: TextInputAction.done,
//                 textAlign: TextAlign.center,
//                 textCapitalization: TextCapitalization.sentences,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 focusNode: focus[2],
//                 textAlignVertical: TextAlignVertical.center,
//                 controller: pinControllers[2],
//                 onChanged: (val) => submit(id: 3, val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//           XSpacing(10),
//           SizedBox(
//             width: 70,
//             //height: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textInputAction: TextInputAction.done,
//                 textAlign: TextAlign.center,
//                 textCapitalization: TextCapitalization.sentences,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 focusNode: focus[3],
//                 textAlignVertical: TextAlignVertical.center,
//                 controller: pinControllers[3],
//                 onChanged: (val) => submit(id: 4, val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//           XSpacing(10),
//           SizedBox(
//             width: 70,
//             //height: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textInputAction: TextInputAction.done,
//                 textAlign: TextAlign.center,
//                 textCapitalization: TextCapitalization.sentences,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 focusNode: focus[4],
//                 textAlignVertical: TextAlignVertical.center,
//                 controller: pinControllers[4],
//                 onChanged: (val) => submit(id: 5, val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//           XSpacing(10),
//           SizedBox(
//             width: 70,
//             //height: 70,
//             child: TextFormField(
//                 decoration: InputDecoration(
//                     isDense: true,
//                     filled: true,
//                     fillColor: kColorBlue10,
//                     contentPadding: EdgeInsets.all(10),
//                     focusColor: kColorBlue,
//                     border: pinBorder,
//                     enabledBorder: pinBorder,
//                     focusedBorder: pinBorder),
//                 textInputAction: TextInputAction.done,
//                 textCapitalization: TextCapitalization.sentences,
//                 textAlign: TextAlign.center,
//                 inputFormatters: [
//                   LengthLimitingTextInputFormatter(1),
//                   FilteringTextInputFormatter.digitsOnly,
//                 ],
//                 obscureText: true,
//                 obscuringCharacter: "•",
//                 textAlignVertical: TextAlignVertical.center,
//                 focusNode: focus[5],
//                 controller: pinControllers[5],
//                 onChanged: (val) => submit(val: val),
//                 style: KTextStyle.medium14
//                     .copyWith(fontSize: 40, color: kColorBlue),
//                 keyboardType: TextInputType.number,
//                 cursorColor: kColorBlue),
//           ),
//         ],
//         mainAxisAlignment: MainAxisAlignment.center,
//       ),
//     );
//   }

//   submit({int? id, required String val}) {
//     if (id != null && val.isNotEmpty)
//       FocusScope.of(context).requestFocus(focus[id]);
//     int index = pinControllers.indexWhere((element) => element.text.isEmpty);
//     if (index == -1) {
//       String text = "";
//       pinControllers.forEach((element) => text += element.text);
//       widget._action(text);
//     }
//   }

//   void reset() {
//     pinControllers.forEach((element) {
//       element.text = "";
//     });
//     FocusScope.of(context).requestFocus(focus[0]);
//   }

//   final OutlineInputBorder pinBorder = OutlineInputBorder(
//       borderRadius: BorderRadius.circular(10),
//       borderSide: BorderSide(color: Colors.transparent));
// }
