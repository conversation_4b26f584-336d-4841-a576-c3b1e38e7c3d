
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:shop/src/components/src/buttons/buttons.dart';

class DialUssdButton extends StatefulWidget {
  final String phoneNumber;
  const DialUssdButton({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<DialUssdButton> createState() => _DialUssdButtonState();
}

class _DialUssdButtonState extends State<DialUssdButton> {
  late String ussdCode = context.read<UserCubit>().ussdCode;

  @override
  void initState() {
    super.initState();
    // fetchUssdCode();
  }

  // void fetchUssdCode() async {
  //   final config = await RemoteConfigService.fetchString(
  //       SHOP_V3_OTP_USSD, jsonEncode(DEFAULT_OTP_USSD));

  //   final code = formatConfigString(
  //       config, DEFAULT_OTP_USSD)[countryCodeFromPhone(widget.phoneNumber)];

  //   ussdCode = code;

  //   if (mounted) {
  //     setState(() {
  //       ussdCode = code;
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return ussdCode.isEmpty
        ? SizedBox.shrink()
        : KButton(
            text: 'or dial USSD $ussdCode',
            onPressed: () async {
              String no = Uri.encodeComponent(ussdCode);
              final link = Uri.parse('tel:$no');
              if (await canLaunchUrl(link)) {
                launchUrl(link);
              }
            },
          );
  }
}
