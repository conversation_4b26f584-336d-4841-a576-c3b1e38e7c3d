import 'package:flutter/material.dart';
import 'package:shop/src/res/values/colors/colors.dart';

class ProgressBar extends StatelessWidget {
  final int index;
  const ProgressBar(this.index, {super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final ColorScheme altColorScheme =
        ColorScheme.fromSeed(seedColor: kColorGrey2);
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: index >= 0
                  ? theme.colorScheme.primary
                  : altColorScheme.primary,
              borderRadius: BorderRadius.horizontal(left: Radius.circular(20)),
            ),
            height: 10,
            margin: EdgeInsets.only(right: 4),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: index >= 1
                  ? theme.colorScheme.primary
                  : altColorScheme.primary,
            ),
            height: 10,
            margin: EdgeInsets.only(right: 4),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: index >= 2
                  ? theme.colorScheme.primary
                  : altColorScheme.primary,
            ),
            height: 10,
            margin: EdgeInsets.only(right: 4),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: index >= 3
                  ? theme.colorScheme.primary
                  : altColorScheme.primary,
              borderRadius: BorderRadius.horizontal(right: Radius.circular(20)),
            ),
            height: 10,
            margin: EdgeInsets.only(right: 4),
          ),
        ),
      ],
    );
  }
}
