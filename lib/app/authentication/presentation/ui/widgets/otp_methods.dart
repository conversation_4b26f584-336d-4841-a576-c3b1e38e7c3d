import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/services/url_service.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import '../../../../../src/res/values/styles/text_style.dart';

class OtpMethods extends StatelessWidget {
  const OtpMethods({
    super.key,
    required this.otpMode,
    required this.handleTap,
    required this.phoneNumber,
    this.title,
    this.subtitle,
    this.ussdCode,
    this.enableWhatsApp = true,
  });

  final ValueNotifier<PhoneAuthMode>? otpMode;
  final Function(PhoneAuthMode? mode) handleTap;
  final String phoneNumber;
  final String? title;
  final String? subtitle;
  final String? ussdCode;
  final bool? enableWhatsApp;

  static Future<void> show(
    BuildContext context, {
    String? ussdCode,
    ValueNotifier<PhoneAuthMode>? otpMode,
    String? title,
    String? subtitle,
    double? heightFactor = 0.5,
    bool? enableWhatsApp,
    required Function(PhoneAuthMode? mode) handleTap,
    required String phoneNumber,
  }) async {
    return await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (_) => SizedBox(
        // heightFactor: heightFactor,
        height: MediaQuery.sizeOf(context).height * heightFactor!,
        child: OtpMethods(
          otpMode: otpMode,
          handleTap: handleTap,
          phoneNumber: phoneNumber,
          title: title,
          subtitle: subtitle,
          ussdCode: ussdCode,
          enableWhatsApp: enableWhatsApp ?? true,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(18),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 22.0, horizontal: 18),
        child: Column(
          children: [
            SvgPicture.asset(kSvgOptMethodsIcon),
            const YMargin(20),
            if (title != null) ...[
              SizedBox(
                width: 300,
                child: Text(
                  '$title',
                  style: KTextStyle.bodyText2.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const YMargin(5),
            ],
            if (subtitle != null) ...[
              SizedBox(
                width: 300,
                child: Text(
                  '$subtitle',
                  style: KTextStyle.bodyText2.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: theme.hintColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const YMargin(40),
            ],
            if (enableWhatsApp == true) ...[
              MethodButton(
                prefixIcon: Icons.whatshot,
                postfixIcon: Icons.arrow_forward,
                text: 'Verify with Whatsapp',
                isWhatsApp: true,
                onTap: () {
                  otpMode?.value = PhoneAuthMode.WhatsApp;
                  handleTap(PhoneAuthMode.WhatsApp);
                },
              ),
              const YMargin(10),
            ],
            // MethodButton(
            //   prefixIcon: Icons.chat_bubble,
            //   postfixIcon: Icons.arrow_forward,
            //   text: 'Verify with SMS',
            //   onTap: () {
            //     otpMode?.value = PhoneAuthMode.SMS;
            //     handleTap(PhoneAuthMode.SMS);
            //   },
            // ),
            if (ussdCode != null && ussdCode!.isNotEmpty) ...[
              const YMargin(10),
              MethodButton(
                prefixIcon: Icons.tag,
                postfixIcon: Icons.phone,
                text: 'Verify with USSD Code',
                onTap: () {
                  UrlService.it.call(Uri.encodeComponent(ussdCode!));
                  handleTap(null);
                },
              )
            ],
          ],
        ),
      ),
    );
  }
}

class MethodButton extends StatelessWidget {
  const MethodButton({
    super.key,
    required this.prefixIcon,
    required this.postfixIcon,
    required this.text,
    required this.onTap,
    this.isWhatsApp = false,
  });

  final IconData prefixIcon;
  final IconData postfixIcon;
  final String text;
  final VoidCallback onTap;

  final bool? isWhatsApp;

  @override
  build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints.tight(Size(388, 48)),
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          elevation: 0,
          side: BorderSide.none,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
        ),
        child: Row(
          children: [
            isWhatsApp! ? SvgPicture.asset(kSvgWhatsapp) : Icon(prefixIcon),
            const XMargin(10),
            Expanded(
              child: Text(
                text,
                style: KTextStyle.bodyText2.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Icon(postfixIcon),
          ],
        ),
      ),
    );
  }
}
