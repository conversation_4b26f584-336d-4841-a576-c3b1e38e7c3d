import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/buttons.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class Info extends StatelessWidget {
  final AuthPageType page;
  const Info(this.page, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          children: [
            Expanded(
                child: Divider(
              thickness: 1,
              height: 1,
            )),
            XSpacing(50),
            Text(
              page == AuthPageType.signUp
                  ? "I already have a TradeDepot account?"
                  : "New to TradeDepot?",
              style: KTextStyle.book14.copyWith(fontSize: 16),
            ),
            XSpacing(50),
            Expanded(
                child: Divider(
              thickness: 1,
              height: 1,
            )),
          ],
        ),
        YSpacing(20),
        page == AuthPageType.signUp
            ? KButton(
                text: 'Sign In',
                isOutline: true,
                width: 400,
                onPressed: () => context.goNamed(LoginPath),
              )
            : KButton(
                text: 'Create a new account',
                isOutline: true,
                width: 400,
                onPressed: () => context.goNamed(SignUpPath),
              ),
      ],
    );
  }
}
