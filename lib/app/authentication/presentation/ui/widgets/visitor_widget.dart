import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:safe_insets/index.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/data/models/visitor.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/presentation/ui/screens/validate_phone/validate_phone_screen.dart';
import 'package:shop/app/authentication/presentation/ui/screens/verify_phone/verify_phone_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_core/config/config.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class VisitorWidget extends StatelessWidget {
  const VisitorWidget(this.visitor, this.phoneNumber, this.pin, this.isDesktop,
      {super.key});

  final Visitor visitor;
  final String phoneNumber;
  final String pin;
  final bool isDesktop;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final browserDetails = visitor.visit?.browserDetails;
    return Padding(
      padding: screenPadding,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Confirm Device',
            style: textTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          const YMargin(20),
          Icon(
            Icons.security,
            size: 70,
            // color: kColorRed2,
          ),
          const YMargin(20),
          Text(
            'Your account will be linked to this device and any other device linked to your account will stop working',
            style: textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const YMargin(20),
          _item(
              'Device: ',
              '${browserDetails?.deviceName} (${browserDetails?.os}, ${browserDetails?.osVersion})',
              textTheme),
          _item('Time: ', visitor.visit?.time.toLocal().customFormat() ?? '',
              textTheme),
          _item('Location: ',
              '${visitor.city}${visitor.visit?.ipLocation.country}', textTheme),
          const YMargin(20),
          // Spacer(),
          SafeArea(
            child: SafeAreaWrap(
              KButtonPrimary(
                text: 'Confirm',
                onTap: () async {
                  final params = SendOTParams(
                    mode: PhoneAuthMode.WhatsApp,
                    phoneNumber: phoneNumber,
                    url: '${config.firebaseServiceUrl!}/$SEND_OTP_PATH',
                  );

                  TdLoader.show(context);

                  final res = await locator.get<SendOTP>().call(params);

                  res.when(
                    success: (data) {
                      Navigator.pop(context);
                      TdLoader.hide();

                      if (isDesktop) {
                        context.pushNamed(
                          WebValidateOTPPath,
                          extra: ValidatePhoneArgs(
                            params: params,
                            pageType: AuthPageType.deviceManagement,
                            countryCode: 'NG',
                            pin: pin,
                          ),
                        );

                        return;
                      }

                      context.pushNamed(
                        ValidateOTPPath,
                        extra: VerifyPhoneArgs(
                          otpParams: params,
                          pageType: AuthPageType.deviceManagement,
                          pin: pin,
                        ),
                      );
                    },
                    apiFailure: (error, _) {
                      TdLoader.hide();
                      Toast.apiError(error, context);
                    },
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _item(String key, String value, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            key,
            style: textTheme.bodyLarge,
          ),
          Flexible(
            child: Text(
              value,
              style: textTheme.bodyLarge,
            ),
          ),
        ],
      ),
    );
  }

  static Future<void> show(
    BuildContext context,
    Visitor visitor,
    String phoneNumber,
    String pin,
    bool isDesktop,
  ) async {
    return isDesktop
        ? showDialog<bool>(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                content: SizedBox(
                  height: 650,
                  width: 400,
                  child: VisitorWidget(visitor, phoneNumber, pin, isDesktop),
                ),
                titleTextStyle: KTextStyle.semiBold24.copyWith(fontSize: 20),
                contentTextStyle: KTextStyle.regular14.copyWith(fontSize: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
                contentPadding:
                    EdgeInsets.only(left: 30, bottom: 30, right: 30),
                titlePadding: EdgeInsets.only(left: 30, top: 30, right: 30),
                actionsPadding: EdgeInsets.zero,
              );
            },
          )
        : showModalBottomSheet<void>(
            isScrollControlled: true,
            context: context,
            builder: (_) => FractionallySizedBox(
              heightFactor: 0.7,
              child: VisitorWidget(visitor, phoneNumber, pin, isDesktop),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(18),
              ),
            ),
            clipBehavior: Clip.antiAlias,
          );
  }
}

extension CustomDateTimeFormat on DateTime {
  String customFormat() {
    final String dayOfWeek = DateFormat('EEEE').format(this);
    final String month = DateFormat.MMM().format(this);
    final String day = DateFormat.d().format(this);
    final String year = DateFormat.y().format(this);
    final String hourMinute = DateFormat.jm().format(this);

    return '$dayOfWeek, $month $day, $year, $hourMinute';
  }
}
