import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/styles/text_style.dart';

class SideBar extends StatelessWidget {
  const SideBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(),
      padding: EdgeInsets.only(top: 100, left: 50, right: 50),
      width: 370,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: SvgPicture.asset(kSvgCart2, fit: BoxFit.cover),
            title:
                Text("Shop", style: KTextStyle.medium14.copyWith(fontSize: 20)),
            subtitle: Text(
                "Buy products at best prices directly from manufacturers",
                maxLines: 2,
                style: KTextStyle.book14.copyWith(fontSize: 16)),
          ),
          YSpacing(20),
          Divider(thickness: 0.5),
          YSpacing(20),
          ListTile(
            leading: SvgPicture.asset(kSvgLoan2, fit: BoxFit.cover),
            title: Text("Loans",
                style: KTextStyle.medium14.copyWith(fontSize: 20)),
            subtitle: Text("Get instant loans to stock up your shop",
                maxLines: 2, style: KTextStyle.book14.copyWith(fontSize: 16)),
          ),
          YSpacing(20),
          Divider(thickness: 0.5),
          YSpacing(20),
          ListTile(
            leading: SvgPicture.asset(kSvgCar, fit: BoxFit.cover),
            title: Text("Delivery",
                style: KTextStyle.medium14.copyWith(fontSize: 20)),
            subtitle: Text("On time and full delivery of orders",
                maxLines: 2, style: KTextStyle.book14.copyWith(fontSize: 16)),
          ),
          YSpacing(20),
          Divider(thickness: 0.5),
          YSpacing(20),
          ListTile(
            leading: SvgPicture.asset(kSvgCard, fit: BoxFit.cover),
            title: Text("Payment Invoice",
                style: KTextStyle.medium14.copyWith(fontSize: 20)),
            subtitle: Text("Easy Accepting or declining payments  invoices.",
                maxLines: 2, style: KTextStyle.book14.copyWith(fontSize: 16)),
          ),
        ],
      ),
    );
  }
}
