import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:shop/src/components/src/dropdown/td_select_text_field.dart';
import 'package:td_commons_flutter/models/index.dart';

import '../../../../../src/components/src/toast/toast.dart';

class TdOutletCategoriesDropdown extends StatelessWidget {
  final ValueChanged<OutletType?> onChanged;
  final bool disabled;
  final String? selectedOutletType;

  TdOutletCategoriesDropdown(
    this.onChanged, {super.key, 
    this.disabled = false,
    this.selectedOutletType = 'Please select an Outlet Type',
  });

  final _loading = ValueNotifier<bool>(true);

  @override
  Widget build(BuildContext context) {
    final child = StreamBuilder<List<OutletType>>(
      stream: _streamOfOutletTypes(),
      initialData: [],
      builder:
          (BuildContext context, AsyncSnapshot<List<OutletType>> snapshot) {
        List<OutletType> outletTypes = snapshot.data ?? [];
        if (snapshot.hasData) {
          _loading.value = false;
        }

        if (snapshot.hasError) {
          _loading.value = false;
          Toast.error(
              'Could not fetch outlet types, please check your internet connection',
              context);
        }
        return ValueListenableBuilder<bool>(
          valueListenable: _loading,
          builder: (context, loading, _) {
            return TdSelectTextField(
              menuItems: outletTypes.isEmpty
                  ? []
                  : outletTypes.map((e) => e.name ?? e.id ?? "").toList(),
              onChange: (val) => onChanged(outletTypes[val]),
              placeHolder: selectedOutletType,
              isEnabled: !disabled,
              isLoading: loading,
            );
          },
        );
      },
    );

    if (disabled) {
      return IgnorePointer(child: child);
    }
    return child;
  }

  static Stream<List<OutletType>> _streamOfOutletTypes() {
    return FirebaseFirestore.instance
        .collection("retailoutlettypes")
        .snapshots()
        .map(
      (snapshot) {
        return snapshot.docs
            .map(
              (snapshot) => OutletType.fromMap(
                snapshot.data(),
              ),
            )
            .toList();
      },
    );
  }
}
