import 'package:flutter/cupertino.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_store/mobile/mobile_sign_up_store_form.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_store/web/web_sign_up_store_form.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class SignUpStoreForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final String countryCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const SignUpStoreForm({
    super.key,
    required this.controllers,
    required this.countryCode,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebSignUpStoreForm(
          controllers: controllers,
          countryCode: countryCode,
          onSetLocation: onSetLocation,
          loading: loading,
          isLoading: isLoading),
      mediumScreen: MobileSignUpStoreForm(
          controllers: controllers,
          countryCode: countryCode,
          onSetLocation: onSetLocation,
          loading: loading,
          isLoading: isLoading),
      smallScreen: MobileSignUpStoreForm(
        controllers: controllers,
        countryCode: countryCode,
        onSetLocation: onSetLocation,
        loading: loading,
        isLoading: isLoading,
      ),
    );
  }
}
