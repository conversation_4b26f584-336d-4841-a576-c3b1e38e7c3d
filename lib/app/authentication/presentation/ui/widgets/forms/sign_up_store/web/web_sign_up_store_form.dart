import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/td_text_field_error.dart';
import 'package:shop/src/components/src/google_places/index.dart' as web;
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/services/location_service.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/vendor/google_places/index.dart';

import '../../../outlet_picker.dart';

class WebSignUpStoreForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final String countryCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const WebSignUpStoreForm({
    super.key,
    required this.controllers,
    required this.countryCode,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TdTextField(
                title: 'Store Name',
                hint: 'Your store/outlet name',
                textController: controllers['name'],
                inputFormatters: [validInput()],
              ),
            ),
            XSpacing(30),
            Expanded(child: _buildOutletType()),
          ],
        ),
        YSpacing(15),
        TdTextField(
          readonly: true,
          title: 'Store Address',
          hint: 'Your store/outlet address',
          textController: controllers['address'],
          onTap: () {
            _showGooglePlaces(context);
          },
          // bottom: _buildCurrentLocation(context),
        ),
        YSpacing(15),
        Row(
          children: [
            Expanded(
              child: TdTextField(
                title: 'Store LGA',
                hint: 'Your store/outlet local government area',
                textController: controllers['lga'],
                inputFormatters: [validInput()],
              ),
            ),
            XSpacing(30),
            Expanded(
              child: TdTextField(
                title: 'Nearest Landmark',
                hint: 'Nearest Landmark to your store',
                textController: controllers['landmark'],
                inputFormatters: [validInput()],
              ),
            )
          ],
        )
      ],
    );
  }

  // Widget _buildCurrentLocation(BuildContext context) {
  //   if (isLoading) return SizedBox.shrink();

  //   return InkWell(
  //     onTap: () async {
  //       loading(true);
  //       final location = await LocationService.getCurrentLocation();
  //       if (location == null) {
  //         Toast.error('Please enable your location', context);
  //         loading(false);
  //         return;
  //       }

  //       try {
  //         final address =
  //             await LocationService.addressFromLocation(location).timeout(
  //           Duration(seconds: 15),
  //           onTimeout: () => throw DioError(
  //             type: DioErrorType.connectTimeout,
  //             requestOptions: RequestOptions(path: ''),
  //           ),
  //         );
  //         onSetLocation(address);
  //         // set address on text field
  //         controllers['address']!.controller!.text = address.address!;
  //       } catch (e, s) {
  //         Toast.error('Unable to extract address. Please try again', context);
  //         ErrorHandler.report(e, s);
  //       }

  //       loading(false);
  //     },
  //     child: Text(
  //       'Use current location',
  //       style: TextStyle(),
  //     ),
  //   );
  // }

  Widget _buildOutletType() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Store Outlet Type'),
        SizedBox(height: 2),
        TdOutletCategoriesDropdown(
          (value) {
            controllers['type']!.controller!.text = value!.id!;
          },
        ),
        TdTextFieldError(
          textController: controllers['type'],
        ),
      ],
    );
  }

  Future<void> _showGooglePlaces(BuildContext context) async {
    FocusScope.of(context).requestFocus(FocusNode());
    Prediction? prediction = await _showPlaceModal(
      context,
      countryCode: countryCode,
      hint: 'Type address',
    );

    if (prediction == null) {
      // may be show snack bar
      return;
    }

    loading(true);

    try {
      final address = await LocationService.addressFromPlaceId(
        prediction.placeId!,
      );

      onSetLocation(address);
      // set address on text field
      controllers['address']!.controller!.text = prediction.description!;
    } catch (e, s) {
      Toast.error('Unable to extract address. Please try again', context);
      ErrorHandler.report(e, s);
    }

    loading(false);
  }

  static Future<Prediction?> _showPlaceModal(BuildContext context,
      {required String countryCode, String? hint, bool isModal = false}) async {
    final config = Provider.of<AppConfig>(context, listen: false);

    return kIsWeb
        ? web.PlacesAutocomplete.show(
            apiKey: config.googleMapsApiKey,
            context: context,
            language: "en",
            components: [
              Component(Component.country, countryCode.toLowerCase())
            ],
            hint: hint!,
          )
        : PlacesAutocomplete.show(
            apiKey: config.googleMapsApiKey!,
            context: context,
            mode: isModal ? Mode.overlay : Mode.fullscreen,
            language: "en",
            components: [
              Component(Component.country, countryCode.toLowerCase())
            ],
            hint: hint!,
          );
  }
}
