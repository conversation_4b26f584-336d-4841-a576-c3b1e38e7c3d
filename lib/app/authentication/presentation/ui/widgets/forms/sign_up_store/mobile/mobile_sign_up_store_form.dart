import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/address_field.dart';
import 'package:shop/src/components/src/form/src/td_text_field_error.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../retail_outlets_type.dart';

class MobileSignUpStoreForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final String countryCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const MobileSignUpStoreForm({
    super.key,
    required this.controllers,
    required this.countryCode,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    final shopTopUpApp = userCubit.domain == AppHost.shopTopUp;

    return Column(
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TdTextField(
          title: 'Store Name',
          hint: 'Enter your store name',
          textController: controllers['name'],
          inputFormatters: [validInput()],
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        // _buildOutletType(),
        // const YMargin(10),
        Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RetailOutletsType(
              title: 'Store Type',
              onChanged: (value) {
                controllers['type']!.controller!.text = value.id!;
              },
            ),
            TdTextFieldError(
              textController: controllers['type'],
            ),
          ],
        ),
        Divider(
          color: Color.fromRGBO(231, 231, 231, 1),
          height: 0,
          thickness: 1,
        ),
        const YMargin(12),
        AddressField(
          title: 'Store Address',
          hint: 'Enter address',
          controller: controllers['address']!,
          countryCode: countryCode,
          isLoading: isLoading,
          onSetLocation: onSetLocation,
          loading: loading,
        ),
        if (!shopTopUpApp) const YMargin(8),
        Visibility(
          visible: shopTopUpApp,
          child: TdTextField(
            hint: 'Local government area',
            textController: controllers['lga'],
            inputFormatters: [validInput()],
          ),
        ),
        Visibility(
          visible: shopTopUpApp,
          child: TdTextField(
            title: 'Nearest Landmark',
            hint: 'E.g Post Office',
            textController: controllers['landmark'],
            inputFormatters: [validInput()],
          ),
        ),
      ],
    );
  }
}
