import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/form/src/address_field.dart';
import 'package:shop/app/loan/presentation/ui/widgets/k_dropdown_input.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class MobileSignUpUserForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final bool disabled;
  final String countryCode;
  final bool hasReferralCode;
  final bool showReferralCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const MobileSignUpUserForm({
    super.key,
    required this.controllers,
    this.disabled = false,
    required this.countryCode,
    this.hasReferralCode = false,
    this.showReferralCode = true,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  void nextFocus(BuildContext context) {
    if (context.mounted) {
      final focusScope = FocusScope.of(context);
      if (focusScope.hasFocus) {
        focusScope.nextFocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    final shopTopUpApp = userCubit.domain == AppHost.shopTopUp;
    return Column(
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TdTextField(
          readonly: disabled,
          title: 'First Name',
          hint: 'Enter your first name',
          textController: controllers['firstName'],
          inputFormatters: [validInput()],
          onSubmitted: () {
            nextFocus(context);
          },
        ),
        TdTextField(
          readonly: disabled,
          title: 'Last Name',
          hint: 'Enter your last name',
          textController: controllers['lastName'],
          inputFormatters: [validInput()],
          onSubmitted: () {
            nextFocus(context);
          },
        ),
        TdDropdownInput(
          title: "Gender",
          hintText: "Select gender",
          label: 'gender',
          options: gender,
          controllers: controllers,
          onChanged: (String? value) {
            final gender = value!.toLowerCase() == 'other'
                ? 'others'
                : value.toLowerCase();
            controllers['gender']?.controller?.text = gender;
          },
          getLabel: (String value) => value,
        ),
        if (shopTopUpApp)
          AddressField(
            title: 'Contact Address',
            hint: 'Your contact address',
            controller: controllers['contactAddress']!,
            countryCode: countryCode,
            isLoading: isLoading,
            onSetLocation: onSetLocation,
            loading: loading,
          ),
        if (showReferralCode) ...[
          Divider(
            color: Color.fromRGBO(231, 231, 231, 1),
            height: 0,
            thickness: 1,
          ),
          const YMargin(12),
        ],
        Visibility(
          visible: showReferralCode,
          maintainState: true,
          maintainAnimation: true,
          maintainSize: true,
          child: TdTextField(
            readonly: hasReferralCode || disabled,
            disabled: hasReferralCode,
            title: 'Referral Code (optional)',
            hint: 'Your referral code',
            textController: controllers['referralCode'],
            inputFormatters: [validInput()],
          ),
        ),
      ],
    );
  }
}
