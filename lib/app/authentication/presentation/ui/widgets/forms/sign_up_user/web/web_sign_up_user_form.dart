import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/loan/presentation/ui/widgets/k_dropdown_input.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/google_places/index.dart' as web;
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/services/location_service.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/vendor/google_places/places.dart';

class WebSignUpUserForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final bool disabled;
  final String countryCode;
  final bool hasReferralCode;
  final bool showReferralCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const WebSignUpUserForm({
    super.key,
    required this.controllers,
    this.disabled = false,
    required this.countryCode,
    this.hasReferralCode = false,
    this.showReferralCode = true,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TdTextField(
                readonly: disabled,
                title: 'First Name',
                hint: 'Your First Name',
                textController: controllers['firstName'],
                inputFormatters: [validInput()],
              ),
            ),
            XSpacing(30),
            Expanded(
              child: TdTextField(
                readonly: disabled,
                title: 'Last Name',
                hint: 'Your Last Name',
                textController: controllers['lastName'],
                inputFormatters: [validInput()],
              ),
            )
          ],
        ),
        YSpacing(15),
        TdTextField(
          readonly: true,
          title: 'Contact Address',
          hint: 'Your contact address',
          textController: controllers['contactAddress'],
          onTap: () {
            _showGooglePlaces(context);
          },
          // bottom: _buildCurrentLocation(context),
        ),
        Container(),
        YSpacing(15),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TdDropdownInput(
                title: 'Gender',
                hintText: "Select your gender",
                label: 'gender',
                options: gender,
                controllers: controllers,
                onChanged: (String? value) {
                  controllers['gender']?.controller?.text = value!;
                },
                getLabel: (String value) => value,
              ),
            ),
            XSpacing(30),
            Expanded(
              child: Visibility(
                visible: showReferralCode,
                maintainState: true,
                maintainAnimation: true,
                maintainSize: true,
                child: TdTextField(
                  readonly: hasReferralCode || disabled,
                  disabled: hasReferralCode,
                  title: 'Referral Code (optional)',
                  hint: 'Your referral code',
                  textController: controllers['referralCode'],
                  inputFormatters: [validInput()],
                ),
              ),
            )
          ],
        )
      ],
    );
  }

  // Widget _buildCurrentLocation(BuildContext context) {
  //   if (isLoading) return SizedBox.shrink();

  //   return InkWell(
  //     onTap: () async {
  //       loading(true);
  //       final location = await LocationService.getCurrentLocation();
  //       if (location == null) {
  //         Toast.error('Please enable your location', context);
  //         loading(false);
  //         return;
  //       }

  //       try {
  //         final address = await LocationService.addressFromLocation(location);

  //         onSetLocation(address);
  //         // set address on text field
  //         controllers['contactAddress']!.controller!.text = address.address!;
  //       } catch (e, s) {
  //         Toast.error('Unable to extract address. Please try again', context);
  //         ErrorHandler.report(e, s);
  //       }

  //       loading(false);
  //     },
  //     child: Text(
  //       'Use current location',
  //       style: TextStyle(),
  //     ),
  //   );
  // }

  Future<void> _showGooglePlaces(BuildContext context) async {
    FocusScope.of(context).requestFocus(FocusNode());
    Prediction? prediction = await _showPlaceModal(
      context,
      countryCode: countryCode,
      hint: 'Type address',
    );

    if (prediction == null) {
      // may be show snack bar
      return;
    }

    loading(true);

    try {
      final address = await LocationService.addressFromPlaceId(
        prediction.placeId!,
      );

      onSetLocation(address);
      // set address on text field
      controllers['contactAddress']!.controller!.text = prediction.description!;
    } catch (e, s) {
      Toast.error('Unable to extract address. Please try again', context);
      ErrorHandler.report(e, s);
    }

    loading(false);
  }

  static Future<Prediction?> _showPlaceModal(BuildContext context,
      {required String countryCode, String? hint, bool isModal = false}) async {
    final config = Provider.of<AppConfig>(context, listen: false);

    return kIsWeb
        ? web.PlacesAutocomplete.show(
            apiKey: config.googleMapsApiKey,
            context: context,
            language: "en",
            components: [
              Component(Component.country, countryCode.toLowerCase())
            ],
            hint: hint,
          )
        : PlacesAutocomplete.show(
            apiKey: config.googleMapsApiKey!,
            context: context,
            mode: isModal ? Mode.overlay : Mode.fullscreen,
            language: "en",
            components: [
              Component(Component.country, countryCode.toLowerCase())
            ],
            hint: hint!,
          );
  }
}
