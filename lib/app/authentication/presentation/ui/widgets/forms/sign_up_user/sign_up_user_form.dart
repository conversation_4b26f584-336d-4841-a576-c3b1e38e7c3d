import 'package:flutter/cupertino.dart';
import 'package:shop/app/authentication/data/models/td_address.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_user/mobile/mobile_sign_up_user_form.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/forms/sign_up_user/web/web_sign_up_user_form.dart';
import 'package:shop/src/components/src/form/form.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class SignUpUserForm extends StatelessWidget {
  final Map<String, TdTextController> controllers;
  final bool disabled;
  final String countryCode;
  final bool showReferralCode;
  final SetAddress onSetLocation;
  final ValueChanged<bool> loading;
  final bool isLoading;

  const SignUpUserForm({
    super.key,
    required this.controllers,
    this.disabled = false,
    required this.countryCode,
    this.showReferralCode = true,
    required this.onSetLocation,
    required this.loading,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebSignUpUserForm(
          controllers: controllers,
          countryCode: countryCode,
          onSetLocation: onSetLocation,
          loading: loading,
          isLoading: isLoading),
      mediumScreen: MobileSignUpUserForm(
          controllers: controllers,
          countryCode: countryCode,
          onSetLocation: onSetLocation,
          loading: loading,
          isLoading: isLoading),
      smallScreen: MobileSignUpUserForm(
          controllers: controllers,
          countryCode: countryCode,
          onSetLocation: onSetLocation,
          loading: loading,
          isLoading: isLoading),
    );
  }
}
