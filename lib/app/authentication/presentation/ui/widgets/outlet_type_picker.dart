import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:td_commons_flutter/models/index.dart';

class OutletCategoriesDropdown extends StatelessWidget {
  final OutletType? selection;
  final InputDecoration decoration;
  final ValueChanged<OutletType?> onChanged;
  final TextStyle? style;
  final bool disabled;

  const OutletCategoriesDropdown(
    this.selection,
    this.onChanged,
    this.decoration, {super.key, 
    this.style,
    this.disabled = false,
  });

  @override
  Widget build(BuildContext context) {
    final child = StreamBuilder<List<OutletType>>(
      stream: _streamOfOutletTypes(),
      builder:
          (BuildContext context, AsyncSnapshot<List<OutletType>> snapshot) {
        List<OutletType> outletTypes = snapshot.data ?? [];
        final found = outletTypes.firstWhereOrNull(
          (OutletType type) => type == selection,
        );
        return DropdownButtonFormField<OutletType>(
          decoration: decoration,
          style: style,
          value: outletTypes.isEmpty ? selection : found,
          onChanged: (outletTypes.isEmpty) ? null : onChanged,
          validator: (OutletType? value) {
            if (value == null && selection == null) {
              return 'Please select an Outlet Type';
            }
            return null;
          },
          items: outletTypes.map<DropdownMenuItem<OutletType>>(
            (OutletType outletType) {
              return DropdownMenuItem<OutletType>(
                value: outletType,
                child: Text(
                  outletType.name ?? outletType.id ?? "",
                ),
              );
            },
          ).toList(),
        );
      },
    );

    if (disabled) {
      return IgnorePointer(child: child);
    }
    return child;
  }

  static Stream<List<OutletType>> _streamOfOutletTypes() {
    return FirebaseFirestore.instance
        .collection("retailoutlettypes")
        .snapshots()
        .map(
      (snapshot) {
        return snapshot.docs
            .map(
              (snapshot) => OutletType.fromMap(
                snapshot.data(),
              ),
            )
            .toList();
      },
    );
  }
}
