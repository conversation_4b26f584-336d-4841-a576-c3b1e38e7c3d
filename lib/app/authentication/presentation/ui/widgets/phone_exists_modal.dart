import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

class PhoneExistsModal extends StatelessWidget {
  final String phoneNumber;
  final String redactedEmail;
  final String email;
  const PhoneExistsModal(
    this.phoneNumber,
    this.redactedEmail,
    this.email, {
    super.key,
  });

  static Future<void> show(
    BuildContext context, {
    required String phoneNumber,
    required String redactedEmail,
    required String email,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: PhoneExistsModal(phoneNumber, redactedEmail, email),
          titleTextStyle: KTextStyle.semiBold24.copyWith(fontSize: 20),
          contentTextStyle: KTextStyle.regular14.copyWith(fontSize: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          contentPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(left: 20, top: 25, bottom: 25, right: 20),
        decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.all(Radius.circular(5))),
        alignment: Alignment.center,
        // padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        // height: 604,
        width: 692, //SizeConfig.scaleX(0.57),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(kSvgPhoneIcon),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                'Account Exists',
                style:
                    textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
              ),
            ),
            Text.rich(
              TextSpan(text: "Your phone number ", children: [
                TextSpan(
                    text: phoneNumber,
                    style: textTheme.bodyLarge
                        ?.copyWith(fontWeight: FontWeight.w600)),
                TextSpan(
                    text: " has an account with a different email address "),
                TextSpan(
                    text: redactedEmail,
                    style: textTheme.bodyLarge
                        ?.copyWith(fontWeight: FontWeight.w600))
              ]),
              style: textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const YMargin(14),
            Text(
              'Please log in with that email address',
              style: textTheme.bodyMedium?.copyWith(
                  color: textTheme.bodyMedium?.color?.withValues(alpha: .6)),
            ),
            const YMargin(24),
            KButtonPrimary(
              text: 'Ok, got it',
              onTap: () => Navigator.of(context)
                ..pop()
                ..pop()
                ..pop()
                ..pop(),
            ),
            const YMargin(15),
            KButtonPrimary.icon(
              text: 'Contact Support',
              icon: SvgPicture.asset(
                kSvgSupportIcon,
                color: colorScheme.primary,
              ),
              color: colorScheme.onPrimary,
              textColor: colorScheme.primary,
              onTap: () async {
                await setupIntercomWithEmail(context, email);
                await Intercom.instance.displayMessages();
              },
            )
          ],
        ),
      ),
    );
  }
}
