import 'package:equatable/equatable.dart';

abstract class PhoneValidateState extends Equatable {
  final List? items;

  const PhoneValidateState([this.items]);

  @override
  List<Object?> get props => (items ?? []);
}

/// UnInitialized
class InitialPhoneValidateState extends PhoneValidateState {
  @override
  String toString() => 'InitState';
}

class ValidatingOTPState extends PhoneValidateState {
  final bool loading;

  ValidatingOTPState(this.loading) : super([loading]);

  @override
  String toString() => 'LoadingState $loading';
}

class ValidatedAfterLoginState extends PhoneValidateState {}

class ValidatedNINState extends PhoneValidateState {}

class ValidatedBVNState extends PhoneValidateState {}

class ValidatePaymentState extends PhoneValidateState {
  final String token;

  const ValidatePaymentState(this.token);

  @override
  List<Object?> get props => [token];
}

class ValidatedBusinessBvnState extends PhoneValidateState {}

class ValidatedOTPState extends PhoneValidateState {
  final String accessToken;
  final String countryCode;

  ValidatedOTPState(
    this.accessToken,
    this.countryCode,
  ) : super([accessToken]);

  @override
  List<Object?> get props => [accessToken, countryCode];

  @override
  String toString() => 'Sent OTP';
}

class FailedToValidateOTP extends PhoneValidateState {
  final String? errorMessage;

  FailedToValidateOTP({this.errorMessage}) : super([errorMessage]);

  @override
  List<Object?> get props => [errorMessage];

  @override
  String toString() => 'ErrorValidatingOtp';
}

class PhoneValidateUserLoggedIn extends PhoneValidateState {
  final String? firstName;

  final String accessToken;

  PhoneValidateUserLoggedIn(this.firstName, this.accessToken)
      : super([accessToken]);

  @override
  String toString() => 'PhoneValidateUserLoggedIn';
}
