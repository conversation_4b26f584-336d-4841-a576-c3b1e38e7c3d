import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

import '../../../data/models/preference.dart';
import '../../../domain/params/post_params.dart';
import '../../../domain/use_cases/get_preferences.dart';
import 'user_cubit.dart';

part 'preferences_state.dart';

class PreferencesCubit extends Cubit<PreferencesState>
    implements OnBeforeLogout {
  PreferencesCubit(this._getPreferences) : super(PreferencesInitial());

  final GetPreferences _getPreferences;

  Future<void> getPreferences(PreferenceType type, [bool forced = false]) async {
    final currentState = state;
    if (currentState is PreferencesLoaded && !forced) {
      final userPreferences = currentState.userPreferences;
      final collectionPreferences = currentState.collectionPreferences;

      emit(PreferencesLoading());

      // This would enable PreferencesLoaded to be emitted
      await Future.delayed(Duration(microseconds: 1));

      emit(
        PreferencesLoaded(
          userPreferences: userPreferences,
          collectionPreferences:
              sortListByOtherList(collectionPreferences, userPreferences),
        ),
      );

      return;
    }

    emit(PreferencesLoading());

    _mapDataToState(type);
  }

  Future<void> updateUserPreferences(List<Preference> userPreferences) async {
    final currentState = state;
    if (currentState is PreferencesLoaded) {
      final collectionPreferences = currentState.collectionPreferences;
      emit(
        PreferencesLoaded(
          userPreferences: userPreferences,
          collectionPreferences:
              sortListByOtherList(collectionPreferences, userPreferences),
          update: false,
        ),
      );
    }
  }

  Future<void> _mapDataToState(PreferenceType type) async {
    final hexCode =
        UserCubit.instance?.currentOutlet?.coordinates?.plusCode6Hex;

    if (hexCode == null) return;

    final params = GetPreferencesParams(type: type, hexCode: hexCode);

    final res = await _getPreferences(params);

    res.when(
      success: (data) {
        final userPref = data[PreferenceType.user] ?? [];
        final colPref = data[PreferenceType.collection] ?? [];
        emit(
          PreferencesLoaded(
            userPreferences: userPref,
            collectionPreferences: sortListByOtherList(colPref, userPref),
          ),
        );
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(PreferencesError(msg));
      },
    );
  }

  List<T> sortListByOtherList<T>(List<T> list1, List<T> list2) {
    list1.sort((a, b) {
      if (list2.contains(a) && list2.contains(b)) {
        return list1.indexOf(a).compareTo(list1.indexOf(b));
      } else if (list2.contains(a)) {
        return -1;
      } else if (list2.contains(b)) {
        return 1;
      } else {
        return 0;
      }
    });

    return list1;
  }

  @override
  Future<void> onBeforeLogout() async {
    emit(PreferencesInitial());
  }

  @override
  Future<void> close() async {
    emit(PreferencesInitial());
    super.close();
  }
}
