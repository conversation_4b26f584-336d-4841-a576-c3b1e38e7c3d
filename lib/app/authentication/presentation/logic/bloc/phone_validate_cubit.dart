import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/page_type.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_token.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_otp.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/phone_validate_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class PhoneValidateCubit extends Cubit<PhoneValidateState> {
  PhoneValidateCubit(
    this._verifyOTP,
    this._login,
  ) : super(InitialPhoneValidateState());
  final VerifyOTP? _verifyOTP;
  final LoginWithToken? _login;

  /// Validates a user inputted OTP
  Future<void> verifyOTP(
    VerifyOTParams params,
    AuthPageType? pageType,
  ) async {
    emit(ValidatingOTPState(true));

    final res = await _verifyOTP!(params);

    res.when(
      success: (data) async {
        if (pageType == AuthPageType.login) {
          _loginUser(
            LoginTokenParams(
              phone: params.phoneNumber,
              token: data.accessToken,
            ),
          );
          return;
        }

        if (pageType == AuthPageType.proceedLogin) {
          emit(ValidatedAfterLoginState());
          return;
        }

        if (pageType == AuthPageType.verifySignUpNin ||
            pageType == AuthPageType.verifyKycNin ||
            pageType == AuthPageType.verifyHomeNin ||
            pageType == AuthPageType.verifyPaymentNin) {
          emit(ValidatedNINState());
          return;
        }

        if (pageType == AuthPageType.verifyKycBvn) {
          emit(ValidatedBVNState());
          return;
        }

        if (pageType == AuthPageType.verifyBusinessBvn) {
          emit(ValidatedBusinessBvnState());
          return;
        }

        if (pageType == AuthPageType.orderPayment) {
          emit(ValidatePaymentState(data.accessToken));
          return;
        }

        emit(ValidatedOTPState(
          data.accessToken,
          data.countryCode,
        ));
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(
          FailedToValidateOTP(
            errorMessage: msg,
            // 'Invalid or expired otp code' //ApiExceptions.getErrorMessage(error),
          ),
        );
      },
    );
  }

  /// Attempt to login a user
  Future<void> _loginUser(LoginTokenParams params) async {
    final res = await _login!(params);

    res.maybeWhen(
      success: (_) {
        emit(PhoneValidateUserLoggedIn(_.firstName, params.token));
      },
      apiFailure: (error, _) {
        emit(FailedToValidateOTP(
          errorMessage: ApiExceptions.getErrorMessage(error),
        ));
      },
      orElse: () {
        emit(FailedToValidateOTP(errorMessage: 'Something went wrong'));
      },
    );
  }
}
