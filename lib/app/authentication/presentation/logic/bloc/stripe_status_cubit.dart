import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/domain/use_cases/verificationStatus.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_status_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class StripeStatusCubit extends Cubit<StripeStatusState> {
  StripeStatusCubit(this._status) : super(StripeStatusInitial());

  final VerificationStatus _status;

//Fetch Strupe Verification status
  Future getVerificationStatus() async {
    emit(StripeStatusLoading());

    final res = await _status(NoParams());
    res.when(
      success: (data) async {
        emit(StripeVerificationStatusRetrieved(data));
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(StripeVerificationStatusFailed(msg));
      },
    );
  }
}
