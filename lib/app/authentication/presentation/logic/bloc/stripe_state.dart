import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';

abstract class StripeState extends Equatable {
  const StripeState();

  @override
  List<Object?> get props => [];
}

class StripeInitial extends StripeState {}

class StripeVerificationInitiated extends StripeState {
  final InitiateStripeResponse response;
  const StripeVerificationInitiated(this.response);

  @override
  List<Object> get props => [];
}

class StripeCompanyVerificationInitiated extends StripeState {
  final InitiateStripeResponse response;
  const StripeCompanyVerificationInitiated(this.response);

  @override
  List<Object> get props => [];
}

class StripeVerificationLoading extends StripeState {}

class StripeVerificationFailed extends StripeState {
  final String msg;
  const StripeVerificationFailed(this.msg);
}

class StripeCompanyVerificationFailed extends StripeState {
  final String msg;
  const StripeCompanyVerificationFailed(this.msg);
}

class StripeVerificationCompleted extends StripeState {
  const StripeVerificationCompleted();

  @override
  List<Object?> get props => [];
}
