import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';

abstract class SmileJobStatusState extends Equatable {
  const SmileJobStatusState();

  @override
  List<Object?> get props => [];
}

class SmileJobStatusInitial extends SmileJobStatusState {}

class SmileJobStatusFetching extends SmileJobStatusState {}

class SmileJobStatusFetchFailed extends SmileJobStatusState {
  final String message;

  const SmileJobStatusFetchFailed(this.message);

  @override
  List<Object> get props => [message];
}

class SmileJobStatusFetchcompleted extends SmileJobStatusState {
  final CurrentJob currentJob;

  const SmileJobStatusFetchcompleted(this.currentJob);

  @override
  List<Object?> get props => [currentJob];
}
