import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';

abstract class StripeStatusState extends Equatable {
  const StripeStatusState();

  @override
  List<Object?> get props => [];
}

class StripeStatusInitial extends StripeStatusState {}

class StripeStatusLoading extends StripeStatusState {}

class StripeVerificationStatusRetrieved extends StripeStatusState {
  final StripeStatusResponse response;
  const StripeVerificationStatusRetrieved(this.response);

  @override
  List<Object?> get props => [response];
}

class StripeVerificationStatusFailed extends StripeStatusState {
  final String msg;
  const StripeVerificationStatusFailed(this.msg);
}
