import 'dart:async';
import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sentry_flutter/sentry_flutter.dart' as sentry;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/anonymous_user.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/fetch_outlet.dart';
import 'package:shop/app/authentication/domain/use_cases/stream_outlet.dart';
import 'package:shop/app/authentication/presentation/listeners/on_anonymous_login.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
import 'package:shop/app/authentication/presentation/listeners/on_logout.dart';
import 'package:shop/app/authentication/presentation/listeners/on_user_changed.dart';
import 'package:shop/app/authentication/presentation/logic/utils/methods.dart';
import 'package:shop/app/loan/presentation/logic/utils/methods.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/utils/remote_config_manager.dart';
import 'package:shop/src/go_router/router.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class UserCubit {
  /// The firebase idToken for the current user.
  ///
  /// Empty string if the user is not logged in.
  static String idToken = '';

  /// The [UserCubit] instance, can be accessed globally.
  static UserCubit? instance;

  final AppRouter navigationService;

  /// To allow [logout] method to be called multiple times
  /// but only handled once
  bool _loggingOut = false;

  bool get isLoggingOut => _loggingOut;

  /// The uid for the current user.
  ///
  /// Empty string if the user is not logged in.
  String userUid = '';

  /// The [FirebaseDateParser] instance.
  FirebaseDateParser dateParser = FirebaseDateParser();

  late SharedPrefService preferences;

  // keep track of the current user value
  final BehaviorSubject<User?> _userSubject = BehaviorSubject<User?>();

  // keep track of the current retail outlet value
  final BehaviorSubject<RetailOutlet?> _outletSubject =
      BehaviorSubject<RetailOutlet?>();

  final _userUpdateController = StreamController<User>();

  StreamSubscription<auth.User?>? _authSubscription;
  StreamSubscription<RetailOutlet?>? _outletSubscription;

  User? _currentUser;
  AnonymousUser? _currentAnonymousUser;
  AppHost? _domain;

  /// The auth event listeners.
  final _listeners = <String, Object?>{};

  /// Returns `true` if [idToken] is not empty, else returns `false`.
  static bool get isLogged => idToken.isNotEmpty;
  bool get isAnonymous => _currentAnonymousUser != null;

  AppHost? get domain => isLogged ? currentOutlet?.domain : _domain;

  bool get isShopTopUpApp => domain == AppHost.shopTopUp;

  void setDomainFromPhone(String phoneNumber) {
    if (phoneNumber.startsWith('+44')) {
      _domain = AppHost.tradeDepot;
    } else {
      _domain = AppHost.shopTopUp;
    }
  }

  void setDomainFromCountry(String country) {
    if (country.toLowerCase == 'gb') {
      _domain = AppHost.tradeDepot;
    } else {
      _domain = AppHost.shopTopUp;
    }
  }

  void setDomain(AppHost domain) {
    _domain = domain;
  }

  void setRetailerDomain(RetailOutlet? outlet) {
    if (outlet?.phoneNumber != null) {
      setDomainFromPhone(outlet!.phoneNumber!);
    } else {
      setDomainFromCountry(outlet?.country ?? '');
    }
  }

  UserCubit(this.navigationService) {
    initPref();
    _authSubscription =
        auth.FirebaseAuth.instance.authStateChanges().listen((user) {
      if (user != null && userUid.isNotEmpty && user.uid == userUid) {
        // return if auth event is called twice
        return;
      }
      // set user id for this instance
      if (user != null) {
        userUid = user.uid;
      }

      handleAuth(user);
    });
    _userUpdateController.stream.listen(_handleUserUpdate);

    // store the current instance
    // UserCubit must not reinitialized anywhere else apart from retailApp
    // can be used in repos where context isn't available
    instance = this;
  }

  void initPref() async => preferences = locator.get<SharedPrefService>();

  /// Process the [user] from [auth.FirebaseAuth].
  Future<void> handleAuth(auth.User? user) async {
    final savedUser = preferences.read(Keys.user);
    final anonUser = preferences.read(Keys.anonUser);

    if (user != null && savedUser != null) {
      User kUser = User.fromMap(json.decode(savedUser));
      await updatingUser(kUser);
      return;
    }

    if (user?.isAnonymous == true && anonUser != null) {
      final anonymousUser = AnonymousUser.fromMap(json.decode(anonUser));
      await updatingAnonymousUser(anonymousUser);
      return;
    }
    preferences.remove(Keys.user);
    _outletSubscription?.cancel();
    _currentUser = null;
    _currentAnonymousUser = null;
    _domain = null;
    _userSubject.sink.add(null);
    _outletSubject.sink.add(null);

    // reset id token
    idToken = '';
    userUid = '';
    return;
  }

  /// Updates current user value.
  Future<void> updatingUser(User user, {bool dry = false}) async {
    setRetailerDomain(user.currentRetailOutlet);
    _currentAnonymousUser = null;
    _currentUser = user;
    _userSubject.sink.add(user);
    _outletSubject.sink.add(user.currentRetailOutlet);
    preferences.save(Keys.user, json.encode(user.toMap()));
    preferences.remove(Keys.anonUser);

    if (!dry) {
      setOutletListener(user.currentRetailOutlet?.id);
      _handleUserUpdate(user);
      _loggingOut = false;
      await _sendUserLoginEvent(user);
    }
  }

  /// Updates current anonymous user value.
  Future<void> updatingAnonymousUser(AnonymousUser user,
      {bool dry = false}) async {
    _currentAnonymousUser = user;
    preferences.save(Keys.anonUser, user.toJson());
    _loggingOut = false;
    userUid = user.id;

    if (!dry) {
      _userSubject.sink.add(null);
      _outletSubject.sink.add(null);
      await _sendAnonymousUserLoginEvent(user);
    }

    return;
  }

  /// Listens to the current retail outlet stream from firestore.
  ///
  /// Updates current retail outlet value.
  void setOutletListener(String? outletId) async {
    await _outletSubscription?.cancel();
    _outletSubscription =
        locator.get<StreamOutlet>().call(outletId).listen((outlet) {
      if (outlet != null) {
        updateOutlet();
      }
    });
  }

  /// Updates current user value.
  void _handleUserUpdate(User user, [bool dry = false]) async {
    _userSubject.sink.add(user);
    _outletSubject.sink.add(user.currentRetailOutlet);
    preferences.save(Keys.user, json.encode(user.toMap()));
    if (!dry) {
      await _sendUserChangedEvent(_currentUser, user);
    }
  }

  /// Updates current retail outlet value.
  Future<void> updateOutlet([bool dry = false]) async {
    if (_currentUser != null) {
      FetchOutletParams params =
          FetchOutletParams(outletId: _currentUser?.currentRetailOutlet?.id);
      final res = await locator.get<FetchOutlet>().call(params);

      res.when(
        success: (RetailOutlet? outlet) async {
          if (_currentUser == null) {
            // User was logged out before response was returned
            return;
          }

          if (outlet != null &&
              _currentUser != null &&
              _currentUser!.retailOutlets!.isNotEmpty) {
            List<RetailOutlet> outlets = _currentUser!.retailOutlets!
                .where((e) => e.id != outlet.id)
                .toList();
            outlets.add(outlet);
            User user = _currentUser!.copyWith(retailOutlets: outlets);
            _handleUserUpdate(user, dry);
            return;
          }
          return;
        },
        apiFailure: (_, __) {
          return;
        },
      );
    }
  }

  /// Logout the current anonymous user
  Future<void> anonLogout(String routePath) async {
    if (!isAnonymous) return;
    await _sendBeforeLogoutEvent();
    preferences.save(
        Keys.storeAddress, json.encode(currentAnonymousUser?.address.toMap()));
    preferences.remove(Keys.anonUser);
    await auth.FirebaseAuth.instance.signOut();
    await handleAuth(null);
    navigationService.router.routerDelegate.navigatorKey.currentContext
        ?.goNamed(routePath);
    await _sendUserLogoutEvent();
    try {
      if (TdLoader.isLoading) TdLoader.hide();
    } catch (e) {}
    return;
  }

  /// Logout the currently authenticated user.
  Future<void> logout({
    bool navigate = true,
  }) async {
    // already being processed
    if (_loggingOut) return;
    _loggingOut = true;

    if (navigate) {
      // Navigate first, before proceeding with the logout process
      // code: #-userCubit-navigate-on-logout
      preferences.storeBool(Keys.loggedOut, true);
      navigationService.router.routerDelegate.navigatorKey.currentContext
          ?.goNamed(OnBoardPath);
    }

    final user = currentUser;
    await _sendBeforeLogoutEvent();
    await _outletSubscription?.cancel();

    // Deactivate FCM Token
    // try {
    //   final fmToken = await getFirebaseMessagingToken();
    //   await FirebaseMessaging.instance.deleteToken();
    //   if (fmToken != null && user != null) {
    //     await deleteFcmToken(user.userId!, fmToken);
    //   }
    // } catch (_) {}

    // Delete Airship ChannelId
    // try {
    //   final channelId = await Airship.channel.identifier;
    //   if (channelId != null && user != null) {
    //     await deleteAirshipChannelId(user.userId!, channelId);
    //   }
    // } catch (e) {}

    try {
      await Intercom.instance.logout();
    } catch (e) {
      // No need to report Intercom hasn't been initialized
    }

    if (user != null) {
      await Segment.track(
        eventName: SegmentEvents.loggedOut,
        properties: {
          'user_login': user.userId,
        },
      );

      sentry.Sentry.configureScope((scope) => scope.setUser(null));
    }

    if (_currentUser?.currentRetailOutlet?.id != null) {
      // final pattern = RegExp(r'^recent-.*-customers$');
      await clearLocalStorageWithExclusions(
        // patternsToPreserve: [pattern],
        keysToPreserve: [
          Keys.recentInvoiceCustomers(_currentUser!.currentRetailOutlet!.id!)
        ],
      );
    }

    await auth.FirebaseAuth.instance.signOut();

    _currentUser = null;
    _currentAnonymousUser = null;
    _domain = null;

    await _sendUserLogoutEvent();

    try {
      if (TdLoader.isLoading) TdLoader.hide();
    } catch (e) {}
  }

  // Sink<User?> get addUser => _userSubject.sink;
  // Sink<User> get updateUser => _userUpdateController.sink;

  /// Returns the current user stream.
  ValueStream<User?> get currentUserStream => _userSubject.stream;

  /// Returns the current retail outlet stream.
  ValueStream<RetailOutlet?> get currentOutletStream => _outletSubject.stream;

  /// Returns the current user value.
  User? get currentUser => _userSubject.stream.valueOrNull;
  AnonymousUser? get currentAnonymousUser => _currentAnonymousUser;

  ///Temp Web Reload
  void load() => _userSubject.sink.add(_userSubject.stream.valueOrNull);

  /// Returns the current retail outlet value.
  RetailOutlet? get currentOutlet => _outletSubject.stream.valueOrNull;

  String get region {
    return switch (domain) {
      AppHost.tradeDepot => 'GB',
      _ => isLogged ? (currentOutlet?.country?.toUpperCase() ?? 'NG') : 'NG',
    };
  }

  String get residence {
    return switch (domain) {
      AppHost.tradeDepot => 'United Kingdom',
      _ => isGhUser
          ? 'Ghana'
          : isZaUser
              ? 'South Africa'
              : 'Nigeria',
    };
  }

  String get ussdCode {
    try {
      final phoneNumber = currentOutlet?.phoneNumber;
      if (phoneNumber == null) return '';
      final configManager = AppConfigManager.instance;
      final Map<String, dynamic> configValue =
          configManager.getConfigValue(ConfigName.otpUssd);
      String ussdCode = configValue[countryCodeFromPhone(phoneNumber)] ?? '';
      return ussdCode;
    } catch (e) {
      return '';
    }
  }

  String? get dialCode {
    final phoneNumber = currentOutlet?.phoneNumber ?? '';
    if (phoneNumber.startsWith('+234')) {
      return '+234';
    } else if (phoneNumber.startsWith('+233')) {
      return '+233';
    } else if (phoneNumber.startsWith('+27')) {
      return '+27';
    } else if (phoneNumber.startsWith('+44')) {
      return '+44';
    } else {
      return null;
    }
  }

  String get maskedPhone {
    final phone = currentUser?.phoneNumber;

    if (phone == null) return '';

    final prefix = phone.substring(0, 4);
    final postfix = phone.substring(phone.length - 4);
    final mask = '******';
    return '$prefix$mask$postfix';
  }

  /// Returns current retail outlet currency code
  String get currencyCode {
    if (currentOutlet != null) {
      // Check if country is available and corresponds to a currency
      return countryToCurrency[currentOutlet!.country] ?? DEFAULT_CURRENCY;
    } else if (currentAnonymousUser != null &&
        currentAnonymousUser!.currency != null &&
        currentAnonymousUser!.currency!.iso != null) {
      // Check if anonymous user has a valid currency ISO code
      return currentAnonymousUser!.currency!.iso!;
    } else {
      // Fallback to default currency
      return DEFAULT_CURRENCY;
    }
  }

  /// Returns `true` for users from Nigeria
  bool get isNgUser => currentOutlet?.country?.toLowerCase() == 'ng';

  /// Returns `true` for users from South African
  bool get isZaUser => currentOutlet?.country?.toLowerCase() == 'za';

  /// Returns `true` for users from Ghana
  bool get isGhUser => currentOutlet?.country?.toLowerCase() == 'gh';

  /// Register cubit listeners if they implement
  /// [OnLogin], [OnBeforeLogout], [OnLogout], [OnUserChanged]. [OnAnonymousLogin]
  void registerListeners(List<Object?> listeners) {
    for (var element in listeners) {
      // generate a unique id for the element
      final id = element.toString().hashCode.toString();

      _listeners[id] = element;
    }
  }

  /// Sends the [OnLogin] event to all registered listeners.
  Future<void> _sendUserLoginEvent(User user) async {
    final values = _listeners.values
        .whereType<OnLogin>()
        .map((e) => (e).onLogin(user));

    await Future.wait(values);
  }

  /// Sends the [OnAnonymousLogin] event to all registered listeners.
  Future<void> _sendAnonymousUserLoginEvent(AnonymousUser user) async {
    final values = _listeners.values
        .whereType<OnAnonymousLogin>()
        .map((e) => (e).onAnonymousLogin(user));

    await Future.wait(values);
  }

  /// Sends the [OnBeforeLogout] event to all registered listeners.
  Future<void> _sendBeforeLogoutEvent() {
    final values = _listeners.values
        .whereType<OnBeforeLogout>()
        .map((e) => (e).onBeforeLogout());

    return Future.wait(values);
  }

  /// Sends the [OnLogout] event to all registered listeners.
  Future<void> _sendUserLogoutEvent() {
    final values = _listeners.values
        .whereType<OnLogout>()
        .map((e) => (e).onLogout());

    return Future.wait(values);
  }

  /// Sends the [OnUserChanged] event to all registered listeners.
  Future<void> _sendUserChangedEvent(User? currentUser, User updatedUser) {
    final values = _listeners.values
        .whereType<OnUserChanged>()
        .map((e) =>
            (e).onUserChanged(currentUser, updatedUser));

    return Future.wait(values);
  }

  void dispose() {
    _userSubject.close();
    _outletSubject.close();
    _authSubscription?.cancel();
    _outletSubscription?.cancel();
    _userUpdateController.close();
  }
}

Future<void> clearLocalStorageWithExclusions({
  List<String>? keysToPreserve,
  List<RegExp>? patternsToPreserve,
}) async {
  final sp = await SharedPreferences.getInstance();
  final preservedValues = <String, dynamic>{};

  // First, preserve keys specified exactly.
  if (keysToPreserve != null) {
    for (final key in keysToPreserve) {
      preservedValues[key] = sp.get(key);
    }
  }

  // Next, for any key in SharedPreferences that matches one of the patterns, preserve it.
  if (patternsToPreserve != null) {
    final allKeys = sp.getKeys();
    for (final key in allKeys) {
      for (final pattern in patternsToPreserve) {
        if (pattern.hasMatch(key)) {
          preservedValues[key] = sp.get(key);
          break; // Once matched, no need to check further patterns.
        }
      }
    }
  }

  // Clear all preferences.
  await sp.clear();

  // Restore preserved keys.
  for (final key in preservedValues.keys) {
    final value = preservedValues[key];
    if (value is String) {
      await sp.setString(key, value);
    } else if (value is int) {
      await sp.setInt(key, value);
    } else if (value is bool) {
      await sp.setBool(key, value);
    } else if (value is double) {
      await sp.setDouble(key, value);
    } else if (value is List<String>) {
      await sp.setStringList(key, value);
    }
  }
}
