import 'package:android_play_install_referrer/android_play_install_referrer.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/sign_up.dart';
import 'package:shop/app/core/services/first_install_service.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'signup_state.dart';

class SignUpCubit extends Cubit<SignUpState> {
  SignUpCubit(this._signUp) : super(SignUpInitial());
  final SignUp? _signUp;
  String? email;
  String? phone;

  /// Registers a new user
  Future<void> signUp(SignUpParams params) async {
    emit(SignUpLoading());

    final res = await _signUp!(params);

    res.when(
      success: (user) async {
        emit(SignUpCompleted(user));

        final data = {
          'user_login': user.userId,
        };
        final outlet = user.currentRetailOutlet!;

        if (outlet.refereeType == 'OUTLET') {
          data['customer referral code'] = outlet.refereeCode;
        } else if (outlet.refereeType == 'USER') {
          data['agent referral code'] = outlet.refereeCode;
        }

        Segment.track(
          eventName: SegmentEvents.accountCreated,
          properties: data,
        );

        final firstInstall = await FirstInstallService.extractValues();

        if (firstInstall != null) {
          Segment.track(eventName: SegmentEvents.appInstall, properties: {
            ...data,
            ...firstInstall,
          });
        }
        // Link has been track, it can be cleared now
        FirstInstallService.clearLink();

        _trackInstallationReferrer(data);

        final sp = await SharedPreferences.getInstance();
        sp.remove(Keys.tdAppPhone);
        sp.remove(Keys.tdAppEmail);
      },
      apiFailure: (error, _) {
        emit(
          SignUpFailed(
            errorMessage: ApiExceptions.getErrorMessage(error),
          ),
        );
      },
    );
  }

  void startLoading() {
    emit(SignUpLoading());
  }

  void setError(String message) {
    emit(SignUpFailed(errorMessage: message));
  }

  void setEmail(String email) {
    this.email = email;
  }

  void setPhone(String pone) {
    phone = phone;
  }

  void _trackInstallationReferrer(Map<String, dynamic> data) async {
    if (kIsWeb) return;

    try {
      final referrerDetails = await AndroidPlayInstallReferrer.installReferrer;
      final referrer = referrerDetails.installReferrer;
      if (referrer == null) return;

      final plus = <String, dynamic>{};

      final items = referrer.split('&');
      for (var item in items) {
        final source = item.split('=');
        plus[source[0]] = source[1];
      }

      Segment.track(
        eventName: SegmentEvents.appInstall,
        properties: {
          ...data,
          ...plus,
        },
      );
    } catch (e) {}
  }
}
