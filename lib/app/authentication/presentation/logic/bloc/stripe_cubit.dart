import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_verification.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class StripeCubit extends Cubit<StripeState> {
  StripeCubit(this._initiate) : super(StripeInitial());

  final InitiateStripeVerification _initiate;

  /// initiates stripe verification
  Future initiateVerification(InitiateStripeRequest request) async {
    emit(StripeVerificationLoading());

    final res = await _initiate(request);
    res.maybeWhen(orElse: () {
      emit(StripeCompanyVerificationFailed('Something went wrong'));
    }, success: (data) async {
      if (request.director != null) {
        emit(StripeCompanyVerificationInitiated(data));
      } else {
        emit(StripeVerificationInitiated(data));
      }
    }, apiFailure: (error, _) {
      final msg = ApiExceptions.getErrorMessage(error);
      emit(StripeCompanyVerificationFailed(msg));
    });
  }

  void setStateToInitial() {
    emit(StripeInitial());
  }
}
