import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/domain/use_cases/job_status.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_status_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class SmileJobStatusCubit extends Cubit<SmileJobStatusState> {
  SmileJobStatusCubit(this._jobStatus) : super(SmileJobStatusInitial());

  final JobStatus? _jobStatus;

  Future<void> checkJobStatus() async {
    emit(SmileJobStatusFetching());
    final res = await _jobStatus!(NoParams());
    res.when(
      success: (currentJob) {
        emit(SmileJobStatusFetchcompleted(currentJob));
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(SmileJobStatusFetchFailed(msg));
      },
    );
  }
}
