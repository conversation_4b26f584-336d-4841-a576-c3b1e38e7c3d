import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/domain/use_cases/company_search.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/company_search_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CompanySearchCubit extends Cubit<CompanySearchState> {
  CompanySearchCubit(this._search) : super(CompanySearchStateInitial());

  final CompanyRegSearch _search;

//Fetch Strupe Verification status
  Future companySearch(String query) async {
    emit(CompanySearchLoading());

    final res = await _search(query);
    res.when(
      success: (data) async {
        emit(CompanySearchcompleted(data));
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(CompanySearchFailed(msg));
      },
    );
  }

  void setInitialState() {
    emit(CompanySearchStateInitial());
  }
}
