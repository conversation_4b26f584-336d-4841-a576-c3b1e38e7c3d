import 'dart:collection';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/current_job.dart';
import 'package:shop/app/authentication/data/models/initiate_job.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_job.dart';
import 'package:shop/app/authentication/domain/use_cases/job_status.dart';
import 'package:shop/app/authentication/domain/use_cases/validate_job.dart';
//import 'package:smile_flutter_3/smile_flutter_3.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

part 'smile_job_state.dart';

class SmileJobCubit extends Cubit<SmileJobState> {
  SmileJobCubit(this._initiateJob, this._validateJob, this._jobStatus)
      : super(SmileJobInitial());

  final InitiateJob? _initiateJob;
  final ValidateJob? _validateJob;
  final JobStatus? _jobStatus;

  /// initiates smile job
  Future initiateJob(InitiateJobRequest request) async {
    emit(SmileJobLoading());

    final res = await _initiateJob!(request);
    res.when(
      success: (data) async {
        if (data.status?.toLowerCase() == 'failed') {
          emit(SmileJobThreeTrialsWarning(data.data?.message ?? ''));
        } else {
          if (data.data?.jobId == null) {
            return; // TODO: ensure jobId is not null
          }
          emit(SmileJobProcessInitiated(data.data!.token, data.data!.jobId,
              data.data!.jobType!, data.data!.enhancedKyc!));
          /*processJob(
              data.data!.jobId.toString(), request.idNumber!, request.jobtype!);*/
        }
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(SmileJobFailed(msg));
      },
    );
  }

  /*processJob(String jobId, String idNumber, String idType) async {
    var userIdInfo = HashMap<String, String>();
    var partnerParams = HashMap<String, String>();
    partnerParams['user_id'] =
        UserCubit.instance!.currentUser!.retailOutlets!.first.id!;
    partnerParams['job_id'] = jobId;
    userIdInfo['id_type'] = idType == "bvn" ? "BVN" : "NIN_V2";
    userIdInfo['country'] = 'NG';
    userIdInfo['id_number'] = idNumber;
    userIdInfo["last_name"] = UserCubit.instance!.currentUser!.lastName!;
    userIdInfo["first_name"] = UserCubit.instance!.currentUser!.firstName!;

    try {
      final result =
          await SmileFlutter.captureSelfie('CAPTURE_TAG', null, false);

      print(result);
      if (result!["SID_RESULT_CODE"] == -1) {
        emit(SmileJobCompleted(partnerParams, userIdInfo));
        _submitJob(partnerParams, userIdInfo, jobId);
      } else {
        emit(SmileJobFailed('Verification process exited'));
      }
    } catch (e) {
      emit(SmileJobFailed('Something went wrong'));
    }
  }*/

  Future<void> validateSmileJob(String jobId) async {
    final res = await _validateJob!(jobId);
    res.when(
      success: (_) {},
      apiFailure: (error, _) {
        _validateJob(jobId);
      },
    );
  }
}
