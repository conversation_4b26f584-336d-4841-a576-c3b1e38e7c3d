import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';

abstract class VerifyPhoneState extends Equatable {
  final List? items;

  const VerifyPhoneState([this.items]);

  @override
  List<Object?> get props => (items ?? []);
}

class VerifyPhoneInitial extends VerifyPhoneState {
  const VerifyPhoneInitial();

  @override
  String toString() => 'InitState';
}

class VerifyPhoneLoading extends VerifyPhoneState {
  final bool loading;

  VerifyPhoneLoading(this.loading) : super([loading]);

  @override
  String toString() => 'LoadingState $loading';
}

class SentOTPState extends VerifyPhoneState {
  final SendOTParams params;

  SentOTPState(this.params) : super([params.phoneNumber]);

  @override
  String toString() => 'Sent OTP';
}

class ByPassedOTPState extends VerifyPhoneState {
  final NinParams params;

  ByPassedOTPState(this.params)
      : super([params.phoneNumber, params.nin, params.isValidated]);

  @override
  String toString() => 'ByPass OTP';
}

class FailedToSendOTPState extends VerifyPhoneState {
  final String? errorMessage;

  FailedToSendOTPState({this.errorMessage}) : super([errorMessage]);

  @override
  String toString() => 'ErrorSendingOtp';
}
