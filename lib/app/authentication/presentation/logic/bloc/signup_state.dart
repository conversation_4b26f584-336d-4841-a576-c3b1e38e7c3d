part of 'signup_cubit.dart';

abstract class SignUpState extends Equatable {
  const SignUpState();
}

class SignUpInitial extends SignUpState {
  @override
  List<Object> get props => [];

  @override
  String toString() => 'SignUpInitial';
}

class SignUpLoading extends SignUpState {
  @override
  List<Object> get props => [];

  @override
  String toString() => 'SignUpLoading';
}

class SignUpCompleted extends SignUpState {
  final User user;

  const SignUpCompleted(this.user);

  @override
  List<Object?> get props => [user];

  @override
  String toString() => 'SignUpCompleted';
}

class SignUpFailed extends SignUpState {
  final String? errorMessage;

  const SignUpFailed({this.errorMessage});

  @override
  String toString() => 'SignUpFailed';

  @override
  List<Object?> get props => [errorMessage];
}
