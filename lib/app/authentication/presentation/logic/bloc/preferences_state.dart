part of 'preferences_cubit.dart';

abstract class PreferencesState extends Equatable {
  const PreferencesState();

  @override
  List<Object?> get props => [];
}

class PreferencesInitial extends PreferencesState {}

class PreferencesLoading extends PreferencesState {}

class PreferencesLoaded extends PreferencesState {
  final List<Preference> userPreferences;
  final List<Preference> collectionPreferences;
  final bool? update;

  const PreferencesLoaded({
    required this.userPreferences,
    required this.collectionPreferences,
    this.update = true,
  });

  @override
  List<Object?> get props => [userPreferences, collectionPreferences, update];
}

class PreferencesError extends PreferencesState {
  final String message;

  const PreferencesError(this.message);

  @override
  List<Object> get props => [message];
}
