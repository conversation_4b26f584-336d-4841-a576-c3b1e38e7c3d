import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';
import 'package:shop/app/authentication/domain/use_cases/company_register.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/company_reg_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class CompanyRegistrationCubit extends Cubit<CompanyRegState> {
  CompanyRegistrationCubit(this._companyRegister) : super(CompanyRegInitial());

  final CompanyRegister _companyRegister;

  Future registerCompany(String companyId) async {
    emit(CompanyRegLoading());

    final res = await _companyRegister(companyId);
    res.when(
      success: (data) async {
        emit(CompanyRegCompleted(data));
      },
      apiFailure: (error, _) {
        final msg = ApiExceptions.getErrorMessage(error);
        emit(CompanyRegFailed(msg));
      },
    );
  }

  void selectCompany(Item? item) {
    emit(CompanySelected(item));
  }

  void setInitialState() {
    emit(CompanyRegInitial());
  }

  void setState(Item? item) {
    emit(CompanySelected(item));
  }
}
