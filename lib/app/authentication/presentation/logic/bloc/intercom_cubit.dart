import 'dart:async';

import 'package:flutter_segment/flutter_segment.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
import 'package:shop/app/homepage/presentation/logic/utils/method.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/enums/loan_status.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/utils/methods.dart';

class IntercomCubit implements OnLogin, OnBeforeLogout {
  final AppConfig config;

  IntercomCubit({required this.config});

  @override
  Future<void> onLogin(User user) async {
    await _registerSegmentUser(user).then((_) => _setupIntercom(user));
    // _setupIntercom(user);
  }

  Future<void> _setupIntercom(User user) async {
    // await Intercom.instance.initialize(
    //   config.intercomAppId!,
    //   androidApiKey: config.intercomAndroidApiKey,
    //   iosApiKey: config.intercomIosApiKey,
    // );
    await _registerIntercomUser(user);
  }

  /// Identify [user] for analytics purposes
  Future<void> _registerSegmentUser(User user) async {
    RetailOutlet? outlet = user.currentRetailOutlet;
    String fullName = toTitleCase('${user.firstName} ${user.lastName}');
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final userDeviceInfo = await deviceInfo();
    await Segment.identify(
      userId: outlet?.id,
      traits: {
        'Name': outlet?.outletBusinessName,
        'UserId': user.userId,
        r"$name": outlet?.outletBusinessName,
        r"$first_name": user.firstName,
        r"$last_name": user.lastName,
        r"$avatar": outlet?.image?.originalUrl,
        'ContactName': fullName,
        'State': outlet?.state,
        'LGA': outlet?.lga,
        'OutletType': outlet?.outletType,
        'ExtChannel': getExtChannel(),
        'Phone': user.phoneNumber,
        r'$phone': user.phoneNumber,
        'CustomerGroup': outlet?.customerGroup,
        'Country': outlet?.country,
        r'$Country': outlet?.country,
        'FirstTransactionAt': outlet?.firstTransactionAt?.toString(),
        'LastTransactionAt': outlet?.lastTransactionAt?.toString(),
        'LoanStatus': LoanStatus.stringFromLoanStatus(outlet?.loanStatus),
        'IsVerified': outlet?.isVerified,
        'AppVersion': packageInfo.version,
        'BuildNumber': packageInfo.buildNumber,
        'Company': user.company?.name,
        'CompanyId': outlet?.id,
        'Email': user.email ?? user.phoneEmail,
        r'$distinct_id': user.userId,
        r'$email': user.email ?? user.phoneEmail,
        "RetailOutletId": outlet?.id,
        "StreetName": outlet?.streetName,
        "PlusCode": outlet?.coordinates?.plusCode,
        "PlusCode6Hex": outlet?.coordinates?.plusCode6Hex,
        'Date': DateTime.now().toString(),
        'gender': outlet?.gender,
        r'$airship_named_user': outlet?.id,
        'AssigneeName': outlet?.assignee.assigneeName,
        'SalesChannel': outlet?.salesChannel,
        'ShopLat': outlet?.coordinates?.latitude,
        'ShopLng': outlet?.coordinates?.longitude,
        'DeviceID': userDeviceInfo['id'],
      },
    );
  }

  Future<void> _registerIntercomUser(User user) async {
    await Intercom.instance.loginIdentifiedUser(userId: user.userId);
    String username = capitalize(user.displayName);
    await Intercom.instance.updateUser(
      name: username,
      phone: user.phoneNumber,
      company: user.company?.name,
      companyId: user.company?.id,
      email: user.email ?? user.phoneEmail,
      customAttributes: {
        "retailOutletId": user.currentRetailOutlet?.id,
        "phoneNumber": user.phoneNumber,
      },
    );
    final token = await getFirebaseMessagingToken();
    if (token != null) {
      Intercom.instance.sendTokenToIntercom(token);
    }
  }

  @override
  Future<void> onBeforeLogout() async {
    Intercom.instance.logout();
  }
}
