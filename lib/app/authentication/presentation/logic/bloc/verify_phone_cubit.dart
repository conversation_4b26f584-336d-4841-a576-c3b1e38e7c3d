import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/data/models/user_status.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/check_phone.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/verify_phone_state.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class VerifyPhoneCubit extends Cubit<VerifyPhoneState> {
  VerifyPhoneCubit(
    this._sendOTP,
    this._checkPhone,
  ) : super(VerifyPhoneInitial());
  final SendOTP? _sendOTP;
  final CheckPhone? _checkPhone;

  /// Send sms with OTP to [params.phoneNumber].
  Future<void> sendOTP(SendOTParams params) async {
    if (!await locator<NetworkConnection>().isDeviceConnected) {
      emit(FailedToSendOTPState(errorMessage: 'No internet connection'));
      return;
    }

    emit(VerifyPhoneLoading(true));
    final res = await _sendOTP!(params);
    res.when(
      success: (_) {
        emit(SentOTPState(params));
      },
      apiFailure: (error, _) {
        emit(
          FailedToSendOTPState(
            errorMessage: ApiExceptions.getErrorMessage(error),
          ),
        );
      },
    );
  }

  void byPassOTP(NinParams params) {
    emit(ByPassedOTPState(params));
  }

  /// Attempt to check if [params.phone] has a valid account.
  Future<UserStatus?> checkPhone(CheckPhoneParams params) async {
    emit(VerifyPhoneLoading(true));

    final res = await _checkPhone!(params);

    return res.maybeWhen(
      success: (data) {
        emit(VerifyPhoneInitial());
        return data;
      },
      apiFailure: (error, _) {
        emit(
          FailedToSendOTPState(
            errorMessage: ApiExceptions.getErrorMessage(error),
          ),
        );
        return null;
      },
      orElse: () {
        emit(FailedToSendOTPState(errorMessage: 'Something went wrong'));
        return null;
      },
    );
  }

  void emitInitial()=>  emit(VerifyPhoneInitial());

  void emitError(String message) {
    emit(FailedToSendOTPState(errorMessage: message));
  }
}
