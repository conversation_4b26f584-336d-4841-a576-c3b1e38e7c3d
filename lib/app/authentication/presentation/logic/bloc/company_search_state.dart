import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';

abstract class CompanySearchState extends Equatable {
  const CompanySearchState();

  @override
  List<Object?> get props => [];
}

class CompanySearchStateInitial extends CompanySearchState {}

class CompanySearchLoading extends CompanySearchState {}

class CompanySearchFailed extends CompanySearchState {
  final String message;

  const CompanySearchFailed(this.message);

  @override
  List<Object> get props => [message];
}

class CompanySearchcompleted extends CompanySearchState {
  final CompanySearchData data;
  const CompanySearchcompleted(this.data);

  @override
  List<Object?> get props => [];
}
