part of 'smile_job_cubit.dart';

abstract class Smile<PERSON>obState extends Equatable {
  const SmileJobState();

  @override
  List<Object?> get props => [];
}

class SmileJobInitial extends SmileJobState {}

class SmileJobProcessInitiated extends SmileJobState {
  final String? token;
  final String jobId;
  final String jobType;
  final bool enhancedKyc;
  const SmileJobProcessInitiated(
      this.token, this.jobId, this.jobType, this.enhancedKyc);

  @override
  List<Object?> get props => [token, enhancedKyc, jobId];
}

class SmileJobLoading extends SmileJobState {}

class SmileJobThreeTrialsWarning extends SmileJobState {
  final String message;

  const SmileJobThreeTrialsWarning(this.message);

  @override
  List<Object> get props => [message];
}

class SmileJobValidationLoading extends SmileJobState {}

class SmileJobStatusRefresh extends SmileJobState {}

class SmileNINReviewStarted extends SmileJobState {}

class SmileJobCompleted extends SmileJobState {
  final HashMap<String, String> partnerParams;
  final HashMap<String, String> userIdInfo;

  const SmileJobCompleted(this.partnerParams, this.userIdInfo);

  @override
  List<Object?> get props => [partnerParams, userIdInfo];
}

class SmileJobStatusLoading extends SmileJobState {}

class SmileJobStatusCompleted extends SmileJobState {
  final CurrentJob currentJob;

  const SmileJobStatusCompleted(this.currentJob);

  @override
  List<Object?> get props => [currentJob];
}

class SmileJobValidationCompleted extends SmileJobState {}

class SmileCheckJobStatusFailed extends SmileJobState {
  final String message;

  const SmileCheckJobStatusFailed(this.message);

  @override
  List<Object> get props => [message];
}

class SmileJobFailed extends SmileJobState {
  final String message;

  const SmileJobFailed(this.message);

  @override
  List<Object> get props => [message];
}

class SmileJobValidationError extends SmileJobState {
  final String message;

  const SmileJobValidationError(this.message);

  @override
  List<Object> get props => [message];
}
