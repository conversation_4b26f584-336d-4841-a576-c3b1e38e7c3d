import 'package:equatable/equatable.dart';
import 'package:shop/app/authentication/data/models/company_search.dart';

abstract class CompanyRegState extends Equatable {
  const CompanyRegState();

  @override
  List<Object?> get props => [];
}

class CompanyRegInitial extends CompanyRegState {}

class CompanyRegLoading extends CompanyRegState {}

class CompanySelected extends CompanyRegState {
  final Item? item;
  const CompanySelected(this.item);
  @override
  List<Object?> get props => [item];
}

class CompanyRegCompleted extends CompanyRegState {
  final String data;
  const CompanyRegCompleted(this.data);

  @override
  List<Object?> get props => [];
}

class CompanyRegFailed extends CompanyRegState {
  final String message;

  const CompanyRegFailed(this.message);

  @override
  List<Object> get props => [message];
}
