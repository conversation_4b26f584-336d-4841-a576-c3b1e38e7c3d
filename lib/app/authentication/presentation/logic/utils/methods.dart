import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fpjs_pro_plugin/fpjs_pro_plugin.dart';
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/authentication/data/models/login_response.dart';
import 'package:shop/app/authentication/domain/params/post_params.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_pin.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/authentication/presentation/ui/widgets/visitor_widget.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/models/date_parser.dart';
import 'package:td_flutter_core/config/DI/di.dart';

class FirebaseDateParser extends DateParser {
  @override
  DateTime? parseDate(date) {
    if (date is DateTime) {
      return date;
    } else if (date is num) {
      try {
        return DateTime.fromMillisecondsSinceEpoch(date.toInt());
      } catch (_) {
        return DateTime.fromMicrosecondsSinceEpoch(date.toInt());
      }
    } else if (date is String) {
      try {
        return DateTime.parse(date);
      } catch (error) {
        return null;
      }
    } else if (date is Timestamp) {
      return date.toDate();
    }

    try {
      return date.toDate();
    } catch (_) {
      return null;
    }
  }
}

Future<void> goHomeMobile(
  BuildContext context, [
  bool intro = false,
  bool resetPassword = false,
]) async {
  if (intro) {
    context.goNamed(HomePath);
  } else if (resetPassword) {
    context.goNamed(HomePath);
  } else {
    // if (kDebugMode) {
    context.goNamed(HomePath);
    return;
    // }

    // final info = await deviceInfo();
    // try {
    //   final userId = UserCubit.instance?.currentUser?.userId;
    //   final res = await FirebaseFirestore.instance.doc('devices/$userId').get();

    //   if (res.exists && res.get('id') == info['id']) {
    //     // User device has not changed.
    //     // Take the user home and don't request for OTP
    //     context.goNamed(HomePath);
    //     return;
    //   }
    // } catch (e) {}

    // // Additional OTP verification on login
    // SharedPreferences.getInstance().then(
    //   (sp) => sp.setBool(Keys.loginOtp, true),
    // );
    // context.goNamed(VerifyOTPPath);
  }
}

Future<void> goHomeWeb(
  BuildContext context, [
  bool intro = false,
  bool resetPassword = false,
]) async {
  if (intro) {
    context.goNamed(HomePath);
  } else if (resetPassword) {
    context.goNamed(LoginPath);
  } else {
    context.goNamed(HomePath);
    return;
    // final info = await deviceInfo();
    // try {
    //   final userId = UserCubit.instance?.currentUser?.userId;
    //   final res = await FirebaseFirestore.instance.doc('devices/$userId').get();

    //   if (res.exists && res.get('id') == info['id']) {
    //     // User device has not changed.
    //     // Take the user home and don't request for OTP
    //     context.goNamed(HomePath);
    //     return;
    //   }
    // } catch (e) {}

    // // Additional OTP verification on login
    // SharedPreferences.getInstance().then(
    //   (sp) => sp.setBool(Keys.loginOtp, true),
    // );
    // context.goNamed(VerifyOTPPath);
  }
}

Future<String> getVisitorId([int retry = 3]) async {
  final isEnabled = config.fingerprintEnabled != null
      ? bool.tryParse(config.fingerprintEnabled!, caseSensitive: false) ?? false
      : false;

  if (isEnabled) {
    try {
      final visitorId = await FpjsProPlugin.getVisitorId();
      return visitorId ?? '';
    } on PlatformException catch (_) {
      if (retry > 0) {
        await Future.delayed(Duration.zero);
        return getVisitorId(retry - 1);
      }
      return '';
    }
  }

  return Future.value('');
}

Future<void> handleLogin(BuildContext context, bool isDesktop,
    String phoneNumber, String pin) async {
  // to prevent splash screen listener from immediately routing to homePage on PWA when userStream has data
  SharedPreferences.getInstance().then(
    (sp) {
      sp.setBool(Keys.skipLoginWebListener, true);
      // Remove saved businessName during pin-reset
      sp.remove(Keys.outletBusinessName);
    },
  );

  TdLoader.show(context);

  final visitorId = await getVisitorId();

  final res = await locator.get<LoginWithPin>().call(
        LoginPinParams(
          phone: phoneNumber,
          pin: pin,
          visitorId: visitorId,
          useDevice: true,
        ),
      );

  res.when(
    success: (res) async {
      if (res.type == LoginResponseType.user) {
        UserCubit.instance?.updatingUser(res.user!);

        final sp = await SharedPreferences.getInstance();
        sp.remove(Keys.resetPin);
        sp.remove(Keys.createPin);

        if (isDesktop) {
          await goHomeWeb(context);
        } else {
          await goHomeMobile(context);
        }
      } else {
        VisitorWidget.show(context, res.visitor!, phoneNumber, pin, isDesktop);
      }

      TdLoader.hide();
    },
    apiFailure: (e, _) {
      TdLoader.hide();
      Toast.apiError(e, context);
    },
  );
}

Future<void> setupFcm(String userId) async {
  try {
    final fmToken = await getFirebaseMessagingToken();
    if (fmToken != null) {
      final data = {'token': fmToken, 'platform': Platform.operatingSystem};
      await FirebaseFirestore.instance
          .collection("users")
          .doc(userId)
          .collection('registration_tokens')
          .doc(fmToken)
          .set(data, SetOptions(merge: true));

      // not supported on web yet
      Intercom.instance.sendTokenToIntercom(fmToken);
    }

    return;
  } catch (e) {
    ErrorHandler.report(e, StackTrace.current,
        hint: {'info': 'Failed to setup FcmToken for push notification'});
    rethrow;
  }
}

Future<void> setupIntercomWithEmail(BuildContext context, String email) async {
  await Intercom.instance.initialize(
    config.intercomAppId!,
    androidApiKey: config.intercomAndroidApiKey,
    iosApiKey: config.intercomIosApiKey,
  );
  await Intercom.instance.loginIdentifiedUser(email: email);
  return;
}
