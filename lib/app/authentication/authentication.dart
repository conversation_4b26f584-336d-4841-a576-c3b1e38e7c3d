import 'package:shop/app/authentication/data/data_source/authentication_remote_data_source.dart';
import 'package:shop/app/authentication/data/data_source/impl/authentication_remote_data_source_impl.dart';
import 'package:shop/app/authentication/data/repos_impl/authentication_repo_impl.dart';
import 'package:shop/app/authentication/domain/use_cases/add_referee_code.dart';
import 'package:shop/app/authentication/domain/use_cases/check_phone.dart';
import 'package:shop/app/authentication/domain/use_cases/company_register.dart';
import 'package:shop/app/authentication/domain/use_cases/company_search.dart';
import 'package:shop/app/authentication/domain/use_cases/create_pin.dart';
import 'package:shop/app/authentication/domain/use_cases/fetch_outlet.dart';
import 'package:shop/app/authentication/domain/use_cases/get_referee.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_job.dart';
import 'package:shop/app/authentication/domain/use_cases/initiate_verification.dart';
import 'package:shop/app/authentication/domain/use_cases/job_status.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_pin.dart';
import 'package:shop/app/authentication/domain/use_cases/login_with_token.dart';
import 'package:shop/app/authentication/domain/use_cases/reset_pin.dart';
import 'package:shop/app/authentication/domain/use_cases/send_otp.dart';
import 'package:shop/app/authentication/domain/use_cases/sign_up.dart';
import 'package:shop/app/authentication/domain/use_cases/update_email.dart';
import 'package:shop/app/authentication/domain/use_cases/validate_job.dart';
import 'package:shop/app/authentication/domain/use_cases/verificationStatus.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_nin.dart';
import 'package:shop/app/authentication/domain/use_cases/verify_otp.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'domain/repos/authentication_repo.dart';
import 'domain/use_cases/add_phone.dart';
import 'domain/use_cases/create_account.dart';
import 'domain/use_cases/get_applied_interest.dart';
import 'domain/use_cases/get_preferences.dart';
import 'domain/use_cases/save_preferences.dart';
import 'domain/use_cases/setup_wallet.dart';
import 'domain/use_cases/stream_outlet.dart';

void registerTdAuthenticationDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<AuthenticationRemoteDataSource>(
    () => AuthenticationRemoteDataSourceImpl(
      locator(),
      config.firebaseServiceUrl ?? '',
    ),
  );

  // Repositories
  locator.registerLazySingleton<AuthenticationRepo>(
    () => AuthenticationRepoImpl(locator(), locator()),
  );

  // Use cases
  locator.registerLazySingleton(() => LoginWithToken(locator()));
  locator.registerLazySingleton(() => LoginWithPin(locator()));
  locator.registerLazySingleton(() => SignUp(locator()));
  locator.registerLazySingleton(() => CheckPhone(locator()));
  locator.registerLazySingleton(() => CreatePin(locator()));
  locator.registerLazySingleton(() => ResetPin(locator()));
  locator.registerLazySingleton(() => FetchOutlet(locator()));
  locator.registerLazySingleton(() => StreamOutlet(locator()));
  // locator.registerLazySingleton(() => VerifyPin(locator()));
  locator.registerLazySingleton(() => SetupWallet(locator()));
  locator.registerLazySingleton(() => GetAppliedInterest(locator()));
  locator.registerLazySingleton(() => VerifyNin(locator()));
  locator.registerLazySingleton(() => CreateAccount(locator()));
  locator.registerLazySingleton(() => GetPreferences(locator()));
  locator.registerLazySingleton(() => SavePreferences(locator()));
  locator.registerLazySingleton(() => InitiateJob(locator()));
  locator.registerLazySingleton(() => CompanyRegSearch(locator()));
  locator.registerLazySingleton(() => CompanyRegister(locator()));
  locator.registerLazySingleton(() => InitiateStripeVerification(locator()));
  locator.registerLazySingleton(() => VerificationStatus(locator()));
  locator.registerLazySingleton(() => ValidateJob(locator()));
  locator.registerLazySingleton(() => JobStatus(locator()));
  locator.registerLazySingleton(() => UpdateEmail(locator()));
  locator.registerLazySingleton(() => AddRefereeCode(locator()));
  locator.registerLazySingleton(() => GetReferee(locator()));
  locator.registerLazySingleton(() => SendOTP(locator()));
  locator.registerLazySingleton(() => VerifyOTP(locator()));
  locator.registerLazySingleton(() => AddPhone(locator()));
}
