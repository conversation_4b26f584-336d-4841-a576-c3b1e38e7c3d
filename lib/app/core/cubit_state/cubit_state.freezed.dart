// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides

part of 'cubit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more informations: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
class _$CubitStateTearOff {
  const _$CubitStateTearOff();

  InitState<T> init<T>() {
    return InitState<T>();
  }

  LoadingState<T> loading<T>({required bool loading}) {
    return LoadingState<T>(
      loading: loading,
    );
  }

  CompletedState<T> completed<T>({required T model}) {
    return CompletedState<T>(
      model: model,
    );
  }

  ErrorState<T> error<T>({required String errorMessage}) {
    return ErrorState<T>(
      errorMessage: errorMessage,
    );
  }
}

/// @nodoc
const $CubitState = _$CubitStateTearOff();

/// @nodoc
mixin _$CubitState<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool loading) loading,
    required TResult Function(T model) completed,
    required TResult Function(String errorMessage) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool loading)? loading,
    TResult Function(T model)? completed,
    TResult Function(String errorMessage)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitState<T> value) init,
    required TResult Function(LoadingState<T> value) loading,
    required TResult Function(CompletedState<T> value) completed,
    required TResult Function(ErrorState<T> value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitState<T> value)? init,
    TResult Function(LoadingState<T> value)? loading,
    TResult Function(CompletedState<T> value)? completed,
    TResult Function(ErrorState<T> value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CubitStateCopyWith<T, $Res> {
  factory $CubitStateCopyWith(
          CubitState<T> value, $Res Function(CubitState<T>) then) =
      _$CubitStateCopyWithImpl<T, $Res>;
}

/// @nodoc
class _$CubitStateCopyWithImpl<T, $Res>
    implements $CubitStateCopyWith<T, $Res> {
  _$CubitStateCopyWithImpl(this._value, this._then);

  final CubitState<T> _value;
  // ignore: unused_field
  final $Res Function(CubitState<T>) _then;
}

/// @nodoc
abstract class $InitStateCopyWith<T, $Res> {
  factory $InitStateCopyWith(
          InitState<T> value, $Res Function(InitState<T>) then) =
      _$InitStateCopyWithImpl<T, $Res>;
}

/// @nodoc
class _$InitStateCopyWithImpl<T, $Res> extends _$CubitStateCopyWithImpl<T, $Res>
    implements $InitStateCopyWith<T, $Res> {
  _$InitStateCopyWithImpl(
      InitState<T> value, $Res Function(InitState<T>) then)
      : super(value, (v) => then(v as InitState<T>));

  @override
  InitState<T> get _value => super._value as InitState<T>;
}

/// @nodoc
class _$InitState<T> with DiagnosticableTreeMixin implements InitState<T> {
  const _$InitState();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CubitState<$T>.init()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'CubitState<$T>.init'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) || (other is InitState<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool loading) loading,
    required TResult Function(T model) completed,
    required TResult Function(String errorMessage) error,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool loading)? loading,
    TResult Function(T model)? completed,
    TResult Function(String errorMessage)? error,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitState<T> value) init,
    required TResult Function(LoadingState<T> value) loading,
    required TResult Function(CompletedState<T> value) completed,
    required TResult Function(ErrorState<T> value) error,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitState<T> value)? init,
    TResult Function(LoadingState<T> value)? loading,
    TResult Function(CompletedState<T> value)? completed,
    TResult Function(ErrorState<T> value)? error,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class InitState<T> implements CubitState<T> {
  const factory InitState() = _$InitState<T>;
}

/// @nodoc
abstract class $LoadingStateCopyWith<T, $Res> {
  factory $LoadingStateCopyWith(
          LoadingState<T> value, $Res Function(LoadingState<T>) then) =
      _$LoadingStateCopyWithImpl<T, $Res>;
  $Res call({bool loading});
}

/// @nodoc
class _$LoadingStateCopyWithImpl<T, $Res>
    extends _$CubitStateCopyWithImpl<T, $Res>
    implements $LoadingStateCopyWith<T, $Res> {
  _$LoadingStateCopyWithImpl(
      LoadingState<T> value, $Res Function(LoadingState<T>) then)
      : super(value, (v) => then(v as LoadingState<T>));

  @override
  LoadingState<T> get _value => super._value as LoadingState<T>;

  @override
  $Res call({
    Object? loading = freezed,
  }) {
    return _then(LoadingState<T>(
      loading: loading == freezed
          ? _value.loading
          : loading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
class _$LoadingState<T>
    with DiagnosticableTreeMixin
    implements LoadingState<T> {
  const _$LoadingState({required this.loading});

  @override
  final bool loading;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CubitState<$T>.loading(loading: $loading)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CubitState<$T>.loading'))
      ..add(DiagnosticsProperty('loading', loading));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is LoadingState<T> &&
            (identical(other.loading, loading) ||
                const DeepCollectionEquality().equals(other.loading, loading)));
  }

  @override
  int get hashCode =>
      runtimeType.hashCode ^ const DeepCollectionEquality().hash(loading);

  @JsonKey(ignore: true)
  @override
  $LoadingStateCopyWith<T, LoadingState<T>> get copyWith =>
      _$LoadingStateCopyWithImpl<T, LoadingState<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool loading) loading,
    required TResult Function(T model) completed,
    required TResult Function(String errorMessage) error,
  }) {
    return loading(this.loading);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool loading)? loading,
    TResult Function(T model)? completed,
    TResult Function(String errorMessage)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this.loading);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitState<T> value) init,
    required TResult Function(LoadingState<T> value) loading,
    required TResult Function(CompletedState<T> value) completed,
    required TResult Function(ErrorState<T> value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitState<T> value)? init,
    TResult Function(LoadingState<T> value)? loading,
    TResult Function(CompletedState<T> value)? completed,
    TResult Function(ErrorState<T> value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class LoadingState<T> implements CubitState<T> {
  const factory LoadingState({required bool loading}) = _$LoadingState<T>;

  bool get loading => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LoadingStateCopyWith<T, LoadingState<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CompletedStateCopyWith<T, $Res> {
  factory $CompletedStateCopyWith(
          CompletedState<T> value, $Res Function(CompletedState<T>) then) =
      _$CompletedStateCopyWithImpl<T, $Res>;
  $Res call({T model});
}

/// @nodoc
class _$CompletedStateCopyWithImpl<T, $Res>
    extends _$CubitStateCopyWithImpl<T, $Res>
    implements $CompletedStateCopyWith<T, $Res> {
  _$CompletedStateCopyWithImpl(
      CompletedState<T> value, $Res Function(CompletedState<T>) then)
      : super(value, (v) => then(v as CompletedState<T>));

  @override
  CompletedState<T> get _value => super._value as CompletedState<T>;

  @override
  $Res call({
    Object? model = freezed,
  }) {
    return _then(CompletedState<T>(
      model: model == freezed
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc
class _$CompletedState<T>
    with DiagnosticableTreeMixin
    implements CompletedState<T> {
  const _$CompletedState({required this.model});

  @override
  final T model;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CubitState<$T>.completed(model: $model)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CubitState<$T>.completed'))
      ..add(DiagnosticsProperty('model', model));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is CompletedState<T> &&
            (identical(other.model, model) ||
                const DeepCollectionEquality().equals(other.model, model)));
  }

  @override
  int get hashCode =>
      runtimeType.hashCode ^ const DeepCollectionEquality().hash(model);

  @JsonKey(ignore: true)
  @override
  $CompletedStateCopyWith<T, CompletedState<T>> get copyWith =>
      _$CompletedStateCopyWithImpl<T, CompletedState<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool loading) loading,
    required TResult Function(T model) completed,
    required TResult Function(String errorMessage) error,
  }) {
    return completed(model);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool loading)? loading,
    TResult Function(T model)? completed,
    TResult Function(String errorMessage)? error,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(model);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitState<T> value) init,
    required TResult Function(LoadingState<T> value) loading,
    required TResult Function(CompletedState<T> value) completed,
    required TResult Function(ErrorState<T> value) error,
  }) {
    return completed(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitState<T> value)? init,
    TResult Function(LoadingState<T> value)? loading,
    TResult Function(CompletedState<T> value)? completed,
    TResult Function(ErrorState<T> value)? error,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(this);
    }
    return orElse();
  }
}

abstract class CompletedState<T> implements CubitState<T> {
  const factory CompletedState({required T model}) = _$CompletedState<T>;

  T get model => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CompletedStateCopyWith<T, CompletedState<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorStateCopyWith<T, $Res> {
  factory $ErrorStateCopyWith(
          ErrorState<T> value, $Res Function(ErrorState<T>) then) =
      _$ErrorStateCopyWithImpl<T, $Res>;
  $Res call({String errorMessage});
}

/// @nodoc
class _$ErrorStateCopyWithImpl<T, $Res>
    extends _$CubitStateCopyWithImpl<T, $Res>
    implements $ErrorStateCopyWith<T, $Res> {
  _$ErrorStateCopyWithImpl(
      ErrorState<T> value, $Res Function(ErrorState<T>) then)
      : super(value, (v) => then(v as ErrorState<T>));

  @override
  ErrorState<T> get _value => super._value as ErrorState<T>;

  @override
  $Res call({
    Object? errorMessage = freezed,
  }) {
    return _then(ErrorState<T>(
      errorMessage: errorMessage == freezed
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
class _$ErrorState<T> with DiagnosticableTreeMixin implements ErrorState<T> {
  const _$ErrorState({required this.errorMessage});

  @override
  final String errorMessage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CubitState<$T>.error(errorMessage: $errorMessage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CubitState<$T>.error'))
      ..add(DiagnosticsProperty('errorMessage', errorMessage));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is ErrorState<T> &&
            (identical(other.errorMessage, errorMessage) ||
                const DeepCollectionEquality()
                    .equals(other.errorMessage, errorMessage)));
  }

  @override
  int get hashCode =>
      runtimeType.hashCode ^ const DeepCollectionEquality().hash(errorMessage);

  @JsonKey(ignore: true)
  @override
  $ErrorStateCopyWith<T, ErrorState<T>> get copyWith =>
      _$ErrorStateCopyWithImpl<T, ErrorState<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool loading) loading,
    required TResult Function(T model) completed,
    required TResult Function(String errorMessage) error,
  }) {
    return error(errorMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool loading)? loading,
    TResult Function(T model)? completed,
    TResult Function(String errorMessage)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(errorMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InitState<T> value) init,
    required TResult Function(LoadingState<T> value) loading,
    required TResult Function(CompletedState<T> value) completed,
    required TResult Function(ErrorState<T> value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InitState<T> value)? init,
    TResult Function(LoadingState<T> value)? loading,
    TResult Function(CompletedState<T> value)? completed,
    TResult Function(ErrorState<T> value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class ErrorState<T> implements CubitState<T> {
  const factory ErrorState({required String errorMessage}) = _$ErrorState<T>;

  String get errorMessage => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ErrorStateCopyWith<T, ErrorState<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
