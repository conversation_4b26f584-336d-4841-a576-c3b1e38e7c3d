import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit_state.freezed.dart';

@freezed
class CubitState<T> with _$CubitState<T> {
  const factory CubitState.init() = InitState<T>;
  const factory CubitState.loading({required bool loading}) = LoadingState<T>;
  const factory CubitState.completed({required T model}) = CompletedState<T>;
  const factory CubitState.error({required String errorMessage}) =
      ErrorState<T>;
}
