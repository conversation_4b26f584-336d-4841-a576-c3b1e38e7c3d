import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shop/app/account_settings/change_pin.dart';
import 'package:shop/app/account_statement/account_statement.dart';
import 'package:shop/app/authentication/authentication.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/beta_level/beta_levels.dart';
import 'package:shop/app/biller/biller.dart';
import 'package:shop/app/browse/browse.dart';
import 'package:shop/app/card_management/card_management.dart';
import 'package:shop/app/collections/collection.dart';
import 'package:shop/app/create_invoice/create_invoice.dart';
import 'package:shop/app/credit/credit_dependencies.dart';
import 'package:shop/app/edit_profile/edit_profile.dart';
import 'package:shop/app/homepage/presentation/ui/widgets/campaign/campaign_di.dart';
import 'package:shop/app/loan/loan.dart';
import 'package:shop/app/my_cart/cart.dart';
import 'package:shop/app/my_items/item.dart';
import 'package:shop/app/notification/notification.dart';
import 'package:shop/app/order/order.dart';
import 'package:shop/app/transactions/transactions.dart';
import 'package:shop/app/pay/pay_dependencies.dart';
import 'package:shop/app/payments/payments_dependencies.dart';
import 'package:shop/app/product_search/search_dependency.dart';
import 'package:shop/app/promotion/promotion.dart';
import 'package:shop/app/terminal/dependencies/terminals_dependencies.dart';
import 'package:shop/app/wallet/wallet.dart';
import 'package:shop/app/wallet_transfer/wallet_transfer_dependencies.dart';
import 'package:shop/src/components/src/firebase/firebase.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/config/service/api_config.dart';

import '../../../../src/components/src/utils/utils.dart';
import '../../../deals/deals.dart';

void setUpLocator(AppConfig config) {
  setupDI(
    apiConfig: ApiConfig(
      config.baseApiUrl ?? '',
      bearerToken: () async {
        final fbUser = auth.FirebaseAuth.instance.currentUser;
        // return null: User is not logged in
        if (fbUser == null) return '';

        try {
          final idToken = await fbUser.getIdToken();

          if (idToken != null) {
            UserCubit.idToken = idToken;
            return UserCubit.idToken;
          }

          return '';
        } catch (e) {
          // Return cached token when we fail to get user's id token
          if (UserCubit.idToken.isNotEmpty) return UserCubit.idToken;

          rethrow;
        }
      },
      apiKey: () async {
        return UserCubit.instance?.currentUser?.apiKey ?? '';
      },
      appName: () async {
        return 'tradedepot';
      },
      appVersion: () async {
        if (kIsWeb) {
          return '';
        } else {
          final PackageInfo packageInfo = await PackageInfo.fromPlatform();
          return packageInfo.version.trim();
        }
      },
      extChannel: () async {
        final channel = getExtChannel();
        return channel;
      },
    ),
  );

  // locator.registerLazySingleton<TdBackgroundLocationService>(
  //     () => TdBackgroundLocationService());

  ///[Route dependency].For navigating without context
  // locator.registerLazySingleton<AppRouter>(() => AppRouter());

  registerFirebaseDependencies();
  registerCollectionDependencies(config);
  registerTdAuthenticationDependencies(config);
  registerLoanDependencies(config);
  registerWalletDependencies(config);
  registerBrowseCollectionDependencies(config);
  registerNotificationDependencies(config);
  registerCartDependencies(config);
  registerEditProfileDependencies(config);
  registerBetaLevelsDependencies(config);
  registerChangePinDependencies(config);
  registerOrderDependencies(config);
  registerCardManagementDependencies(config);
  registerItemDependencies();
  registerPromotionDependencies(config);
  registerCampaignDependencies(config);
  registerBillerDependencies(config);
  registerPayDependencies(config);
  registerSearchDependencies(config);
  registerPaymentsDependencies(config);
  registerDealsDependencies(config);
  registerWalletTransferDependencies(config);
  registerCreditDependencies(config);
  registerTerminalsDependencies(config);
  registerTransactionsDependencies(config);
  registerAccountStatementDependencies(config);
  registerCreateInvoiceDependencies(config);
}
