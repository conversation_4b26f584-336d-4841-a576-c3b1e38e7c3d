import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:app_links/app_links.dart';

const APP_INSTALL_SUBDOMAIN = '7oywd';

/// Not longer in use
/// Replaced by SignUpCubit._trackInstallationReferrer()
class FirstInstallService {
  static String key = 'app_install';

  static Future<void> initialize() async {
    AppLinks().uriLinkStream.listen((uri) {
      if (!uri.host.startsWith(APP_INSTALL_SUBDOMAIN)) return;
      // store app_install event
      setLink(uri.toString());
    });
  }

  static Future<String?> getLink() async {
    final sp = await SharedPreferences.getInstance();
    return sp.getString(FirstInstallService.key);
  }

  static Future<void> setLink(String? link) async {
    if (link == null || link.isEmpty) return;

    final sp = await SharedPreferences.getInstance();
    await sp.setString(key, link);
  }

  static Future<bool> clearLink() async {
    final sp = await SharedPreferences.getInstance();
    return sp.remove(key);
  }

  static Future<Map<String, String>?> extractValues() async {
    final link = await getLink();
    if (link == null || link.isEmpty) return null;

    final res = await http.get(Uri.parse(link));

    // Extract a url from
    // validate("https://m.shoptopup.com/?_branch_match_id=908281119295377472&amp;utm_source=Facebook&amp;utm_medium=paid%20advertising")
    // within the html response
    final regex = RegExp(r'validate\("(.+?)"\)');
    final matches = regex.allMatches(res.body);
    if (matches.isEmpty) return null;
    final m = matches.last;

    String? url = m.group(1);

    if (url == null) return null;

    // Fix url and parse uri to be able to extract the query parameters
    url = url.replaceAll('&amp;', '&');

    final u = Uri.parse(url);

    // {_branch_match_id, utm_source, utm_medium}
    return u.queryParameters;
  }
}
