import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart' as intl;
import 'package:provider/provider.dart';
import 'package:shop/app/collections/presentation/ui/widgets/quickbuy_widget.dart';
import 'package:shop/app/my_cart/domain/use-cases/create_cart.dart';
import 'package:shop/app/my_cart/presentation/logic/bloc/order_preview_cubit.dart';
import 'package:shop/app/my_cart/presentation/logic/utils/quick_buy_notifier.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/order_payment.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/order_preview.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/payment_required_items_screen.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/toast/custom_toast.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import '../../../../../src/res/values/analytics/segment_events.dart';
import '../../../../authentication/presentation/logic/bloc/user_cubit.dart';
import '../../../../my_cart/domain/params/cart_params.dart';
import '../../../../my_cart/domain/use-cases/create_order.dart';
import '../../../../my_cart/domain/use-cases/preview_order.dart';
import '../../../../my_cart/presentation/logic/bloc/cart_cubit.dart';
import '../../../../my_cart/presentation/logic/utils/methods.dart';
import '../../../../my_cart/presentation/ui/screen/my_cart.dart';
import '../../../../my_cart/presentation/ui/screen/supplier_items.dart';

const noUserFoundErrorMsg = 'No user found, please refresh app or login again';

class VariantQuantityPicker extends StatefulWidget {
  final Variant variant;
  // final num quantityAvailable;
  final bool isOutOfStock;
  final bool? loading;
  final String? text;
  final Color? textColor;
  final Color? color;
  final OrderPreview? orderPreview;
  final Function(int)? onQuantityChanged;
  final double? height;
  final bool? rebuildCart;
  final String position;
  final bool? ignoreQuickBuy;

  const VariantQuantityPicker({
    super.key,
    required this.variant,
    // required this.quantityAvailable,
    required this.isOutOfStock,
    this.loading = false,
    this.text,
    this.textColor,
    this.color,
    this.orderPreview,
    this.onQuantityChanged,
    this.height,
    this.rebuildCart = true,
    required this.position,
    this.ignoreQuickBuy = false,
  });

  @override
  State<VariantQuantityPicker> createState() => _VariantQuantityPickerState();
}

class _VariantQuantityPickerState extends State<VariantQuantityPicker> {
  double _fontSize = 18.0;
  final _focusNode = FocusNode();

  String get position => widget.position;
  int get quantityAvailable => widget.variant.quantityAvailable;
  bool get isOutOfStock => widget.isOutOfStock;
  bool get isSupplierItem => widget.orderPreview != null;
  bool get ignoreQuickBuy => widget.ignoreQuickBuy!;

  final _inputCount = ValueNotifier<int?>(null);

  @override
  void initState() {
    super.initState();
    _processCartItem();
    _focusNode.addListener(_handleFocusChanges);
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _focusNode.removeListener(_handleFocusChanges);
    super.dispose();
  }

  void _handleFocusChanges() {
    if (_focusNode.hasFocus) {
      cartButtonNotifier.value = false;
      supplierButtonNotifier.value = false;
    } else {
      cartButtonNotifier.value = true;
      supplierButtonNotifier.value = true;

      if (_inputCount.value != null) {
        clearItemFromCart();
      }
    }
  }

  void _processCartItem() {
    final cartCubit = context.read<CartCubit>();
    final itemInCart = cartCubit.item(widget.variant.variantId);

    if (itemInCart == null) return;

    late CartItem cartItem;

    if (isSupplierItem) {
      final supplierDetails = widget.orderPreview!.supplierDetails;
      final supplierDetail =
          (supplierDetails?.isNotEmpty == true) ? supplierDetails?.first : null;

      cartItem = CartItem(
        count: itemInCart.count ?? 0,
        variant: widget.variant,
        supplier: SupplierData(
          metadata: widget.orderPreview?.metadata,
          mdv: supplierDetail?.mdv,
          routeExempt: supplierDetail?.routeExempt,
          distributorId: supplierDetail?.distributorId,
          tenantId: supplierDetail?.tenantId,
          locationId: supplierDetail?.locationId,
          companyAddressId: supplierDetail?.companyAddressId,
          visitType: supplierDetail?.visitType,
          serviceAreaCodes: supplierDetail?.serviceAreaCodes,
          baseOwned: supplierDetail?.baseOwned,
          mcv: supplierDetail?.mcv,
          baseLocationId: supplierDetail?.baseLocationId,
        ),
      );
    } else {
      cartItem = CartItem(
        count: itemInCart.count ?? 0,
        variant: widget.variant,
      );
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      cartCubit.addToCart(cartItem, position);
    });
  }

  bool addItemToCart(
    BuildContext context, [
    int? quickBuyCount,
    bool? silent = false,
  ]) {
    bool addedToCart = false;

    if (quantityAvailable < 1) {
      Toast.info('This item is out of stock', context, title: 'OOS');
      return addedToCart;
    }

    final cartCubit = context.read<CartCubit>();

    final item = cartCubit.item(widget.variant.variantId) ??
        CartItem(count: 0, variant: widget.variant);

    final count = quickBuyCount ?? item.count ?? 0;

    if (count < quantityAvailable) {
      if (quickBuyCount != null) {
        cartCubit.addToCart(
          item.copyWith(count: quickBuyCount),
          position,
        );
        addedToCart = true;
      } else {
        cartCubit.addToCart(
          item.copyWith(count: count + 1),
          position,
        );
        addedToCart = true;
      }

      final quantity = count + 1;
      if (widget.onQuantityChanged != null) widget.onQuantityChanged!(quantity);

      if (widget.rebuildCart!) {
        cartRebuildNotifier.value = !cartRebuildNotifier.value;
      }

      if (!silent!) {
        trackAddEvent(item);
      }
    } else {
      CustomToast.show(context,
          message: 'Only $quantityAvailable quantity left', duration: 1);
    }

    return addedToCart;
  }

  void clearItemFromCart() {
    final cartCubit = context.read<CartCubit>();
    final item = cartCubit.item(widget.variant.variantId)!.copyWith(count: 0);
    cartCubit.addToCart(
      item,
      position,
    );

    if (widget.onQuantityChanged != null) widget.onQuantityChanged!(0);

    cartRebuildNotifier.value = !cartRebuildNotifier.value;

    trackRemoveEvent(item);
  }

  void removeFromCart() {
    final cartCubit = context.read<CartCubit>();
    final item = cartCubit.item(widget.variant.variantId);

    if (item == null) return;

    final count = item.count ?? 0;

    if (count > 0) {
      cartCubit.addToCart(
        item.copyWith(count: count - 1),
        position,
      );
    } else {
      clearItemFromCart();
    }

    final quantity = count - 1;
    if (widget.onQuantityChanged != null) widget.onQuantityChanged!(quantity);

    cartRebuildNotifier.value = !cartRebuildNotifier.value;
  }

  void _updateFontSize(int lines) {
    double newSize = 16.0;
    if (lines > 5) {
      newSize = 14.0;
    } else if (lines > 3) {
      newSize = 16.0;
    } else {
      newSize = 18.0;
    }

    if (newSize != _fontSize) {
      if (mounted) {
        setState(() {
          _fontSize = newSize;
        });
      }
    }
  }

  void trackAddEvent(CartItem item) {
    try {
      final user = context.read<UserCubit>().currentUser;
      intl.NumberFormat formatter = intl.NumberFormat.currency(
          decimalDigits: 0,
          locale: Localizations.localeOf(context).toString(),
          name: "");

      /// Track Added Item
      String imgUrl =
          getSmallImageURL(item.variant.variantId, config.environment);
      Segment.track(
        eventName: SegmentEvents.productAdded,
        properties: {
          "category": item.variant.category,
          'category_group': item.variant.categoryGroup,
          'product_variant': item.variant.name,
          "product_name": item.variant.productName,
          "variant_id": item.variant.variantId,
          "SKU": item.variant.code,
          "price": widget.variant.inventory?.variantPrice ??
              item.variant.promoPrice ??
              item.variant.price,
          "quantity": item.count,
          "position": widget.position,
          "image_url": imgUrl,
          'wallet_balance': formatter.format(
              user?.currentRetailOutlet?.walletAccount?.currentBalance ?? 0),
          'currency': item.variant.currency?.toMap(),
        },
      );
    } catch (_) {}
  }

  void trackRemoveEvent(CartItem item) {
    try {
      String imgUrl =
          getSmallImageURL(item.variant.variantId, config.environment);

      /// Track Removed Item
      Segment.track(
        eventName: SegmentEvents.productRemoved,
        properties: {
          "category": item.variant.category,
          "category_group": item.variant.categoryGroup,
          "product_name": item.variant.name,
          "variant": item.variant.variantId,
          "SKU": item.variant.code,
          "price": item.variant.promoPrice ?? item.variant.price,
          "quantity": item.count,
          "position": position,
          "image_url": imgUrl,
          'currency': item.variant.currency?.toMap(),
        },
      );
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    intl.NumberFormat formatter = intl.NumberFormat.currency(
        decimalDigits: 0,
        locale: Localizations.localeOf(context).toString(),
        name: "");

    return Consumer<QuickBuyNotifier>(
      builder: (context, qb, _) {
        final quickBuyEnabled = ignoreQuickBuy ? false : qb.quickBuyEnabled;
        final colorScheme = amberColorScheme(Theme.of(context).brightness);

        return isOutOfStock
            ? Column(
                children: [
                  const YMargin(5),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                    decoration: BoxDecoration(
                        color: colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(4)),
                    child: Text(
                      'Out of stock',
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.primary),
                    ),
                  ),
                ],
              )
            : Container(
                height: widget.height ?? 36,
                width: double.maxFinite,
                margin: const EdgeInsets.only(top: 5),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(196, 196, 196, 0.1),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Builder(builder: (context) {
                  final cartCubit = Provider.of<CartCubit>(context);
                  final cartItem = cartCubit.item(widget.variant.variantId);
                  final itemIsInCart = cartItem != null;
                  final itemCount =
                      itemIsInCart ? (cartItem.count ?? 0).toString() : '';
                  // final itemCount = itemIsInCart
                  //     ? (cartItem.count ?? 0) <= quantityAvailable
                  //         ? (cartItem.count ?? '').toString()
                  //         : quantityAvailable.toString()
                  //     : '';

                  return quickBuyEnabled
                      ? _addBtn(
                          title: 'Add to Order',
                          onTap: () {
                            QuickBuyWidget.show(
                              context,
                              isWeb: false,
                              variant: widget.variant,
                              quantityAvailable: quantityAvailable,
                              handleQuickBuy: (int count) =>
                                  _handleQuickBuyCheckout(
                                context,
                                formatter,
                                count,
                              ),
                            );
                          },
                        )
                      : itemIsInCart
                          ? Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: kColorDisabled),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    flex: 2,
                                    child: InkWell(
                                      onTap: removeFromCart,
                                      child: Container(
                                        height: double.maxFinite,
                                        width: double.maxFinite,
                                        decoration: BoxDecoration(
                                          color: kColorDisabled,
                                          borderRadius:
                                              BorderRadius.circular(8).copyWith(
                                            topRight: Radius.zero,
                                            bottomRight: Radius.zero,
                                          ),
                                        ),
                                        child: const Icon(Icons.remove),
                                      ),
                                    ),
                                  ),
                                  // const XMargin(14),
                                  Expanded(
                                    flex: 4,
                                    child: Center(
                                      child: TextField(
                                        controller:
                                            TextEditingController.fromValue(
                                                TextEditingValue(
                                          text: itemCount,
                                          selection: TextSelection.collapsed(
                                            offset: itemCount.length,
                                          ),
                                        )),
                                        focusNode: _focusNode,
                                        autofocus: false,
                                        keyboardType: TextInputType.number,
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                          NoLeadingZeroFormatter(),
                                          MaxValueTextInputFormatter(
                                              maxValue: quantityAvailable,
                                              onMaxValueExceeded: () {
                                                CustomToast.show(context,
                                                    message: getDisplayMessage(
                                                        quantityAvailable),
                                                    duration: 1);
                                              }),
                                        ],
                                        onChanged: (value) {
                                          final valueCount =
                                              int.tryParse(value);
                                          final count = valueCount ?? 0;

                                          if (count == 0) {
                                            _inputCount.value = 0;
                                            return;
                                          }
                                          _inputCount.value = null;

                                          final item = cartItem;

                                          if (count <= quantityAvailable) {
                                            cartCubit.addToCart(
                                              item.copyWith(count: count),
                                              position,
                                            );

                                            if (widget.onQuantityChanged !=
                                                null) {
                                              widget.onQuantityChanged!(count);
                                            }
                                          }

                                          _updateFontSize(value.length);
                                        },
                                        textAlign: TextAlign.center,
                                        textDirection: TextDirection.ltr,
                                        style: Theme.of(context)
                                            .textTheme
                                            .headlineMedium
                                            ?.copyWith(fontSize: _fontSize),
                                        decoration: InputDecoration(
                                          enabledBorder: InputBorder.none,
                                          focusedBorder: InputBorder.none,
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.fromLTRB(
                                              0,
                                              0,
                                              0,
                                              widget.height != null ? 5 : 15),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // const XMargin(14),
                                  Expanded(
                                    flex: 2,
                                    child: Container(
                                      height: double.maxFinite,
                                      width: double.maxFinite,
                                      decoration: BoxDecoration(
                                        color: kColorDisabled,
                                        borderRadius:
                                            BorderRadius.circular(8).copyWith(
                                          topLeft: Radius.zero,
                                          bottomLeft: Radius.zero,
                                        ),
                                      ),
                                      child: InkWell(
                                        onTap: () => addItemToCart(context),
                                        child: const Icon(Icons.add),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : _addBtn(
                              title: widget.text ?? 'Add to Cart',
                              onTap: () {
                                addItemToCart(context);
                              },
                            );
                }),
              );
      },
    );
  }

  Widget _addBtn({
    required String title,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    return TextButton(
      onPressed: onTap,
      style: TextButton.styleFrom(
        backgroundColor: widget.color ?? theme.colorScheme.primary,
        minimumSize: Size(double.maxFinite, widget.height ?? 36),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: widget.textColor ?? theme.colorScheme.onPrimary,
            ),
      ),
    );
  }

  Future<void> _handleQuickBuyCheckout(
    BuildContext context,
    intl.NumberFormat formatter,
    int quickBuyCount,
  ) async {
    final userCubit = UserCubit.instance;
    final user = userCubit?.currentUser;
    final outlet = userCubit?.currentOutlet;

    if (user == null || outlet == null) {
      Toast.error(noUserFoundErrorMsg, context);
      return;
    }

    TdLoader.show(context);

    final cartItem = context.read<CartCubit>().item(widget.variant.variantId) ??
        CartItem(count: quickBuyCount, variant: widget.variant);

    String refCode = '';
    List<CartItem?> cartItems = [cartItem.copyWith(count: quickBuyCount)];

    PreviewOrderParams params = PreviewOrderParams(
      cartItem: cartItems,
      outlet: outlet,
      refCode: refCode,
      userId: user.userId!,
    );

    final res = await locator.get<PreviewOrder>().call(params);

    res.when(
      success: (Map<String, dynamic>? result) {
        if (result!['res']['status'] == 'success') {
          Map<String, dynamic> data = result['res']['data'];
          final List<dynamic>? priceUpdate = data['priceUpdates'];
          OrderPreviewDetail orderPreviewDetail =
              OrderPreviewDetail.fromMap(data);

          final fulfilledOrders = getFulfilledOrders(orderPreviewDetail);

          // Track Initiated QuickBuy checkout
          Segment.track(
              eventName: SegmentEvents.quickBuyInitiated,
              properties: {
                'items': cartItems.map((e) => e?.toMap()).toList()
              }).ignore();

          if (fulfilledOrders.isNotEmpty) {
            if (userCubit?.isNgUser != true ||
                (outlet.creditEnabled ?? false)) {
              _createOrder(fulfilledOrders, context);
            } else {
              final previewCubit = context.read<OrderPreviewCubit>();
              previewCubit.setPreview(fulfilledOrders);
              previewCubit.setPreviewDetail(orderPreviewDetail);
              previewCubit.setPreviewMap(data);
              addItemToCart(context, quickBuyCount, true);
              handlePayment(data, orderPreviewDetail, userCubit?.currentOutlet,
                  fulfilledOrders);
            }
          } else {
            final unFulfilledOrders = getUnFulfilledOrders(orderPreviewDetail);
            final error = unFulfilledOrders.first.metadata?.error;
            final isMdqError = error == BELOW_MDQ;
            final isOosError = OOS.contains(error);

            if (isMdqError) {
              context.read<QuickBuyNotifier>().disableQuickBuy();
              TdLoader.hide();
              Toast.info('Exiting Quick Buy Mode', context, duration: 5);
            } else if (isOosError) {
              final errorMessage = getErrorMessage(unFulfilledOrders.first);
              TdLoader.hide();
              Toast.error(errorMessage, context, duration: 5);
            } else {
              final addedToCart = addItemToCart(context, quickBuyCount);

              if (addedToCart) {
                // context.read<QuickBuyNotifier>().disableQuickBuy();
                context.pushNamed(
                  CartPath,
                  extra: MyCartArgs(
                    unFulfilledOrders: unFulfilledOrders,
                    fulfilledOrders: fulfilledOrders,
                    priceUpdates: priceUpdate
                        ?.map((e) => PriceUpdate.fromMap(e))
                        .toList(),
                    orderPreviewDetail: orderPreviewDetail,
                    data: data,
                    restartCheckout: false,
                  ),
                );
                TdLoader.hide();
              } else {
                TdLoader.hide();
                Toast.error(
                  '$quantityAvailable quantity left',
                  context,
                  duration: 5,
                );
              }
            }

            trackQuickBuyError();
          }
        } else {
          // removeFromCart();
          trackQuickBuyError();
          context.read<QuickBuyNotifier>().disableQuickBuy();
          TdLoader.hide();
          Toast.error(UNEXPECTED_ERROR, context, duration: 5);
        }
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        String message = ApiExceptions.getErrorMessage(error);
        if (message.toLowerCase().startsWith(BLOCKED_OUTLET_ERROR)) {
          message = ORDER_PREVIEW_ERROR;
        }
        Toast.error(message, context, duration: 5);

        trackQuickBuyError();
      },
    );
  }

  void handlePayment(
      Map<String, Object?>? previewMap,
      OrderPreviewDetail detail,
      RetailOutlet? outlet,
      List<OrderPreview> fulfilledOrders) async {
    final res = await locator.get<CreateCart>().call(previewMap!);
    res.when(
      success: (id) {
        try {
          final paymentRequired =
              fulfilledOrders.any((e) => e.paymentRequired!);

          if (!(outlet?.paymentRequired ?? false) && paymentRequired) {
            context.pushNamed(
              OrderPaymentRequiredPath,
              extra: PaymentRequiredArgs(
                fulfilledOrders: fulfilledOrders,
                cartId: id,
                couponId: null,
                showPaymentTypesUi: false,
              ),
            );
          } else {
            context.pushNamed(
              OrderPaymentPath,
              extra: OrderPaymentArgs(
                orderDetail: detail,
                cartId: id,
                couponId: null,
                showPaymentTypesUi: false,
              ),
            );
          }
        } catch (_) {
          TdLoader.hide();
        }
        TdLoader.hide();
      },
      apiFailure: (error, code) {
        TdLoader.hide();
        final msg = ApiExceptions.getErrorMessage(error);
        Toast.error(msg, context);
      },
    );
  }

  void trackQuickBuyError() {
    try {
      final cartItem = context.read<CartCubit>().item(widget.variant.variantId);
      final cartItems = [cartItem];
      Segment.track(
          eventName: SegmentEvents.quickBuyFailed,
          properties: {'items': cartItems.map((e) => e?.toMap()).toList()});
    } catch (_) {}
  }

  Future<void> _createOrder(
      List<OrderPreview> orderPreview, BuildContext context) async {
    final userCubit = UserCubit.instance;
    final user = userCubit?.currentUser;
    final outlet = userCubit?.currentOutlet;

    if (user == null || outlet == null) {
      TdLoader.hide();
      Toast.error(noUserFoundErrorMsg, context);
      return;
    }

    CreateOrderParams params = CreateOrderParams(
      orders: orderPreview,
      outletId: outlet.id,
      userId: user.userId,
    );

    final res = await locator.get<CreateOrder>().call(params);

    res.when(
      success: (result) {
        if (result?['res']['status'] == 'success') {
          TdLoader.hide();
          if (mounted) {
            Toast.success('Order Placed Successfully', context);
          }

          Segment.track(
            eventName: SegmentEvents.quickBuyCompleted,
            properties: {
              'order-items': orderPreview.map((e) => e.toMap()).toList()
            },
          ).ignore();
        } else {
          TdLoader.hide();
          Toast.error(UNEXPECTED_ERROR, context, duration: 5);
          trackQuickBuyError();
        }
      },
      apiFailure: (error, _) {
        TdLoader.hide();
        String message = ApiExceptions.getErrorMessage(error);
        Toast.error(message, context, duration: 5);
        trackQuickBuyError();
      },
    );
  }
}

class NoLeadingZeroFormatter extends FilteringTextInputFormatter {
  NoLeadingZeroFormatter() : super.allow(RegExp(r'^([1-9]|[1-9][0-9]*)'));

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.startsWith('0')) {
      return oldValue;
    }
    return newValue;
  }
}

class MaxValueTextInputFormatter extends TextInputFormatter {
  final int maxValue;
  final VoidCallback? onMaxValueExceeded;

  // Track when we've shown the toast to avoid showing it multiple times
  bool _hasShownToast = false;

  // Debounce timer to prevent multiple toasts
  Timer? _debounceTimer;

  MaxValueTextInputFormatter({required this.maxValue, this.onMaxValueExceeded});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isNotEmpty) {
      int enteredValue = int.tryParse(newValue.text) ?? 0;
      if (enteredValue > maxValue) {
        // Only show toast if we haven't shown it recently
        if (!_hasShownToast) {
          _hasShownToast = true;

          // Execute the callback if provided
          if (onMaxValueExceeded != null) {
            onMaxValueExceeded!();
          }

          // Reset the flag after a delay to allow showing toast again
          _debounceTimer?.cancel();
          _debounceTimer = Timer(const Duration(seconds: 2), () {
            _hasShownToast = false;
          });
        }

        // Return the old value if the entered value exceeds the maximum
        return oldValue;
      }
    }

    // Reset the flag when valid input is entered
    _hasShownToast = false;
    return newValue;
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  final int decimalRange;
  final List<String> allowedDecimals;

  DecimalTextInputFormatter({
    required this.decimalRange,
    this.allowedDecimals = const [],
  }) : assert(decimalRange >= 0, 'Decimal range must be non-negative');

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final String text = newValue.text;

    // Allow empty text.
    if (text.isEmpty) {
      return newValue;
    }

    // Prevent more than one decimal point.
    if (text.split('.').length - 1 > 1) {
      return oldValue;
    }

    // If there's a decimal point, process the decimals.
    if (text.contains('.')) {
      final int decimalPointIndex = text.indexOf('.');
      final String decimals = text.substring(decimalPointIndex + 1);

      // If allowedDecimals list is provided, restrict to those values.
      if (allowedDecimals.isNotEmpty) {
        // Determine the maximum allowed length among the allowed decimal values.
        final int maxAllowedLength = allowedDecimals
            .map((s) => s.length)
            .reduce((a, b) => a > b ? a : b);

        // Reject if the entered decimals exceed the longest allowed decimal.
        if (decimals.length > maxAllowedLength) {
          return oldValue;
        }

        // Allow the user to type the decimal point without any digits yet.
        if (decimals.isNotEmpty) {
          // Check that the entered decimals are a prefix of one of the allowed values.
          final bool matchesPrefix = allowedDecimals.any(
            (allowed) => allowed.startsWith(decimals),
          );
          if (!matchesPrefix) {
            return oldValue;
          }
        }
      } else {
        // Otherwise, enforce a strict decimal range.
        if (decimals.length > decimalRange) {
          return oldValue;
        }
      }
    }

    return newValue;
  }
}
