import 'package:flutter/cupertino.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/mobile/mobile_variant_grid_item.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/web/web_variant_grid_item.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/variant.dart';

class VariantGridItem extends StatelessWidget {
  final Variant variant;
  final double aspectRatio;
  final double? height;
  final GestureTapCallback? onTap;
  final bool isGridView;
  final String position;
  final List<Variant> variantList;
  final String? collectionName;
  final bool displayLikeButton;
  final bool? isDetailsPage;
  final OrderPreview? orderPreview;
  final Function(int)? onQuantityChanged;
  final bool? rebuildCart;
  final bool? ignoreQuickBuy;
  final bool? supplierItem;
  final bool fromSearch;

  const VariantGridItem(
      {super.key,
      required this.variant,
      this.aspectRatio = itemDefaultAspectRatio,
      this.height = itemDefaultHeight,
      this.onTap,
      this.isGridView = false,
      this.displayLikeButton = true,
      required this.position,
      required this.variantList,
      this.collectionName,
      this.isDetailsPage = false,
      this.orderPreview,
      this.onQuantityChanged,
      this.rebuildCart = true,
      this.ignoreQuickBuy = false,
      this.supplierItem = false,
      this.fromSearch = false});

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebVariantGridItem(
        variant: variant,
        position: position,
        variantList: variantList,
        displayLikeButton: displayLikeButton,
        isGridView: isGridView,
        onTap: onTap,
        collectionName: collectionName,
        isDetailsPage: isDetailsPage!,
        onQuantityChanged: onQuantityChanged,
        rebuildCart: rebuildCart,
      ),
      smallScreen: MobileVariantGridItem(
        aspectRatio: aspectRatio,
        height: height!,
        variant: variant,
        position: position,
        isGridView: isGridView,
        variantList: variantList,
        onTap: onTap,
        collectionName: collectionName,
        isDetailsPage: isDetailsPage!,
        onQuantityChanged: onQuantityChanged,
        rebuildCart: rebuildCart,
        ignoreQuickBuy: ignoreQuickBuy!,
        fromSearch: fromSearch,
      ),
      mediumScreen: MobileVariantGridItem(
        aspectRatio: aspectRatio,
        height: height!,
        variant: variant,
        position: position,
        variantList: variantList,
        isGridView: isGridView,
        onTap: onTap,
        collectionName: collectionName,
        isDetailsPage: isDetailsPage!,
        onQuantityChanged: onQuantityChanged,
        rebuildCart: rebuildCart,
        ignoreQuickBuy: ignoreQuickBuy!,
        fromSearch: fromSearch,
      ),
    );
  }
}
