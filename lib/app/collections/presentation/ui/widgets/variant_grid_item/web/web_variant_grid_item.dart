import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/utils/values.dart';
import 'package:shop/src/components/src/widgets/add_to_cart.dart';
import 'package:shop/src/components/src/widgets/cached_image/index.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:shop/src/components/src/widgets/helpers/promo_badge.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/colors/colors.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_commons_flutter/utils/methods.dart';

import '../../../../../../my_items/presentation/ui/widget/wish_button.dart';

const _kdefaultAspectRatio = 0.947;
const _kNameHeight = 60.0;

class WebVariantGridItem extends StatefulWidget {
  final Variant variant;
  final double aspectRatio;
  final GestureTapCallback? onTap;
  final bool isGridView;
  final String position;
  final List<Variant> variantList;
  final String? collectionName;
  final bool displayLikeButton;
  final bool isDetailsPage;
  final Function(int)? onQuantityChanged;
  final bool? rebuildCart;
  // final String? category;
  // final String? categoryGroup;

  const WebVariantGridItem({
    super.key,
    required this.variant,
    this.aspectRatio = _kdefaultAspectRatio,
    this.onTap,
    this.isGridView = false,
    this.displayLikeButton = true,
    required this.position,
    required this.variantList,
    this.collectionName,
    required this.isDetailsPage,
    this.onQuantityChanged,
    this.rebuildCart = true,
    // required this.category,
    // required this.categoryGroup,
  });

  @override
  State<WebVariantGridItem> createState() => _WebVariantGridItemState();
}

class _WebVariantGridItemState extends State<WebVariantGridItem> {
  late final VariantInventory? inventory = widget.variant.inventory;

  @override
  Widget build(BuildContext context) {
    final outlet = Provider.of<UserCubit>(context).currentOutlet;
    final num price = widget.variant.retailPrice(outlet?.customerGroup) ??
        widget.variant.price ??
        0;

    final updatedPrice = inventory?.variantPrice ?? price;
    final updatedVariant = widget.variant.copyWith(price: updatedPrice);
    
    return InkWell(
      child: PromoBadge(
        variant: updatedVariant,
        child: Container(
          key: ValueKey(updatedVariant.variantId),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5),
            boxShadow: [
              BoxShadow(
                  color: kShadow,
                  offset: Offset(0, 5),
                  blurRadius: 10,
                  spreadRadius: 0)
            ],
          ),
          width: 185,
          height: 232,
          child: _gridItem(),
        ),
      ),
      onTap: () {
        _showCartModal(updatedVariant, inventory, context);
        Segment.track(
          eventName: SegmentEvents.productClicked,
          properties: {
            'name': widget.collectionName,
            'category': updatedVariant.category,
            'category_group': updatedVariant.categoryGroup,
            'product': widget.variant.toMap(),
            'products':
                widget.variantList.map((products) => products.toMap()).toList(),
          },
        );
      },
    );
  }

  void _showCartModal(
    Variant variant,
    VariantInventory? inventory,
    BuildContext context,
  ) async {
    final outlet = UserCubit.instance?.currentOutlet;
    FocusScope.of(context).unfocus();

    if (outlet == null) return;

    Navigator.of(context, rootNavigator: true).push(
      PageRouteBuilder(
        opaque: false, // set to false
        pageBuilder: (_, __, ___) => AddToCart(
          outlet: outlet,
          variant: variant,
          position: widget.position,
          variantInventory: inventory,
          shouldFetchInventory: inventory == null,
          onAddToCart: widget.onQuantityChanged,
          isDetailsPage: widget.isDetailsPage,
        ),
      ),
    );

    if (!mounted) return;
    setState(() {
      //rebuild to get _item
    });
  }

  Widget _gridItem() {
    final user = UserCubit.instance?.currentUser;
    final outlet = UserCubit.instance?.currentOutlet;
    final textScale = MediaQuery.of(context).textScaleFactor;
    final currency = widget.variant.currencySymbol ?? DEFAULT_CURRENCY;
    final symbolStyle = KTextStyle.medium14.copyWith(fontSize: 16);

    final num price = widget.variant.retailPrice(outlet?.customerGroup) ??
        widget.variant.price ??
        0;

    if (price == 0) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 114,
                    width: 114,
                    child:
                        CachedImage(widget.variant.variantId, ImageSize.large),
                  ),
                ),
                XSpacing(5),
                if (widget.displayLikeButton)
                  WishButton(
                    userId: user?.userId,
                    theme: WishTheme.dark,
                    variant: widget.variant,
                  ),
              ],
            ),
          ),
          YSpacing(10),
          Text(
            capitalize(widget.variant.brandName ?? ''),
            style: KTextStyle.book14.copyWith(fontSize: 12),
          ),
          Container(
            constraints: BoxConstraints.loose(
                Size(double.infinity, _kNameHeight * textScale)),
            child: Text(
              toTitleCase(widget.variant.name ?? ''),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: KTextStyle.regular14.copyWith(color: kColorGreyAlt),
            ),
          ),
          if (widget.variant.summary != null)
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  SizedBox(
                    height: 16.0,
                    child: SvgPicture.asset(
                      'lib/assets/svg/tag/tags.svg',
                      width: 8.0,
                      height: 8.0,
                    ),
                  ),
                  SizedBox(width: 5.0),
                  Expanded(
                    flex: 1,
                    child: Text(
                      toTitleCase(widget.variant.name ?? ''),
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: Colors.red[500]),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 3,
                    ),
                  ),
                ]),
          Wrap(
            children: <Widget>[
              CurrencyItem(
                price,
                currency,
                amountStyle: symbolStyle,
                symbolStyle: symbolStyle,
              )
            ],
          ),
        ],
      ),
    );
  }
}
