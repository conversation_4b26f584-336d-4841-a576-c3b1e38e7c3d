import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:safe_insets/safe_area_wrap.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/presentation/ui/widgets/grid_item_price.dart';
import 'package:shop/app/my_cart/domain/params/cart_params.dart';
import 'package:shop/app/my_cart/domain/use-cases/create_cart.dart';
import 'package:shop/app/my_cart/domain/use-cases/create_order.dart';
import 'package:shop/app/my_cart/domain/use-cases/preview_order.dart';
import 'package:shop/app/my_cart/presentation/logic/bloc/order_preview_cubit.dart';
import 'package:shop/app/my_cart/presentation/logic/utils/methods.dart';
import 'package:shop/app/my_cart/presentation/logic/utils/quick_buy_notifier.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/my_cart.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/order_payment.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/order_preview.dart';
import 'package:shop/app/my_cart/presentation/ui/screen/payment_required_items_screen.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/similar_items_widget.dart';
import 'package:shop/app/product_search/domain/usecase/set_recently_viewed.dart';
import 'package:shop/app/product_search/presentation/logic/recently_viewed_cubit.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/loader/td_loader.dart';
import 'package:shop/src/components/src/toast/custom_toast.dart';
import 'package:shop/src/components/src/utils/values.dart';
import 'package:shop/src/components/src/widgets/cached_image/index.dart';
import 'package:shop/src/components/src/widgets/deals_description.dart';
import 'package:shop/src/components/src/widgets/helpers/deals_badge.dart';
import 'package:shop/src/res/extensions/extensions.dart';
import 'package:shop/src/res/extensions/strings.dart';
import 'package:shop/src/res/values/config/keys.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';
import 'package:td_flutter_core/services/local_storage/local_storage.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../../../src/components/src/toast/toast.dart';
import '../../../../../../../src/res/assets/svgs/svgs.dart';
import '../../../../../../../src/res/values/analytics/segment_events.dart';
import '../../../../../../my_cart/presentation/logic/bloc/cart_cubit.dart';
import '../../../../../../my_items/domain/params/item_params.dart';
import '../../../../../../my_items/domain/use-cases/remove_item.dart';
import '../../../../../../my_items/domain/use-cases/save_item.dart';
import '../../../../../../my_items/presentation/ui/logic/wishlist_cubit.dart';
import '../../variant_quantity_picker.dart';

class MobileVariantGridItem extends StatelessWidget {
  final Variant variant;
  final double aspectRatio;
  final double height;
  final GestureTapCallback? onTap;
  final bool isGridView;
  final String position;
  final List<Variant> variantList;
  final String? collectionName;
  final bool isDetailsPage;
  final OrderPreview? orderPreview;
  final Function(int)? onQuantityChanged;
  final bool? rebuildCart;
  final bool? ignoreQuickBuy;
  final bool fromSearch;

  const MobileVariantGridItem(
      {super.key,
      required this.variant,
      required this.aspectRatio,
      required this.height,
      this.onTap,
      this.isGridView = false,
      required this.position,
      required this.variantList,
      this.collectionName,
      required this.isDetailsPage,
      this.orderPreview,
      this.onQuantityChanged,
      this.rebuildCart = true,
      this.ignoreQuickBuy = false,
      this.fromSearch = false});

  VariantInventory? get inventory => variant.inventory;

  num _price(RetailOutlet? outlet) {
    // use customer-group price when available
    final price =
        variant.retailPrice(outlet?.customerGroup) ?? variant.price ?? 0;

    return (inventory?.variantPrice != null && inventory!.variantPrice! > 0)
        ? inventory!.variantPrice!
        : price;
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = Provider.of<UserCubit>(context);

    final price = _price(userCubit.currentOutlet);

    final num updatedPrice = switch (userCubit.domain) {
      AppHost.tradeDepot => variant.vatPrice ?? price,
      _ => price,
    };

    return DealsBadge(
      variant: variant.copyWith(price: updatedPrice),
      inventory: inventory,
      child: _item(inventory, updatedPrice, context),
    );
  }

  Widget _item(
      VariantInventory? inventory, num updatedPrice, BuildContext context) {
    // bool itemOutOfStock = inventory?.outOfStock ?? false;
    final itemOutOfStock = variant.isOutOfStock;
    // final variantQuantityAvailable = variant.quantityAvailable;
    // num variantQuantityAvailable =
    //     inventory?.quantityAvailable ?? defaultProductCount;

    final textTheme = Theme.of(context).textTheme;
    final amountStyle =
        textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600);

    // if (updatedPrice == 0) {
    //   return const SizedBox.shrink();
    // }

    return GestureDetector(
      onTap: () async {
        if (fromSearch) {
          Segment.track(
            eventName: SegmentEvents.productSearched,
            properties: {
              'query': variant.name,
            },
          );
        }

        RecentlyViewedCubit cubit(BuildContext context) => context.read();

        if (isDetailsPage) {
          Navigator.pop(context);
        }

        if (itemOutOfStock) return;

        GridItemDetails.show(
          context,
          variant: variant,
          // variantQuantityAvailable: variantQuantityAvailable,
        );

        //add to recent items
        await locator.get<SetRecentlyViewed>().call(variant);
        if (context.mounted) {
          cubit(context).updateRecentItems(variant);
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 2,
            child: Center(
              child: AspectRatio(
                aspectRatio: aspectRatio,
                child: CachedImage(variant.variantId, ImageSize.large),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 6, 10, 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  GridItemPrice(
                    variant: variant,
                    promoStyle: amountStyle!,
                    priceStyle: textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(
                    height: 38,
                    child: Text(
                      (variant.name ?? '').toTitleCase(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                  ),
                  VariantQuantityPicker(
                      rebuildCart: rebuildCart,
                      variant: variant.copyWith(price: updatedPrice),
                      // loading: loading,
                      // quantityAvailable: variantQuantityAvailable,
                      isOutOfStock: itemOutOfStock,
                      position: position,
                      ignoreQuickBuy: ignoreQuickBuy,
                      onQuantityChanged: (quantity) {
                        if (onQuantityChanged != null) {
                          onQuantityChanged!(quantity);
                        }
                      }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class GridItemDetails extends StatefulWidget {
  final Variant variant;
  // final num variantQuantityAvailable;
  final VoidCallback? onAddItem;
  const GridItemDetails({
    super.key,
    required this.variant,
    // required this.variantQuantityAvailable,
    this.onAddItem,
  });

  static Future<bool?> show(
    BuildContext context, {
    required Variant variant,
    // required num variantQuantityAvailable,
  }) {
    return showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: true,
      useRootNavigator: true,
      context: context,
      builder: (_) => FractionallySizedBox(
        heightFactor: 0.95,
        child: GridItemDetails(
          variant: variant,
          // variantQuantityAvailable: variantQuantityAvailable,
        ),
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  @override
  State<GridItemDetails> createState() => _GridItemDetailsState();
}

class _GridItemDetailsState extends State<GridItemDetails> {
  late CartCubit cartCubit;
  late CartItem cartItem;
  bool expanded = false;

  int get quantityAvailable => widget.variant.quantityAvailable;

  int get count => cartItem.count ?? 0;

  // num get count => (cartItem.count ?? 0) <= quantityAvailable
  //     ? (cartItem.count ?? 0)
  //     : quantityAvailable;
  Variant get variant => widget.variant;
  bool get hasDescription => variant.unitDescription?.description != null;
  bool get hasPromo => variant.hasPromo;
  late SharedPrefService sp;

  @override
  void initState() {
    sp = locator.get<SharedPrefService>();
    cartCubit = context.read<CartCubit>();
    cartItem = cartCubit.items.firstWhere(
        (element) => element.variant.variantId == widget.variant.variantId,
        orElse: () => CartItem(variant: widget.variant, count: 0));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = Provider.of<UserCubit>(context);
    final textTheme = Theme.of(context).textTheme;
    NumberFormat formatter = NumberFormat.currency(
        decimalDigits: 0,
        locale: Localizations.localeOf(context).toString(),
        name: "");

    final retailPrice =
        variant.retailPrice(userCubit.currentOutlet?.customerGroup) ??
            variant.price ??
            0;

    final price = switch (userCubit.domain) {
      AppHost.tradeDepot => variant.vatPrice ?? retailPrice,
      _ => retailPrice,
    };

    return SafeAreaWrap(
      Material(
        color: Theme.of(context).colorScheme.surface,
        child: CustomScrollView(
          physics: const NeverScrollableScrollPhysics(),
          slivers: [
            SliverPersistentHeader(
              pinned: true,
              delegate: GridItemDelegate(widget.variant),
            ),
            SliverFillRemaining(
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 213,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(
                                  width: 8,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .outlineVariant
                                      .withValues(alpha: .2)),
                            ),
                          ),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              child: AspectRatio(
                                aspectRatio: 0.9,
                                child: CachedImage(
                                    variant.variantId, ImageSize.large),
                              ),
                            ),
                          ),
                        ),
                        Container(
                          width: screenHeight(context, percent: 1.0),
                          //   color: Theme.of(context).colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            '${widget.variant.name}'.toTitleCase(),
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        SizedBox(
                          width: screenHeight(context, percent: 1.0),
                          //   color: Theme.of(context).colorScheme.onPrimary,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const YMargin(10),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: GridItemPrice(
                                  variant: variant.copyWith(price: price),
                                  promoStyle: textTheme.headlineMedium!,
                                  priceStyle: textTheme.bodyLarge!,
                                ),
                              ),
                              const YMargin(20),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.fromLTRB(16, 0, 9, 0),
                          height: 59,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            // color: Theme.of(context).colorScheme.onPrimary,
                            border: Border(
                              top: BorderSide(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .surfaceContainerHighest
                                      .withValues(alpha: .5)),
                              bottom: hasDescription
                                  ? BorderSide.none
                                  : BorderSide(
                                      width: 8,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .outlineVariant
                                          .withValues(alpha: .2)),
                            ),
                          ),
                          child: BlocBuilder<WishlistCubit, WishlistState>(
                            builder: (context, state) {
                              bool exist =
                                  switch (UserCubit.instance?.isAnonymous) {
                                true => (() {
                                    final savedItems = sp.read(Keys.favorites);
                                    if (savedItems == null) return false;
                                    final List<dynamic> decodedItem =
                                        jsonDecode(savedItems) ?? [];
                                    final variants = decodedItem
                                        .map((e) =>
                                            Variant.fromMap(jsonDecode(e)))
                                        .toList();

                                    final exist = variants.any((element) =>
                                        element.variantId == variant.variantId);
                                    return exist;
                                  })(),
                                _ => (() {
                                    if (state is WishlistLoaded) {
                                      final exist = state.docs.any(
                                        (element) =>
                                            element.id == variant.variantId,
                                      );
                                      return exist;
                                    }
                                    return false;
                                  })()
                              };

                              return Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${exist ? 'Remove from' : 'Add to'} favorites',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                  IconButton(
                                      onPressed: exist
                                          ? removeFromFavorites
                                          : addToFavorites,
                                      icon: Icon(
                                        exist ? Icons.remove : Icons.add,
                                      ))
                                ],
                              );
                            },
                          ),
                        ),
                        if (hasDescription)
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 500),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              border: Border(
                                top: BorderSide(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .outlineVariant
                                        .withValues(alpha: .2)),
                                bottom: expanded
                                    ? BorderSide.none
                                    : BorderSide(
                                        width: 8,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .outlineVariant
                                            .withValues(alpha: .2)),
                              ),
                            ),
                            child: ExpansionPanelList(
                              expansionCallback: (_, isExpanded) {
                                setState(() {
                                  expanded = isExpanded;
                                });
                              },
                              animationDuration:
                                  const Duration(milliseconds: 500),
                              // dividerColor: kColorDisabled,

                              elevation: 0,
                              expandedHeaderPadding: EdgeInsets.zero,
                              children: [
                                ExpansionPanel(
                                  canTapOnHeader: true,
                                  backgroundColor:
                                      Theme.of(context).colorScheme.surface,
                                  headerBuilder: (context, isOpen) {
                                    return Container(
                                      color:
                                          Theme.of(context).colorScheme.surface,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                      ),
                                      height: 59,
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          'Description',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium,
                                        ),
                                      ),
                                    );
                                  },
                                  body: Container(
                                    padding: const EdgeInsets.all(16),
                                    width: double.infinity,
                                    child: Text(
                                      '${variant.unitDescription?.description}',
                                      style:
                                          Theme.of(context).textTheme.bodyLarge,
                                    ),
                                  ),
                                  isExpanded: expanded,
                                )
                              ],
                            ),
                          ),
                        SizedBox(
                          //   color: Theme.of(context).colorScheme.onPrimary,
                          child: Column(
                            children: [
                              if (hasPromo)
                                DealsDescription(
                                  variant: widget.variant,
                                  isDetailsPage: true,
                                ),
                              SimilarItemsWidget(
                                variantIds: ['${variant.variantId}'],
                                title: 'Frequently bought together',
                                isDetailsPage: true,
                              ),
                              SizedBox(
                                height: hasPromo ? 200 : 120,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    left: 0,
                    child: Container(
                      // padding: const EdgeInsets.fromLTRB(20, 15, 20, 5),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        border: Border(
                          top: BorderSide(
                              width: 1,
                              color: Theme.of(context)
                                  .colorScheme
                                  .surfaceContainerHighest
                                  .withValues(alpha: .5)),
                        ),
                      ),
                      child: Column(
                        children: [
                          SafeAreaWrap(
                            SafeArea(
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(20, 15, 20, 5),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              if ((cartItem.count ?? 0) > 0) {
                                                setState(() {
                                                  cartItem = cartItem.copyWith(
                                                      count: (cartItem.count ??
                                                              0) -
                                                          1);
                                                });
                                              }
                                            },
                                            child: SvgPicture.asset(
                                              kSvgRemoveIcon,
                                              width: 48,
                                              height: 48,
                                            ),
                                          ),
                                          const XMargin(12),
                                          Expanded(
                                            child: FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                count.parse(),
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .headlineMedium,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ),
                                          const XMargin(14),
                                          InkWell(
                                            onTap: () {
                                              if ((cartItem.count ?? 0) + 1 >
                                                  quantityAvailable) {
                                                CustomToast.show(context,
                                                    message:
                                                        'Only $quantityAvailable quantity left',
                                                    duration: 1);
                                              } else {
                                                setState(() {
                                                  cartItem = cartItem.copyWith(
                                                      count: (cartItem.count ??
                                                              0) +
                                                          1);
                                                });
                                              }
                                            },
                                            child: SvgPicture.asset(
                                              kSvgAddIcon,
                                              width: 48,
                                              height: 48,
                                            ),
                                          ),
                                          const XMargin(14),
                                        ],
                                      ),
                                    ),
                                    Consumer<QuickBuyNotifier>(
                                      builder: (context, qb, _) {
                                        final quickBuyEnabled =
                                            qb.quickBuyEnabled;
                                        return KButtonPrimary(
                                          text: quickBuyEnabled
                                              ? 'Add to Order'
                                              : 'Add to Cart',
                                          disabled: (cartItem.count == null ||
                                              cartItem.count == 0),
                                          onTap: () => quickBuyEnabled
                                              ? _handleQuickBuyCheckout(
                                                  context,
                                                  formatter,
                                                  count.toInt(),
                                                )
                                              : addItemToCart(),
                                          constraints: BoxConstraints.tight(
                                              const Size(146, 48)),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleQuickBuyCheckout(
    BuildContext context,
    NumberFormat formatter,
    int quickBuyCount,
  ) async {
    final userCubit = UserCubit.instance;
    final user = userCubit?.currentUser;
    final outlet = userCubit?.currentOutlet;

    if (user == null || outlet == null) {
      Toast.error(noUserFoundErrorMsg, context);
      return;
    }

    TdLoader.show(context);

    // addItemToCart(false, quickBuyCount);

    String refCode = '';
    List<CartItem?> cartItems = [cartItem.copyWith(count: quickBuyCount)];

    PreviewOrderParams params = PreviewOrderParams(
      cartItem: cartItems,
      outlet: outlet,
      refCode: refCode,
      userId: user.userId!,
    );

    final res = await locator.get<PreviewOrder>().call(params);

    res.when(
      success: (Map<String, dynamic>? result) async {
        if (result!['res']['status'] == 'success') {
          Map<String, dynamic> data = result['res']['data'];
          final List<dynamic>? priceUpdate = data['priceUpdates'];
          OrderPreviewDetail orderPreviewDetail =
              OrderPreviewDetail.fromMap(data);

          final fulfilledOrders = getFulfilledOrders(orderPreviewDetail);

          // Track Initiated QuickBuy checkout
          Segment.track(
              eventName: SegmentEvents.quickBuyInitiated,
              properties: {
                'items': cartItems.map((e) => e?.toMap()).toList()
              }).ignore();

          if (fulfilledOrders.isNotEmpty) {
            if (userCubit?.isNgUser != true ||
                (outlet.creditEnabled ?? false)) {
              _createOrder(fulfilledOrders, context);
            } else {
              final previewCubit = context.read<OrderPreviewCubit>();
              previewCubit.setPreview(fulfilledOrders);
              previewCubit.setPreviewDetail(orderPreviewDetail);
              previewCubit.setPreviewMap(data);
              addItemToCart(false, quickBuyCount);
              handlePayment(data, orderPreviewDetail, userCubit?.currentOutlet,
                  fulfilledOrders);
            }
          } else {
            final unFulfilledOrders = getUnFulfilledOrders(orderPreviewDetail);
            final error = unFulfilledOrders.first.metadata?.error;
            final isMdqError = error == BELOW_MDQ;
            final isOosError = OOS.contains(error);

            reportUnfulfilledOrders(unFulfilledOrders);

            // removeFromCart();

            if (isMdqError) {
              context.read<QuickBuyNotifier>().disableQuickBuy();
              TdLoader.hide();
              Toast.info('Exiting Quick Buy Mode', context, duration: 5);
            } else if (isOosError) {
              final errorMessage = getErrorMessage(unFulfilledOrders.first);
              TdLoader.hide();
              Toast.error(errorMessage, context, duration: 5);
            } else {
              final addedToCart = addItemToCart(false, quickBuyCount);

              if (addedToCart) {
                context.pushNamed(
                  CartPath,
                  extra: MyCartArgs(
                    unFulfilledOrders: unFulfilledOrders,
                    fulfilledOrders: fulfilledOrders,
                    priceUpdates: priceUpdate
                        ?.map((e) => PriceUpdate.fromMap(e))
                        .toList(),
                    orderPreviewDetail: orderPreviewDetail,
                    data: data,
                    restartCheckout: false,
                  ),
                );
                TdLoader.hide();
              } else {
                TdLoader.hide();
                Toast.error(
                  '$quantityAvailable quantity left',
                  context,
                  duration: 5,
                );
              }
            }
          }
        } else {
          // removeFromCart();
          context.read<QuickBuyNotifier>().disableQuickBuy();
          TdLoader.hide();
          Toast.error(UNEXPECTED_ERROR, context, duration: 5);
        }
      },
      apiFailure: (error, _) {
        // removeFromCart();
        TdLoader.hide();
        String message = ApiExceptions.getErrorMessage(error);
        if (message.toLowerCase().startsWith(BLOCKED_OUTLET_ERROR)) {
          message = ORDER_PREVIEW_ERROR;
        }
        Toast.error(message, context, duration: 5);
      },
    );
  }

  void handlePayment(
      Map<String, Object?>? previewMap,
      OrderPreviewDetail detail,
      RetailOutlet? outlet,
      List<OrderPreview> fulfilledOrders) async {
    final res = await locator.get<CreateCart>().call(previewMap!);
    res.when(
      success: (id) {
        try {
          final paymentRequired =
              fulfilledOrders.any((e) => e.paymentRequired!);

          if (!(outlet?.paymentRequired ?? false) && paymentRequired) {
            context.pushNamed(
              OrderPaymentRequiredPath,
              extra: PaymentRequiredArgs(
                fulfilledOrders: fulfilledOrders,
                cartId: id,
                couponId: null,
                showPaymentTypesUi: false,
              ),
            );
          } else {
            context.pushNamed(
              OrderPaymentPath,
              extra: OrderPaymentArgs(
                orderDetail: detail,
                cartId: id,
                couponId: null,
                showPaymentTypesUi: false,
              ),
            );
          }
        } catch (_) {
          TdLoader.hide();
        }
        TdLoader.hide();
      },
      apiFailure: (error, code) {
        TdLoader.hide();
        final msg = ApiExceptions.getErrorMessage(error);
        Toast.error(msg, context);
      },
    );
  }

  Future<void> _createOrder(
      List<OrderPreview> orderPreview, BuildContext context) async {
    final userCubit = UserCubit.instance;
    final user = userCubit?.currentUser;
    final outlet = userCubit?.currentOutlet;

    if (user == null || outlet == null) {
      TdLoader.hide();
      Toast.error(noUserFoundErrorMsg, context);
      return;
    }

    CreateOrderParams params = CreateOrderParams(
      orders: orderPreview,
      outletId: outlet.id,
      userId: user.userId,
    );

    String errorMessage = 'An unexpected error occurred, please try again';
    final res = await locator.get<CreateOrder>().call(params);

    res.when(
      success: (result) {
        if (result?['res']?['status'] == 'success') {
          TdLoader.hide();
          if (mounted) {
            Toast.success('Order Placed Successfully', context);
          }
          // removeFromCart();
          setState(() {
            cartItem = cartItem.copyWith(count: 0);
          });

          /// Track Completed Checkout
          trackCompletedCheckout(orderPreview, cartCubit.items,
              SegmentEvents.checkoutCompleted, user.userId);
        } else {
          TdLoader.hide();
          Toast.error(errorMessage, context, duration: 5);
        }
      },
      apiFailure: (error, _) {
        // removeFromCart();
        TdLoader.hide();
        String message = ApiExceptions.getErrorMessage(error);
        Toast.error(message, context, duration: 5);
      },
    );
  }

  bool addItemToCart([bool showSuccess = true, int? quickBuyCount]) {
    bool addedToCart = false;

    final newCount = quickBuyCount ?? count;
    if (count <= quantityAvailable) {
      if (quickBuyCount != null) {
        cartCubit.addToCart(
          cartItem.copyWith(count: quickBuyCount),
          'variant_grid_item',
        );
        cartItem = cartItem.copyWith(count: quickBuyCount);
        addedToCart = true;
      } else {
        cartCubit.addToCart(
          cartItem.copyWith(count: newCount),
          'variant_grid_item',
        );
        addedToCart = true;
      }

      if (widget.onAddItem != null) widget.onAddItem!();

      if (showSuccess) {
        Toast.success(toTitleCase(variant.name ?? ''), context,
            title: 'Added to Cart', duration: 2);
      }
    } else {
      final title = quantityAvailable < 1 ? 'OOS' : 'Quantity left';
      final message = quantityAvailable < 1
          ? 'This item is out of stock'
          : 'Only ${widget.variant.quantityAvailable} quantity left';

      Toast.info(message, context, title: title);
    }

    return addedToCart;
  }

  void removeFromCart() {
    cartCubit.addToCart(
      cartItem.copyWith(count: 0),
      'variant_grid_item',
    );
  }

  void updateState() {
    context.read<WishlistCubit>().handleAnonUserWishItems();
    setState(() {});
  }

  void addToFavorites() async {
    final userCubit = UserCubit.instance;
    if (userCubit?.currentUser?.id == null && userCubit?.isAnonymous != true) {
      return;
    }

    (switch (userCubit?.isAnonymous) {
      true => () {
          try {
            final savedItems = sp.read(Keys.favorites);
            List<dynamic> decodedItems = [];
            if (savedItems != null) {
              decodedItems = jsonDecode(savedItems) ?? [];
            }
            final newItem = jsonEncode(variant.toMap());
            decodedItems.add(newItem);
            sp.save(Keys.favorites, jsonEncode(decodedItems));
            updateState();
            Toast.success(toTitleCase(variant.name ?? ''), context,
                title: 'Added to favorites', duration: 2);
          } catch (e) {
            //
          }
        },
      _ => () async {
          locator.get<SaveItem>().call(
                SaveItemsParams(
                  userId: userCubit?.currentUser?.id,
                  variant: variant,
                ),
              );
          Toast.success(toTitleCase(variant.name ?? ''), context,
              title: 'Added to favorites', duration: 2);
          await Segment.track(
            eventName: SegmentEvents.productWishlist,
            properties: {
              'products': variant.toMap(),
              'category': variant.category,
              'category_group': variant.categoryGroup,
            },
          );
        }
    })();
  }

  void removeFromFavorites() async {
    final userCubit = UserCubit.instance;
    if (userCubit?.currentUser?.id == null && userCubit?.isAnonymous != true) {
      return;
    }

    (switch (userCubit?.isAnonymous) {
      true => () {
          try {
            final savedItems = sp.read(Keys.favorites);
            List<dynamic> decodedItems = [];
            if (savedItems != null) {
              decodedItems = jsonDecode(savedItems) ?? [];
            }
            final variants = decodedItems
                .map((e) => Variant.fromMap(jsonDecode(e)))
                .toList();
            variants.removeWhere(
                (element) => element.variantId == variant.variantId);
            final variantsJson =
                variants.map((e) => jsonEncode(e.toMap())).toList();
            sp.save(Keys.favorites, jsonEncode(variantsJson));
            updateState();
            Toast.error(toTitleCase(variant.name ?? ''), context,
                title: 'Removed from favorites', duration: 2);
          } catch (e) {
            //
          }
        },
      _ => () async {
          locator.get<RemoveItem>().call(
                SaveItemsParams(
                  userId: userCubit?.currentUser?.id,
                  variant: variant,
                ),
              );
          Toast.error(toTitleCase(variant.name ?? ''), context,
              title: 'Removed from favorites', duration: 2);
          await Segment.track(
            eventName: SegmentEvents.productWishlist,
            properties: {
              'products': variant.toMap(),
              'category': variant.category,
              'category_group': variant.categoryGroup,
            },
          );
        }
    })();
  }
}

class GridItemDelegate extends SliverPersistentHeaderDelegate {
  final Variant variant;
  GridItemDelegate(this.variant);

  final double minHeight = 54;
  final double maxHeight = 54;
  final envUrl = config.environment == Environment.dev
      ? 'https://dev.tradedepot.co/products'
      : 'https://tradedepot.co/products';

  // Future<String> buildLink() async {
  //   final PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //   String packageName = packageInfo.packageName;

  //   final dynamicLinkParams = DynamicLinkParameters(
  //     link: Uri.parse('$envUrl/products/${variant.variantId}'),
  //     uriPrefix: envUrl,
  //     androidParameters: AndroidParameters(packageName: packageName),
  //   );

  //   // final link =
  //   //     await FirebaseDynamicLinks.instance.buildLink(dynamicLinkParams);
  //   final link =
  //       await FirebaseDynamicLinks.instance.buildShortLink(dynamicLinkParams);

  //   return link.shortUrl.toString();
  // }

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return (!kIsWeb && Platform.isIOS)
        ? SafeAreaWrap(gridTopBar(context))
        : gridTopBar(context);
  }

  Widget gridTopBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // color: Theme.of(context).colorScheme.onPrimary,
        border: Border(
          bottom: BorderSide(
              width: 1, color: Theme.of(context).colorScheme.outlineVariant),
        ),
        boxShadow: const [
          /* BoxShadow(
            blurRadius: 1,
            offset: Offset(1, 0),
          )*/
        ],
      ),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.clear,
              size: 30,
            ),
          ),
          IconButton(
            onPressed: () async {
              // final link = await buildLink();
              Share.share('$envUrl/${variant.variantId}');
            },
            icon: const Icon(
              Icons.ios_share,
              size: 30,
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
