import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/browse_item.dart';
import 'package:shop/app/collections/presentation/logic/bloc/collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_state.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/empty_state/empty_widget.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';

const kItemPadding = 6.0;
const _kItemWidth = 185.0;
const _kItemHeight = 210.0;
const _kHexCode = "6FR5F832+";

class CollectionItems extends StatefulWidget {
  final String position;
  CollectionItems(this.position, {super.key});
  final routeName = BrowseCollectionsPath;

  @override
  _ItemDetailState createState() => _ItemDetailState();
}

class _ItemDetailState extends State<CollectionItems> {
  late VariantCollectionCubit _cubit;
  late UserCubit _userCubit;
  late String collectionId;
  late String collectionName;
  ScrollController? _scrollController;
  late String hexCode;
  int batch = 1;
  int? itemCount;

  @override
  void initState() {
    _cubit = BlocProvider.of<VariantCollectionCubit>(context, listen: false);
    _userCubit = context.read();
    _scrollController = ScrollController();
    if (_cubit.state.collection != null) {
      itemCount = _cubit.state.collection!.total;
    }
    _scrollController!.addListener(_onScroll);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController!.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController!.position.pixels ==
        _scrollController!.position.maxScrollExtent) {
      if (_cubit.state.collection!.variants!.length < itemCount!) {
        batch++;
        _cubit.fetchMoreVariantCollection(hexCode, batch.toString());
      }
    }
  }

  final _delegate = const SliverGridDelegateWithMaxCrossAxisExtent(
      maxCrossAxisExtent: _kItemWidth,
      childAspectRatio: 1.0,
      mainAxisExtent: _kItemHeight,
      crossAxisSpacing: 10,
      mainAxisSpacing: 10);

  void _handleErrorButton(context) {
    BlocProvider.of<VariantCollectionCubit>(context, listen: false)
        .fetchVariantCollectionList(collectionId, hexCode);
  }

  Widget _buildVariantCollection() {
    return BlocBuilder<VariantCollectionCubit, VariantCollectionState>(
      builder: (context, state) {
        Widget child = SizedBox.shrink();
        VariantCollectionState currentState = state;
        if (state.isLoading) {
          child = SizedBox(
            height: 150,
            width: 200,
            child: KLoader(),
          );
        } else if (currentState.isError == true) {
          child = KErrorScreen(
            state.errorCode,
            () => _handleErrorButton(context),
            displayErrorCode: true,
          );
        } else if ((currentState.collection?.variants?.length ?? 0) == 0) {
          child = EmptyIconWidget();
        } else {
          List<Variant>? variants = state.collection?.variants;
          itemCount = state.collection!.total;
          int length = variants?.length ?? 0;
          child = Padding(
            padding: EdgeInsets.only(left: 10, right: 10, top: 20),
            child: GridView.builder(
              controller: _scrollController,
              shrinkWrap: true,
              gridDelegate: _delegate,
              itemCount: length,
              itemBuilder: (context, index) {
                if (index >= length) {
                  return _cubit.state.collection!.variants!.length < itemCount!
                      ? KLoader()
                      : SizedBox.shrink();
                }

                Variant variant = variants![index];
                return VariantGridItem(
                  key: ValueKey(variant.variantId),
                  variant: variant,
                  variantList: variants,
                  collectionName: collectionName,
                  isGridView: true,
                  position: widget.position,
                );
              },
            ),
          );
        }

        return AnimatedSwitcher(
          duration: kThemeAnimationDuration,
          switchInCurve: Curves.easeOut,
          switchOutCurve: Curves.fastOutSlowIn,
          transitionBuilder: (Widget widget, Animation<double> animation) {
            return SizeTransition(
              axis: Axis.vertical,
              axisAlignment: -1.0,
              sizeFactor: animation,
              child: widget,
            );
          },
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(left: 30, right: 30, top: 20),
        constraints: BoxConstraints.expand(),
        alignment: Alignment.topCenter,
        child: StreamBuilder<BrowseItem>(
          initialData: context.read<CollectionCubit>().drawerItem.value,
          stream: context.read<CollectionCubit>().drawerItem,
          builder: (context, snapshot) {
            collectionId = snapshot.data!.id;
            collectionName = snapshot.data!.name;
            hexCode = _userCubit.currentUser?.outletLocation!.toPlus6Hex() ??
                _kHexCode;
            _cubit.fetchVariantCollectionList(collectionId, hexCode);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  capitalize(collectionName),
                  style: KTextStyle.semiBold24.copyWith(fontSize: 32),
                ),
                YSpacing(10),
                Expanded(
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: _buildVariantCollection(),
                  ),
                ),
              ],
            );
          },
        ));
  }
}

class CollectionItemsArgs {
  final String collectionId;
  final String collectionName;
  final String position;
  CollectionItemsArgs(this.collectionId, this.position, this.collectionName);
}
