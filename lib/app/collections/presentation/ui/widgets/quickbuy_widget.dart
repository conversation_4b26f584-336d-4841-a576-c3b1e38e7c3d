import 'package:flutter/material.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_quantity_picker.dart';
import 'package:shop/src/components/src/toast/custom_toast.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:shop/src/res/values/styles/form_style.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../../src/components/src/buttons/src/k_button_primary.dart';

class QuickBuyWidget extends StatefulWidget {
  final bool isWeb;
  final Variant variant;
  final num quantityAvailable;
  final Function(int) handleQuickBuy;

  const QuickBuyWidget({
    super.key,
    required this.isWeb,
    required this.variant,
    required this.quantityAvailable,
    required this.handleQuickBuy,
  });

  static Future<void> show(
    BuildContext context, {
    required bool isWeb,
    required Variant variant,
    required num quantityAvailable,
    required Function(int) handleQuickBuy,
  }) {
    return isWeb
        ? showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return AlertDialog(
                content: QuickBuyWidget(
                  isWeb: isWeb,
                  variant: variant,
                  quantityAvailable: quantityAvailable,
                  handleQuickBuy: handleQuickBuy,
                ),
                titleTextStyle: KTextStyle.semiBold24.copyWith(fontSize: 20),
                contentTextStyle: KTextStyle.regular14.copyWith(fontSize: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
                contentPadding: EdgeInsets.only(left: 30, bottom: 30, right: 0),
                titlePadding: EdgeInsets.only(left: 30, top: 30, right: 0),
                actionsPadding: EdgeInsets.zero,
              );
            },
          )
        : showModalBottomSheet(
            isScrollControlled: true,
            useRootNavigator: true,
            context: context,
            builder: (_) => FractionallySizedBox(
              heightFactor: 0.7,
              child: QuickBuyWidget(
                isWeb: isWeb,
                variant: variant,
                quantityAvailable: quantityAvailable,
                handleQuickBuy: handleQuickBuy,
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(18),
              ),
            ),
            clipBehavior: Clip.antiAlias,
          );
  }

  @override
  State<QuickBuyWidget> createState() => _QuickBuyWidgetState();
}

class _QuickBuyWidgetState extends State<QuickBuyWidget> {
  String _quantity = '';

  int get count {
    final valueCount = int.tryParse(_quantity);
    return valueCount ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final counts = [5, 10, 15, 20];
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              YMargin(30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Buy item',
                    style: textTheme.headlineSmall,
                  ),
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Icon(
                      Icons.close,
                      size: 30,
                    ),
                  ),
                ],
              ),
              YMargin(20),
              Text(
                'SELECT OR ENTER QUANTITY',
                style: textTheme.bodySmall,
              ),
              YMargin(40),
              Align(
                alignment: Alignment.center,
                child: SizedBox(
                  height: 31,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    shrinkWrap: true,
                    children: counts
                        .map(
                          (e) => InkWell(
                            onTap: () {
                              if (e <= widget.quantityAvailable) {
                                // _controller.text = '$e';
                                setState(() {
                                  _quantity = '$e';
                                });
                              }
                            },
                            child: Container(
                              width: 38,
                              height: 31,
                              alignment: Alignment.center,
                              margin: EdgeInsets.symmetric(horizontal: 2),
                              decoration: BoxDecoration(
                                shape: BoxShape.rectangle,
                              ),
                              child: Text(
                                '$e',
                                style: textTheme.bodyMedium,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
              const YMargin(30),
              Text(
                'Enter quantity',
                style: textTheme.bodyMedium,
              ),
              const YMargin(8),
              TextField(
                autofocus: false,
                // controller: _controller,
                controller: TextEditingController.fromValue(TextEditingValue(
                  text: _quantity,
                  selection: TextSelection.collapsed(
                    offset: _quantity.length,
                  ),
                )),
                inputFormatters: [
                  NoLeadingZeroFormatter(),
                  MaxValueTextInputFormatter(
                      maxValue: widget.quantityAvailable.toInt(),
                      onMaxValueExceeded: () {
                        CustomToast.show(context,
                            message:
                                'Only ${widget.quantityAvailable} quantity left',
                            duration: 1);
                      }),
                ],
                keyboardType: TextInputType.number,
                textDirection: TextDirection.ltr,
                style: Theme.of(context).textTheme.bodyLarge,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  enabledBorder: FormStyle.formBorder,
                  focusedBorder: FormStyle.focusedFormBorder,
                  border: FormStyle.formBorder,
                  // filled: true,
                  // fillColor: Colors.white,
                  constraints: BoxConstraints.tight(Size(388, 48)),
                ),
                onChanged: (value) {
                  final valueCount = int.tryParse(value);
                  final count = valueCount ?? 0;

                  if (count <= widget.quantityAvailable) {
                    setState(() {
                      _quantity = value;
                    });
                  }
                },
              ),
              YMargin(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('TOTAL  VALUE', style: textTheme.bodySmall),
                  Flexible(
                    child: CurrencyItem(
                      count * (widget.variant.price ?? 0),
                      widget.variant.currencyCode ??
                          UserCubit.instance!.currencyCode,
                      amountStyle: textTheme.bodyLarge?.copyWith(fontSize: 18),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              KButtonPrimary(
                onTap: () {
                  Navigator.pop(context);

                  // final valueCount = int.tryParse(_quantity);
                  // final count = valueCount ?? 0;

                  if (count > 0) {
                    widget.handleQuickBuy(count);
                  }
                },
                text: 'Place Order',
              ),
              YMargin(25),
            ],
          ),
        ),
      ],
    );
  }
}
