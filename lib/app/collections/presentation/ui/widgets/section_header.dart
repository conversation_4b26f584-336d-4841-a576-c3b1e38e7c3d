import 'package:flutter/material.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';

const kSectionHeaderHeight = 60.0;

class SectionHeader extends StatelessWidget {
  final String? title;
  final Widget? rightItem;
  final double verticalHeight;
  final bool borderTop;
  final bool borderBottom;
  final TextStyle? titleStyle;
  final String? headerText;
  final ColorScheme? headerBgColor;
  const SectionHeader({
    super.key,
    this.title,
    this.rightItem,
    this.verticalHeight = kSectionHeaderHeight,
    this.borderBottom = false,
    this.borderTop = false,
    this.titleStyle,
    this.headerText,
    this.headerBgColor,
  });

  bool get hasHeader => headerText != null;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: hasHeader ? 95 : kSectionHeaderHeight,
      decoration: BoxDecoration(
        color: headerBgColor?.primary,
        borderRadius: hasHeader
            ? BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              )
            : BorderRadius.zero,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              SizedBox(
                width: defaultHorizontalContentPadding,
              ),
              Expanded(
                child: Text(
                  title ?? "",
                  style: titleStyle ??
                      Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          overflow: TextOverflow.ellipsis,
                          color: headerBgColor?.onPrimary),
                ),
              ),
              rightItem != null
                  ? Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: rightItem!,
                    )
                  : Container(),
              SizedBox(
                width: 20.0,
              ),
            ],
          ),
          if (hasHeader) ...[
            Padding(
              padding: const EdgeInsets.only(left: 22.0),
              child: Text(
                '$headerText',
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(color: headerBgColor?.onPrimary),
              ),
            )
          ]
        ],
      ),
    );
  }
}
