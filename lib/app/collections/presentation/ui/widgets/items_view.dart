import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/browse/presentation/ui/widgets/scroll_options.dart';
import 'package:shop/app/collections/presentation/ui/widgets/item_detail.dart';
import 'package:shop/app/collections/presentation/ui/widgets/section_header.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import 'package:shop/app/product_search/presentation/screens/related_items/related_items.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

class ItemsView extends StatelessWidget {
  final List<Variant> itemsList;
  final Variant variant;
  final String? title;
  final bool showSearch;
  final String position;

  ItemsView({super.key, 
    required this.itemsList,
    this.title,
    required this.variant,
    this.showSearch = true,
    required this.position,
  });

  final double _kItemWidth = 144.0;
  final double _kWebItemWidth = 185.0;
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final List<Variant> previewItems =
        itemsList.length > 5 ? itemsList.sublist(0, 5) : itemsList;
    final bool canViewMore = itemsList.length > 5;

    getHeight() {
      double height = MediaQuery.of(context).size.height;

      return height <= 545
          ? 0.37
          : (height > 545 && height <= 600)
              ? 0.33
              : 0.27;
    }

    final bool isLargeScreen = ResponsiveDesign.isLargeScreen(context);
    final theme = Theme.of(context);
    return SizedBox(
      height: screenHeight(context, percent: getHeight()),
      width: screenWidth(context, percent: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          isLargeScreen
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      capitalize(title),
                      style: KTextStyle.medium14.copyWith(fontSize: 16),
                      textAlign: TextAlign.left,
                    ),
                    ScrollOptions(() {
                      Navigator.pop(context);
                      context.goNamed(
                        DashboardRelatedItemsPath,
                        extra:
                            RelatedItemsArgs(variant, relatedItems: itemsList),
                      );
                    },
                        () => _scrollController.animateTo(
                            _scrollController.position.maxScrollExtent,
                            duration: Duration(milliseconds: 400),
                            curve: Curves.easeIn),
                        () => _scrollController.animateTo(0,
                            duration: Duration(milliseconds: 400),
                            curve: Curves.easeIn))
                  ],
                )
              : SectionHeader(
                  title: capitalize(title),
                  /*  titleStyle: KTextStyle.subtitleTitleText
                      .copyWith(fontSize: 14, fontWeight: FontWeight.w500),*/
                  rightItem: canViewMore
                      ? InkResponse(
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ItemDetails(
                                args: ItemDetailsArgs(
                                  itemsList: itemsList,
                                  title: title,
                                  position: position,
                                  showSearch: showSearch,
                                ),
                              ),
                            ),
                          ),
                          highlightColor: Theme.of(context).highlightColor,
                          splashColor: Theme.of(context).splashColor,
                          child: Text('View All',
                              style: KTextStyle.hintText.copyWith(
                                  color: canViewMore
                                      ? theme.colorScheme.primary
                                      : theme.textTheme.bodySmall!.color,
                                  fontWeight: FontWeight.w600)),
                        )
                      : const SizedBox.shrink(),
                ),
          YMargin(10),
          Expanded(
            child: SizedBox(
              // margin: const EdgeInsets.only(left: 10, right: 10),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                controller: _scrollController,
                itemCount: previewItems.length,
                itemExtent: isLargeScreen ? _kWebItemWidth : _kItemWidth,
                itemBuilder: (BuildContext context, index) {
                  final int length = itemsList.length;
                  if (index >= length) {
                    return Container();
                  }
                  final variant = previewItems[index];
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5, vertical: 15),
                    /*           width: 159,
                    height: 200,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        boxShadow: [
                          BoxShadow(
                              color: kColorBlue10,
                              offset: Offset(0, 5),
                              blurRadius: 10)
                        ],
                        color: kColorWhite),*/
                    child: VariantGridItem(
                      key: ValueKey(variant.variantId),
                      variant: variant,
                      displayLikeButton: false,
                      position: position,
                      variantList: itemsList,
                      collectionName: title,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
