import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_detail/collection_detail.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';

const _kItemWidth = 185.0;
const kItemPadding = 6.0;
const _kItemHeight = 210.0;

class WebCollectionList extends StatefulWidget {
  final Collection collection;
  final bool showSearch;
  final String position;

  const WebCollectionList({
    super.key,
    required this.collection,
    this.showSearch = true,
    required this.position,
  });

  @override
  State<StatefulWidget> createState() {
    return _CollectionList();
  }
}

class _CollectionList extends State<WebCollectionList> {
  late AppConfig config = Provider.of<AppConfig>(context);
  late List<Variant> itemsList = widget.collection.variants!;
  late String? title = widget.collection.name;
  ValueNotifier<bool> showMore = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.transparent,
        child: LayoutBuilder(
          builder: (context, constraints) {
            int itemCount = ((constraints.maxWidth / _kItemWidth) * 2).round();

            ///-3
            itemCount = itemCount.isEven ? itemCount : itemCount + 1;
            List<Variant> previewItems = itemsList.length > itemCount
                ? itemsList.sublist(0, itemCount)
                : itemsList;
            final bool canViewMore = itemsList.length > itemCount;
            String imageUrl =
                getLargeImageURL(widget.collection.id, config.environment);
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Text(
                        capitalize(title),
                        style: KTextStyle.semiBold24.copyWith(fontSize: 32),
                      ),
                    ),
                    if (canViewMore)
                      Padding(
                        padding: EdgeInsets.only(top: 30, right: 5),
                        child: InkWell(
                            child: Text(
                              "See All",
                              style: KTextStyle.medium14.copyWith(fontSize: 16),
                            ),
                            onTap: () {
                              Segment.track(
                                eventName: SegmentEvents.productListViewed,
                                properties: {
                                  'list_id': widget.collection.id,
                                  'list_name': widget.collection.name,
                                  'category': widget.collection.type,
                                  'products': itemsList
                                      .map((item) => item.toMap())
                                      .toList(),
                                  'products_quantity': itemsList.length,
                                  'products_position':
                                      widget.collection.position,
                                  'image_url': imageUrl,
                                  'position': widget.position,
                                },
                              );
                              context.goNamed(
                                DashboardCollectionDetailsPath,
                                extra: CollectionDetailArgs(widget.collection,
                                    widget.showSearch, widget.position),
                              );
                            }),
                      )
                  ],
                ),
                YSpacing(5),
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child:
                      /*Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 10,
                      runSpacing: 10,
                      crossAxisAlignment: WrapCrossAlignment.start,
                      children: previewItems
                          .map(
                            (e) => VariantGridItem(
                              key: ValueKey(e.variantId),
                              variant: e,
                              position: position,
                              variantList: itemsList,
                              collectionName: title,
                            ),
                          )
                          .toList(),
                    )*/
                      GridView.builder(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    gridDelegate:
                        const SliverGridDelegateWithMaxCrossAxisExtent(
                            maxCrossAxisExtent: _kItemWidth,
                            childAspectRatio: 1.0,
                            mainAxisExtent: _kItemHeight,
                            mainAxisSpacing: 10),
                    itemCount: previewItems.length,
                    itemBuilder: (BuildContext context, index) {
                      final int length = itemsList.length;
                      if (index >= length) {
                        return Container();
                      }
                      final variant = previewItems[index];
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: kItemPadding),
                        child: VariantGridItem(
                          key: ValueKey(variant.variantId),
                          variant: variant,
                          position: widget.position,
                          variantList: itemsList,
                          collectionName: title,
                        ),
                      );
                    },
                  ),
                ),
                /*           if (canViewMore)
                  Center(
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 15, horizontal: 5),
                      child: TextButton(
                        onPressed: () {
                          Segment.track(
                            eventName: SegmentEvents.productListViewed,
                            properties: {
                              'list_id': widget.collection.id,
                              'list_name': widget.collection.name,
                              'category': widget.collection.type,
                              'products': itemsList
                                  .map((item) => item.toMap())
                                  .toList(),
                              'products_quantity': itemsList.length,
                              'products_position': widget.collection.position,
                              'image_url': imageUrl,
                              'position': widget.position,
                            },
                          );
                          context.pushReplacementNamed(DashboardCollectionDetailsPath,
                              arguments: CollectionDetailArgs(widget.collection,
                                  widget.showSearch, widget.position));
                        },
                        child: Row(
                          children: [
                            Text(
                              "View More",
                              style: KTextStyle.medium14
                                  .copyWith(fontSize: 16, color: kColorBlue),
                            ),
                            XSpacing(10),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 12,
                              color: kColorBlue,
                            ),
                          ],
                          mainAxisAlignment: MainAxisAlignment.center,
                        ),
                        style: ButtonStyles.kTextButtonStyle.copyWith(
                          fixedSize: MaterialStateProperty.all(
                            Size(375, 48),
                          ),
                        ),
                      ),
                    ),
                  ),*/
                const SizedBox(height: 30),
              ],
            );
          },
        ));
  }
}
