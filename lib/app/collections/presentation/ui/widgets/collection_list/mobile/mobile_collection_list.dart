import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_detail/collection_detail.dart';
import 'package:shop/app/collections/presentation/ui/widgets/section_header.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import 'package:shop/app_config.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:shop/src/res/values/analytics/segment_events.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../../../logic/utils/methods.dart';

const kItemPadding = 6.0;

class MobileCollectionList extends StatefulWidget {
  final Collection collection;
  final bool showSearch;
  final String position;

  const MobileCollectionList({
    super.key,
    required this.collection,
    this.showSearch = true,
    required this.position,
  });

  @override
  State<MobileCollectionList> createState() => _MobileCollectionListState();
}

class _MobileCollectionListState extends State<MobileCollectionList> {
  bool get hasHeader => widget.collection.headerText != null;
  double get height => hasHeader ? 98 : itemDefaultHeight;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    List<Variant> itemsList = widget.collection.validVariants;
    String? title = widget.collection.name;
    final List<Variant> previewItems =
        itemsList.length > 5 ? itemsList.sublist(0, 5) : itemsList;
    // final bool canViewMore = itemsList.length > 5;
    String imageUrl =
        getLargeImageURL(widget.collection.id, config.environment);
    final ColorScheme? colorScheme = widget.collection.headerBgColor != null
        ? ColorScheme.fromSeed(
            seedColor: color(widget.collection.headerBgColor)!,
            brightness: theme.brightness)
        : null;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: hasHeader ? 8 : 0),
      child: Container(
        height: hasHeader ? 320 : 285,
        margin: const EdgeInsets.symmetric(vertical: 6).copyWith(bottom: 10),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          /*  border: hasHeader
              ? Border.all(
                  color:
                      // _isHovered || _isTapped
                      //     ?
                      color(widget.collection.headerBgColor)!
                  // : Colors.transparent,
                  )
              : null,*/
          borderRadius: hasHeader
              ? const BorderRadius.all(
                  Radius.circular(16),
                )
              : BorderRadius.zero,
          boxShadow: [
            BoxShadow(
              color: theme.brightness == Brightness.dark
                  ? colorScheme?.surface.withValues(alpha: 0.05) ??
                      theme.colorScheme.surface.withValues(alpha: 0.05)
                  : colorScheme?.onSurface.withValues(alpha: 0.05) ??
                      theme.colorScheme.onSurface.withValues(alpha: 0.05),
              spreadRadius: 4,
              blurRadius: 8,
              offset: const Offset(0, 3), // changes position of shadow
            )
            // hasHeader
            //     ? BoxShadow(
            //         color: Colors.grey.withValues(alpha: 0.5),
            //         spreadRadius: 0.5,
            //         blurRadius: 5,
            //         offset: Offset(0, 1),
            //       )
            //     : BoxShadow(
            //         color: Color.fromRGBO(0, 0, 0, 0.04),
            //         spreadRadius: 2,
            //         blurRadius: 5,
            //         offset: Offset(0, 3),
            //       )
          ],
        ),
        child: Column(
          children: [
            SectionHeader(
              title: capitalize(title),
              headerText: widget.collection.headerText,
              headerBgColor: colorScheme,
              // titleStyle: Theme.of(context).textTheme.headlineMedium,
              rightItem: InkResponse(
                onTap: () {
                  Segment.track(
                    eventName: SegmentEvents.productListViewed,
                    properties: {
                      'list_id': widget.collection.id,
                      'list_name': widget.collection.name,
                      'category': widget.collection.type,
                      'products':
                          itemsList.map((item) => item.toMap()).toList(),
                      'products_quantity': itemsList.length,
                      'products_position': widget.collection.position,
                      'image_url': imageUrl,
                      'position': widget.position,
                    },
                  );

                  context.pushNamed(
                    CollectionDetailsPath,
                    extra: CollectionDetailArgs(
                        widget.collection, widget.showSearch, widget.position),
                  );
                },
                highlightColor: Theme.of(context).highlightColor,
                splashColor: Theme.of(context).splashColor,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'See all',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: widget.collection.headerBgColor != null
                              ? theme.colorScheme.surface
                              : theme.colorScheme.primary),
                    ),
                    Icon(Icons.chevron_right,
                        color: widget.collection.headerBgColor != null
                            ? theme.colorScheme.surface
                            : theme.colorScheme.primary),
                  ],
                ),
              ),
            ),
            const YMargin(15),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: previewItems.length,
                itemExtent: defaultItemExtent,
                physics: const BouncingScrollPhysics(),
                itemBuilder: (BuildContext context, index) {
                  final int length = itemsList.length;
                  if (index >= length) {
                    return Container();
                  }
                  final variant = previewItems[index];
                  return Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: kItemPadding),
                    child: VariantGridItem(
                      key: ValueKey(variant.variantId),
                      variant: variant,
                      position: widget.position,
                      variantList: itemsList,
                      collectionName: title,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
