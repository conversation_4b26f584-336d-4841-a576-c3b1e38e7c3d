import 'package:flutter/cupertino.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_list/mobile/mobile_collection_list.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_list/web/web_collection_list.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class CollectionList extends StatelessWidget {
  final Collection collection;
  final bool showSearch;
  final String position;

  const CollectionList({
    super.key,
    required this.collection,
    this.showSearch = true,
    required this.position,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebCollectionList(
        collection: collection,
        position: position,
        showSearch: showSearch,
      ),
      mediumScreen: MobileCollectionList(
        collection: collection,
        position: position,
        showSearch: showSearch,
      ),
      smallScreen: MobileCollectionList(
        collection: collection,
        position: position,
        showSearch: showSearch,
      ),
    );
  }
}
