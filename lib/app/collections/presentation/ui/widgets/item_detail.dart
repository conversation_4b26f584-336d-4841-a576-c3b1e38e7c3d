import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/cart_icon.dart';
import 'package:shop/src/components/src/widgets/search_widget.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';

import 'variant_grid_item/variant_grid_item.dart';

const kItemPadding = 6.0;

class ItemDetails extends StatefulWidget {
  final ItemDetailsArgs args;

  const ItemDetails({super.key, 
    required this.args,
  });

  @override
  _ItemDetailsState createState() => _ItemDetailsState();
}

class _ItemDetailsState extends State<ItemDetails> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final isSmallScreen = size.height < kGridScreenHeight;

    final listDelegate = SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      childAspectRatio:
          (size.width / size.height) * (isSmallScreen ? 0.9 : 1.4),
      crossAxisSpacing: 10.0,
      mainAxisSpacing: 10.0,
    );

    return Scaffold(
      appBar: AppBar(
        elevation: 1,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_outlined,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          Container(),
          // NotificationBell(),
          CartIcon(),
        ],
      ),
      body: DecoratedBox(
        decoration: BoxDecoration(),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                padding: screenPadding,
                child: buildIntro(context),
              ),
            ),
            if (widget.args.showSearch!)
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                sliver: SliverPersistentHeader(
                  pinned: false,
                  delegate: KSearchDelegate(position: 'Item Details'),
                ),
              ),
            SliverPadding(
              padding: EdgeInsets.only(left: 10, right: 10, top: 20),
              sliver: SliverGrid(
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    Variant variant = widget.args.itemsList[index];
                    return VariantGridItem(
                      key: ValueKey(variant.variantId),
                      variant: variant,
                      variantList: widget.args.itemsList,
                      collectionName: widget.args.title,
                      isGridView: true,
                      position: widget.args.position ?? '',
                    );
                  },
                  childCount: widget.args.itemsList.length,
                ),
                gridDelegate: listDelegate,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildIntro(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.args.title!,
          style: KTextStyle.headerTitleText,
        ),
      ],
    );
  }
}

class ItemIconButton extends StatelessWidget {
  const ItemIconButton({
    super.key,
    required this.iconInfo,
    required this.iconSize,
    required this.handleTap,
  });

  final String iconInfo;
  final double iconSize;
  final VoidCallback handleTap;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      padding: EdgeInsets.all(0),
      onPressed: handleTap,
      icon: SvgPicture.asset(
        iconInfo,
        width: iconSize,
      ),
    );
  }
}

class ItemDetailsArgs {
  final List<Variant> itemsList;
  final String? title;
  final String? subtitle;
  final String? position;
  final bool? showSearch;

  ItemDetailsArgs({
    required this.itemsList,
    this.title,
    this.subtitle,
    this.position,
    this.showSearch = false,
  });
}
