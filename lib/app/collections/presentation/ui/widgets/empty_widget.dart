import 'package:flutter/material.dart';

class EmptyWidget extends StatelessWidget {
  final String errorMessage;
  final Widget image;
  final onPress;
  const EmptyWidget(this.errorMessage, this.image, [this.onPress]);
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Container(
          alignment: Alignment.center,
          width: 100,
          height: 100,
          child: image,
        ),
        SizedBox(
          height: 16.0,
        ),
        Text(
          errorMessage,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        SizedBox(
          height: 16.0,
        ),
        onPress != null
            ? Center(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                  ),
                  onPressed: () {
                    onPress();
                  },
                  child: Text("Retry"),
                ),
              )
            : Container(),
      ],
    );
  }
}
