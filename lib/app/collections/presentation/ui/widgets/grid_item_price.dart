import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/presentation/logic/utils/methods.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:td_commons_flutter/app_host.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class GridItemPrice extends StatelessWidget {
  const GridItemPrice({
    super.key,
    required this.variant,
    required this.promoStyle,
    required this.priceStyle,
  });

  final Variant variant;
  final TextStyle promoStyle;
  final TextStyle priceStyle;

  VariantInventory? get inventory => variant.inventory;

  num _price(RetailOutlet? outlet) {
    // use customer-group price when available
    final price =
        variant.retailPrice(outlet?.customerGroup) ?? variant.price ?? 0;

    return (inventory?.variantPrice != null && inventory!.variantPrice! > 0)
        ? inventory!.variantPrice!
        : price;
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = Provider.of<UserCubit>(context);

    final price = _price(userCubit.currentOutlet);

    final num vatPrice = switch (userCubit.domain) {
      AppHost.tradeDepot => variant.vatPrice ?? price,
      _ => price,
    };

    final currency =
        (variant.currencySymbol != null && variant.currencySymbol!.isNotEmpty)
            ? variant.currencySymbol!
            : (variant.currencyCode != null && variant.currencyCode!.isNotEmpty)
                ? variant.currencyCode!
                : DEFAULT_CURRENCY;

    return isPriceReductionPromo(variant)
        ? FittedBox(
            fit: BoxFit.scaleDown,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                CurrencyItem(
                  priceReductionPromoPrice(variant.promotion!, vatPrice),
                  currency,
                  amountStyle: promoStyle,
                ),
                const XMargin(6),
                CurrencyItem(
                  vatPrice,
                  currency,
                  amountStyle: priceStyle.copyWith(
                    decoration: TextDecoration.lineThrough,
                    decorationThickness: 2.0,
                  ),
                )
              ],
            ),
          )
        : Wrap(
            children: <Widget>[
              CurrencyItem(
                vatPrice,
                currency,
                amountStyle: promoStyle,
              )
            ],
          );
  }
}
