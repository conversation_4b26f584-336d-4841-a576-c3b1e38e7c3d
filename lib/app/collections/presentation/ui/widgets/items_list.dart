import 'package:flutter/material.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import 'item_detail.dart';
import 'section_header.dart';
import 'variant_grid_item/variant_grid_item.dart';

class ItemsList extends StatelessWidget {
  final List<Variant> itemsList;
  final String? title;
  final bool? showSearch;
  final String position;
  final double? height;
  final bool? isDetailsPage;

  const ItemsList({super.key, 
    required this.itemsList,
    this.title,
    this.showSearch = true,
    required this.position,
    this.height = itemDefaultHeight,
    this.isDetailsPage = false,
  });

  final double _kItemWidth = 150.0;
  final double _kWebItemWidth = 185.0;

  @override
  Widget build(BuildContext context) {
    final List<Variant> previewItems =
        itemsList.length > 5 ? itemsList.sublist(0, 5) : itemsList;
    // final bool canViewMore = itemsList.length > 5;

    final bool isLargeScreen = ResponsiveDesign.isLargeScreen(context);

    return SizedBox(
      height: height,
      width: screenWidth(context, percent: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          isLargeScreen
              ? Text(
                  capitalize(title),
                  style: Theme.of(context).textTheme.headlineMedium,
                  textAlign: TextAlign.left,
                )
              : Container(
                  //  color: Theme.of(context).colorScheme.onPrimary,
                  // padding: const EdgeInsets.only(top: 10),
                  child: SectionHeader(
                      title: capitalize(title),
                      //   titleStyle: Theme.of(context).textTheme.headlineMedium,
                      rightItem: InkResponse(
                        onTap: () {
                          if (isDetailsPage == true) {
                            Navigator.pop(context);
                          }

                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ItemDetails(
                                args: ItemDetailsArgs(
                                  itemsList: itemsList,
                                  title: title,
                                  position: position,
                                  showSearch: showSearch!,
                                ),
                              ),
                            ),
                          );
                        },
                        highlightColor: Theme.of(context).highlightColor,
                        splashColor: Theme.of(context).splashColor,
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text('See all',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary)),
                              Icon(Icons.chevron_right,
                                  color: Theme.of(context).colorScheme.primary),
                            ],
                          ),
                        ),
                      )),
                ),
          Container(
            //  color: Theme.of(context).colorScheme.onPrimary,
            height: 10,
          ),
          Expanded(
            child: Container(
              // color: Theme.of(context).colorScheme.onPrimary,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                physics: BouncingScrollPhysics(),
                itemCount: previewItems.length,
                itemExtent: isLargeScreen ? _kWebItemWidth : _kItemWidth,
                itemBuilder: (BuildContext context, index) {
                  final int length = itemsList.length;
                  if (index >= length) {
                    return Container();
                  }
                  final variant = previewItems[index];
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: kItemPadding),
                    child: VariantGridItem(
                      height: height!,
                      key: ValueKey(variant.variantId),
                      variant: variant,
                      position: position,
                      variantList: itemsList,
                      collectionName: title,
                      isDetailsPage: isDetailsPage,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
