import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_state.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/cart_icon.dart';
import 'package:shop/app/product_search/presentation/logic/search_bloc.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/search_widget.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

import '../../variant_grid_item/variant_grid_item.dart';

const kItemPadding = 6.0;

class MobileCollectionDetail extends StatefulWidget {
  final Collection collection;
  final bool showSearch;
  final String position;
  const MobileCollectionDetail(this.collection, this.showSearch, this.position, {super.key});

  @override
  _ItemDetailState createState() => _ItemDetailState();
}

class _ItemDetailState extends State<MobileCollectionDetail> {
  late VariantCollectionCubit _cubit;
  late UserCubit _userCubit;
  ScrollController? _scrollController;
  String? hexCode;
  int batch = 1;
  int? itemCount;

  @override
  void initState() {
    _cubit = BlocProvider.of<VariantCollectionCubit>(context, listen: false);

    _userCubit = context.read();
    _scrollController = ScrollController();
    _scrollController?.addListener(_onScroll);
    if (_cubit.state.collection != null) {
      itemCount = _cubit.state.collection!.total;
    }
    hexCode = _userCubit.currentUser?.outletLocation?.toPlus6Hex();
    _cubit.fetchVariantCollection(widget.collection, hexCode);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController?.position.pixels ==
        _scrollController?.position.maxScrollExtent) {
      if (_cubit.state.collection?.variants == null || itemCount == null) {
        return;
      }
      if (_cubit.state.collection!.variants!.length < itemCount!) {
        batch++;
        _cubit.fetchMoreVariantCollection(hexCode, batch.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    String? title = widget.collection.name;
    final theme = Theme.of(context);

    final size = MediaQuery.sizeOf(context);
    final isSmallScreen = size.height < kGridScreenHeight;

    final listDelegate = SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      childAspectRatio:
          (size.width / size.height) * (isSmallScreen ? 0.9 : 1.4),
      crossAxisSpacing: 10.0,
      mainAxisSpacing: 10.0,
    );

    void handleErrorButton(context) {
      BlocProvider.of<VariantCollectionCubit>(context, listen: false)
          .fetchVariantCollection(widget.collection, hexCode);
    }

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        notificationPredicate: (notification) => false,

        title: InkWell(
          onTap: () {
            AppConfig config = context.read<AppConfig>();
            UserCubit userCubit = context.read<UserCubit>();
            final userStream = userCubit.currentUserStream;
            PopularItemsCache popularItemsCache = PopularItemsCache();
            context.pushNamed(searchPath,
                extra: SearchArgs(
                    position: 'Mobile Collection Detail',
                    config: config,
                    userStream: userStream,
                    popularItemsCache: popularItemsCache));
          },
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: theme.colorScheme.surfaceContainerHighest),
            //   constraints: BoxConstraints(minHeight: 7, maxHeight: 14),
            padding: EdgeInsets.all(12),

            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  kSvgSearchIcon,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const XMargin(10),
                Text(
                  'Search for brand or item',
                  style: theme.textTheme.bodySmall
                      ?.copyWith(color: theme.colorScheme.onSurface),
                ),
              ],
            ),
          ),
        ),
        //toolbarHeight: 100,
        // centerTitle: true,
        leading: BackButton(),
        actions: [
          CartIcon(
            iconColor: Theme.of(context).disabledColor,
          ),
          XMargin(8)
        ],
      ),
      body: DecoratedBox(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
        ),
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20)
                    .copyWith(top: 10),
                child: Text(widget.collection.name ?? 'Collection',
                    style: Theme.of(context).textTheme.headlineSmall),
              ),
            ),
            /* SliverPadding(
              padding: const EdgeInsets.only(top: 24, bottom: 3),
              sliver: SliverPersistentHeader(
                pinned: false,
                delegate: KSearchDelegate(
                    addPadding: true, position: 'Mobile Collection Detail'),
              ),
            ),*/
            BlocBuilder<VariantCollectionCubit, VariantCollectionState>(
              builder: (context, state) {
                Widget sliver;
                VariantCollectionState currentState = state;
                if (state.isLoading) {
                  sliver = SliverFillRemaining(
                      hasScrollBody: false, child: KLoader());
                } else if (!currentState.isLoading &&
                    currentState.isError == true &&
                    ((currentState.collection == null) ||
                        (currentState.collection?.variants == null) ||
                        (currentState.collection?.variants?.length == 0))) {
                  sliver = SliverFillRemaining(
                    hasScrollBody: false,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        KErrorScreen(state.errorCode, () {
                          handleErrorButton(context);
                        }, displayErrorCode: true)
                      ],
                    ),
                  );
                } else {
                  List<Variant> variants = state.collection?.variants ?? [];
                  itemCount = state.collection!.total;
                  int length = variants.length;
                  sliver = SliverPadding(
                    padding: EdgeInsets.only(left: 10, right: 10, top: 20),
                    sliver: SliverGrid(
                      delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          if (index >= length) {
                            return _cubit.state.collection!.variants!.length <
                                    itemCount!
                                ? KLoader()
                                : SizedBox.shrink();
                          }

                          Variant variant = variants[index];
                          return VariantGridItem(
                            key: ValueKey(variant.variantId),
                            variant: variant,
                            variantList: variants,
                            collectionName: title,
                            isGridView: true,
                            position: widget.position,
                          );
                        },
                        childCount: (state.hasReachedMax ||
                                length < variantCollectionsLimit)
                            ? length
                            : length + 1,
                      ),
                      gridDelegate: listDelegate,
                    ),
                  );
                }

                return sliver;
              },
            )
          ],
        ),
      ),
    );
  }
}

class ItemIconButton extends StatelessWidget {
  const ItemIconButton({
    super.key,
    required this.iconInfo,
    required this.iconSize,
    required this.handleTap,
  });

  final String iconInfo;
  final double iconSize;
  final VoidCallback handleTap;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      padding: EdgeInsets.all(0),
      onPressed: handleTap,
      icon: SvgPicture.asset(
        iconInfo,
        width: iconSize,
      ),
    );
  }
}
