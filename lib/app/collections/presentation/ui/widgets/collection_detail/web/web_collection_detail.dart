import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_state.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/margin.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import '../../variant_grid_item/variant_grid_item.dart';

const kItemPadding = 6.0;
const _kItemWidth = 185.0;
const _kItemHeight = 210.0;

class WebCollectionDetail extends StatefulWidget {
  final Collection collection;
  final bool showSearch;
  final String position;
  const WebCollectionDetail(this.collection, this.showSearch, this.position, {super.key});

  @override
  _ItemDetailState createState() => _ItemDetailState();
}

class _ItemDetailState extends State<WebCollectionDetail> {
  late VariantCollectionCubit _cubit;
  late UserCubit _userCubit;
  ScrollController? _scrollController;
  String? hexCode;
  int batch = 1;
  int? itemCount;

  @override
  void initState() {
    _cubit = BlocProvider.of<VariantCollectionCubit>(context, listen: false);
    _userCubit = context.read();
    _scrollController = ScrollController();
    _scrollController?.addListener(_onScroll);
    hexCode = _userCubit.currentUser?.outletLocation?.toPlus6Hex();
    if (_cubit.state.collection != null) {
      itemCount = _cubit.state.collection!.total;
    }
    _cubit.fetchVariantCollection(widget.collection, hexCode);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController?.position.pixels ==
        _scrollController?.position.maxScrollExtent) {
      if (_cubit.state.collection!.variants!.length < itemCount!) {
        batch++;
        _cubit.fetchMoreVariantCollection(hexCode, batch.toString());
      }
    }
  }

  final _delegate = const SliverGridDelegateWithMaxCrossAxisExtent(
      maxCrossAxisExtent: _kItemWidth,
      childAspectRatio: 1.0,
      mainAxisExtent: _kItemHeight,
      crossAxisSpacing: 10,
      mainAxisSpacing: 10);

  void _handleErrorButton(context) {
    BlocProvider.of<VariantCollectionCubit>(context, listen: false)
        .fetchVariantCollection(widget.collection, hexCode);
  }

  Widget _buildVariantCollection() {
    return BlocBuilder<VariantCollectionCubit, VariantCollectionState>(
      builder: (context, state) {
        Widget child = SizedBox.shrink();

        VariantCollectionState currentState = state;
        //todo empty state
        if (state.isLoading) {
          child = KLoader();
        } else if (currentState.isLoading != true &&
            currentState.isError == true &&
            ((currentState.collection == null) ||
                (currentState.collection?.variants == null) ||
                (currentState.collection?.variants?.length == 0))) {
          child = KErrorScreen(
            state.errorCode,
            () => _handleErrorButton(context),
            displayErrorCode: true,
          );
        } else {
          itemCount = state.collection!.total;
          List<Variant> variants = state.collection?.variants ?? [];
          int length = variants.length;
          child = Padding(
            padding: EdgeInsets.only(left: 10, right: 10, top: 20),
            child: GridView.builder(
              controller: _scrollController,
              shrinkWrap: true,
              gridDelegate: _delegate,
              itemCount: length,
              itemBuilder: (context, index) {
                if (index >= length) {
                  return _cubit.state.collection!.variants!.length < itemCount!
                      ? KLoader()
                      : SizedBox.shrink();
                }

                Variant variant = variants[index];
                return VariantGridItem(
                  key: ValueKey(variant.variantId),
                  variant: variant,
                  variantList: variants,
                  collectionName: widget.collection.name,
                  isGridView: true,
                  position: widget.position,
                );
              },
            ),
          );
        }

        return AnimatedSwitcher(
          duration: kThemeAnimationDuration,
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 30, right: 30, top: 20),
      constraints: BoxConstraints.expand(),
      alignment: Alignment.topCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            capitalize(widget.collection.name),
            style: KTextStyle.semiBold24.copyWith(fontSize: 32),
          ),
          YSpacing(10),
          Expanded(
            child: Align(
              alignment: Alignment.topCenter,
              child: _buildVariantCollection(),
            ),
          ),
        ],
      ),
    );
  }
}
