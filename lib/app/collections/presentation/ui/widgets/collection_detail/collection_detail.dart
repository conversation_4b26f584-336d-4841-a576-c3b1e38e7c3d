import 'package:flutter/cupertino.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_detail/mobile/mobile_collection_detail.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_detail/web/web_collection_detail.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class CollectionDetail extends StatelessWidget {
  final CollectionDetailArgs args;

  const CollectionDetail(this.args, {super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen:
          WebCollectionDetail(args.collection, args.showSearch, args.position),
      smallScreen: MobileCollectionDetail(
          args.collection, args.showSearch, args.position),
      mediumScreen: MobileCollectionDetail(
          args.collection, args.showSearch, args.position),
    );
  }
}

class CollectionDetailArgs {
  final Collection collection;
  final bool showSearch;
  final String position;

  CollectionDetailArgs(this.collection, this.showSearch, this.position);
}
