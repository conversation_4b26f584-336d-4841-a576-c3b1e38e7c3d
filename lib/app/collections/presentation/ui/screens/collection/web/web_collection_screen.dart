import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/index.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_list/collection_list.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

const home = 'home';
//const _kCollectionHeight = 580.0;

class WebCollectionScreen extends StatefulWidget {
  const WebCollectionScreen({super.key});

  @override
  _CollectionScreenState createState() => _CollectionScreenState();
}

class _CollectionScreenState extends State<WebCollectionScreen> {
  @override
  void initState() {
    Future.microtask(() {
      RetailOutlet? outlet =
          Provider.of<UserCubit>(context, listen: false).currentOutlet;
      BlocProvider.of<CollectionCubit>(context, listen: false)
          .fetchOutletCollection(outlet?.coordinates?.plusCode6Hex);
      // BlocProvider.of<PromotionCubit>(context, listen: false).syncPromotion();
    });
    super.initState();
  }

  void _handleErrorButton(context) {
    RetailOutlet? outlet =
        Provider.of<UserCubit>(context, listen: false).currentOutlet;
    if (outlet != null) {
      BlocProvider.of<CollectionCubit>(context, listen: false)
          .fetchOutletCollection(outlet.coordinates?.plusCode6Hex);
    }
    // BlocProvider.of<PromotionCubit>(context, listen: false)
    //     .fetchPromotion(outlet);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CollectionCubit, CollectionState>(
      builder: (context, state) {
        final List<Widget> slivers = [];
        if (state.isLoading) {
          slivers.add(
            Center(
              child: KLoader(),
            ),
          );
        } else if (state.isError! && state.result == null) {
          slivers.add(
            KErrorScreen(
              state.errorCode,
              () => _handleErrorButton(context),
              displayErrorCode: true,
            ),
          );
        } else if (state.availableInYourArea != null) {
          if (state.availableInYourArea!) {
            slivers.add(
              Column(
                mainAxisSize: MainAxisSize.min,
                children: state.result!.collections!
                    .map(
                      (e) => CollectionList(
                        key: PageStorageKey(e.id),
                        collection: e,
                        position: home,
                      ),
                    )
                    .toList(),
              ),
            );
          } else {
            slivers.add(
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(kSvgloanFailed),
                    YMarginScale(0.07),
                    SizedBox(
                      width: screenWidth(context, percent: 0.5),
                      child: Text(
                        "Sorry, we are unavailable in your location",
                        style: KTextStyle.subtitleTitleText,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        } else {
          slivers.add(
            Center(
              child: KLoader(),
            ),
          );
        }
        return SingleChildScrollView(
          key: PageStorageKey("home_collections"),
          padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
          child: slivers.first,
        );
      },
    );
  }
}
