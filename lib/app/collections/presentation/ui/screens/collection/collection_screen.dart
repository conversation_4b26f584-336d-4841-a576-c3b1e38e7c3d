import 'package:flutter/cupertino.dart';
import 'package:shop/app/collections/presentation/ui/screens/collection/mobile/mobile_collection_screen.dart';
import 'package:shop/app/collections/presentation/ui/screens/collection/web/web_collection_screen.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class CollectionScreen extends StatelessWidget {
  const CollectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: const WebCollectionScreen(),
      mediumScreen: const MobileCollectionScreen(),
      smallScreen: const MobileCollectionScreen(),
    );
  }
}
