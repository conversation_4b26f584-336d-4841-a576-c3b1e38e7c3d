import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/index.dart';
import 'package:shop/app/collections/presentation/ui/widgets/collection_list/collection_list.dart';
import 'package:shop/app/homepage/presentation/logic/bloc/brands_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/base_loan_cubit.dart';
import 'package:shop/src/components/components.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/widgets/placeholders.dart';
import 'package:shop/src/res/assets/svgs/svgs.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

const home = 'home';

class MobileCollectionScreen extends StatelessWidget {
  const MobileCollectionScreen({super.key});

  Future<void> _handleErrorButton(BuildContext context) async {
    final userCubit = context.read<UserCubit>();

    if (userCubit.isAnonymous) {
      fetch(context);
      return;
    }

    fetch(context);
    await userCubit.updateOutlet();

    if (userCubit.isNgUser) {
      if (context.mounted) {
        context.read<BaseLoanCubit>().syncState(userCubit.currentOutlet?.id);
      }
    }
  }

  void fetch(BuildContext context) {
    BlocProvider.of<BrandsCubit>(context, listen: false).fetchBrands();
    final outlet = context.read<UserCubit>().currentOutlet;
    context
        .read<CollectionCubit>()
        .fetchOutletCollection(outlet?.coordinates?.plusCode6Hex, true);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return BlocConsumer<CollectionCubit, CollectionState>(
      listener: (context, state) {
        if (state.isError == true && state.result != null) {
          Toast.error(state.errorMessage, context, duration: 2);
        }
      },
      builder: (context, state) {
        Widget child = const SizedBox.shrink();
        if (state.isLoading) {
          child = Shimmer.fromColors(
            baseColor: Colors.grey.withValues(alpha: 0.3),
            highlightColor: Theme.of(context).colorScheme.primaryContainer,
            child: const Column(
              children: [
                YMargin(16),
                GridItemListShimmer(),
              ],
            ),
          );
        } else if (state.isError! && state.result == null) {
          child = KErrorScreen(
            state.errorCode,
            () => _handleErrorButton(context),
            displayErrorCode: true,
          );
        } else {
          if (state.availableInYourArea == true &&
              state.result != null &&
              state.result!.isNotEmpty) {
            child = ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: state.result!.collections!.length,
              // itemExtent: _kCollectionHeight,
              itemBuilder: (context, i) {
                return Column(
                  children: [
                    CollectionList(
                      key: PageStorageKey(state.result!.collections![i].id),
                      collection: state.result!.collections![i],
                      position: home,
                    ),
                    i != state.result!.collections!.length - 1
                        ? const SizedBox(
                            height: 10,
                          )
                        : const SizedBox.shrink(),
                  ],
                );
              },
              padding: EdgeInsets.zero,
            );
          } else {
            child = Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SvgPicture.asset(kSvgEmptyCollectionIcon),
                  const YMargin(20),
                  Text(
                    "No items for your location",
                    style: textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  const YMargin(10),
                  Text(
                    "We’re expanding quickly so please \ncheck back soon",
                    style: textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }
        }

        // else {
        //   child = Center(
        //     child: KLoader(),
        //   );
        // }

        return AnimatedSwitcher(
          key: const PageStorageKey("home_collections"),
          duration: kThemeAnimationDuration,
          switchInCurve: Curves.easeOut,
          switchOutCurve: Curves.fastOutSlowIn,
          transitionBuilder: (Widget widget, Animation<double> animation) {
            return SizeTransition(
              axis: Axis.vertical,
              axisAlignment: -1.0,
              sizeFactor: animation,
              child: widget,
            );
          },
          child: child,
        );
      },
    );
  }
}
