import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/presentation/logic/bloc/new_items_cubit.dart';
import 'package:shop/app/collections/presentation/ui/widgets/variant_grid_item/variant_grid_item.dart';
import 'package:shop/app/my_cart/presentation/ui/widget/cart_icon.dart';
import 'package:shop/app/notification/presentation/ui/widgets/notification_bell.dart';
import 'package:shop/src/components/src/buttons/src/k_back_button.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/loader/loader.dart';
import 'package:shop/src/components/src/widgets/empty_state/empty_widget.dart';
import 'package:shop/src/components/src/widgets/search_widget.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:shop/src/res/values/styles/text_style.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_src/scaler/src/margin.dart';

const title = 'What’s New';
const subtitle = 'The latest items for you to restock your outlet.';

class MobileNewItemsScreen extends StatefulWidget {
  const MobileNewItemsScreen({super.key});

  @override
  _NewItemsScreenState createState() => _NewItemsScreenState();
}

class _NewItemsScreenState extends State<MobileNewItemsScreen> {
  ScrollController? _scrollController;
  late RetailOutlet? outlet;
  late NewItemsParams params;
  late NewItemsCubit _cubit;
  num? totalItems;

  @override
  void initState() {
    _cubit = BlocProvider.of<NewItemsCubit>(context, listen: false);
    _scrollController = ScrollController();
    _scrollController!.addListener(_onScroll);
    outlet = context.read<UserCubit>().currentOutlet;
    params = NewItemsParams(
      hexCode: outlet?.coordinates?.plusCode6Hex,
      batch: 1,
    );
    _cubit.fetchItems(params);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController!.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController!.position.pixels ==
        _scrollController!.position.maxScrollExtent) {
      _cubit.fetchMoreItems(outlet?.coordinates?.toPlus6Hex());
    }
  }

  List<Widget> _buildHeaderSlivers(
      BuildContext context, bool innerBoxIsScrolled) {
    return <Widget>[
      SliverToBoxAdapter(
        child: Container(
          padding: EdgeInsets.only(left: 20, right: 20, bottom: 25, top: 20),
          child: buildIntro(context),
          // color: Colors.white,
        ),
      ),
      SliverOverlapAbsorber(
        // This widget takes the overlapping behavior of the SliverAppBar,
        // and redirects it to the SliverOverlapInjector below. If it is
        // missing, then it is possible for the nested "inner" scroll view
        // below to end up under the SliverAppBar even when the inner
        // scroll view thinks it has not been scrolled.
        // This is not necessary if the "headerSliverBuilder" only builds
        // widgets that do not overlap the next sliver.
        handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
        sliver: SliverPersistentHeader(
          pinned: false,
          delegate: KSearchDelegate(addPadding: true, position: 'New Items'),
        ),
      ),
    ];
  }

  void _handleErrorButton(context) {
    _cubit.fetchItems(params);
  }

  @override
  Widget build(BuildContext context) {
    getHeight() {
      double height = MediaQuery.of(context).size.height;

      return height <= 545
          ? 37
          : (height > 545 && height <= 600)
              ? 32
              : 34;
    }

    final listDelegate = SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 8.0,
        crossAxisSpacing: 8.0,
        childAspectRatio: 33 / getHeight());

    Widget buildVariantList() {
      return BlocConsumer<NewItemsCubit, NewItemsState>(
        listener: (context, state) {
          if (!state.isLoading) {
            setState(() {
              totalItems = state.total;
            });
          }
        },
        builder: (context, state) {
          Widget child = SizedBox.shrink();

          if (state.isLoading) {
            child = KLoader();
          } else if (!state.isLoading &&
              state.isError == true &&
              state.variants == null) {
            child = KErrorScreen(
              state.errorCode,
              () => _handleErrorButton(context),
              displayErrorCode: true,
            );
          } else {
            List<Variant>? variants = state.variants;
            int length = variants?.length ?? 0;

            if (length == 0) {
              child = EmptyAssetWidget(
                assetUrl: kSvgWhatsNewIcon,
                message: 'No new items now',
                width: 70,
                height: 70,
              );
            } else {
              child = GridView.builder(
                controller: _scrollController,
                shrinkWrap: true,
                gridDelegate: listDelegate,
                itemCount: (state.hasReachedMax || length < variantListLimit)
                    ? length
                    : length + 1,
                itemBuilder: (context, index) {
                  if (index >= length) {
                    return KLoader();
                  }

                  Variant variant = variants![index];
                  return VariantGridItem(
                    key: ValueKey(variant.variantId),
                    variant: variant,
                    variantList: variants,
                    collectionName: title,
                    isGridView: true,
                    position: '',
                  );
                },
              );
            }
          }

          return AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            switchInCurve: Curves.easeOut,
            switchOutCurve: Curves.fastOutSlowIn,
            transitionBuilder: (Widget widget, Animation<double> animation) {
              return SizeTransition(
                axis: Axis.vertical,
                axisAlignment: -1.0,
                sizeFactor: animation,
                child: widget,
              );
            },
            child: child,
          );
        },
      );
    }

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: KBackButton(),
        actions: [
          CartIcon(
            iconColor: Theme.of(context).disabledColor,
          ),
          NotificationBell(),
        ],
      ),
      body: NestedScrollView(
        headerSliverBuilder: _buildHeaderSlivers,
        body: Padding(
          padding: EdgeInsets.only(left: 10, right: 10, top: 20),
          child: buildVariantList(),
        ),
      ),
    );
  }

  Widget buildIntro(BuildContext context) {
    TextStyle style = KTextStyle.bodyText2;

    final text = totalItems != null
        ? RichText(
            text: TextSpan(
              style: KTextStyle.subtitleTitleText,
              children: <TextSpan>[
                TextSpan(text: 'We have a total of '),
                TextSpan(
                    text: '$totalItems ',
                    style: style.copyWith(fontWeight: FontWeight.w500)),
                TextSpan(text: 'new items, view to restock your outlet.'),
              ],
            ),
          )
        : Text('The latest items for you to restock your outlet.');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "What's New",
          style: KTextStyle.headerTitleText,
        ),
        YMargin(20),
        text,
      ],
    );
  }
}
