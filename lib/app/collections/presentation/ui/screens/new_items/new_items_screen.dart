import 'package:flutter/cupertino.dart';
import 'package:shop/app/collections/presentation/ui/screens/new_items/mobile/mobile_new_items_screen.dart';
import 'package:shop/app/collections/presentation/ui/screens/new_items/web/web_new_items_screen.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/responsive/responsive_design.dart';

class NewItemsScreen extends StatelessWidget {
  const NewItemsScreen({super.key});
  final routeName = WhatsNewPath;

  @override
  Widget build(BuildContext context) {
    return ResponsiveDesign(
      largeScreen: WebNewItemsScreen(),
      mediumScreen: MobileNewItemsScreen(),
      smallScreen: MobileNewItemsScreen(),
    );
  }
}
