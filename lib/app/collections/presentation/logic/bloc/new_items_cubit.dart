import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_new_items.dart';
import 'package:shop/src/components/src/utils/values.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/service_exceptions/src/api_exceptions.dart';

part 'new_items_state.dart';

class NewItemsCubit extends Cubit<NewItemsState> {
  final FetchNewItems _fetchNewItems;
  NewItemsCubit(this._fetchNewItems) : super(NewItemsState.initial());

  /// Returns newly added product items.
  Future<void> fetchItems(NewItemsParams params) async {
    emit(state.copyWith(
        isLoading: true,
        isError: false,
        errorMessage: null,
        batch: params.batch));
    _mapNewItemsToState(params);
  }

  /// Returns newly added product items.
  Future<void> fetchMoreItems(String? hexCode) async {
    final NewItemsState currentState = state;

    if (currentState.paginationLoading ||
        currentState.isLoading ||
        currentState.hasReachedMax) {
      return;
    }

    int batch = currentState.batch + 1;

    emit(NewItemsState.paginationLoading(
        currentState.variants, currentState.total, batch));

    NewItemsParams params = NewItemsParams(
      hexCode: hexCode,
      batch: batch,
    );

    _mapMoreNewItemsToState(params);
  }

  Future<void> _mapNewItemsToState(NewItemsParams params) async {
    final currentState = state;
    final res = await _fetchNewItems(params);

    res.maybeWhen(
      success: (data) {
        emit(NewItemsState.loaded(
          data.variants,
          data.total,
          currentState.batch,
        ));
      },
      apiFailure: (error, _) {
        String errorMessage = ApiExceptions.getErrorMessage(error);
        emit(NewItemsState.error(
          currentState.variants,
          currentState.total,
          errorMessage,
        ));
      },
      orElse: () {
        emit(NewItemsState.error(
          currentState.variants,
          currentState.total,
          UNEXPECTED_ERROR,
        ));
      },
    );
  }

  Future<void> _mapMoreNewItemsToState(NewItemsParams params) async {
    final currentState = state;
    final res = await _fetchNewItems(params);

    res.maybeWhen(
      success: (data) {
        List<Variant> aggregateVariants = [
          ...?currentState.variants,
          ...data.variants
        ];

        return emit(currentState.copyWith(
          variants: aggregateVariants,
          paginationLoading: false,
          hasReachedMax:
              data.variants.length < variantListLimit || data.variants.isEmpty,
        ));
      },
      apiFailure: (error, _) {
        String errorMessage = ApiExceptions.getErrorMessage(error);
        emit(NewItemsState.error(
          currentState.variants,
          currentState.total,
          errorMessage,
        ));
      },
      orElse: () {
        emit(NewItemsState.error(
          currentState.variants,
          currentState.total,
          UNEXPECTED_ERROR,
        ));
      },
    );
  }
}
