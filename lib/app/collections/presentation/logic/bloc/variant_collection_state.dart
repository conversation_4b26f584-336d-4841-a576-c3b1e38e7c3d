import 'package:flutter/foundation.dart';
import 'package:shop/app/browse/data/models/collection.dart';

@immutable
class VariantCollectionState {
  final bool isLoading;
  final Collection? collection;
  final bool? isError;
  final String? errorMessage;
  final String? errorCode;
  final bool paginationLoading;
  final bool hasReachedMax;
  final bool hasLoaded;

  const VariantCollectionState({
    required this.isLoading,
    required this.paginationLoading,
    this.collection,
    this.isError,
    this.errorMessage,
    this.errorCode,
    required this.hasReachedMax,
    required this.hasLoaded,
  });

  factory VariantCollectionState.initialState() {
    return VariantCollectionState(
      isLoading: false,
      hasLoaded: false,
      isError: false,
      collection: null,
      errorMessage: null,
      hasReachedMax: false,
      paginationLoading: false,
    );
  }

  factory VariantCollectionState.paginationLoading(Collection? collection) {
    return VariantCollectionState(
        isLoading: false,
        hasLoaded: true,
        isError: false,
        paginationLoading: true,
        collection: collection,
        hasReachedMax: false,
        errorMessage: null);
  }

  factory VariantCollectionState.variantCollectionLoadError(
    String errorCode,
    String errorMessage,
    Collection? collection,
  ) {
    return VariantCollectionState(
      isLoading: false,
      hasLoaded: false,
      errorMessage: errorMessage,
      errorCode: errorCode,
      collection: collection,
      isError: true,
      paginationLoading: false,
      hasReachedMax: false,
    );
  }

  VariantCollectionState copyWith({
    bool? isLoading,
    Collection? collection,
    bool? paginationLoading,
    int? transactionsCount,
    bool? isError,
    String? errorMessage,
    String? errorCode,
    bool? hasReachedMax,
    bool? hasLoaded,
  }) {
    return VariantCollectionState(
        isLoading: isLoading ?? this.isLoading,
        hasReachedMax: hasReachedMax ?? this.hasReachedMax,
        paginationLoading: paginationLoading ?? this.paginationLoading,
        collection: collection ?? this.collection,
        errorCode: errorCode ?? this.errorCode,
        isError: isError ?? this.isError,
        errorMessage: errorMessage ?? this.errorMessage,
        hasLoaded: hasLoaded ?? this.hasLoaded);
  }

  @override
  String toString() {
    return '''VariantCollectionState {
      isLoading: $isLoading,
      Collection: $Collection,
      isError: $isError,
      errorMessage: $errorMessage,
      errorCode: $errorCode,
      paginationLoading : $paginationLoading,
      hasReachedMax: $hasReachedMax,
      hasLoaded: $hasLoaded,
    }''';
  }
}
