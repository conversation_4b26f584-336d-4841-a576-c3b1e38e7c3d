part of 'new_items_cubit.dart';

class NewItemsState {
  final List<Variant>? variants;
  final int total;
  final bool isLoading;
  final bool? isError;
  final String? errorMessage;
  final String? errorCode;
  final bool paginationLoading;
  final bool hasReachedMax;
  final int batch;

  NewItemsState({
    this.variants,
    required this.total,
    required this.isLoading,
    required this.paginationLoading,
    this.isError,
    this.errorMessage,
    this.errorCode,
    required this.hasReachedMax,
    required this.batch,
  });

  factory NewItemsState.initial() {
    return NewItemsState(
      variants: null,
      total: 0,
      isLoading: false,
      isError: false,
      errorMessage: null,
      hasReachedMax: false,
      paginationLoading: false,
      batch: 1,
    );
  }

  factory NewItemsState.loaded(List<Variant> variants, int total, int batch) {
    return NewItemsState(
      variants: variants,
      total: total,
      isLoading: false,
      isError: false,
      errorMessage: null,
      hasReachedMax: variants.length < variantListLimit || variants.isEmpty,
      paginationLoading: false,
      batch: batch,
    );
  }

  factory NewItemsState.paginationLoading(
      List<Variant>? variants, int total, int batch) {
    return NewItemsState(
      variants: variants,
      total: total,
      isLoading: false,
      isError: false,
      paginationLoading: true,
      hasReachedMax: false,
      errorMessage: null,
      batch: batch,
    );
  }

  factory NewItemsState.error(
    List<Variant>? variants,
    int total,
    String errorMessage,
  ) {
    return NewItemsState(
      variants: variants,
      total: total,
      isLoading: false,
      errorMessage: errorMessage,
      errorCode: errorMessage,
      isError: true,
      paginationLoading: false,
      hasReachedMax: false,
      batch: 1,
    );
  }

  NewItemsState copyWith({
    List<Variant>? variants,
    int? total,
    bool? isLoading,
    bool? paginationLoading,
    int? transactionsCount,
    bool? isError,
    String? errorMessage,
    String? errorCode,
    bool? hasReachedMax,
    int? batch,
  }) {
    return NewItemsState(
      variants: variants ?? this.variants,
      total: total ?? this.total,
      isLoading: isLoading ?? this.isLoading,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      paginationLoading: paginationLoading ?? this.paginationLoading,
      errorCode: errorCode ?? this.errorCode,
      isError: isError ?? this.isError,
      errorMessage: errorMessage ?? this.errorMessage,
      batch: batch ?? this.batch,
    );
  }

  @override
  String toString() {
    return '''NewItemsState {
      variants: $variants,
      total: $total,
      isLoading: $isLoading,
      isError: $isError,
      errorMessage: $errorMessage,
      errorCode: $errorCode,
      paginationLoading : $paginationLoading,
      hasReachedMax: $hasReachedMax,
      batch: $batch,
    }''';
  }
}
