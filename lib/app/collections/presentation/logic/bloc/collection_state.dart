import 'package:flutter/foundation.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';

@immutable
class CollectionState {
  final bool isLoading;
  final bool? isError;
  final String? errorMessage;
  final String? errorCode;
  final bool? availableInYourArea;
  final CollectionResult? result;

  const CollectionState({
    required this.isLoading,
    this.isError,
    this.errorMessage,
    this.errorCode,
    this.availableInYourArea,
    this.result,
  });

  factory CollectionState.initialState() {
    return CollectionState(
      isLoading: false,
      isError: false,
      errorMessage: null,
      errorCode: null,
      availableInYourArea: null,
      result: null,
    );
  }

  factory CollectionState.     loadError(String errorMessage, String erroCode,
      CollectionResult? result, bool? availableInYourArea) {
    return CollectionState(
      isLoading: false,
      isError: true,
      errorMessage: errorMessage,
      errorCode: erroCode,
      result: result,
      availableInYourArea: availableInYourArea,
    );
  }

  CollectionState copyWith({
    bool? isLoading,
    bool? isError,
    String? errorMessage,
    String? errorCode,
    bool? availableInYourArea,
    CollectionResult? result,
  }) {
    return CollectionState(
      isLoading: isLoading ?? this.isLoading,
      isError: isError ?? this.isError,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      availableInYourArea: availableInYourArea ?? this.availableInYourArea,
      result: result ?? this.result,
    );
  }

  @override
  String toString() {
    return '''BestSelling state {
      isLoading: $isLoading,
      isError: $isError,
      errorMessage: $errorMessage,
      errorCode: $errorCode,
      availableInYourArea: $availableInYourArea,
      result: $result,
    }''';
  }
}
