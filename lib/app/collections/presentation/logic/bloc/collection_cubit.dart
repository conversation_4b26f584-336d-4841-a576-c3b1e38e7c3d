import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shop/app/authentication/data/models/anonymous_user.dart';
import 'package:shop/app/authentication/presentation/listeners/on_anonymous_login.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/listeners/on_login.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/browse_item.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_collection.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'collection_state.dart';

class CollectionCubit extends Cubit<CollectionState>
    implements OnLogin, OnAnonymousLogin, OnBeforeLogout {
  final FetchCollection _fetchCollection;
  RetailOutlet? _outlet;
  String? _hexCode;
  final drawerItem = BehaviorSubject<BrowseItem>.seeded(browseItems.first);
  CollectionCubit(this._fetchCollection)
      : super(CollectionState.initialState());

  @override
  Future<void> onLogin(User user) async {
    final outlet = user.currentRetailOutlet;
    final hexCode = outlet?.coordinates?.plusCode6Hex;

    if (outlet == null) return;

    _outlet = outlet;
    _hexCode = hexCode;
    fetchOutletCollection(hexCode, true);
  }

  @override
  Future<void> onAnonymousLogin(AnonymousUser user) async {
    _hexCode = user.hexCode;
    fetchOutletCollection(user.hexCode, true);
  }

  @override
  Future<void> onBeforeLogout() async {
    _outlet = null;
    emit(CollectionState.initialState());
  }

  // Fetch RetailOutlet Collection with HTTP API.
  Future<void> syncOutletCollection([bool reload = false]) async {
    if (_outlet == null && UserCubit.instance?.isAnonymous != true) return;
    final CollectionState currentState = state;
    return await _mapResultToState(
        currentState, CollectionParams(_outlet, reload), _hexCode);
  }

  /// Fetch RetailOutlet Collection with HTTP API.
  Future<void> fetchOutletCollection(String? hexCode,
      [bool reload = false]) async {
    if (_outlet == null && UserCubit.instance?.isAnonymous != true) return;
    final CollectionState currentState = state;
    emit(currentState.copyWith(isLoading: true, isError: false));
    return await _mapResultToState(
        currentState, CollectionParams(_outlet, reload), hexCode);
  }

  // /// Refresh RetailOutlet Collection with HTTP API.
  // Future syncOutletCollection() async {
  //   if (currentParams != null) {
  //     final CollectionState currentState = state;
  //     if (currentState.isLoading) return;
  //     _mapResultToState(currentState, currentParams!);
  //   }
  // }

  Future<void> _mapResultToState(CollectionState currentState,
      CollectionParams params, String? hexCode) async {
    // if (currentState.isLoading) return;
    final res = await _fetchCollection(params);

    res.when(
      success: (data) {
        emit(currentState.copyWith(
            isLoading: false,
            isError: false,
            availableInYourArea: data!.availableInArea,
            result: data));
        // _handleInventorySync(data, hexCode);
        return;
      },
      apiFailure: (error, _) {
        String errorMessage = ApiExceptions.getErrorMessage(error);
        emit(CollectionState.loadError(errorMessage, errorMessage,
            currentState.result, currentState.availableInYourArea));
        return;
      },
    );
  }

  // Future<void> _handleInventorySync(
  //   CollectionResult? data,
  //   String? hexCode,
  // ) async {
  //   final collections = data?.collections ?? [];
  //   if (collections.isEmpty || hexCode == null) return;

  //   final List<Collection> updatedCollections = [];

  //   for (final collection in collections) {
  //     try {
  //       final res = await locator.get<FetchVariantInventoryList>().call(
  //             VariantInventoryListParams(
  //               hexCode: hexCode,
  //               variantIds: collection.validVariantIds,
  //             ),
  //           );

  //       res.when(
  //         success: (inventories) {
  //           final validVariants = collection.validVariants;
  //           final _updatedVariants =
  //               updateVariantsWithInventory(validVariants, inventories);
  //           updatedCollections
  //               .add(collection.copyWith(variants: _updatedVariants));
  //         },
  //         apiFailure: (error, _) {
  //           updatedCollections.add(collection);
  //           ErrorHandler.report(
  //             error,
  //             null,
  //             hint: {
  //               'info':
  //                   'Api Failure during inventory fetch for home collection: ${collection.id}'
  //             },
  //           );
  //         },
  //       );
  //     } catch (error) {
  //       updatedCollections.add(collection);
  //       ErrorHandler.report(
  //         error,
  //         StackTrace.current,
  //         hint: {
  //           'info':
  //               'Exception during inventory fetch for home collection: ${collection.id}'
  //         },
  //       );
  //     }
  //   }

  //   final filteredCollection = updatedCollections
  //       .where((e) => e.variants != null && e.variants!.isNotEmpty)
  //       .toList();

  //   final updatedResult = data?.copyWith(collections: filteredCollection);
  //   final currentState = state.copyWith(
  //     isLoading: false,
  //     isError: false,
  //     availableInYourArea: data!.availableInArea,
  //     result: updatedResult,
  //   );
  //   emit(currentState);
  // }

  @override
  Future<void> close() {
    _outlet = null;
    _hexCode = null;
    drawerItem.close();
    return super.close();
  }
}
