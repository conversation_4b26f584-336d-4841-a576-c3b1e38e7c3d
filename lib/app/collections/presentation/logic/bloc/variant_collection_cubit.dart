import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shop/app/authentication/presentation/listeners/on_before_logout.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_more_variant_collection.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_variant_collection.dart';
import 'package:td_commons_flutter/models/cache.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'variant_collection_state.dart';

const variantCollectionsLimit = 30;

class VariantCollectionCache extends CacheData<Collection> {
  VariantCollectionCache();
}

class VariantCollectionCubit extends Cubit<VariantCollectionState>
    implements OnBeforeLogout {
  final FetchVariantCollection? _fetchVariantCollection;
  final FetchMoreVariantCollection? _fetchMoreVariantCollection;
  final VariantCollectionCache variantCollectionCache;

  VariantCollectionCubit(this._fetchVariantCollection,
      this._fetchMoreVariantCollection, this.variantCollectionCache)
      : super(VariantCollectionState.initialState());

  void init() {
    variantCollectionCache.clear();
    emit(VariantCollectionState.initialState());
  }

  void setVariantCollection(Collection collection,
      [String? hexCode, bool? silent]) {
    final VariantCollectionState currentState = state;
    emit(currentState.copyWith(
      collection: collection.copyWith(variants: collection.validVariants),
      isError: false,
      isLoading: false,
      hasReachedMax: collection.variants!.length < variantCollectionsLimit ||
          collection.variants == [],
      paginationLoading: false,
    ));
  }

  /// Fetch a list a `product collection`  with HTTP API.
  ///
  /// Returns a paginated list.
  Future fetchVariantCollection(Collection collection, String? hexCode,
      [bool silent = false]) async {
    final VariantCollectionState currentState = state;

    final cachedCollection =
        variantCollectionCache.get('${hexCode}_${collection.id}');

    // if (currentState.isLoading) return;

    RetailOutlet? outlet = UserCubit.instance?.currentOutlet;

    if (outlet == null && UserCubit.instance?.isAnonymous != true) {
      final msg = 'outlet is null, please refresh app or login again';
      emit(VariantCollectionState.variantCollectionLoadError(
          msg, msg, currentState.collection));
      return;
    }

    // if (!silent) {
    emit(currentState.copyWith(
        isError: false,
        isLoading: true,
        hasReachedMax: false,
        hasLoaded: false));
    // }

    if (cachedCollection == null) {
      VariantCollectionParams params = VariantCollectionParams(
        id: collection.id,
        hexCode: hexCode,
        outletId: outlet?.id,
        customerGroup: outlet?.customerGroup,
      );

      final result = await _fetchVariantCollection!(params);

      result.when(
        success: (Collection data) {
          Collection newCollection = (data.variants == null ||
                  (data.variants != null && data.variants!.isEmpty))
              ? collection
              : data;
          newCollection =
              newCollection.copyWith(variants: newCollection.validVariants);
          variantCollectionCache.set(
              '${hexCode}_${collection.id}', newCollection);
          // _handleInventorySync(newCollection, hexCode);
          return emit(currentState.copyWith(
            collection: newCollection,
            isLoading: false,
            isError: false,
            hasLoaded: true,
            hasReachedMax: data.variants!.length < variantCollectionsLimit ||
                data.variants == [],
            paginationLoading: false,
          ));
        },
        apiFailure: (error, _) {
          // _handleInventorySync(currentState.collection, hexCode);
          String message = ApiExceptions.getErrorMessage(error);
          emit(VariantCollectionState.variantCollectionLoadError(
              message, message, currentState.collection));
        },
      );
    } else {
      // _handleInventorySync(cachedCollection, hexCode);
      emit(
        currentState.copyWith(
          collection: cachedCollection,
          isLoading: false,
          isError: false,
          hasLoaded: true,
          hasReachedMax:
              cachedCollection.variants!.length < variantCollectionsLimit ||
                  cachedCollection.variants!.isEmpty,
        ),
      );
    }
  }

  ///Web implementation for categories browse
  Future fetchVariantCollectionList(String id, String? hexCode) async {
    final VariantCollectionState currentState = state;

    final cachedCollection = variantCollectionCache.get('${hexCode}_$id');

    if (currentState.isLoading) return;

    RetailOutlet? outlet = UserCubit.instance?.currentOutlet;

    if (outlet == null) return;

    emit(currentState.copyWith(
        isError: false, isLoading: true, hasReachedMax: false));

    if (cachedCollection == null) {
      VariantCollectionParams params = VariantCollectionParams(
        id: id,
        hexCode: hexCode,
        outletId: outlet.id!,
        customerGroup: outlet.customerGroup,
      );

      final result = await _fetchVariantCollection!(params);

      result.when(
        success: (Collection data) {
          Collection? newCollection = (data.variants == null ||
                  (data.variants != null && data.variants!.isEmpty))
              ? null
              : data;
          if (newCollection != null) {
            newCollection =
                newCollection.copyWith(variants: newCollection.validVariants);
          }

          variantCollectionCache.set('${hexCode}_$id', newCollection!);
          // _handleInventorySync(newCollection, hexCode);
          return emit(currentState.copyWith(
            collection: newCollection,
            isLoading: false,
            isError: false,
            hasReachedMax: data.variants!.length < variantCollectionsLimit ||
                data.variants == [],
            paginationLoading: false,
          ));
        },
        apiFailure: (error, _) {
          // _handleInventorySync(currentState.collection, hexCode);
          String message = ApiExceptions.getErrorMessage(error);
          emit(VariantCollectionState.variantCollectionLoadError(
              message, message, currentState.collection));
        },
      );
    } else {
      // _handleInventorySync(cachedCollection, hexCode);
      emit(
        currentState.copyWith(
          collection: cachedCollection,
          isLoading: false,
          isError: false,
          hasReachedMax:
              cachedCollection.variants!.length < variantCollectionsLimit ||
                  cachedCollection.variants!.isEmpty,
        ),
      );
    }
  }

  /// Returns a list a `product collection`  with HTTP API.
  ///
  /// Returns a paginated list.
  Future fetchMoreVariantCollection(String? hexCode, String batch) async {
    final VariantCollectionState currentState = state;

    if (currentState.paginationLoading ||
        currentState.isLoading ||
        currentState.hasReachedMax) {
      return;
    }

    emit(VariantCollectionState.paginationLoading(currentState.collection));

    RetailOutlet? outlet = UserCubit.instance?.currentOutlet;

    MoreVariantCollectionParams params = MoreVariantCollectionParams(
      id: currentState.collection?.id,
      plusCode6Hex: hexCode,
      // lastId: lastCollectionVariant.variantId,
      batch: batch,
      outletId: outlet?.id,
      customerGroup: outlet?.customerGroup,
    );

    final result = await _fetchMoreVariantCollection!(params);

    result.when(
      success: (Collection data) {
        List<Variant> aggregateVariants = [
          ...?currentState.collection?.variants,
          ...?data.variants
        ];

        Collection aggregateCollection = currentState.collection!.copyWith(
            variants: aggregateVariants.where((e) => !e.isOutOfStock).toList());

        variantCollectionCache.set(
            '${hexCode}_${aggregateCollection.id}', aggregateCollection);

        // _handleInventorySync(aggregateCollection, hexCode);

        return emit(currentState.copyWith(
          collection: aggregateCollection,
          paginationLoading: false,
          hasReachedMax: ((data.variants!.length < variantCollectionsLimit) ||
              (data.variants == [])),
        ));
      },
      apiFailure: (error, _) {
        // _handleInventorySync(currentState.collection, hexCode);
        String message = ApiExceptions.getErrorMessage(error);
        emit(VariantCollectionState.variantCollectionLoadError(
            message, message, currentState.collection));
      },
    );
  }

  // Future<void> _handleInventorySync(
  //     Collection? collection, String? hexCode) async {
  //   if (collection == null ||
  //       collection.validVariants.isEmpty ||
  //       hexCode == null) return;

  //   try {
  //     final res = await locator.get<FetchVariantInventoryList>().call(
  //           VariantInventoryListParams(
  //             hexCode: hexCode,
  //             variantIds: collection.validVariantIds,
  //           ),
  //         );

  //     res.when(
  //       success: (inventories) {
  //         final validVariants = collection.validVariants;
  //         final _updatedVariants =
  //             updateVariantsWithInventory(validVariants, inventories);

  //         final currentState = state.copyWith(
  //           collection: collection.copyWith(variants: _updatedVariants),
  //           isLoading: false,
  //           isError: false,
  //           hasReachedMax: state.hasReachedMax,
  //         );

  //         emit(currentState);
  //       },
  //       apiFailure: (error, _) {
  //         ErrorHandler.report(
  //           error,
  //           null,
  //           hint: {
  //             'info':
  //                 'Api Failure during inventory fetch for variant collection: ${collection.id}'
  //           },
  //         );
  //       },
  //     );
  //   } catch (error) {
  //     ErrorHandler.report(
  //       error,
  //       StackTrace.current,
  //       hint: {
  //         'info':
  //             'Exception during inventory fetch for variant collection: ${collection.id}'
  //       },
  //     );
  //   }
  // }

  @override
  Future<void> onBeforeLogout() async {
    init();
  }

  @override
  Future<void> close() {
    init();
    return super.close();
  }
}
