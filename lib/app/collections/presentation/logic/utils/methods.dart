import 'package:flutter/widgets.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/src/components/src/utils/utils.dart';
import 'package:td_commons_flutter/enums/reward_type.dart';
import 'package:td_commons_flutter/models/promotion.dart';
import 'package:td_commons_flutter/models/variant.dart';

Color? color(String? hexCode) {
  if (hexCode == null) return null;
  final colorCode = hexCode.startsWith('#') ? hexCode.substring(1) : hexCode;
  return Color(int.parse(colorCode, radix: 16) + 0xFF000000);
}

double calculatePercentage(num number, num percentage) {
  return (percentage / 100) * number;
}

bool isPriceReductionPromo(Variant variant) {
  if (variant.hasPromo && customerIsEligible(variant)) {
    final type = variant.promotion!.rewards.first.type;
    if (type == RewardType.itemFixedAmount ||
        type == RewardType.itemPercentage) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}

num priceReductionPromoPrice(Promotion promotion, num variantPrice) {
  final type = promotion.rewards.first.type;

  if (type == RewardType.itemFixedAmount) {
    final value = promotion.rewards.first.value;
    final newPrice = variantPrice - value;
    return newPrice < 0 ? 0 : newPrice;
  }

  if (type == RewardType.itemPercentage) {
    final value = promotion.rewards.first.value;
    final newPrice = variantPrice - calculatePercentage(variantPrice, value);
    return newPrice < 0 ? 0 : newPrice;
  }

  return variantPrice;
}

bool customerIsEligible(Variant variant) {
  final emptyCustomerGroup = variant.promotion?.customerGroups.isEmpty ?? false;
  final emptyExtChannel = variant.promotion?.extChannel.isEmpty ?? false;
  final validCustomerGroup = variant.promotion?.customerGroups.contains(
        UserCubit.instance?.currentOutlet?.customerGroup,
      ) ??
      false;
  final validExtChannel = variant.promotion!.extChannel.contains(
    getExtChannel(),
  );

  bool eligible = false;

  if (emptyCustomerGroup && emptyExtChannel) {
    eligible = true;
  }

  if (emptyCustomerGroup && validExtChannel) {
    eligible = true;
  }

  if (emptyExtChannel && validCustomerGroup) {
    eligible = true;
  }

  if (validCustomerGroup && validExtChannel) {
    eligible = true;
  }

  return eligible;
}
