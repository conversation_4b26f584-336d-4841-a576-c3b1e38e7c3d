import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/data/repo_impl/collection_repo_impl.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Base class for [CollectionRepoImplementation]
abstract class CollectionRepo {
  Future<ApiResult<CollectionResult?>> fetchCollection(CollectionParams params);

  Future<ApiResult<Collection>> fetchVariantCollection(
      VariantCollectionParams params);

  Future<ApiResult<Collection>> fetchMoreVariantCollection(
      MoreVariantCollectionParams params);

  Future<ApiResult<VariantInventory>> fetchVariantInventory(
      VariantInventoryParams params);

  Future<ApiResult<List<VariantInventory>>> fetchVariantInventoryList(
      VariantInventoryListParams params);

  Future<ApiResult<NewItemsResult>> fetchNewItems(NewItemsParams params);

  Future<ApiResult<List<Variant>>> fetchVariantsWithIds(List<String> params);

  Future<ApiResult<List<Collection>>> fetchBrands(String hexCode);

  Future<ApiResult<List<Variant>>> fetchVariants(FetchVariantsParams params);
}
