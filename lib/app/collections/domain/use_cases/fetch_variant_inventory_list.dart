import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_commons_flutter/models/index.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariantInventoryList
    with
        UseCases<ApiResult<List<VariantInventory>>,
            VariantInventoryListParams> {
  FetchVariantInventoryList(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo _repo;

  /// Return a list of variant stock count.
  @override
  Future<ApiResult<List<VariantInventory>>> call(
          VariantInventoryListParams params) =>
      _repo.fetchVariantInventoryList(params);
}
