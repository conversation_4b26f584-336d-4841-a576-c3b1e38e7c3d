import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariantsWithIds
    with UseCases<ApiResult<List<Variant>>, List<String>> {
  FetchVariantsWithIds(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns a list of product variants.
  @override
  Future<ApiResult<List<Variant>>> call(List<String> params) =>
      _repo!.fetchVariantsWithIds(params);
}
