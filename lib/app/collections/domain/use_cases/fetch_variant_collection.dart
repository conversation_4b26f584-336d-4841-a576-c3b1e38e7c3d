import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariantCollection
    with UseCases<ApiResult<Collection>, VariantCollectionParams> {
  FetchVariantCollection(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns product collection for
  /// the given [params.plusCode6Hex].
  ///
  /// Returns a paginated result.
  @override
  Future<ApiResult<Collection>> call(VariantCollectionParams params) =>
      _repo!.fetchVariantCollection(params);
}
