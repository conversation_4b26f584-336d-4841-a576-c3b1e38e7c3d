import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchBrands with UseCases<ApiResult<List<Collection>>, String> {
  FetchBrands(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns a list of `brands` as `List<Collection>`
  @override
  Future<ApiResult<List<Collection>>> call(String hexCode) =>
      _repo!.fetchBrands(hexCode);
}
