import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariants
    with UseCases<ApiResult<List<Variant>>, FetchVariantsParams> {
  FetchVariants(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns product collection for
  /// the given [params.plusCode6Hex].
  ///
  /// Returns a paginated result.
  @override
  Future<ApiResult<List<Variant>>> call(FetchVariantsParams params) =>
      _repo!.fetchVariants(params);
}
