import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchVariantInventory
    with UseCases<ApiResult<VariantInventory>, VariantInventoryParams> {
  FetchVariantInventory(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo _repo;

  /// Return the nunber of products in stock for the given [params].
  @override
  Future<ApiResult<VariantInventory>> call(VariantInventoryParams params) =>
      _repo.fetchVariantInventory(params);
}
