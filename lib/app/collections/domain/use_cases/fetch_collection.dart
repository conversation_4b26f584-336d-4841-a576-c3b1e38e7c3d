import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchCollection
    with UseCases<ApiResult<CollectionResult?>, CollectionParams> {
  FetchCollection(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns `page collection` products for
  /// the given [outlet.coordinates.plusCode6Hex].
  @override
  Future<ApiResult<CollectionResult?>> call(CollectionParams params) =>
      _repo!.fetchCollection(params);
}
