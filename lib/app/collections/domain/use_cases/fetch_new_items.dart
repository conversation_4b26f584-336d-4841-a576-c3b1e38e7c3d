import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

class FetchNewItems with UseCases<ApiResult<NewItemsResult>, NewItemsParams> {
  FetchNewItems(this._repo);

  /// Instance of [CollectionRepo].
  final CollectionRepo? _repo;

  /// Returns newly added products.
  @override
  Future<ApiResult<NewItemsResult>> call(NewItemsParams params) =>
      _repo!.fetchNewItems(params);
}
