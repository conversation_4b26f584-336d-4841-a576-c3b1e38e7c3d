import 'package:shop/app/collections/data/models/collection_result_cache.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_collection.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_variant_inventory.dart';
import 'package:shop/app/collections/domain/use_cases/fetch_variants_with_ids.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

import 'data/data_source/collection_data_source.dart';
import 'data/data_source/collection_data_source_impl.dart';
import 'data/repo_impl/collection_repo_impl.dart';
import 'domain/repositories/collection_repo.dart';
import 'domain/use_cases/fetch_brands.dart';
import 'domain/use_cases/fetch_more_variant_collection.dart';
import 'domain/use_cases/fetch_new_items.dart';
import 'domain/use_cases/fetch_variant_collection.dart';
import 'domain/use_cases/fetch_variant_inventory_list.dart';
import 'domain/use_cases/fetch_variants.dart';

void registerCollectionDependencies(AppConfig config) {
  // Use cases
  locator.registerLazySingleton(() => FetchCollection(locator()));
  locator.registerLazySingleton(() => FetchVariantCollection(locator()));
  locator.registerLazySingleton(() => FetchMoreVariantCollection(locator()));
  locator.registerLazySingleton(() => FetchVariantInventory(locator()));
  locator.registerLazySingleton(() => FetchVariantInventoryList(locator()));
  locator.registerLazySingleton(() => FetchNewItems(locator()));
  locator.registerLazySingleton(() => FetchVariantsWithIds(locator()));
  locator.registerLazySingleton(() => FetchBrands(locator()));
  locator.registerLazySingleton(() => CollectionsResultCache());
  locator.registerLazySingleton(() => FetchVariants(locator()));

  // Repositories
  locator.registerLazySingleton<CollectionRepo>(
    () => CollectionRepoImplementation(
        collectionDataSource: locator(), networkConnection: locator()),
  );

  // Data sources
  locator.registerLazySingleton<CollectionDataSource>(
    () => CollectionDataSourceImplementation(
      locator(),
      locator(),
      config.firebaseServiceUrl,
    ),
  );
}
