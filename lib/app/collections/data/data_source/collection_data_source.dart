import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'collection_data_source_impl.dart';

/// Base class for [CollectionDataSourceImplementation].
abstract class CollectionDataSource {
  Future<CollectionResult?> fetchCollection(CollectionParams params);

  Future<Collection> fetchMoreVariantCollection(
      MoreVariantCollectionParams params);

  Future<Collection> fetchVariantCollection(VariantCollectionParams params);

  Future<VariantInventory> fetchVariantInventory(VariantInventoryParams params);

  Future<List<VariantInventory>> fetchVariantInventoryList(
      VariantInventoryListParams params);

  Future<NewItemsResult> fetchNewItems(NewItemsParams params);

  Future<List<Variant>> fetchVariantsWithIds(List<String> params);

  Future<List<Collection>> fetchBrands(String hexCode);

  Future<List<Variant>> fetchVariants(FetchVariantsParams params);
}
