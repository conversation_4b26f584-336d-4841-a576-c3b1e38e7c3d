import 'package:shop/app/authentication/data/data_source/impl/authentication_remote_data_source_impl.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/data/models/collection_result_cache.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/services/api/td_api.dart';

import 'collection_data_source.dart';

/// Implements [CollectionDataSource] abstract class.
///
/// Makes network call.
class CollectionDataSourceImplementation implements CollectionDataSource {
  static const String _getNewItemsPath = 'shop/v4/getNewItems';
  static const String _pageCollectionPath = 'shop/v5/getPageCollection';
  static const String _collectionPath = 'shop/v4/getProductCollection';
  static const String _variantInventoryPath = 'shop/v4/getVariantInventory';
  static const String _brandsPath = 'shop/v5/getCategories';
  static const String _fetchVariantsPath = 'shop/v5/getVariants';

  /// Instance of [TdApiClient].
  ///
  /// Handles all http network request.
  final TdApiClient _apiClient;

  /// [CollectionsResultCache] cache.
  final CollectionsResultCache? _cache;

  /// API base url.
  final String? _firebaseServiceUrl;

  CollectionDataSourceImplementation(
    this._apiClient,
    this._cache,
    this._firebaseServiceUrl,
  );

  /// Returns `page collection` products for the given
  /// [outlet.coordinates.plusCode6Hex].
  @override
  Future<CollectionResult?> fetchCollection(CollectionParams params) async {
    late String url;
    late String cacheKey;
    CollectionResult? result;

    if (UserCubit.instance?.isAnonymous == true) {
      final anonUser = UserCubit.instance!.currentAnonymousUser!;
      url =
          '$_firebaseServiceUrl/shop/v4/getPageCollection?page=home&lat=${anonUser.latitude}&lng=${anonUser.longitude}';
      cacheKey = '${anonUser.id}_${anonUser.longitude}_${anonUser.latitude}';
    } else {
      final outlet = params.outlet;
      String hexCode = outlet?.coordinates?.plusCode6Hex ?? '';
      String outletId = outlet?.id ?? '';
      String customerGroup = outlet?.customerGroup ?? '';

      url =
          "$_firebaseServiceUrl/$_pageCollectionPath?page=home&plusCode6Hex=${Uri.encodeComponent(hexCode)}&outletId=$outletId&customerGroup=$customerGroup";

      cacheKey = '${outletId}_${hexCode}_$customerGroup';
    }

    final cacheResponse = _cache?.get(cacheKey);

    if (params.reload != true && cacheResponse != null) {
      result = cacheResponse;
      return result;
    } else {
      final res = await _apiClient.get(url);

      List<dynamic> col = res.data['collection'];
      List<dynamic> prefCol = res.data['preferenceCollection'] ?? [];

      List<Collection> collections =
          col.map((collection) => Collection.fromMap(collection)).toList();

      List<Collection> prefCollections =
          prefCol.map((collection) => Collection.fromMap(collection)).toList();

      CollectionResult result = CollectionResult(
          collections: [...prefCollections, ...collections],
          availableInArea: collections.isNotEmpty);

      _cache?.set(cacheKey, result);

      // try {
      //   if (UserCubit.instance?.isAnonymous == true) {
      //     final hexCode = res.data['plusCode6Hex'];
      //     final currency = getCurrencyFromCollections(collections);
      //     CartCubit.instance?.updateAnonymousCart(hexCode, currency);
      //     final anonUser = UserCubit.instance!.currentAnonymousUser!;
      //     UserCubit.instance?.updatingAnonymousUser(
      //       anonUser.copyWith(
      //         hexCode: hexCode,
      //         currency: currency,
      //       ),
      //       dry: true,
      //     );
      //   } else {
      //     final currency = getCurrencyFromCollections(collections);
      //     CartCubit.instance?.updateUserCart(currency);
      //   }
      // } catch (_) {}

      return result;
    }
  }

  /// Fetch product collection for the given [params.plusCode6Hex].
  ///
  /// Returns a paginated result.
  @override
  Future<Collection> fetchVariantCollection(
      VariantCollectionParams params) async {
    late String url;
    if (UserCubit.instance?.isAnonymous == true) {
      final anonUser = UserCubit.instance!.currentAnonymousUser!;
      final query = encodeMap(params.toMapForAnonUser(anonUser.hexCode));
      url = '$_firebaseServiceUrl/$_collectionPath?$query';
    } else {
      final query = encodeMap(params.toMap());
      url = '$_firebaseServiceUrl/$_collectionPath?$query';
    }

    final res = await _apiClient.get(url);
    final variantCollection = res.data['collection'];

    Collection collection = Collection.fromMap(variantCollection);
    return collection;
  }

  /// Returns product collection for the given [params.plusCode6Hex].
  ///
  /// Returns a paginated result.
  @override
  Future<Collection> fetchMoreVariantCollection(
      MoreVariantCollectionParams params) async {
    final query = encodeMap(params.toMap());
    String url = '$_firebaseServiceUrl/$_collectionPath?$query';
    final res = await _apiClient.get(url);
    final variantCollection = res.data['collection'];
    Collection collection = Collection.fromMap(variantCollection);
    return collection;
  }

  /// Returns the number of products in stock for the given [params].
  @override
  Future<VariantInventory> fetchVariantInventory(
      VariantInventoryParams params) async {
    final query = encodeMap(params.toMap());
    String url = '$_firebaseServiceUrl/$_variantInventoryPath?$query';
    final res = await _apiClient.get(url);

    late VariantInventory variantInventory;

    if (res.data != null && res.data['data'] != null) {
      final inventory = res.data['data'];
      variantInventory = VariantInventory.fromMap(inventory);
    } else {
      variantInventory = VariantInventory(
          variantId: params.variantId,
          quantity: 0,
          isExternal: false,
          variantPrice: 0);
    }

    return variantInventory;
  }

  @override
  Future<List<VariantInventory>> fetchVariantInventoryList(
      VariantInventoryListParams params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'path': 'v3/inventory/confirm-stock',
        'data': {
          "hexCode": params.hexCode,
          "variantIds": params.variantIds,
        }
      },
    );

    final result = res.data;
    List<dynamic> items = result['data'];
    final inventories = items.map((e) => VariantInventory.fromMap(e)).toList();
    return inventories;
  }

  @override
  Future<NewItemsResult> fetchNewItems(NewItemsParams params) async {
    String url =
        '$_firebaseServiceUrl/$_getNewItemsPath?hexCode=${Uri.encodeComponent(params.hexCode!)}&limit=${params.limit}&batch=${params.batch}&order=${params.order}';

    final res = await _apiClient.get(url);

    final result = res.data;

    List<dynamic> items = result['data'];
    int total = result['total'];

    List<Variant> variants =
        items.map((item) => Variant.fromMap(item)).toList();

    return NewItemsResult(variants: variants, total: total);
  }

  @override
  Future<List<Variant>> fetchVariantsWithIds(List<String> params) async {
    final res = await _apiClient.post(
      '$_firebaseServiceUrl/shop/v3/proxy',
      data: {
        'method': 'post',
        'type': 'claimsUrl',
        'path': '/get-variants',
        'data': {'variantIds': params}
      },
    );

    final result = res.data;
    List<dynamic> items = result['data'];
    final variants = items.map((e) => Variant.fromMap(e)).toList();
    return variants;
  }

  @override
  Future<List<Collection>> fetchBrands(String hexCode) async {
    String url =
        "$_firebaseServiceUrl/$_brandsPath?plusCode6Hex=${Uri.encodeComponent(hexCode)}&type=brands";

    final res = await _apiClient.get(url);

    List<dynamic> brands = res.data['collectionData'];

    return brands.map((e) => Collection.fromMap(e)).toList();
  }

  @override
  Future<List<Variant>> fetchVariants(FetchVariantsParams params) async {
    String url = "$_firebaseServiceUrl/$_fetchVariantsPath";

    final res = await _apiClient.post(url, data: params.toMap());

    List<dynamic> variants = res.data['result']['productVariants'];

    return variants.map((e) => Variant.fromMap(e)).toList();
  }
}

Currency? getCurrencyFromCollections(List<Collection> collections) {
  if (collections.isEmpty) return null;

  for (final collection in collections) {
    if (collection.variants == null || collection.variants!.isEmpty) {
      return null;
    }

    for (final variant in collection.variants!) {
      if (variant.currency != null) {
        return variant.currency;
      }
    }
  }
  return null;
}
