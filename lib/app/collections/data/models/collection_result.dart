import 'package:equatable/equatable.dart';
import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';

class CollectionResult extends Equatable {
  final List<Collection>? collections;
  final bool? availableInArea;
  const CollectionResult({this.collections, this.availableInArea});

  @override
  List get props => [collections, availableInArea];

  List<String> get variantIds =>
      collections
          ?.where((collection) => collection.variants != null)
          .expand((collection) => collection.variants!)
          .map((variant) => variant.variantId)
          .where((variantId) => variantId != null)
          .map((variantId) => variantId!)
          .toList() ??
      [];

  @override
  String toString() {
    return '''CollectionResult {
      collections: $collections,
      availableInArea: $availableInArea,
    }''';
  }

  Map<String, dynamic> toMap() {
    return {
      'collections': collections?.map((x) => x.toMap()).toList(),
      'availableInArea': availableInArea,
    };
  }

  factory CollectionResult.fromMap(Map<String, dynamic> map) {
    return CollectionResult(
      collections: map['collections'] != null
          ? List<Collection>.from(
              map['collections']?.map((x) => Collection.fromMap(x)))
          : null,
      availableInArea: map['availableInArea'],
    );
  }

  CollectionResult copyWith({
    List<Collection>? collections,
    bool? availableInArea,
  }) {
    return CollectionResult(
      collections: collections ?? this.collections,
      availableInArea: availableInArea ?? this.availableInArea,
    );
  }

  bool get isNotEmpty => collections != null && collections!.isNotEmpty;
}

class CollectionParams {
  final RetailOutlet? outlet;
  final bool? reload;

  CollectionParams(this.outlet, [this.reload = false]);
}

class VariantCollectionResult extends Equatable {
  final List<Variant>? variants;
  const VariantCollectionResult({this.variants});
  @override
  List get props => [variants];

  @override
  String toString() {
    return '''VariantCollectionResult {
      variants: $variants,
    }''';
  }
}

class VariantCollectionParams extends Equatable {
  final String? id;
  final int limit;
  final String? hexCode;
  final String? batch;
  final String? outletId;
  final String? customerGroup;

  const VariantCollectionParams({
    required this.id,
    this.limit = 30,
    required this.hexCode,
    this.batch = "1",
    this.outletId,
    required this.customerGroup,
  });

  @override
  List get props => [id, limit, hexCode, customerGroup, batch];

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'limit': limit,
      'batch': batch,
      'plusCode6Hex': hexCode,
      'outletId': outletId,
      'customerGroup': customerGroup,
    };
  }

  Map<String, dynamic> toMapForAnonUser(String? hexCode) {
    return {
      'id': id,
      'limit': limit,
      'batch': batch,
      'plusCode6Hex': hexCode,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class NewItemsParams extends Equatable {
  final int limit;
  final String? hexCode;
  final int batch;
  final String? order;

  const NewItemsParams({
    this.limit = variantListLimit,
    required this.hexCode,
    required this.batch,
    this.order = 'desc',
  });

  @override
  List get props => [limit, hexCode, batch, order];

  Map<String, dynamic> toMap() {
    return {
      'limit': limit,
      'hexCode': hexCode,
      'batch': batch,
      'order': order,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class NewItemsResult extends Equatable {
  final List<Variant> variants;
  final int total;

  const NewItemsResult({
    required this.variants,
    this.total = 0,
  });

  @override
  List get props => [variants, total];

  Map<String, dynamic> toMap() {
    return {
      'variants': variants.toList(),
      'total': total,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class MoreVariantCollectionParams extends Equatable {
  final String? id;
  final int limit;
  final String? plusCode6Hex;
  // final String? lastId;
  final String? batch;
  final String? outletId;
  final String? customerGroup;

  const MoreVariantCollectionParams({
    required this.id,
    this.limit = 30,
    required this.plusCode6Hex,
    required this.batch,
    //  required this.lastId,
    required this.outletId,
    required this.customerGroup,
  });

  @override
  List get props => [
        id, limit, plusCode6Hex, // lastId,
        batch, customerGroup
      ];

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'limit': limit,
      'plusCode6Hex': plusCode6Hex,
      //   'lastId': lastId,
      "batch": batch,
      'outletId': outletId,
      'customerGroup': customerGroup,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class VariantInventoryParams extends Equatable {
  final String? hexCode;
  final String? variantId;
  final String? outletId;

  const VariantInventoryParams({
    this.hexCode,
    this.variantId,
    this.outletId,
  });

  @override
  List get props => [hexCode, variantId, outletId];

  Map<String, dynamic> toMap() {
    return {
      'hexCode': hexCode,
      'variantId': variantId,
      'outletId': outletId,
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}

class VariantInventoryListParams extends Equatable {
  final String hexCode;
  final List<String> variantIds;

  const VariantInventoryListParams({
    required this.hexCode,
    required this.variantIds,
  });

  @override
  List get props => [hexCode, variantIds];

  Map<String, dynamic> toMap() {
    return {
      'hexCode': hexCode,
      'variantId': variantIds.toList(),
    };
  }

  @override
  String toString() {
    return '${toMap()}';
  }
}
