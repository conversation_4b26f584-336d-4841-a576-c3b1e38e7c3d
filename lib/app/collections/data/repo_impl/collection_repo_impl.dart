import 'package:shop/app/browse/data/models/collection.dart';
import 'package:shop/app/collections/data/data_source/collection_data_source.dart';
import 'package:shop/app/collections/data/models/collection_result.dart';
import 'package:shop/app/collections/domain/repositories/collection_repo.dart';
import 'package:shop/src/components/src/utils/dio.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/td_flutter_core.dart';

/// Implements the [CollectionRepo] abstract class.
///
/// [CollectionRepo] make calls to [CollectionDataSource].
class CollectionRepoImplementation implements CollectionRepo {
  /// Instance of [CollectionDataSource].
  final CollectionDataSource? collectionDataSource;

  /// Returns network connectivity state.
  final NetworkConnection? networkConnection;

  CollectionRepoImplementation(
      {this.collectionDataSource, this.networkConnection});

  /// Call [CollectionDataSource] to returns `page collection` products for
  /// the given [outlet.coordinates.plusCode6Hex].
  @override
  Future<ApiResult<CollectionResult?>> fetchCollection(
      CollectionParams params) async {
    if (await networkConnection!.isDeviceConnected) {
      try {
        CollectionResult? result =
            await collectionDataSource!.fetchCollection(params);
        return ApiResult.success(data: result);
      } on DioError catch (exception) {
        if (exception.response?.statusCode == 404) {
          CollectionResult result =
              const CollectionResult(collections: [], availableInArea: false);
          return ApiResult.success(data: result);
        }
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      } catch (exception, s) {
        ErrorHandler.report(exception, s);
        return ApiResult.apiFailure(
            error: ApiExceptions.getDioException(exception));
      }
    } else {
      return const ApiResult.apiFailure(
          error: ApiExceptions.noInternetConnection());
    }
  }

  /// Call [CollectionDataSource]
  @override
  Future<ApiResult<Collection>> fetchVariantCollection(
      VariantCollectionParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchVariantCollection(params),
    );
  }

  /// Call [CollectionDataSource] to fetch product collection for
  /// the given [params.plusCode6Hex].
  ///
  /// Returns a paginated result.
  @override
  Future<ApiResult<Collection>> fetchMoreVariantCollection(
      MoreVariantCollectionParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchMoreVariantCollection(params),
    );
  }

  /// Call [CollectionDataSource] to return the number of products in stock for the given [params].
  @override
  Future<ApiResult<VariantInventory>> fetchVariantInventory(
      VariantInventoryParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchVariantInventory(params),
    );
  }

  @override
  Future<ApiResult<List<VariantInventory>>> fetchVariantInventoryList(
      VariantInventoryListParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchVariantInventoryList(params),
    );
  }

  @override
  Future<ApiResult<NewItemsResult>> fetchNewItems(NewItemsParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchNewItems(params),
    );
  }

  @override
  Future<ApiResult<List<Variant>>> fetchVariantsWithIds(List<String> params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchVariantsWithIds(params),
    );
  }

  @override
  Future<ApiResult<List<Collection>>> fetchBrands(String hexCode) {
    return dioInterceptor(
      () => collectionDataSource!.fetchBrands(hexCode),
    );
  }

  @override
  Future<ApiResult<List<Variant>>> fetchVariants(FetchVariantsParams params) {
    return dioInterceptor(
      () => collectionDataSource!.fetchVariants(params),
    );
  }
}
