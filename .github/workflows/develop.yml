name: Continuous Integration & Deployment

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop
jobs:
  build_web_prime:
    name: Build (Prime Web)
    runs-on: ubuntu-latest
    environment:
      name: dev
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.5'
      - name: Run dart env
        run: dart tool/env.dart
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
          ORDER_PAYSTACK_URL : https://dev.tradedepot.co/app-paystack.html
          FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
          WHATSAPP_URL : https://r5zbm6nq76.execute-api.us-east-1.amazonaws.com/dev
          PRIVACY_URL: https://dev.tradedepot.co/privacy
          TERMS_URL: https://dev.tradedepot.co/terms
          SHIPPING_AND_RETURNS: https://dev.tradedepot.co/returns
          INTERCOM_ANDROID_API_KEY: ${{ secrets.TD_INTERCOM_ANDROID_API_KEY }}
          INTERCOM_IOS_API_KEY: ${{ secrets.TD_INTERCOM_IOS_API_KEY }}
          INTERCOM_APP_ID: ${{ vars.TD_INTERCOM_APP_ID }}
          DSN: ${{ secrets.DSN }}
          CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
          APP_HOST: tradedepot
          TERMINAL_ENABLED: "true"
          DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          RECAPTCHA_SITE_KEY: ${{ secrets.RECAPTCHA_SITE_KEY }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          FIREBASE_DATABASE_URL: ${{ secrets.FIREBASE_DATABASE_URL }}
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          FIREBASE_APP_ID: ${{ secrets.FIREBASE_WEB_APP_ID }}
          MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
          SMILE_CALLBACK_URL: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev/smile-webhook
          BUSINESS_FILE_UPLOAD: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_KYBDOC_BUCKET: td-doc-dev
          FINGER_PRINT_ENDPOINT: "https://metrics.sandbox.prime.shoptopup.com"
          FINGER_PRINT_KEY: ${{ secrets.FINGER_PRINT_KEY }}
          FINGER_PRINT_ENABLED: "false"
         
         
      - name: Run Web env
        run: . ./parse-tmp-env.sh ./web/index.example.html ./web/index.html
        env:
          SEGMENT_MOBILE_WEB: ${{ secrets.SEGMENT_MOBILE_WEB }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
      # - run: flutter upgrade
      - run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - run: flutter pub get
      - run: flutter test
      - run: flutter config --enable-web
      - run: flutter build web --release --dart-define=FLUTTER_WEB_USE_EXPERIMENTAL_CANVAS_RICH_TEXT=true --source-maps #--web-renderer canvaskit
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
      - name: Deploy static site to S3 bucket
        env:
          S3_BUCKET_NAME: dev-prime-shoptopup-web
          S3_CACHE_MAX_AGE: ${{ secrets.S3_CACHE_MAX_AGE }}
        run: |
          aws s3 sync build/web s3://dev-prime-shoptopup-web --delete
          aws s3 cp s3://dev-prime-shoptopup-web/ s3://dev-prime-shoptopup-web/ --metadata-directive REPLACE \
          --exclude "*" --include "*.jpg" --include "*.gif" --include "*.png" \
          --recursive --cache-control max-age=$S3_CACHE_MAX_AGE
      - name: Invalidate Cloud front distribution
        env:
          CLOUD_FRONT_DISTRIBUTION: ${{ secrets.PRIME_CLOUD_FRONT_DISTRIBUTION }}
        run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"

  build_web_mobile:
    name: Build (Web/PWA)
    runs-on: ubuntu-latest
    environment:
      name: dev
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.5'
      - name: Run dart env
        run: dart tool/env.dart
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
          ORDER_PAYSTACK_URL: https://dev.tradedepot.co/app-paystack.html
          WHATSAPP_URL : https://r5zbm6nq76.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
          PRIVACY_URL: https://dev.tradedepot.co/privacy
          TERMS_URL: https://dev.tradedepot.co/terms
          SHIPPING_AND_RETURNS: https://dev.tradedepot.co/returns
          INTERCOM_ANDROID_API_KEY: ${{ secrets.TD_INTERCOM_ANDROID_API_KEY }}
          INTERCOM_IOS_API_KEY: ${{ secrets.TD_INTERCOM_IOS_API_KEY }}
          INTERCOM_APP_ID: ${{ vars.TD_INTERCOM_APP_ID }}
          DSN: ${{ secrets.DSN }}
          CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
          DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          APP_HOST: tradedepot
          RECAPTCHA_SITE_KEY: ${{ secrets.RECAPTCHA_SITE_KEY }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          FIREBASE_DATABASE_URL: ${{ secrets.FIREBASE_DATABASE_URL }}
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          FIREBASE_APP_ID: ${{ secrets.FIREBASE_WEB_APP_ID }}
          MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
          SMILE_CALLBACK_URL: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev/smile-webhook
          BUSINESS_FILE_UPLOAD: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_KYBDOC_BUCKET: td-doc-dev
          FINGER_PRINT_ENDPOINT: "https://metrics.sandbox.m.shoptopup.com"
          FINGER_PRINT_KEY: ${{ secrets.FINGER_PRINT_KEY }}
          FINGER_PRINT_ENABLED: "true"
          TERMINAL_ENABLED: "true"
       
      - name: Run Web env
        run: . ./parse-tmp-env.sh ./web/index.example.html ./web/index.html
        env:
          SEGMENT_MOBILE_WEB: ${{ secrets.SEGMENT_MOBILE_WEB }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
      # - run: flutter upgrade
      - run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - run: flutter pub get
      - run: flutter test
      - run: flutter config --enable-web
      - run: flutter build web --release --dart-define=FLUTTER_WEB_USE_EXPERIMENTAL_CANVAS_RICH_TEXT=true --source-maps #--web-renderer canvaskit
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
      - name: Deploy static site to S3 bucket
        env:
          S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
          S3_CACHE_MAX_AGE: ${{ secrets.S3_CACHE_MAX_AGE }}
        run: |
          aws s3 sync build/web s3://$S3_BUCKET_NAME --delete
          aws s3 cp s3://$S3_BUCKET_NAME/ s3://$S3_BUCKET_NAME/ --metadata-directive REPLACE \
          --exclude "*" --include "*.jpg" --include "*.gif" --include "*.png" \
          --recursive --cache-control max-age=$S3_CACHE_MAX_AGE
      - name: Invalidate Cloud front distribution
        env:
          CLOUD_FRONT_DISTRIBUTION: ${{ secrets.CLOUD_FRONT_DISTRIBUTION }}
        run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"

  build_trade_depot_apk:
    name: Build TradeDepot (APK)
    runs-on: ubuntu-latest
    environment:
      name: dev
    steps:
      - uses: actions/checkout@v3
      - name: Setup Java
        uses: actions/setup-java@v2
        with:
          distribution: 'zulu'
          java-version: '17.x'
      - name: Setup flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.5'
      - name: Output Build Name
        uses: satackey/action-js-inline@release-master
        id: getTag
        with:
          script: |
            const core = require('@actions/core')
            const github = require('@actions/github')
            const number = github.context && github.context.issue && github.context.issue.number;
            const buildName = number ? `PR-${number}` : `${process.env.GITHUB_EVENT_NAME || "develop"}-${process.env.GITHUB_RUN_NUMBER}`
            core.setOutput('buildName', buildName)
      - name: Run dart env
        run: dart tool/env.dart
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
          ORDER_PAYSTACK_URL: https://dev.tradedepot.co/app-paystack.html
          WHATSAPP_URL : https://r5zbm6nq76.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
          PRIVACY_URL: https://dev.tradedepot.co/privacy
          TERMS_URL: https://dev.tradedepot.co/terms
          SHIPPING_AND_RETURNS: https://dev.tradedepot.co/returns
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
          INTERCOM_ANDROID_API_KEY: ${{ secrets.TD_INTERCOM_ANDROID_API_KEY }}
          INTERCOM_IOS_API_KEY: ${{ secrets.TD_INTERCOM_IOS_API_KEY }}
          INTERCOM_APP_ID: ${{ vars.TD_INTERCOM_APP_ID }}
          DSN: ${{ secrets.DSN }}
          CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
          APP_HOST: tradedepot
          DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
          SMILE_CALLBACK_URL: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev/smile-webhook
          BUSINESS_FILE_UPLOAD: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_KYBDOC_BUCKET: td-doc-dev
          TERMINAL_ENABLED: "true"
          FINGER_PRINT_ENDPOINT: "https://metrics.sandbox.m.shoptopup.com"
          FINGER_PRINT_KEY: ${{ secrets.FINGER_PRINT_KEY }}
          FINGER_PRINT_ENABLED: "true"
          SEGMENT_IOS: ${{ secrets.SHOP_TOPUP_SEGMENT_IOS }}
          SEGMENT_ANDROID: ${{ secrets.SHOP_TOPUP_SEGMENT_ANDROID }}
          AIRSHIP_APP_KEY: ${{ secrets.SHOP_TOP_UP_AIRSHIP_APP_KEY }}
          AIRSHIP_APP_SECRET: ${{ secrets.SHOP_TOP_UP_AIRSHIP_APP_SECRET  }}
          NOTIFICATION_ICON: 'ic_stat_tradedepot'
          NOTIFICATION_CHANNEL: 'TradeDepot'
          NOTIFICATION_ACCENT: '#FF8D06'

    #  - run: flutter upgrade
      - name: Run git config
        run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - name: Decode google-services.json
        env:
          GOOGLE_SERVICES: ${{ secrets.SHOP_TOP_UP_GOOGLE_SERVICES }}
        run: |
          mkdir -p android/app/src/dev
          echo $GOOGLE_SERVICES > android/app/src/dev/google-services.json
      - name: Decode key.properties
        env:
          KEY_PROPERTIES: ${{ secrets.KEY_PROPERTIES }}
        run: |
          mkdir -p android
          echo $KEY_PROPERTIES | base64 --decode > android/key.properties
      - name: Decode Key Store File
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        run: |
          mkdir -p android/app
          echo $SIGNING_KEY | base64 --decode > android/app/shop-release-key.keystore
      # - name: Decode Airship Config Properties
      #   env:
      #     AIRSHIP_PROPERTIES: ${{ secrets.AIRSHIP_PROPERTIES }}
      #   run: |
      #     mkdir -p android/app/src/dev/assets
      #     echo $AIRSHIP_PROPERTIES | base64 --decode > android/app/src/dev/assets/airshipconfig.properties
      - name: Run flutter clean
        run: flutter clean
      - name: Install flutter dependencies
        run: flutter pub get
      # - name: Test
      #   run: flutter test --coverage
      # - name: Setup Codecov
      #   run: curl -Os https://uploader.codecov.io/latest/linux/codecov && chmod +x codecov
      # - name: Coverage report
      #   run: ./codecov -t ${{ secrets.CODECOV_TOKEN }}
      - name: Build the apk
        run: |
         flutter build apk --release \
         --flavor dev \
         --build-name ${{ steps.getTag.outputs.buildName }} \
         --build-number ${{ github.run_number }} \
         --dart-define=INTERCOM_APP_ID=${{ vars.TD_INTERCOM_APP_ID }} \
         --dart-define=INTERCOM_ANDROID_API_KEY=${{ secrets.TD_INTERCOM_ANDROID_API_KEY }} \
      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: apk-trade-depot-build
          path: build/app/outputs/apk/dev/release


  build_trade_depot_up_ios:
    name: Build TradeDepot iOS (IPA)
    runs-on: macos-15
    environment:
      name: dev
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.10' 
      - name: Select Xcode 16.3.0
        run: |
          sudo xcode-select -s /Applications/Xcode_16.3.0.app
      - name: Install the Apple certificate and provisioning profile
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.APPSTORE_CERT_BASE64 }}
          P12_PASSWORD: ${{ secrets.APPSTORE_CERT_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.SHOP_TOP_UP_MOBILE_PROVISION_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          # create variables
          CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db

          # import certificate and provisioning profile from secrets
          echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode -o $CERTIFICATE_PATH
          echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode -o $PP_PATH

          # create temporary keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH

          # import certificate to keychain
          security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH

          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles
      - name: Install codemagic-cli-tools
        run: python -m pip install codemagic-cli-tools
      - name: Set up code signing settings on Xcode project
        run: |
          xcode-project use-profiles --profile ~/Library/MobileDevice/Provisioning\ Profiles/build_pp.mobileprovision \
          --export-options-plist=$RUNNER_TEMP/ExportOptions.plist \
      - name: Setup flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.5'
      - name: Output Build Name
        uses: satackey/action-js-inline@release-master
        id: getTag
        with:
          script: |
            const core = require('@actions/core')
            const github = require('@actions/github')
            const number = github.context && github.context.issue && github.context.issue.number;
            const buildName = number ? `PR-${number}` : `${process.env.GITHUB_EVENT_NAME || "develop"}-${process.env.GITHUB_RUN_NUMBER}`
            core.setOutput('buildName', buildName)
      - name: Run dart env
        run: dart tool/env.dart
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
          ORDER_PAYSTACK_URL: https://dev.tradedepot.co/app-paystack.html
          WHATSAPP_URL : https://r5zbm6nq76.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
          PRIVACY_URL: https://dev.tradedepot.co/privacy
          TERMS_URL: https://dev.tradedepot.co/terms
          SHIPPING_AND_RETURNS: https://dev.tradedepot.co/returns
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
          INTERCOM_ANDROID_API_KEY: ${{ secrets.TD_INTERCOM_ANDROID_API_KEY }}
          INTERCOM_IOS_API_KEY: ${{ secrets.TD_INTERCOM_IOS_API_KEY }}
          INTERCOM_APP_ID: ${{ vars.TD_INTERCOM_APP_ID }}
          DSN: ${{ secrets.DSN }}
          CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
          APP_HOST: tradedepot
          DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          VAPID_KEY: ${{ secrets.VAPID_KEY }}
          MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
          SMILE_CALLBACK_URL: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev/smile-webhook
          BUSINESS_FILE_UPLOAD: https://irwwkqfy8d.execute-api.us-east-1.amazonaws.com/dev
          FIREBASE_KYBDOC_BUCKET: td-doc-dev
          TERMINAL_ENABLED: "true"
          FINGER_PRINT_ENDPOINT: "https://metrics.sandbox.m.shoptopup.com"
          FINGER_PRINT_KEY: ${{ secrets.FINGER_PRINT_KEY }}
          FINGER_PRINT_ENABLED: "true"
          SEGMENT_IOS: ${{ secrets.SHOP_TOPUP_SEGMENT_IOS }}
          SEGMENT_ANDROID: ${{ secrets.SHOP_TOPUP_SEGMENT_ANDROID }}
          AIRSHIP_APP_KEY: ${{ secrets.SHOP_TOP_UP_AIRSHIP_APP_KEY }}
          AIRSHIP_APP_SECRET: ${{ secrets.SHOP_TOP_UP_AIRSHIP_APP_SECRET  }}
 

      # - run: flutter upgrade
      - name: Run git config
        run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      
      - name: Run flutter clean
        run: flutter clean
      - name: Install flutter dependencies
        run: flutter pub get
      # - name: Test
      #   run: flutter test --coverage
      # - name: Setup Codecov
      #   run: curl -Os https://uploader.codecov.io/latest/linux/codecov && chmod +x codecov
      # - name: Coverage report
      #   run: ./codecov -t ${{ secrets.CODECOV_TOKEN }}
      - name: Build and sign IPA
        run: |
          flutter build ipa --release \
            --flavor dev \
            --build-name  ${{ steps.getTag.outputs.buildName }} \
            --build-number ${{ github.run_number }} \
            --dart-define=INTERCOM_APP_ID=${{ vars.TD_INTERCOM_APP_ID }} \
            --dart-define=INTERCOM_IOS_KEY=${{ secrets.TD_INTERCOM_IOS_API_KEY }} \
            --export-options-plist=$RUNNER_TEMP/ExportOptions.plist  \
      # Collect the file and upload as artifact         
      - name: collect ipa artifacts
        uses: actions/upload-artifact@v4
        with:
         name: trade-depot-ipa-build
         # Path to the release files
         path: build/ios/ipa/*.ipa
      # Important! Cleanup: remove the certificate and provisioning profile from the runner!
      - name: Clean up keychain and provisioning profile
        if: ${{ always() }}
        run: |
          security delete-keychain $RUNNER_TEMP/app-signing.keychain-db
          rm ~/Library/MobileDevice/Provisioning\ Profiles/build_pp.mobileprovision

  deploy_trade_depot_android_apk:
    name: Upload TradeDepot Android App to Firebase App Distribution
    needs: [build_trade_depot_apk]
    environment:
      name: dev
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Java
        uses: actions/setup-java@v2
        with:
          distribution: 'zulu'
          java-version: '17'
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: apk-trade-depot-build
      - name: Upload APK
        uses: wzieba/Firebase-Distribution-Github-Action@v1.7.0
        with:
          appId: ${{secrets.FIREBASE_SHOP_TOP_UP_ANDROID_APP_ID}}
          token: ${{secrets.FIREBASE_TOKEN}}
          groups: shop-app-testers
          file: app-dev-release.apk
        env:
          FIREBASE_APP_ID: ${{ secrets.FIREBASE_SHOP_TOP_UP_ANDROID_APP_ID }}


  deploy_trade_depot_ios_ipa:
    name: Upload TradeDepot iOS App to Firebase App Distribution
    needs: [build_trade_depot_up_ios]
    environment:
      name: dev
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: trade-depot-ipa-build
      - name: Upload IPA
        uses: wzieba/Firebase-Distribution-Github-Action@v1.7.0
        with:
          appId: ${{secrets.FIREBASE_SHOP_TOP_UP_IOS_APP_ID}}
          token: ${{secrets.FIREBASE_TOKEN}}
          groups: shop-app-testers
          file: TradeDepotDev.ipa
        env:
          FIREBASE_APP_ID: ${{ secrets.FIREBASE_SHOP_TOP_UP_IOS_APP_ID }}