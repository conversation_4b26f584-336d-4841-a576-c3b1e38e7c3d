name: Continuous Integration & Deployment

on:
  push:
    tags:
      - 'beta*'

jobs:
  # build_web:
  #   name: Build (Web)
  #   runs-on: ubuntu-latest
  #   environment:
  #     name: prod
  #   steps:
  #     - uses: actions/checkout@v3
  #     - uses: subosito/flutter-action@v2
  #       with:
  #         flutter-version: '3.10.5'
  #         channel: 'stable'
  #     - name: Output Build Name
  #       uses: satackey/action-js-inline@release-master
  #       id: getTag
  #       with:
  #         script: |
  #           const core = require('@actions/core')
  #           // get tag
  #           const ref = process.env.GITHUB_REF // refs/tags/v0.3.1
  #           const tag = ref.split('/').slice(-1)[0] // refs/tags/0.3.1 → v0.3.1
  #           let buildName = tag
  #           if (tag.startsWith("beta")) {
  #             buildName = tag.substring(4);
  #           }
  #           core.setOutput('tagName', tag)
  #           core.setOutput('buildName', buildName)
  #     - name: Run dart env
  #       run: dart tool/env.dart
  #       env:
  #         GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
  #         PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
  #         FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
  #         PRIVACY_URL: https://shoptopup.com/privacy
  #         TERMS_URL: https://shoptopup.com/terms
  #         INTERCOM_ANDROID_API_KEY: ${{ secrets.INTERCOM_ANDROID_API_KEY }}
  #         INTERCOM_IOS_API_KEY: ${{ secrets.INTERCOM_IOS_API_KEY }}
  #         INTERCOM_APP_ID: ${{ secrets.INTERCOM_APP_ID }}
  #         DSN: ${{ secrets.DSN }}
  #         BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
  #         CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
  #         APP_HOST: ${{ secrets.APP_HOST }}
  #         DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
  #         ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
  #         SENTRY_RELEASE_NAME: ${{ steps.getTag.outputs.tagName }}
  #         RECAPTCHA_SITE_KEY: ${{ secrets.RECAPTCHA_SITE_KEY }}
  #         FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
  #         FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
  #         FIREBASE_DATABASE_URL: ${{ secrets.FIREBASE_DATABASE_URL }}
  #         FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
  #         FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
  #         FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
  #         FIREBASE_APP_ID: ${{ secrets.FIREBASE_WEB_APP_ID }}
  #         MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
  #         SMILE_CALLBACK_URL: https://d8w0snbim6.execute-api.us-east-1.amazonaws.com/prod/smile-webhook
  #         BUSINESS_FILE_UPLOAD: https://d8w0snbim6.execute-api.us-east-1.amazonaws.com/prod
  #         FIREBASE_KYBDOC_BUCKET: td-doc-prod
  #         TERMINAL_ENABLED: "true"
  #     - name: Run Web env
  #       run: . ./parse-tmp-env.sh ./web/index.example.html ./web/index.html
  #       env:
  #         SEGMENT_MOBILE_WEB: ${{ secrets.SEGMENT_MOBILE_WEB }}
  #         INTERCOM_APP_ID: ${{ secrets.INTERCOM_APP_ID }}
  #         BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
  #     # - run: flutter upgrade
  #     - run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
  #     - run: flutter pub get
  #     - run: flutter test
  #     - run: flutter config --enable-web
  #     - run: flutter build web --release --web-renderer html --dart-define=FLUTTER_WEB_USE_EXPERIMENTAL_CANVAS_RICH_TEXT=true --source-maps #--web-renderer canvaskit
  #     - name: Configure AWS Credentials
  #       uses: aws-actions/configure-aws-credentials@v1
  #       with:
  #         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
  #         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  #         aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
  #     - name: Deploy static site to S3 bucket
  #       env:
  #         S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
  #         S3_CACHE_MAX_AGE: ${{ secrets.S3_CACHE_MAX_AGE }}
  #       run: |
  #         aws s3 sync build/web s3://$S3_BUCKET_NAME --delete
  #         aws s3 cp s3://$S3_BUCKET_NAME/ s3://$S3_BUCKET_NAME/ --metadata-directive REPLACE \
  #         --exclude "*" --include "*.jpg" --include "*.gif" --include "*.png" \
  #         --recursive --cache-control max-age=$S3_CACHE_MAX_AGE
  #     - name: Invalidate Cloud front distribution
  #       env:
  #         CLOUD_FRONT_DISTRIBUTION: ${{ secrets.CLOUD_FRONT_DISTRIBUTION }}
  #       run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"
  #     - name: Invalidate Prime Cloud front distribution
  #       env:
  #         CLOUD_FRONT_DISTRIBUTION: ${{ secrets.PRIME_CLOUD_FRONT_DISTRIBUTION }}
  #       run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"
  #     - name: Install Sentry cli
  #       run: curl -sL https://sentry.io/get-cli/ | bash
  #     - name: Upload release to sentry
  #       run: |
  #         sentry-cli releases new ${{ steps.getTag.outputs.tagName }} 
  #         sentry-cli releases files ${{ steps.getTag.outputs.tagName }} upload-sourcemaps build/web
  #         sentry-cli releases finalize  ${{ steps.getTag.outputs.tagName }}
  #       env:
  #         SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  #         SENTRY_DSN: ${{ secrets.DSN }}
  #         SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
  #         SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}

  build_apk:
    name: Build apk
    runs-on: ubuntu-latest
    environment:
      name: prod
    steps:
      - uses: actions/checkout@v3
      - name: Setup Java
        uses: actions/setup-java@v2
        with:
          distribution: 'zulu'
          java-version: '17.x'
      - name: Setup flutter
        uses: subosito/flutter-action@v2
        with:
          # flutter-version: '3.10.5'
          channel: 'stable'
      - name: Output Build Name
        uses: satackey/action-js-inline@release-master
        id: getTag
        with:
          script: |
            const core = require('@actions/core')
            // get tag
            const ref = process.env.GITHUB_REF // refs/tags/v0.3.1
            const tag = ref.split('/').slice(-1)[0] // refs/tags/0.3.1 → v0.3.1
            let buildName = tag
            if (tag.startsWith("beta")) {
              buildName = `${tag.substring(4)}-beta`;
            }
            core.setOutput('tagName', tag)
            core.setOutput('buildName', buildName)
      - name: Run dart env
        run: dart tool/env.dart
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          PAYSTACK_KEY: ${{ secrets.PAYSTACK_KEY }}
          FIREBASE_SERVICE_URL: ${{ secrets.FIREBASE_SERVICE_URL }}
          PRIVACY_URL: https://dev.tradedepot.co/privacy
          TERMS_URL: https://dev.tradedepot.co/terms
          BRANCH_KEY: ${{ secrets.BRANCH_KEY }}
          INTERCOM_ANDROID_API_KEY: ${{ secrets.INTERCOM_ANDROID_API_KEY }}
          INTERCOM_IOS_API_KEY: ${{ secrets.INTERCOM_IOS_API_KEY }}
          INTERCOM_APP_ID: ${{ secrets.INTERCOM_APP_ID }}
          DSN: ${{ secrets.DSN }}
          CATALOG_SEARCH_URL: ${{ secrets.CATALOG_SEARCH_URL }}
          APP_HOST: ${{ secrets.APP_HOST }}
          DYNAMIC_LINK_DOMAIN: ${{ secrets.DYNAMIC_LINK_DOMAIN }}
          ENVIRONMENT: ${{ secrets.ENVIRONMENT }}
          MONO_PUBLIC_KEY: ${{ secrets.MONO_PUBLIC_KEY }}
          SENTRY_RELEASE_NAME: ${{ steps.getTag.outputs.tagName }}
          SMILE_CALLBACK_URL: https://d8w0snbim6.execute-api.us-east-1.amazonaws.com/prod/smile-webhook
          BUSINESS_FILE_UPLOAD: https://d8w0snbim6.execute-api.us-east-1.amazonaws.com/prod
          FIREBASE_KYBDOC_BUCKET: td-doc-prod
          TERMINAL_ENABLED: "true"
          FINGER_PRINT_ENDPOINT: "https://metrics.m.shoptopup.com"
          FINGER_PRINT_KEY: ${{ secrets.FINGER_PRINT_KEY }}
          FINGER_PRINT_ENABLED: "true"

      - run: flutter upgrade
      - name: Run git config
        run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - name: Decode google-services.json
        env:
          GOOGLE_SERVICES: ${{ secrets.GOOGLE_SERVICES }}
        run: |
          mkdir -p android/app/src/prod
          echo $GOOGLE_SERVICES > android/app/src/prod/google-services.json
      - name: Decode key.properties
        env:
          KEY_PROPERTIES: ${{ secrets.KEY_PROPERTIES }}
        run: |
          mkdir -p android
          echo $KEY_PROPERTIES | base64 --decode > android/key.properties
      - name: Decode Key Store File
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        run: |
          mkdir -p android/app
          echo $SIGNING_KEY | base64 --decode > android/app/shop-release-key.keystore
      - name: Decode Airship Config Properties
        env:
          AIRSHIP_PROPERTIES: ${{ secrets.AIRSHIP_PROPERTIES }}
        run: |
          mkdir -p android/app/src/prod/assets
          echo $AIRSHIP_PROPERTIES | base64 --decode > android/app/src/prod/assets/airshipconfig.properties
      - name: Decode Radar Config Properties
        env:
          RADAR_PROPERTIES: ${{ secrets.RADAR_PROPERTIES }}
        run: |
          mkdir -p android/app/src/prod/assets
          echo $RADAR_PROPERTIES | base64 --decode > android/app/src/prod/assets/radarconfig.properties
      - name: Run flutter clean
        run: flutter clean
      - name: Install flutter dependencies
        run: flutter pub get
      - name: Test
        run: flutter test
      - name: Build APK
        run: flutter build apk --split-per-abi --release --flavor prod --build-name  ${{ steps.getTag.outputs.buildName }}  --build-number 200${{ github.run_number }} --dart-define=INTERCOM_APP_ID=${{ secrets.INTERCOM_APP_ID }} --dart-define=INTERCOM_ANDROID_API_KEY=${{ secrets.INTERCOM_ANDROID_API_KEY }}
      - name: Upload APK
        uses: actions/upload-artifact@master
        with:
          name: apk-build
          path: build/app/outputs/apk/prod/release
      - name: Build APP Bundle
        run: flutter build appbundle --release --flavor prod --build-name  ${{ steps.getTag.outputs.buildName }}  --build-number 200${{ github.run_number }} --dart-define=INTERCOM_APP_ID=${{ secrets.INTERCOM_APP_ID }} --dart-define=INTERCOM_ANDROID_API_KEY=${{ secrets.INTERCOM_ANDROID_API_KEY }}
      - name: Upload App Bundle
        uses: actions/upload-artifact@master
        with:
          name: appbundle-build
          path: build/app/outputs/bundle/prodRelease

  deploy_apk:
    name: Create Github Release
    needs: [build_apk]
    environment:
      name: prod
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Java
        uses: actions/setup-java@v2
        with:
          distribution: 'zulu'
          java-version: '17'
      - name: Download App Artifact
        uses: actions/download-artifact@v4
        with:
          name: apk-build
      - name: Download Bundle Artifact
        uses: actions/download-artifact@v4
        with:
          name: appbundle-build
      - name: Zip Artifact
        run: |
          zip --junk-paths app app-prod-arm64-v8a-release.apk app-prod-armeabi-v7a-release.apk app-prod-release.aab

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false

      - name: Upload Release Asset
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps
          asset_path: ./app.zip
          asset_name: app.zip
          asset_content_type: application/zip
